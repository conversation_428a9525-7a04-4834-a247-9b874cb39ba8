package com.juneyaoair.sso.service;

import com.juneyaoair.sso.dto.LoginResult;
import com.juneyaoair.sso.dto.LogoutParam;
import com.juneyaoair.sso.dto.ParseTokenResult;
import com.juneyaoair.sso.dto.RefreshAccountStatusParam;
import com.juneyaoair.sso.dto.UserInfo;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/2 13:14
 */
public interface ITokenService {

    /**
     * 创建会员登录凭据
     * @param channelCode 登录渠道
     * @param ffpId 会员ID
     * @param ffpNo 会员卡号
     * @return
     */
    LoginResult createToken(String channelCode, String ffpId, String ffpNo);

    /**
     * 生成token
     * @param channelNo
     * @param userInfo
     * @return
     */
    LoginResult createToken(String channelNo, UserInfo userInfo);

    /**
     * 解析token
     * @param token
     * @return
     */
    ParseTokenResult parseToken(String token);

    /**
     * 注销登录
     * @param logoutParam
     */
    void logout(String channelNo, LogoutParam logoutParam);

    /**
     * 刷新会员账号状态
     * @param refreshAccountStatusParam
     */
    void refreshAccountStatus(RefreshAccountStatusParam refreshAccountStatusParam);
}
