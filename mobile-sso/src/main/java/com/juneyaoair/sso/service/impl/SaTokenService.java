package com.juneyaoair.sso.service.impl;

import cn.dev33.satoken.jwt.exception.SaJwtException;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.dto.MemberAccountInfo;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.sso.dto.LoginResult;
import com.juneyaoair.sso.dto.LogoutParam;
import com.juneyaoair.sso.dto.ParseTokenResult;
import com.juneyaoair.sso.dto.RefreshAccountStatusParam;
import com.juneyaoair.sso.dto.UserInfo;
import com.juneyaoair.sso.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description  本服务使用公共的saToken框架服务
 * @date 2024/2/4 9:42
 */
@Slf4j
@Service
public class SaTokenService implements ITokenService {

    /** 会员ID */
    private static final String FFP_ID = "ffpId";
    /** 会员卡号 */
    private static final String FFP_NO = "ffpNo";
    /** 用户openId(如：微信openId) */
    private static final String OPEN_ID = "openId";
    /** 用户userId(如：支付宝userId) */
    private static final String USER_ID = "userId";

    @Autowired
    private IMemberService memberService;
    @Autowired
    private CommonService commonService;

    @Override
    public LoginResult createToken(String channelCode, String ffpId, String ffpNo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setFfpId(ffpId);
        userInfo.setFfpNo(ffpNo);
        LoginResult loginResult = createToken(channelCode, userInfo);
        // 账号是否启用状态
        if (!loginResult.isAccountStatus()) {
            throw MultiLangServiceException.fail("账户状态异常，请联系客服核实");
        }
        return loginResult;
    }

    @Override
    public LoginResult createToken(String channelNo, UserInfo userInfo) {
        // 获取渠道信息
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        // 配置文件存在登录使用的渠道 登录渠道切换为配置的渠道 否则使用原渠道
        String loginChannel = StringUtils.isBlank(channelInfo.getLoginChannelCode()) ? channelNo : channelInfo.getLoginChannelCode();
        //根据不同渠道设置token有效期
        SaLoginModel saLoginModel = new SaLoginModel()
                .setDevice(loginChannel)
                .setExtra(FFP_ID, userInfo.getFfpId())
                .setExtra(FFP_NO, userInfo.getFfpNo());
        if (StringUtils.isNotBlank(userInfo.getOpenId())) {
            saLoginModel.setExtra(OPEN_ID, userInfo.getOpenId());
        }
        if (StringUtils.isNotBlank(userInfo.getUserId())) {
            saLoginModel.setExtra(USER_ID, userInfo.getUserId());
        }
        // 登录凭证有效期 单位秒
        if (channelInfo.getExpiresIn() != null){
            saLoginModel.setTimeout(channelInfo.getExpiresIn());
        }
        // 最低活跃频率，单位：秒
        if (channelInfo.getActiveTimeout() != null){
            saLoginModel.setActiveTimeout(channelInfo.getActiveTimeout());
        }
        StpUtil.login(userInfo.getFfpNo(), saLoginModel);
        SaTokenInfo saTokenInfo = StpUtil.getTokenInfo();
        LoginResult loginResult = new LoginResult();
        loginResult.setAccessToken(saTokenInfo.getTokenValue());
        loginResult.setExpiresIn(saTokenInfo.getTokenTimeout());
        loginResult.setFfpNo(userInfo.getFfpNo());
        loginResult.setLoginVersion("1");
        // 查询账号信息
        MemberAccountInfo accountInfo = memberService.getAccountInfo(userInfo.getFfpNo(), true);
        loginResult.setAccountStatus(accountInfo.isAccountStatus());
        // 以下自动是为了兼容中文APP新加属性
        loginResult.setFfpId(accountInfo.getFfpId());
        loginResult.setName(accountInfo.getName());
        loginResult.setSex(accountInfo.getSex());
        loginResult.setMemberTel(accountInfo.getMemberTel());
        loginResult.setMemberEmail(accountInfo.getEmail());
        loginResult.setHeadImageUrl(accountInfo.getHeadImageUrl());
        return loginResult;
    }

    @Override
    public ParseTokenResult parseToken(String token) {
        try {
            ParseTokenResult parseTokenResult = new ParseTokenResult();
            // 获取登录账号ID 存在ID代表token有效登录状态
            Object loginId = StpUtil.getLoginIdByToken(token);
            parseTokenResult.setLogin(null != loginId);
            String ffpId = (String) StpUtil.getExtra(token, FFP_ID);
            String ffpNo = (String) StpUtil.getExtra(token, FFP_NO);
            String openId = (String) StpUtil.getExtra(token, OPEN_ID);
            String userId = (String) StpUtil.getExtra(token, USER_ID);
            parseTokenResult.setFfpId(ffpId);
            parseTokenResult.setFfpNo(ffpNo);
            parseTokenResult.setOpenId(openId);
            parseTokenResult.setUserId(userId);
            // token渠道
            String channelCode = StpUtil.getLoginDeviceByToken(token);
            parseTokenResult.setChannelCode(channelCode);
            // 查询账号信息
            MemberAccountInfo accountInfo = memberService.getAccountInfo(ffpNo, true);
            parseTokenResult.setAccountStatus(accountInfo.isAccountStatus());
            return parseTokenResult;
        } catch (SaJwtException jwtException) {
            log.error("token解析异常，token：{} 异常原因:", token, jwtException);
            throw new MultiLangServiceException(CommonErrorCode.INVALID_TOKEN);
        }
    }

    @Override
    public void logout(String channelNo, LogoutParam logoutParam) {
        if (StringUtils.isAllBlank(logoutParam.getToken(), logoutParam.getFfpCardNo())) {
            throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED);
        }
        // 存在token 基于token注销登录
        if (StringUtils.isNotBlank(logoutParam.getToken())) {
            StpUtil.logoutByTokenValue(logoutParam.getToken());
        }
        // 获取渠道信息
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        // 配置文件存在登录使用的渠道 登录渠道切换为配置的渠道 否则使用原渠道
        String loginChannel = StringUtils.isBlank(channelInfo.getLoginChannelCode()) ? channelNo : channelInfo.getLoginChannelCode();
        // 存在会员卡号 基于会员卡号、设备注销登录
        if (StringUtils.isNotBlank(logoutParam.getFfpCardNo())) {
            StpUtil.logout(logoutParam.getFfpCardNo(), loginChannel);
        }
    }

    @Override
    public void refreshAccountStatus(RefreshAccountStatusParam refreshAccountStatusParam) {
        memberService.getAccountInfo(refreshAccountStatusParam.getFfpCardNo(), false);
    }

}
