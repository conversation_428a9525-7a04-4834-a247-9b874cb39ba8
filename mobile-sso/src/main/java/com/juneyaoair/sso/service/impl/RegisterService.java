package com.juneyaoair.sso.service.impl;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.util.PatternUtil;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.dto.*;
import com.juneyaoair.oneorder.api.crm.service.IAccountService;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.email.EmailConfigEnum;
import com.juneyaoair.oneorder.api.email.service.IMailService;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.enums.*;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mobile.RedisKeyFormatUtil;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.dto.EmailContent;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import com.juneyaoair.oneorder.tools.utils.PhoneUtil;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import com.juneyaoair.oneorder.util.ContactUtil;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import com.juneyaoair.sso.dto.*;
import com.juneyaoair.sso.service.IRegisterService;
import com.juneyaoair.sso.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/10 9:15
 */
@Slf4j
@Service
public class RegisterService implements IRegisterService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private IMailService mailService;
    @Autowired
    private LocaleUtil localeUtil;
    @Autowired
    private ITokenService tokenService;

    @Override
    public ResponseData<LoginResult> register(BizDto bizDto, RegisterParam registerParam, LanguageEnum language, boolean autoLogin) {
        String account;
        String redisKey;
        if (RegisterTypeEnum.MOBILE.equals(registerParam.getRegisterType())) {
            //验证手机号格式
            PatternUtil.checkGlobalPhone(registerParam.getPhone());
            CrmPhoneInfo crmPhoneInfo = PhoneUtil.formatCrmPhoneInfo(registerParam.getPhone());
            account = PhoneUtil.formatMobile(crmPhoneInfo.getAreaId(), crmPhoneInfo.getPhone());
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(account, CaptchaFuncEnum.REGISTER.name()));
        } else if (RegisterTypeEnum.EMAIL.equals(registerParam.getRegisterType())) {
            //验证邮箱格式
            PatternUtil.checkEmail(registerParam.getEmail());
            account = registerParam.getEmail();
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(account, CaptchaFuncEnum.REGISTER.name()));
        } else {
            throw MultiLangServiceException.fail("不支持的注册方式");
        }
        //验证校验码
        String checkCodeCache = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(checkCodeCache)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_INVALID);
        }
        CheckDayLicense ipErrorCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_REGISTER_IP_ERROR, CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.name(), CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.getMessage());
        CheckDayLicense ffpErrorCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_REGISTER_ACCOUNT_ERROR, CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(false, ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
        if (!checkCodeCache.equals(registerParam.getCheckCode())) {
            commonService.addDayLicense(ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
            //记录访问出错次数
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_ERROR);
        }
        //验证码验证成功清除
        redisUtil.remove(redisKey);
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_REGISTER_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_REGISTER_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        //调用第三方注册接口
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        MultiLanguageRegisterAccountReqDto multiLanguageRegisterAccountReqDto = buildMultiLanguageRegisterAccountReqDto(bizDto, registerParam, language);
        if (RegisterTypeEnum.MOBILE.equals(registerParam.getRegisterType())) {
            //注意此处手机号格式为852-********
            multiLanguageRegisterAccountReqDto.setMobile(registerParam.getPhone());
        } else {
            multiLanguageRegisterAccountReqDto.setEmail(account);
        }
        CrmRequestDto<MultiLanguageRegisterAccountReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), multiLanguageRegisterAccountReqDto);
        MultiLanguageRegisterAccountResultDto multiLanguageRegisterAccountResultDto = accountService.register(crmRequestDto);
        if(autoLogin){
            return ResponseData.suc(tokenService.createToken(bizDto.getHeadChannelCode(), String.valueOf(multiLanguageRegisterAccountResultDto.getMemberId()), multiLanguageRegisterAccountResultDto.getMemberCardNo()));
        }
        return ResponseData.suc();
    }

    @Override
    public void sendSms(BizDto bizDto, Captcha captcha, LanguageEnum language) {
        //验证手机号格式
        PatternUtil.checkGlobalPhone(captcha.getPhone());
        CrmPhoneInfo crmPhoneInfo = PhoneUtil.formatCrmPhoneInfo(captcha.getPhone());
        String account = PhoneUtil.formatMobile(crmPhoneInfo.getAreaId(), crmPhoneInfo.getPhone());

        CaptchaFuncEnum smsFuncEnum = captcha.getFunc();
        SMSTemplateEnum smsTemplate = SMSTemplateEnum.getSMSTemplate(language, smsFuncEnum);
        if (smsTemplate == null) {
            throw MultiLangServiceException.fail("模板未进行初始化");
        }
        //IP以及卡号验证
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_SEND_SMS_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_SEND_SMS_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        //特殊场景需要查询账户是否存在
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        // 校验账号是否存在 重置密码、账号绑定 需要校验账号是否存在
        Set<CaptchaFuncEnum> checkAccountSet = Sets.newHashSet(CaptchaFuncEnum.RESET_LOGIN, CaptchaFuncEnum.THIRD_PARTY_BIND);
        if (checkAccountSet.contains(smsFuncEnum)) {
            QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReqDto = new QueryCardNoByDataTypeReqDto();
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.MOBILE.geteName());
            queryCardNoByDataTypeReqDto.setDataValue(account);
            CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryCardNoByDataTypeReqDto);
            accountService.queryCardNoByDataType(crmRequestDto);
        }
        //发送短信
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(account, smsFuncEnum.name()));
        String sendCode = commonService.getChkCode();
        redisUtil.set(redisKey, sendCode, 200);
        Map<String, String> extras = Maps.newHashMap();
        extras.put("sendCode", sendCode);
        extras.put("function", localeUtil.getTips(smsFuncEnum.name(), language.name()));
        captchaService.commonSmsSend(smsTemplate.name(), crmPhoneInfo.getAreaId(), crmPhoneInfo.getPhone(), extras);
    }

    @Override
    public void sendEmail(BizDto bizDto, EmailCaptcha emailCaptcha, LanguageEnum language) {
        CaptchaFuncEnum smsFuncEnum = emailCaptcha.getFunc();
        //IP以及卡号验证
        CheckDayLicense ipCheckDayLicense;
        CheckDayLicense ffpCheckDayLicense;
        switch (emailCaptcha.getFunc()) {
            case REGISTER: {
                ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.REGISTER_SERVICE_SEND_EMAIL_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
                ffpCheckDayLicense = new CheckDayLicense(emailCaptcha.getEmail(), CheckLicenseFuncEnum.REGISTER_SERVICE_SEND_EMAIL_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
                break;
            }
            default: {
                ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_SEND_EMAIL_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
                ffpCheckDayLicense = new CheckDayLicense(emailCaptcha.getEmail(), CheckLicenseFuncEnum.SERVICE_SEND_EMAIL_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
                break;
            }
        }
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        //特殊场景需要查询账户是否存在
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        // 校验账号是否存在 重置密码、账号绑定 需要校验账号是否存在
        Set<CaptchaFuncEnum> checkAccountSet = Sets.newHashSet(CaptchaFuncEnum.RESET_LOGIN, CaptchaFuncEnum.THIRD_PARTY_BIND);
        if (checkAccountSet.contains(smsFuncEnum)) {
            QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReqDto = new QueryCardNoByDataTypeReqDto();
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.EMAIL.geteName());
            queryCardNoByDataTypeReqDto.setDataValue(emailCaptcha.getEmail());
            CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryCardNoByDataTypeReqDto);
            accountService.queryCardNoByDataType(crmRequestDto);
        }
        //邮件验证码
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(emailCaptcha.getEmail(), smsFuncEnum.name()));
        String sendCode = commonService.getChkCode();
        redisUtil.set(redisKey, sendCode, 200);
        //发送邮件
        EmailContent emailContent = commonService.initEmailTemplate(smsFuncEnum, language);
        String template = emailContent.getContent();
        template = template.replace("{{email}}", emailCaptcha.getEmail());
        template = template.replace("{{code}}", sendCode);
        mailService.sendEmail(emailCaptcha.getEmail(), emailContent.getSubject(), template, EmailConfigEnum.CRM);
    }

    @Override
    public void sendCaptchaByCard(BizDto bizDto, CaptchaByCard captchaByCard, LanguageEnum language) {
        SMSTemplateEnum smsTemplate = null;
        EmailContent emailContent = null;
        String contactValue = captchaByCard.getContactValue();
        ContactTypeEnum contactType = captchaByCard.getContactType();
        if (ContactTypeEnum.MOBILE.equals(contactType)) {
            PatternUtil.checkGlobalPhone(captchaByCard.getContactValue());
            smsTemplate = SMSTemplateEnum.getSMSTemplate(language, CaptchaFuncEnum.RESET_LOGIN);
            contactValue = PhoneUtil.removeMobile86(captchaByCard.getContactValue());
            if (smsTemplate == null) {
                log.error("language:{},smsFuncEnum:{},短信配置不正确", language, CaptchaFuncEnum.RESET_LOGIN.name());
                throw MultiLangServiceException.fail("模板未进行初始化");
            }
        } else if (ContactTypeEnum.EMAIL.equals(contactType)) {
            PatternUtil.checkEmail(contactValue);
            emailContent = commonService.initEmailTemplate(CaptchaFuncEnum.RESET_LOGIN, language);
            if (emailContent == null) {
                log.error("language:{},CaptchaFuncEnum:{},邮箱配置不正确", language, CaptchaFuncEnum.RESET_LOGIN.name());
                throw MultiLangServiceException.fail("未匹配到正确配置");
            }
        } else {
            throw MultiLangServiceException.fail("暂不支持的验证方式");
        }
        QueryAccountByMemberIdResultDto queryAccountByMemberIdResultDto = accountService.queryAccountByMemberId(bizDto, captchaByCard.getFfpCardNo());
        if (CollectionUtils.isEmpty(queryAccountByMemberIdResultDto.getContacts())) {
            throw MultiLangServiceException.fail("请先维护联系方式");
        }
        CrmContactDto crmContactDto = ContactUtil.filterMemberContact(queryAccountByMemberIdResultDto.getContacts(), contactType);
        if (crmContactDto == null) {
            throw MultiLangServiceException.fail("无匹配的联系方式");
        }
        //比对输入联系方式与会员信息拥有的联系方式进行比对
        if (!contactValue.equalsIgnoreCase(crmContactDto.getContactValue())) {
            throw MultiLangServiceException.fail("联系信息不匹配");
        }
        String sendCode = commonService.getChkCode();
        if (smsTemplate != null) {
            //发送短信
            String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(crmContactDto.getContactValue(), CaptchaFuncEnum.RESET_LOGIN.name()));
            redisUtil.set(redisKey, sendCode, 300);
            Map<String, String> extras = Maps.newHashMap();
            extras.put("sendCode", sendCode);
            extras.put("function", localeUtil.getTips(CaptchaFuncEnum.RESET_LOGIN.name(), language.name()));
            CrmPhoneInfo crmPhoneInfo = PhoneUtil.formatCrmPhoneInfo(crmContactDto.getContactValue());
            captchaService.commonSmsSend(smsTemplate.name(), crmPhoneInfo.getAreaId(), crmPhoneInfo.getPhone(), extras);
        }
        if (emailContent != null) {
            String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(crmContactDto.getContactValue(), CaptchaFuncEnum.RESET_LOGIN.name()));
            redisUtil.set(redisKey, sendCode, 600);
            String template = emailContent.getContent();
            template = template.replace("{{code}}", sendCode);
            mailService.sendEmail(crmContactDto.getContactValue(), emailContent.getSubject(), template, EmailConfigEnum.CRM);
        }
    }

    @Override
    public void resetLogin(BizDto bizDto, ResetLogin resetLogin) {
        //执行验证码检验逻辑
        CrmAccountDto crmAccountDto = this.checkCaptcha(bizDto, resetLogin);
        //次数正常计数
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.RESET_LOGIN_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(crmAccountDto.getCrmAccount(), CheckLicenseFuncEnum.RESET_LOGIN_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        //根据联系方式查询卡号信息
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReqDto = new QueryCardNoByDataTypeReqDto();
        queryCardNoByDataTypeReqDto.setDataType(crmAccountDto.getContactType().geteName());
        queryCardNoByDataTypeReqDto.setDataValue(crmAccountDto.getCrmAccount());
        CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryCardNoByDataTypeReqDto);
        QueryCardNoByDataTypeResultDto queryCardNoByDataTypeResultDto = accountService.queryCardNoByDataType(crmRequestDto);
        //重置登录密码
        ResetLoginPasswordReqDto resetLoginPasswordReqDto = new ResetLoginPasswordReqDto();
        resetLoginPasswordReqDto.setMemberId(queryCardNoByDataTypeResultDto.getMemberId());
        resetLoginPasswordReqDto.setPassword(resetLogin.getPwd());
        CrmRequestDto<ResetLoginPasswordReqDto> crmRequestDto2 = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), resetLoginPasswordReqDto);
        accountService.resetLoginPasswordV2(crmRequestDto2);
        //业务执行成功 清除验证码
        redisUtil.remove(crmAccountDto.getRedisKey());
    }

    @Override
    public CrmAccountDto checkCaptcha(BizDto bizDto, CheckCaptchaParam checkCaptchaParam) {
        String redisKey;
        CrmAccountDto checkCaptchaDto = checkCaptchaParam.buildCheckCaptchaAccount();
        if (ContactTypeEnum.MOBILE.equals(checkCaptchaDto.getContactType())) {
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(checkCaptchaDto.getCrmAccount(), CaptchaFuncEnum.RESET_LOGIN.name()));
        } else if (ContactTypeEnum.EMAIL.equals(checkCaptchaDto.getContactType())) {
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(checkCaptchaDto.getCrmAccount(), CaptchaFuncEnum.RESET_LOGIN.name()));
        } else {
            throw MultiLangServiceException.fail("不支持的找回方式");
        }
        checkCaptchaDto.setRedisKey(redisKey);
        //验证校验码
        String checkCodeCache = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(checkCodeCache)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_INVALID);
        }
        //错误验证码记录
        CheckDayLicense ipErrorCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.RESET_LOGIN_IP_ERROR, CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.name(), CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.getMessage());
        CheckDayLicense ffpErrorCheckDayLicense = new CheckDayLicense(checkCaptchaDto.getCrmAccount(), CheckLicenseFuncEnum.RESET_LOGIN_ACCOUNT_ERROR, CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(false, ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
        if (!checkCodeCache.equals(checkCaptchaParam.getCaptcha())) {
            commonService.addDayLicense(ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
            //记录访问出错次数
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_ERROR);
        }
        return checkCaptchaDto;
    }

    @Override
    public List<ResetLoginMethod> queryResetLoginMethod(BizDto bizDto, String ffpCardNo) {
        QueryAccountByMemberIdResultDto queryAccountByMemberIdResultDto = accountService.queryAccountByMemberId(bizDto, ffpCardNo);
        List<ResetLoginMethod> resetLoginMethodList = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryAccountByMemberIdResultDto.getContacts())) {
            return resetLoginMethodList;
        }
        CrmContactDto phoneContact = ContactUtil.filterMemberContact(queryAccountByMemberIdResultDto.getContacts(), ContactTypeEnum.MOBILE);
        if (phoneContact != null) {
            ResetLoginMethod resetLoginMethod = new ResetLoginMethod();
            resetLoginMethod.setContactType(ContactTypeEnum.MOBILE);
            resetLoginMethod.setContactValue(SensitiveInfoHider.hidePhone(phoneContact.getContactValue()));
            resetLoginMethodList.add(resetLoginMethod);
        }
        CrmContactDto emailContact = ContactUtil.filterMemberContact(queryAccountByMemberIdResultDto.getContacts(), ContactTypeEnum.EMAIL);
        if (emailContact != null) {
            ResetLoginMethod resetLoginMethod = new ResetLoginMethod();
            resetLoginMethod.setContactType(ContactTypeEnum.EMAIL);
            resetLoginMethod.setContactValue(SensitiveInfoHider.hideMail(emailContact.getContactValue()));
            resetLoginMethodList.add(resetLoginMethod);
        }
        return resetLoginMethodList;
    }

    @Override
    public void resetLoginByCard(BizDto bizDto, ResetLoginByCardParam resetLoginByCardParam) {
        ContactTypeEnum contactType = resetLoginByCardParam.getContactType();
        QueryAccountByMemberIdResultDto queryAccountByMemberIdResultDto = accountService.queryAccountByMemberId(bizDto, resetLoginByCardParam.getFfpCardNo());
        if (CollectionUtils.isEmpty(queryAccountByMemberIdResultDto.getContacts())) {
            throw MultiLangServiceException.fail("请先维护联系方式");
        }
        CrmContactDto crmContactDto = ContactUtil.filterMemberContact(queryAccountByMemberIdResultDto.getContacts(), resetLoginByCardParam.getContactType());
        if (crmContactDto == null) {
            throw MultiLangServiceException.fail("无匹配的联系方式");
        }
        String redisKey;
        if (ContactTypeEnum.MOBILE.equals(contactType)) {
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(crmContactDto.getContactValue(), CaptchaFuncEnum.RESET_LOGIN.name()));
        } else if (ContactTypeEnum.EMAIL.equals(contactType)) {
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(crmContactDto.getContactValue(), CaptchaFuncEnum.RESET_LOGIN.name()));
        } else {
            throw MultiLangServiceException.fail("暂不支持的验证方式");
        }
        //验证校验码
        String checkCodeCache = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(checkCodeCache)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_INVALID);
        }
        CheckDayLicense ipErrorCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.RESET_LOGIN_IP_ERROR, CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.name(), CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.getMessage());
        CheckDayLicense ffpErrorCheckDayLicense = new CheckDayLicense(crmContactDto.getContactValue(), CheckLicenseFuncEnum.RESET_LOGIN_ACCOUNT_ERROR, CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(false, ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
        if (!checkCodeCache.equals(resetLoginByCardParam.getCaptcha())) {
            commonService.addDayLicense(ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
            //记录访问出错次数
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_ERROR);
        }
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.RESET_LOGIN_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(resetLoginByCardParam.getFfpCardNo(), CheckLicenseFuncEnum.RESET_LOGIN_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        //重置登录密码
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        ResetLoginPasswordReqDto resetLoginPasswordReqDto = new ResetLoginPasswordReqDto();
        resetLoginPasswordReqDto.setMemberId(resetLoginByCardParam.getFfpCardNo());
        resetLoginPasswordReqDto.setPassword(resetLoginByCardParam.getPwd());
        CrmRequestDto<ResetLoginPasswordReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), resetLoginPasswordReqDto);
        accountService.resetLoginPasswordV2(crmRequestDto);
        //重置成功删除验证码
        redisUtil.remove(redisKey);
    }

    private @NotNull MultiLanguageRegisterAccountReqDto buildMultiLanguageRegisterAccountReqDto(BizDto bizDto, RegisterParam registerParam, LanguageEnum language) {
        //目前已知国际APP注册时可以不输入登录密码
        if (!ChannelCodeEnum.G_MOBILE.getChannelCode().equals(bizDto.getHeadChannelCode())) {
            if (StringUtils.isBlank(registerParam.getPwd())) {
                log.error("{},不支持登录密码为空", bizDto.getHeadChannelCode());
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED);
            }
        }
        MultiLanguageRegisterAccountReqDto multiLanguageRegistAccountReqDto = new MultiLanguageRegisterAccountReqDto();
        multiLanguageRegistAccountReqDto.setBirthday(registerParam.getBirthDate());
        multiLanguageRegistAccountReqDto.setCertificateType(registerParam.getIdType());
        multiLanguageRegistAccountReqDto.setCertificateNumber(registerParam.getIdNumber());
        multiLanguageRegistAccountReqDto.setEfirstName(registerParam.getFirstName());
        multiLanguageRegistAccountReqDto.setElastName(registerParam.getLastName());
        multiLanguageRegistAccountReqDto.setLanguageType(language.name());
        multiLanguageRegistAccountReqDto.setPassword(registerParam.getPwd());
        multiLanguageRegistAccountReqDto.setSalutationCode(registerParam.getTitle().getCode());
        //
        String idType = registerParam.getIdType();
        if (CertificateTypeEnum.PASSPORT.getShowCode().equals(idType)
                || CertificateTypeEnum.HK_MACAO_MTP.getShowCode().equals(idType)
                || CertificateTypeEnum.TAIWAN_MTP.getShowCode().equals(idType)) {
            multiLanguageRegistAccountReqDto.setSigningAuthority(registerParam.getIdCountry());
            multiLanguageRegistAccountReqDto.setValidDate(registerParam.getIdExpireDate());
        }
        multiLanguageRegistAccountReqDto.setNationality(registerParam.getNationality());
        return multiLanguageRegistAccountReqDto;
    }
}
