package com.juneyaoair.sso.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserInfoShareRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.util.PatternUtil;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.dto.*;
import com.juneyaoair.oneorder.api.crm.service.IAccountService;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberLoginService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.common.AccountTypeEnum;
import com.juneyaoair.oneorder.common.common.LoginTypeEnum;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestInterface;
import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.RegisterTypeEnum;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.mobile.RedisKeyFormatUtil;
import com.juneyaoair.oneorder.mobile.config.GeetestPropertiesConfig;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import com.juneyaoair.oneorder.tools.utils.PhoneUtil;
import com.juneyaoair.oneorder.utils.HoStringUtils;
import com.juneyaoair.sso.config.ThirdAuthConfig;
import com.juneyaoair.sso.constant.ApiConstant;
import com.juneyaoair.sso.dto.*;
import com.juneyaoair.sso.enums.ThreePartyLoginEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 10:52
 */
@Service
@Slf4j
public class LoginService {

    @Autowired
    private GeetestPropertiesConfig geetestPropertiesConfig;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private LocaleUtil localeUtil;

    @Autowired
    private IMemberLoginService memberLoginService;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private ITokenService tokenService;
    @Autowired
    private TongDunService tongDunService;
    @Autowired
    private ThirdAuthConfig thirdAuthConfig;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private CommonService commonService;

    /**
     * 账号密码登录
     *
     * @param bizDto
     * @param blackBox
     * @param loginBody
     * @return
     */
    public LoginResult createToken(BizDto bizDto, String blackBox, LoginBody loginBody) {
        String originAccount = loginBody.getUserName();
        String account = originAccount;
        //查询账户信息
        String pwd = loginBody.getPassword();
        QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReqDto = new QueryCardNoByDataTypeReqDto();
        AccountTypeEnum accountType = AccountTypeEnum.ACCOUNT;
        //邮箱格式验证
        if (originAccount.matches(PatternCommon.EMAIL)) {
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.EMAIL.geteName());
            queryCardNoByDataTypeReqDto.setDataValue(account);
            accountType = AccountTypeEnum.EMAIL;
        } else if (originAccount.contains(HoStringUtils.LINE_THROUGH)) {
            String phoneAreaCode = originAccount.split("-")[0];
            String phone = originAccount.split("-")[1];
            //验证手机号格式
            PatternUtil.checkPhoneArea(phoneAreaCode);
            PatternUtil.checkPhone(phone);
            account = PhoneUtil.formatMobile(phoneAreaCode, phone);
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.MOBILE.geteName());
            queryCardNoByDataTypeReqDto.setDataValue(account);
            accountType = AccountTypeEnum.PHONE;
        } else if (!originAccount.matches(PatternCommon.HO_CARD)) {
            throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_NOT_EXIST);
        }
        //同盾验证
        tongDunService.loginAntiFraud(bizDto, blackBox, originAccount, accountType, LoginTypeEnum.PASSWORD);
        //访问错误次数检验
        CheckDayLicense ipErrorCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_LOGIN_IP_ERROR, CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.name(),CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.getMessage());
        CheckDayLicense ffpErrorCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_LOGIN_ACCOUNT_ERROR, CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.name(),CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(false, ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
        //访问次数检验
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_LOGIN_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(),CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_LOGIN_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(),CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        //执行账户查询操作
        String ffpCardNo = account;
        if (StringUtils.isNotBlank(queryCardNoByDataTypeReqDto.getDataType())) {
            CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryCardNoByDataTypeReqDto);
            QueryCardNoByDataTypeResultDto queryCardNoByDataTypeResultDto = accountService.queryCardNoByDataType(crmRequestDto);
            ffpCardNo = queryCardNoByDataTypeResultDto.getMemberId();
        }
        //账户密码验证
        LoginPasswordCheckReqDto loginPasswordCheckReqDto = new LoginPasswordCheckReqDto();
        loginPasswordCheckReqDto.setMemberId(ffpCardNo);
        loginPasswordCheckReqDto.setPassword(pwd);
        CrmRequestDto<LoginPasswordCheckReqDto> crmRequestDto2 = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), loginPasswordCheckReqDto);
        LoginPasswordCheckResultDto loginPasswordCheckResultDto = accountService.loginPasswordCheck(crmRequestDto2);
        if (loginPasswordCheckResultDto.isVerifyResult()) {
            //此处暂时缺少正确的文案提示，后期提供调整
            if (loginPasswordCheckResultDto.getValidAccount() != null && !loginPasswordCheckResultDto.getValidAccount()) {
                throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_NOT_EXIST);
            }
            //利用jwt生成token
            return tokenService.createToken(bizDto.getHeadChannelCode(), String.valueOf(loginPasswordCheckResultDto.getId()), loginPasswordCheckResultDto.getMemberId());
        } else {
            //会员卡号不存在
            if (StringUtils.isBlank(loginPasswordCheckResultDto.getMemberId())) {
                throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_NOT_EXIST);
            }
            LoginResult loginResult = new LoginResult();
            commonService.addDayLicense(ipErrorCheckDayLicense);
            long remainCount = commonService.addDayLicense(ffpErrorCheckDayLicense);
            loginResult.setRemainCount(remainCount);
            return loginResult;
        }
    }

    /**
     * 短信验证码/邮箱验证码快捷登录
     *
     * @param bizDto
     * @param loginCaptchaBody
     * @param blackBox
     * @return
     */
    public LoginResult createToken(BizDto bizDto, String blackBox, LoginCaptchaBody loginCaptchaBody) {
        String account;
        String tongDunAccount;
        String redisKey;
        //查询账户信息
        AccountTypeEnum accountType;
        QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReqDto = new QueryCardNoByDataTypeReqDto();
        if (RegisterTypeEnum.MOBILE.equals(loginCaptchaBody.getLoginType())) {
            //验证手机号格式
            PatternUtil.checkPhoneArea(loginCaptchaBody.getCountryCode());
            PatternUtil.checkPhone(loginCaptchaBody.getAccount());
            account = PhoneUtil.formatMobile(loginCaptchaBody.getCountryCode(), loginCaptchaBody.getAccount());
            tongDunAccount = loginCaptchaBody.getCountryCode() + "-" + loginCaptchaBody.getAccount();
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.MOBILE.geteName());
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(account, CaptchaFuncEnum.LOGIN.name()));
            accountType = AccountTypeEnum.PHONE;
        } else if (RegisterTypeEnum.EMAIL.equals(loginCaptchaBody.getLoginType())) {
            //验证邮箱格式
            PatternUtil.checkEmail(loginCaptchaBody.getAccount());
            account = loginCaptchaBody.getAccount();
            tongDunAccount = loginCaptchaBody.getAccount();
            queryCardNoByDataTypeReqDto.setDataType(ContactTypeEnum.EMAIL.geteName());
            redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(account, CaptchaFuncEnum.LOGIN.name()));
            accountType = AccountTypeEnum.EMAIL;
        } else {
            throw MultiLangServiceException.fail("不支持的登录方式");
        }
        //同盾验证
        tongDunService.loginAntiFraud(bizDto, blackBox, tongDunAccount, accountType, LoginTypeEnum.SMS);
        //验证校验码
        String checkCodeCache = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(checkCodeCache)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_INVALID);
        }
        CheckDayLicense ipErrorCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_LOGIN_IP_ERROR, CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.name(),CommonErrorCode.DEVICE_IP_ERROR_OVER_LIMIT.getMessage());
        CheckDayLicense ffpErrorCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_LOGIN_ACCOUNT_ERROR, CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.name(),CommonErrorCode.ACCOUNT_ERROR_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(false, ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
        if (!checkCodeCache.equals(loginCaptchaBody.getCaptcha())) {
            commonService.addDayLicense(ipErrorCheckDayLicense, ffpErrorCheckDayLicense);
            //记录访问出错次数
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_ERROR);
        }
        //验证码验证成功清除
        redisUtil.remove(redisKey);
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.SERVICE_LOGIN_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(),CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(account, CheckLicenseFuncEnum.SERVICE_LOGIN_ACCOUNT, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(),CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        queryCardNoByDataTypeReqDto.setDataValue(account);
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryCardNoByDataTypeReqDto);
        QueryCardNoByDataTypeResultDto queryCardNoByDataTypeResultDto = accountService.queryCardNoByDataType(crmRequestDto);
        accountService.queryAccountByMemberId(bizDto,queryCardNoByDataTypeResultDto.getMemberId());
        //利用jwt生成token
        return tokenService.createToken(bizDto.getHeadChannelCode(), String.valueOf(queryCardNoByDataTypeResultDto.getId()), queryCardNoByDataTypeResultDto.getMemberId());
    }

    /**
     * 获取三方授权登录地址
     *  QQ接口文档地址：https://wiki.connect.qq.com/使用authorization_code获取access_token
     *  微信接口文档地址：https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html
     * @return
     */
    public ThirdPartyLoginUrlResult getThirdPartyLoginUrl(String channelCode, ThirdPartyLoginUrlParam thirdPartyLoginUrlParam) {
        switch (thirdPartyLoginUrlParam.getThreeParty()) {
            case QQ:
                return getQqLoginUrl(channelCode);
            case WECHAT:
                return getWechatLoginUrl(channelCode);
            case ALIPAY:
                return getAlipayLoginUrl(channelCode);
            default:
                throw MultiLangServiceException.fail("暂不支持的三方登录类型");
        }
    }

    /**
     * 获取支付宝登录地址
     * 文档地址：https://opendocs.alipay.com/support/04y56c
     * @return
     */
    private ThirdPartyLoginUrlResult getAlipayLoginUrl(String channelCode) {
        String state = UUID.randomUUID().toString().replaceAll("-", "");
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.ALIPAY + ":" + state);
        redisUtil.set(redisKey, channelCode, 60 * 3L);
        String loginUrl = thirdAuthConfig.getAlipayOpenAuthUrl() + ApiConstant.ALIPAY_OAUTH_AUTHORIZE + "?" +
                "scope=auth_user&app_id=" + thirdAuthConfig.getAlipayAppId() +
                "&redirect_uri=" + HttpUtil.urlEncode(thirdAuthConfig.getAlipayRedirectUri()) +
                "&state=" + state;
        ThirdPartyLoginUrlResult thirdPartyLoginUrlResult = new ThirdPartyLoginUrlResult();
        thirdPartyLoginUrlResult.setLoginUrl(loginUrl);
        return thirdPartyLoginUrlResult;
    }

    /**
     * 获取微信登录地址
     * @return
     */
    private ThirdPartyLoginUrlResult getWechatLoginUrl(String channelCode) {
        String state = UUID.randomUUID().toString().replaceAll("-", "");
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.WECHAT + ":"  + state);
        redisUtil.set(redisKey, channelCode, 60 * 3L);
        String loginUrl = thirdAuthConfig.getWechatOpenUrl() + ApiConstant.WECHAT_QR_CONNECT + "?" +
                "response_type=code&scope=snsapi_login&appid=" + thirdAuthConfig.getWechatAppId() +
                "&redirect_uri=" + HttpUtil.urlEncode(thirdAuthConfig.getWechatRedirectUri()) +
                "&state=" + state;
        ThirdPartyLoginUrlResult thirdPartyLoginUrlResult = new ThirdPartyLoginUrlResult();
        thirdPartyLoginUrlResult.setLoginUrl(loginUrl);
        return thirdPartyLoginUrlResult;
    }

    /**
     * 获取QQ登录地址
     * @return
     */
    private ThirdPartyLoginUrlResult getQqLoginUrl(String channelCode) {
        String state = UUID.randomUUID().toString().replaceAll("-", "");
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.QQ + ":"  + state);
        redisUtil.set(redisKey, channelCode, 60 * 3L);
        String loginUrl = thirdAuthConfig.getQqAuthUrl() + ApiConstant.QQ_OAUTH_AUTHORIZE + "?" +
                "response_type=code&client_id=" + thirdAuthConfig.getQqAppId() +
                "&redirect_uri=" + HttpUtil.urlEncode(thirdAuthConfig.getQqRedirectUri()) +
                "&state=" + state;
        ThirdPartyLoginUrlResult thirdPartyLoginUrlResult = new ThirdPartyLoginUrlResult();
        thirdPartyLoginUrlResult.setLoginUrl(loginUrl);
        return thirdPartyLoginUrlResult;
    }

    /**
     * 获取QQ Authorization Code
     * 接口文档地址：https://wiki.connect.qq.com/使用authorization_code获取access_token
     * @param code
     * @param state
     * @return
     */
    public ThirdPartyLoginResult qqLoginCallBack(String code, String state) {
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.QQ + ":"  + state);
        String channelCode = redisUtil.getStr(redisKey);
        // redis无值 代表state无效或者已过期
        if (StringUtils.isBlank(channelCode)) {
            throw MultiLangServiceException.fail("当前链接已失效，请重新获取");
        }
        // 通过Authorization Code获取Access Token
        QQAccessTokenResult qqAccessTokenResult = getQQAccessToken(code);
        String openId = getQQOpenId(qqAccessTokenResult.getAccess_token());
        // 查询用户绑定信息 TODO
        /*boolean bindMember = false;
        // 已绑定 生成登录信息
        if (bindMember) {
            LoginResult loginResult = tokenService.createToken(channelCode, "ffpId", "ffpNo");
            ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
            thirdPartyLoginResult.setBindMember(bindMember);
            thirdPartyLoginResult.setLoginResult(loginResult);
            return thirdPartyLoginResult;
        }
         */
        // 未查询到绑定用户信息 调用接口获取用户个人信息
        QQUserInfo qqUserInfo = getQQUserInfo(qqAccessTokenResult.getAccess_token(), openId);
        ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
        thirdPartyLoginResult.setBindMember(true);
        ThirdPartyUserInfo thirdPartyUserInfo = new ThirdPartyUserInfo();
        thirdPartyUserInfo.setThirdPartyType(ThreePartyLoginEnum.QQ);
        thirdPartyUserInfo.setThirdPartyUserId(commonService.getEncrypt(openId));
        thirdPartyUserInfo.setNickName(qqUserInfo.getNickname());
        thirdPartyUserInfo.setHeadImageUrl(qqUserInfo.getFigureurl_qq_1());
        thirdPartyLoginResult.setThirdPartyUserInfo(thirdPartyUserInfo);
        return thirdPartyLoginResult;
    }

    /**
     * QQ通过Authorization Code获取Access Token以及openId
     * @param code
     * @return
     */
    private QQAccessTokenResult getQQAccessToken(String code) {
        Map<String, String> paramMap = Maps.newHashMap();
        // 授权类型
        paramMap.put("grant_type", "authorization_code");
        paramMap.put("client_id", thirdAuthConfig.getQqAppId());
        paramMap.put("client_secret", thirdAuthConfig.getQqSecret());
        paramMap.put("code", code);
        // 成功授权后的回调地址 需与生成code时传入的redirect_uri保持一致。(如果是移动端app，可以不填)
        paramMap.put("redirect_uri", thirdAuthConfig.getQqRedirectUri());
        // 默认是x-www-form-urlencoded格式，如果填写json，则返回json格式
        paramMap.put("fmt", "json");
        // need_openid=1，表示同时获取openid
        paramMap.put("need_openid", "1");
        HttpResult httpResult = HttpUtil.doGet(thirdAuthConfig.getQqAuthUrl() + ApiConstant.QQ_OAUTH_TOKEN, paramMap);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type type = new TypeToken<QQAccessTokenResult>() {
        }.getType();
        QQAccessTokenResult baseResult = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (baseResult == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (!"0".equals(baseResult.getError())) {
            log.error("调用QQ获取access_token接口失败，返回信息：{}", httpResult.getResponse());
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        return baseResult;
    }

    /**
     * QQ获取用户OpenID
     */
    private String getQQOpenId(String accessToken) {
        Map<String, String> paramMap = Maps.newHashMap();
        // 授权类型
        paramMap.put("access_token", accessToken);
        // 因历史原因，默认是jsonpb格式，如果填写json，则返回json格式
        paramMap.put("fmt", "json");
        HttpResult httpResult = HttpUtil.doGet(thirdAuthConfig.getQqAuthUrl() + ApiConstant.QQ_OAUTH_ME, paramMap);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type type = new TypeToken<QQOpenIdResult>() {
        }.getType();
        QQOpenIdResult baseResult = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (baseResult == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (!"0".equals(baseResult.getError())) {
            log.error("调用QQ获取OpenId失败，返回信息：{}", httpResult.getResponse());
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        return baseResult.getOpenid();
    }

    /**
     * QQ获取用户信息
     */
    private QQUserInfo getQQUserInfo(String accessToken, String openId) {
        Map<String, String> paramMap = Maps.newHashMap();
        // 授权类型
        paramMap.put("access_token", accessToken);
        paramMap.put("oauth_consumer_key", thirdAuthConfig.getQqAppId());
        paramMap.put("openid", openId);
        HttpResult httpResult = HttpUtil.doGet(thirdAuthConfig.getQqAuthUrl() + ApiConstant.QQ_GET_USER_INFO, paramMap);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type type = new TypeToken<QQUserInfo>() {
        }.getType();
        QQUserInfo baseResult = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (baseResult == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (0 != baseResult.getRet()) {
            log.error("调用QQ获取用户信息失败，返回信息：{}", httpResult.getResponse());
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        return baseResult;
    }

    /**
     * 微信通过code获取access_token
     * @param code
     * @param state
     */
    public ThirdPartyLoginResult wechatLoginCallBack(String code, String state) {
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.WECHAT + ":"  + state);
        String channelCode = redisUtil.getStr(redisKey);
        // redis无值 代表state无效或者已过期
        if (StringUtils.isBlank(channelCode)) {
            throw MultiLangServiceException.fail("当前链接已失效，请重新获取");
        }
        // 通过code获取access_token
        WechatAccessTokenResult baseResult = getWechatAccessToken(code);
        // 查询用户绑定信息 TODO
        /*boolean bindMember = false;
        // 已绑定 生成登录信息
        if (bindMember) {
            LoginResult loginResult = tokenService.createToken(channelCode, "ffpId", "ffpNo");
            ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
            thirdPartyLoginResult.setBindMember(bindMember);
            thirdPartyLoginResult.setLoginResult(loginResult);
            return thirdPartyLoginResult;
        }*/
        // 未查询到绑定用户信息 调用接口获取微信用户个人信息
        WechatUserInfoResult userInfoResult = getWechatUserInfo(baseResult.getAccess_token(), baseResult.getOpenid());
        ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
        thirdPartyLoginResult.setBindMember(true);
        ThirdPartyUserInfo thirdPartyUserInfo = new ThirdPartyUserInfo();
        thirdPartyUserInfo.setThirdPartyType(ThreePartyLoginEnum.WECHAT);
        thirdPartyUserInfo.setThirdPartyUserId(commonService.getEncrypt(userInfoResult.getUnionid()));
        thirdPartyUserInfo.setNickName(userInfoResult.getNickname());
        thirdPartyUserInfo.setHeadImageUrl(userInfoResult.getHeadimgurl());
        thirdPartyLoginResult.setThirdPartyUserInfo(thirdPartyUserInfo);
        return thirdPartyLoginResult;
    }

    /**
     * 基于微信accessToken和openid查询用户信息
     * @param accessToken
     * @param openid
     * @return
     */
    private WechatUserInfoResult getWechatUserInfo(String accessToken, String openid) {
        Map<String, String> userInfoMap = Maps.newHashMap();
        // 授权类型
        userInfoMap.put("access_token", accessToken);
        userInfoMap.put("openid", openid);
        HttpResult userInfoHttpResult = HttpUtil.doGet(thirdAuthConfig.getWechatApiUrl() + ApiConstant.WECHAT_USER_INFO, userInfoMap);
        if (!userInfoHttpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(userInfoHttpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type userInfoType = new TypeToken<WechatUserInfoResult>() {
        }.getType();
        WechatUserInfoResult userInfoResult = HoAirGsonUtil.fromJson(userInfoHttpResult.getResponse(), userInfoType);
        if (userInfoResult == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (StringUtils.isNotBlank(userInfoResult.getErrcode())) {
            log.error("调用微信获取access_token接口失败，返回信息：{}", userInfoHttpResult.getResponse());
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        return userInfoResult;
    }

    /**
     * 基于code获取微信AccessToken信息
     * @param code
     * @return
     */
    private WechatAccessTokenResult getWechatAccessToken(String code) {
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("appid", thirdAuthConfig.getWechatAppId());
        paramMap.put("secret", thirdAuthConfig.getWechatSecret());
        paramMap.put("code", code);
        paramMap.put("grant_type", "authorization_code");
        HttpResult httpResult = HttpUtil.doGet(thirdAuthConfig.getWechatApiUrl() + ApiConstant.WECHAT_ACCESS_TOKEN, paramMap);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type type = new TypeToken<WechatAccessTokenResult>() {
        }.getType();
        WechatAccessTokenResult baseResult = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (baseResult == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (StringUtils.isNotBlank(baseResult.getErrcode())) {
            log.error("调用微信获取access_token接口失败，返回信息：{}", httpResult.getResponse());
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        return baseResult;
    }

    /**
     * 支付宝通过code获取用户信息并登录
     * @param code
     * @param state
     * @return
     */
    public ThirdPartyLoginResult alipayLoginCallBack(String code, String state) {
        final String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.THIRD_PARTY_LOGIN + ThreePartyLoginEnum.ALIPAY + ":"  + state);
        String channelCode = redisUtil.getStr(redisKey);
        // redis无值 代表state无效或者已过期
        if (StringUtils.isBlank(channelCode)) {
            throw MultiLangServiceException.fail("当前链接已失效，请重新获取");
        }
        // 通过code获取access_token
        AlipaySystemOauthTokenResponse oauthTokenResponse = getAlipayToken(code);
        // 查询用户绑定信息 TODO
        /*boolean bindMember = false;
        // 已绑定 生成登录信息
        if (bindMember) {
            LoginResult loginResult = tokenService.createToken(channelCode, "ffpId", "ffpNo");
            ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
            thirdPartyLoginResult.setBindMember(bindMember);
            thirdPartyLoginResult.setLoginResult(loginResult);
            return thirdPartyLoginResult;
        }
         */

        // 未查询到绑定用户信息 调用接口获取支付宝用户个人信息
        AlipayUserInfoShareResponse alipayUserInfo = getAlipayUserInfo(oauthTokenResponse.getAccessToken());
        ThirdPartyLoginResult thirdPartyLoginResult = new ThirdPartyLoginResult();
        thirdPartyLoginResult.setBindMember(true);
        ThirdPartyUserInfo thirdPartyUserInfo = new ThirdPartyUserInfo();
        thirdPartyUserInfo.setThirdPartyType(ThreePartyLoginEnum.ALIPAY);
        thirdPartyUserInfo.setThirdPartyUserId(commonService.getEncrypt(alipayUserInfo.getUserId()));
        thirdPartyUserInfo.setNickName(alipayUserInfo.getNickName());
        thirdPartyUserInfo.setHeadImageUrl(alipayUserInfo.getAvatar());
        thirdPartyLoginResult.setThirdPartyUserInfo(thirdPartyUserInfo);
        return thirdPartyLoginResult;
    }

    /**
     * 使用支付宝access_token获取用户信息
     * @param accessToken
     * @return
     */
    private AlipayUserInfoShareResponse getAlipayUserInfo(String accessToken) {
        AlipayClient alipayClient = new DefaultAlipayClient(thirdAuthConfig.getAlipayOpenApiUrl(),
                thirdAuthConfig.getAlipayAppId(), thirdAuthConfig.getAlipayPrivateKey(),
                "json", "UTF-8", thirdAuthConfig.getAlipayPublicKey(), "RSA2");
        AlipayUserInfoShareRequest request = new AlipayUserInfoShareRequest();
        try {
            AlipayUserInfoShareResponse response = alipayClient.execute(request, accessToken);
            if (!response.isSuccess()) {
                log.error("调用支付宝获取用户信息接口失败，accessToken：{} 返回编码：{} 返回描述：{}", accessToken, response.getSubCode(), response.getSubMsg());
                throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
            }
            return response;
        } catch (AlipayApiException e) {
            log.error("调用支付宝获取用户信息接口失败，accessToken：{} 异常信息：", accessToken, e);
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 支付宝auth_code换取access_token及用户userId
     * @param code
     * @return
     */
    private AlipaySystemOauthTokenResponse getAlipayToken(String code) {
        AlipayClient alipayClient = new DefaultAlipayClient(thirdAuthConfig.getAlipayOpenApiUrl(),
                thirdAuthConfig.getAlipayAppId(), thirdAuthConfig.getAlipayPrivateKey(),
                "json", "UTF-8", thirdAuthConfig.getAlipayPublicKey(), "RSA2");
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(code);
        request.setGrantType("authorization_code");
        try {
            return alipayClient.execute(request);
        } catch (AlipayApiException e) {
            log.error("调用支付宝获取access_token接口失败，授权code：{} 异常信息：", code, e);
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 绑定三方账号并登录
     * @param requestData
     * @param bindThirdPartyLoginParam
     * @return
     */
    public LoginResult bindThirdPartyLogin(RequestInterface requestData, BindThirdPartyLoginParam bindThirdPartyLoginParam) {
        // 账号
        String account;
        String redisKey;
        switch (bindThirdPartyLoginParam.getAccountType()) {
            case MOBILE:
                account = PhoneUtil.formatMobile(bindThirdPartyLoginParam.getCountryCode(), bindThirdPartyLoginParam.getAccountValue());
                redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createSmsCaptchaKey(account, CaptchaFuncEnum.THIRD_PARTY_BIND.name()));
                break;
            case EMAIL:
                account = bindThirdPartyLoginParam.getAccountValue();
                redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(account, CaptchaFuncEnum.THIRD_PARTY_BIND.name()));
                break;
            default:
                throw MultiLangServiceException.fail("账号类型无效");
        }
        // 验证码校验
        commonService.checkVerifyCode(redisKey, bindThirdPartyLoginParam.getVerifyCode());
        // 查询账号信息
        QueryCardNoByDataTypeReqDto queryCardNoByDataTypeReq = new QueryCardNoByDataTypeReqDto();
        queryCardNoByDataTypeReq.setDataType(bindThirdPartyLoginParam.getAccountType().name());
        queryCardNoByDataTypeReq.setDataValue(account);
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getOriginIp(), queryCardNoByDataTypeReq);
        QueryCardNoByDataTypeResultDto queryCardNoByDataTypeResult = accountService.queryCardNoByDataType(crmRequestDto);
        // 对数据进行解密
        String thirdPartyUserId = commonService.getDecrypt(bindThirdPartyLoginParam.getThirdPartyUserId());
        // 绑定三方信息
        switch (bindThirdPartyLoginParam.getThirdPartyType()) {
            case WECHAT:
                break;
            case QQ:
                break;
            case ALIPAY:
                break;
            default:
                throw MultiLangServiceException.fail("第三方类型无效");
        }
        // 基于用户信息进行登录
        return tokenService.createToken(requestData.getChannelNo(), String.valueOf(queryCardNoByDataTypeResult.getId()), queryCardNoByDataTypeResult.getMemberId());
    }

    /**
     * 调用会员系统注销登录态
     *
     * @param bizDto
     * @param ffpId
     * @param uscka
     */
    public void loginOut(BizDto bizDto, String ffpId, String uscka) {
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        Header header = Header.builder()
                .ClientIP(bizDto.getIp())
                .MemberId(Long.valueOf(ffpId))
                .Token(uscka)
                .Timestamp(System.currentTimeMillis())
                .build();
        PtApiCRMRequest ptApiCRMRequest = PtApiCRMRequest.builder()
                .requestId(HoAirUuidUtil.randomUUID8())
                .Channel(channelInfo.getChannelCode())
                .ChannelPwd(channelInfo.getChannelPwd())
                .Header(header)
                .build();
        memberLoginService.Logout(ptApiCRMRequest);
    }

}
