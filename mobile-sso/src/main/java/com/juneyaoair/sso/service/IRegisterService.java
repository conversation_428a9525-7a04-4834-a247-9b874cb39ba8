package com.juneyaoair.sso.service;

import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.sso.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/10 9:12
 */
public interface IRegisterService {
    /**
     * 会员注册接口
     * @param bizDto
     * @param autoLogin 自动登录标记
     */
    ResponseData<LoginResult> register(BizDto bizDto, RegisterParam registerParam, LanguageEnum language, boolean autoLogin);

    /**
     * 短信验证码
     * @param bizDto
     * @param captcha
     */
    void sendSms(BizDto bizDto, Captcha captcha, LanguageEnum language);

    /**
     * @description 邮箱验证码
     * <AUTHOR>
     * @date 2024/10/29 22:09
     * @param bizDto
     * @param emailCaptcha
     * @param language
     * @return void
     **/
    void sendEmail(BizDto bizDto, EmailCaptcha emailCaptcha, LanguageEnum language);

    /**
     * @description 根据卡号发送验证码
     * <AUTHOR>
     * @date 2024/11/8 13:03
     * @param bizDto
     * @param captchaByCard
     * @param language
     * @return void
     **/
    void sendCaptchaByCard(BizDto bizDto, CaptchaByCard captchaByCard, LanguageEnum language);
    /**
     * @description
     * <AUTHOR>
     * @date 2024/11/5 17:16
     * @param bizDto
     * @param resetLogin
     * @return void
     **/
    void resetLogin(BizDto bizDto, ResetLogin resetLogin);
    /**
     * @description 校验验证码是否正确
     * <AUTHOR>
     * @date 2025/7/7 10:33
     * @param bizDto
     * @param checkCaptchaParam
     * @return void
     **/
    CrmAccountDto checkCaptcha(BizDto bizDto, CheckCaptchaParam checkCaptchaParam);

    /**
     * @description 查询支持的找回方式
     * <AUTHOR>
     * @date 2024/11/6 16:25
     * @param bizDto
     * @param ffpCardNo
     * @return void
     **/
    List<ResetLoginMethod> queryResetLoginMethod(BizDto bizDto, String ffpCardNo);
    /**
     * @description 根据卡号重置登录密码
     * <AUTHOR>
     * @date 2024/11/8 15:13
     * @param bizDto
     * @param resetLoginByCardParam
     * @return void
     **/
    void resetLoginByCard(BizDto bizDto,ResetLoginByCardParam resetLoginByCardParam);
}
