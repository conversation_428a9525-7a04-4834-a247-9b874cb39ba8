package com.juneyaoair.sso.controller;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.sign.SaSignUtil;
import cn.dev33.satoken.sso.config.SaSsoServerConfig;
import cn.dev33.satoken.sso.processor.SaSsoServerProcessor;
import cn.dev33.satoken.stp.StpUtil;
import com.dtflys.forest.Forest;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.dto.CrmContactDto;
import com.juneyaoair.oneorder.api.crm.dto.QueryAccountByMemberIdResultDto;
import com.juneyaoair.oneorder.api.crm.service.IAccountService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.impl.GeetestService;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.mobile.config.GeetestPropertiesConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.tools.utils.PhoneUtil;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import com.juneyaoair.oneorder.util.ContactUtil;
import com.juneyaoair.sso.dto.*;
import com.juneyaoair.sso.service.LoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/29 14:47
 */
@Slf4j
@Api(value = "SsoServerController", tags = "SSO统一登录服务")
@RestController
public class SsoServerController extends BaseController {

    @Autowired
    private LoginService loginService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private LocaleUtil localeUtil;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private GeetestPropertiesConfig geetestPropertiesConfig;

    /**
     * SSO-Server端：处理所有SSO相关请求
     * http://{host}:{port}/sso/auth			-- 单点登录授权地址，接受参数：redirect=授权重定向地址
     * http://{host}:{port}/sso/doLogin		-- 账号密码登录接口，接受参数：name、pwd
     * http://{host}:{port}/sso/checkTicket	-- Ticket校验接口（isHttp=true时打开），接受参数：ticket=ticket码、ssoLogoutCall=单点注销回调地址 [可选]
     * http://{host}:{port}/sso/signout		-- 单点注销地址（isSlo=true时打开），接受参数：loginId=账号id、sign=参数签名
     */
   /* @RequestMapping("/sso/*")
    public Object ssoRequest() {
        return SaSsoServerProcessor.instance.dister();
    }*/

    // SSO-Server：统一认证地址
    @RequestMapping("/sso/auth")
    public Object ssoAuth() {
        return SaSsoServerProcessor.instance.ssoAuth();
    }

    // SSO-Server：RestAPI 登录接口
/*    @RequestMapping("/sso/doLogin")
    public Object ssoDoLogin() {
        return SaSsoServerProcessor.instance.ssoDoLogin();
    }*/

    // SSO-Server：校验ticket 获取账号id
    @RequestMapping("/sso/checkTicket")
    public Object ssoCheckTicket() {
        return SaSsoServerProcessor.instance.ssoCheckTicket();
    }

    // SSO-Server：单点注销
    @RequestMapping("/sso/signout")
    public Object ssoSignout() {
        return SaSsoServerProcessor.instance.ssoSignout();
    }

    /**
     * 配置SSO相关参数
     */
    @Autowired
    private void configSso(SaSsoServerConfig ssoServer) {
        // 配置：未登录时返回的View
        /* ssoServer.notLoginView = () -> {
            return new ModelAndView("sa-login.html");
        };

        // 配置：登录处理函数
        ssoServer.doLoginHandle = (name, pwd) -> {
            //登录类型
            LoginBody loginBody = new LoginBody();
            loginBody.setUserName(name);
            loginBody.setPassword(pwd);
            LoginResult loginResult = loginService.createToken("B2C",loginBody);
            return ResponseData.suc(loginResult);
        };*/

        // 配置 Http 请求处理器 （在模式三的单点注销功能下用到，如不需要可以注释掉）
        ssoServer.sendHttp = url -> {
            try {
                log.info("------ 发起请求：{}", url);
                String resStr = Forest.get(url).executeAsString();
                log.info("------ 请求结果：{}", resStr);
                return resStr;
            } catch (Exception e) {
                log.error("配置SSO相关参数异常:", e);
                return null;
            }
        };
    }

    @ApiLog
    @ApiOperation(value = "账号密码登录", notes = "账号密码登录")
    @PostMapping("/login")
    public ResponseData<LoginResult> login(@RequestBody @Validated RequestDataDto<LoginBody> requestData, HttpServletRequest request) {
        LoginBody loginBody = requestData.getData();
        if (ObjectUtils.isEmpty(loginBody)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        BizDto bizDto = initBizDto(request);
        //极验验证
        if ("Y".equals(geetestPropertiesConfig.getGeetestLogin())) {
            geetestService.validate(SceneEnum.LOGIN, requestData);
        }
        LoginResult loginResult = loginService.createToken(bizDto, requestData.getBlackBox(), loginBody);
        if (StringUtils.isNotBlank(loginResult.getAccessToken())) {
            return ResponseData.suc(loginResult);
        } else {
            return ResponseData.fail(CommonErrorCode.LOGIN_ERROR, localeUtil.getTips(CommonErrorCode.LOGIN_ERROR.name()), loginResult);
        }
    }

    @ApiLog
    @ApiOperation(value = "验证码登录", notes = "验证码登录，支持邮箱以及短信验证码")
    @PostMapping("/captchaLogin")
    public ResponseData<LoginResult> captchaLogin(@RequestBody @Validated RequestDataDto<LoginCaptchaBody> requestData, HttpServletRequest request) {
        LoginCaptchaBody loginBody = requestData.getData();
        if (ObjectUtils.isEmpty(loginBody)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(loginService.createToken(bizDto, requestData.getBlackBox(), loginBody));
    }

    @ApiLog
    @ApiOperation(value = "getThirdPartyLoginUrl", notes = "获取三方授权登录地址")
    @PostMapping("/getThirdPartyLoginUrl")
    public ResponseData<ThirdPartyLoginUrlResult> getThirdPartyLoginUrl(@RequestBody @Validated RequestDataDto<ThirdPartyLoginUrlParam> requestData) {
        ThirdPartyLoginUrlResult thirdPartyLoginUrlResult = loginService.getThirdPartyLoginUrl(requestData.getChannelNo(), requestData.getData());
        return ResponseData.suc(thirdPartyLoginUrlResult);
    }

    @ApiLog
    @ApiOperation(value = "qqLoginCallBack", notes = "QQ通过code获取用户信息并登录")
    @GetMapping("/qqLoginCallBack")
    public ResponseData<ThirdPartyLoginResult> qqLoginCallBack(@RequestParam(name = "code") String code, @RequestParam("state") String state) {
        ThirdPartyLoginResult thirdPartyLoginResult = loginService.qqLoginCallBack(code, state);
        return ResponseData.suc(thirdPartyLoginResult);
    }

    @ApiLog
    @ApiOperation(value = "wechatLoginCallBack", notes = "微信通过code获取用户信息并登录")
    @GetMapping("/wechatLoginCallBack")
    public ResponseData<ThirdPartyLoginResult> wechatLoginCallBack(@RequestParam(name = "code") String code, @RequestParam("state") String state) {
        ThirdPartyLoginResult thirdPartyLoginResult = loginService.wechatLoginCallBack(code, state);
        return ResponseData.suc(thirdPartyLoginResult);
    }

    @ApiLog
    @ApiOperation(value = "alipayLoginCallBack", notes = "支付宝通过code获取用户信息并登录")
    @GetMapping("/alipayLoginCallBack")
    public ResponseData<ThirdPartyLoginResult> alipayLoginCallBack(@RequestParam(name = "code") String code, @RequestParam("state") String state) {
        ThirdPartyLoginResult thirdPartyLoginResult = loginService.alipayLoginCallBack(code, state);
        return ResponseData.suc(thirdPartyLoginResult);
    }

    @ApiLog
    @ApiOperation(value = "bindThirdPartyLogin", notes = "绑定三方账号并登录")
    @PostMapping("/bindThirdPartyLogin")
    public ResponseData<LoginResult> bindThirdPartyLogin(@RequestBody @Validated RequestDataDto<BindThirdPartyLoginParam> requestData) {
        LoginResult loginResult = loginService.bindThirdPartyLogin(requestData, requestData.getData());
        return ResponseData.suc(loginResult);
    }

    @RequestMapping("/sso/getData")
    public ResponseData getData(String apiType, String loginId) {
        System.out.println("---------------- 获取数据 ----------------");
        System.out.println("apiType=" + apiType);
        System.out.println("loginId=" + loginId);
        // 校验签名：只有拥有正确秘钥发起的请求才能通过校验
        SaSignUtil.checkRequest(SaHolder.getRequest());
        // 自定义返回结果（模拟）
        return new ResponseData<>();
    }

    @ApiLog
    @ApiOperation(value = "loginOut", notes = "注销登录态")
    @PostMapping("/loginOut")
    public ResponseData loginOut(@RequestBody @Validated RequestDataDto requestData, HttpServletRequest request, HttpServletResponse response) {
        BizDto bizDto = initBizDto(request);
        //因为前端获取不到对应的cookie，此处自行从cookie获取
        if (ChannelCodeEnum.B2C.getChannelCode().equalsIgnoreCase(bizDto.getHeadChannelCode())) {
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                String uscka = "";
                for (Cookie cookie : cookies) {
                    if (".uscka".equals(cookie.getName())) {
                        uscka = cookie.getValue();
                        break;
                    }
                }
                log.info("当前获取到的uscka:{}", uscka);
                if (StringUtils.isNotBlank(uscka) && StringUtils.isNotBlank(requestData.getFfpId())) {
                    loginService.loginOut(bizDto, requestData.getFfpId(), uscka);
                    Cookie newCookie = new Cookie(".uscka", null);
                    newCookie.setMaxAge(0); //立即删除型
                    //newCookie.setDomain(domain);//保存cookie的IP地址,则是删除这个IP的cookie
                    newCookie.setPath("/"); //项目所有目录均有效，这句很关键，否则不敢保证删除
                    newCookie.setHttpOnly(true);
                    response.addCookie(newCookie); //重新写入，将覆盖之前的
                }
            }
            return ResponseData.suc();
        }
        // 获取渠道信息
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        // 配置文件存在登录使用的渠道 登录渠道切换为配置的渠道 否则使用原渠道
        String loginChannel = StringUtils.isBlank(channelInfo.getLoginChannelCode()) ? bizDto.getHeadChannelCode() : channelInfo.getLoginChannelCode();
        // 会话注销，根据账号id 和 设备类型
        StpUtil.logout(requestData.getFfpNo(), loginChannel);
        return ResponseData.suc();
    }

    @ApiLog
    @ApiOperation(value = "checkToken", notes = "校验token是否有效")
    @PostMapping("/checkToken")
    public ResponseData<Object> checkToken(@RequestBody @Validated RequestDataDto requestData) {
        return ResponseData.suc(null);
    }

    @ApiLog
    @ApiOperation(value = "checkAccount", notes = "积分商城账户状态检查")
    @PostMapping("/checkAccount")
    public ResponseData<CheckAccountResult> checkAccount(@RequestBody @Validated RequestDataDto requestData, HttpServletRequest request, HttpServletResponse response) {
        if (!requestData.isLoginFlag()) {
            throw new MultiLangServiceException(CommonErrorCode.INVALID_TOKEN);
        }
        BizDto bizDto = initBizDto(request);
        QueryAccountByMemberIdResultDto queryAccountByMemberIdResult = accountService.queryAccountByMemberId(bizDto, requestData.getFfpNo());
        CrmContactDto crmContactDto = ContactUtil.filterMemberContact(queryAccountByMemberIdResult.getContacts(), ContactTypeEnum.MOBILE);
        CrmContactDto emailContactDto = ContactUtil.filterMemberContact(queryAccountByMemberIdResult.getContacts(), ContactTypeEnum.EMAIL);
        CheckAccountResult checkAccountResult = new CheckAccountResult();
        if (ObjectUtils.isNotEmpty(crmContactDto) && StringUtils.isNotBlank(crmContactDto.getContactValue())) {
            CrmPhoneInfo crmPhoneInfo = PhoneUtil.formatCrmPhoneInfo(crmContactDto.getContactValue());
            String areaId = StringUtils.isBlank(crmPhoneInfo.getAreaId()) ? "86" : crmPhoneInfo.getAreaId();
            checkAccountResult.setPhoneNo(areaId + "-" + crmPhoneInfo.getPhone());
        }
        if (ObjectUtils.isNotEmpty(emailContactDto) && StringUtils.isNotBlank(emailContactDto.getContactValue())) {
            checkAccountResult.setEmail(emailContactDto.getContactValue());
        }
        checkAccountResult.setFfpNo(requestData.getFfpNo());
        return ResponseData.suc(checkAccountResult);
    }
}
