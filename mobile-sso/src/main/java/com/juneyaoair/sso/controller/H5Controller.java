package com.juneyaoair.sso.controller;

import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.sso.util.SaSsoConsts;
import cn.dev33.satoken.sso.template.SaSsoUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import cn.dev33.satoken.util.SaResult;

/**
 * 前后台分离架构下集成SSO所需的代码 （SSO-Server端）
 * <p>（注：如果不需要前后端分离架构下集成SSO，可删除此包下所有代码）</p>
 * <AUTHOR>
 *
 */
@RestController
public class H5Controller {
	/**
	 * 获取 redirectUrl 
	 */
	@RequestMapping("/sso/getRedirectUrl")
	private ResponseData getRedirectUrl(String redirect, String mode, String client) {
		// 未登录情况下，返回 NOT_LOGIN
		if(StpUtil.isLogin() == false) {
			return ResponseData.fail(CommonErrorCode.NOT_LOGIN);
		}
		// 已登录情况下，构建 redirectUrl 
		if(SaSsoConsts.MODE_SIMPLE.equals(mode)) {
			// 模式一 
			SaSsoUtil.checkRedirectUrl(SaFoxUtil.decoderUrl(redirect));
			return ResponseData.suc(redirect);
		} else {
			// 模式二或模式三 
			String redirectUrl = SaSsoUtil.buildRedirectUrl(StpUtil.getLoginId(), client, redirect);
			return ResponseData.suc(redirectUrl);
		}
	}
}
