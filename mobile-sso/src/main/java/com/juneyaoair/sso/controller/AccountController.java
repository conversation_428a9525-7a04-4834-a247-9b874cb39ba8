package com.juneyaoair.sso.controller;

import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.SensitiveOperationEnum;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.tools.utils.PhoneUtil;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import com.juneyaoair.sso.dto.*;
import com.juneyaoair.sso.service.IRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/10 8:59
 */
@Slf4j
@Api(value = "AccountController", tags = "会员账户注册服务")
@RestController
public class AccountController extends BaseController {
    @Autowired
    private IRegisterService registerService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private TongDunService tongDunService;

    @ApiLog
    @PostMapping("/register")
    @ApiOperation(value = "会员标准注册", notes = "会员注册服务")
    public ResponseData<LoginResult> register(@RequestBody @Validated RequestDataDto<RegisterParam> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        return registerService.register(bizDto, requestDataDto.getData(),requestDataDto.getLanguage(),true);
    }

    @ApiLog
    @PostMapping("/sendSms")
    @ApiOperation(value = "发送短信验证码", notes = "短信验证码发送，仅限用于无登录状态下的短信发送")
    public ResponseData sendSms(@RequestBody @Validated RequestDataDto<Captcha> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        //极验验证
        geetestService.validate(SceneEnum.SMS_GLOBAL, requestDataDto);
        //同盾验证
        Captcha captcha = requestDataDto.getData();
        CrmPhoneInfo crmPhoneInfo = PhoneUtil.formatCrmPhoneInfo(captcha.getPhone());
        tongDunService.sendSmsOrEmail(bizDto, requestDataDto.getBlackBox(), crmPhoneInfo, "", captcha.getFunc(), SensitiveOperationEnum.SEND_SMS_RISK_CTRL.name());
        //发送验证码
        registerService.sendSms(bizDto, captcha, requestDataDto.getLanguage());
        return ResponseData.suc();
    }

    @ApiLog
    @PostMapping("/sendEmail")
    @ApiOperation(value = "发送邮箱验证码", notes = "邮箱验证码发送，仅限用于无登录态下的邮箱发送")
    public ResponseData sendEmail(@RequestBody @Validated RequestDataDto<EmailCaptcha> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        //极验验证
        geetestService.validate(SceneEnum.EMAIL_GLOBAL, requestDataDto);
        //同盾验证
        EmailCaptcha emailCaptcha = requestDataDto.getData();
        tongDunService.sendSmsOrEmail(bizDto, requestDataDto.getBlackBox(), null, emailCaptcha.getEmail(), emailCaptcha.getFunc(), SensitiveOperationEnum.SEND_EMAIL_CTRL.name());
        //发送邮箱验证码
        registerService.sendEmail(bizDto, emailCaptcha, requestDataDto.getLanguage());
        return ResponseData.suc();
    }

    @ApiLog
    @PostMapping("/sendCaptchaByCard")
    @ApiOperation(value = "通过卡号发送验证码", notes = "通过卡号发送短信/邮箱验证码,仅限用于无登录态下的发送")
    public ResponseData sendCaptchaByCard(@RequestBody @Validated RequestDataDto<CaptchaByCard> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        CaptchaByCard captchaByCard = requestDataDto.getData();
        //同盾检验
        registerService.sendCaptchaByCard(bizDto, captchaByCard, requestDataDto.getLanguage());
        return ResponseData.suc();
    }

    @ApiLog
    @PostMapping("/queryResetLoginMethod")
    @ApiOperation(value = "通过卡号选择支持的找回方式", notes = "通过卡号选择支持的找回方式")
    public ResponseData<List<ResetLoginMethod>> queryResetLoginMethod(@RequestBody @Validated RequestDataDto<ResetLoginMethodParam> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        ResetLoginMethodParam resetLoginMethodParam = requestDataDto.getData();
        //通过卡号选择支持的找回方式
        return ResponseData.suc(registerService.queryResetLoginMethod(bizDto, resetLoginMethodParam.getFfpCardNo()));
    }

    @ApiLog
    @PostMapping("/checkCaptcha")
    @ApiOperation(value = "校验验证码", notes = "校验手机验证码/邮箱验证码输入是否正确")
    public ResponseData checkCaptcha(@RequestBody @Validated RequestDataDto<CheckCaptchaParam> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        //校验验证码
        registerService.checkCaptcha(bizDto, requestDataDto.getData());
        return ResponseData.suc();
    }
    @PostMapping("/resetLogin")
    @ApiOperation(value = "通过验证码重置登录密码", notes = "通过手机验证码/邮箱验证码重置登录密码")
    public ResponseData resetLogin(@RequestBody @Validated RequestDataDto<ResetLogin> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        //重置登录密码
        registerService.resetLogin(bizDto, requestDataDto.getData());
        return ResponseData.suc();
    }

    @ApiLog
    @PostMapping("/resetLoginByCard")
    @ApiOperation(value = "通过卡号重置登录密码", notes = "通过卡号重置登录密码")
    public ResponseData resetLoginByCard(@RequestBody @Validated RequestDataDto<ResetLoginByCardParam> requestDataDto, HttpServletRequest request) {
        checkObjNotNUll(requestDataDto.getData());
        BizDto bizDto = initBizDto(request);
        ResetLoginByCardParam resetLoginByCardParam=requestDataDto.getData();
        //重置登录密码
        registerService.resetLoginByCard(bizDto, resetLoginByCardParam);
        return ResponseData.suc();
    }
}
