package com.juneyaoair.sso.config;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/5 11:27
 */

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.config.SaSignConfig;
import cn.dev33.satoken.sign.SaSignTemplate;
import cn.dev33.satoken.sso.template.SaSsoServerTemplate;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 自定义 SaSsoServerTemplate 子类
 */
@Component
public class CustomSaSsoServerTemplate extends SaSsoServerTemplate {
    /**
     * 允许的请求源
     */
    @ApolloJsonValue("${sso.server.client.key:[\"b2cweb:UJmrAiUVaSLuGznpqxtiEHTqIfCmVioJ\"]}")
    private Set<String> clientKeySet;

    // 存储所有 client 的秘钥
    static Map<String, SaSignTemplate> signMap = new HashMap<>();

    @Override
    public SaSignTemplate getSignTemplate(String client) {
        ClientInfo clientInfo = initClientInfo(client);
        if(clientInfo == null){
            // 找不到就返回全局默认的 SaSignTemplate
            return SaManager.getSaSignTemplate();
        }
        // 先从自定义的 signMap 中获取
        SaSignTemplate saSignTemplate = signMap.get(client);
        if (saSignTemplate != null) {
            String oldKey = saSignTemplate.getSecretKey();
            if(oldKey.equals(clientInfo.getSecretKey())){
                return saSignTemplate;
            }else{
                //密钥不匹配，删除原有配置
                signMap.remove(client) ;
            }
        }
        SaSignTemplate newSaSignTemplate = new SaSignTemplate(new SaSignConfig(clientInfo.getSecretKey()));
        signMap.put(client, newSaSignTemplate);
        return newSaSignTemplate;
    }

    private ClientInfo initClientInfo(String client){
        if (CollectionUtils.isNotEmpty(clientKeySet)) {
            for (String s : clientKeySet) {
                String c = s.split(":")[0];
                String key = s.split(":")[1];
                if (StringUtils.isNoneBlank(c, key) && client.equals(c)) {
                    return new ClientInfo(client,key);
                }
            }
        }
        return null;
    }

    class ClientInfo{
        private String clientId;
        private String secretKey;

        public ClientInfo(String clientId, String secretKey) {
            this.clientId = clientId;
            this.secretKey = secretKey;
        }

        public String getClientId() {
            return clientId;
        }

        public String getSecretKey() {
            return secretKey;
        }
    }
}

