package com.juneyaoair.sso.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 第三方授权配置信息
 * @date 2024/8/26 9:28
 */
@Data
@Configuration
public class ThirdAuthConfig {

    // QQ授权地址服务器地址
    @Value("${third.qq.authUrl:https://graph.qq.com}")
    private String qqAuthUrl;
    // QQ应用ID
    @Value("${third.qq.appId:*********}")
    private String qqAppId;
    // QQ应用密钥
    @Value("${third.qq.secret:7277fe681fb7ee43c89a4c87f27b6fc7}")
    private String qqSecret;
    // QQ授权回调地址
    @Value("${third.qq.redirectUri:https://passport.juneyaoair.com/Member/QQLoginCallBack}")
    private String qqRedirectUri;

    // 微信授权地址服务器地址
    @Value("${third.wechat.openUrl:https://open.weixin.qq.com}")
    private String wechatOpenUrl;
    @Value("${third.wechat.apiUrl:https://api.weixin.qq.com}")
    private String wechatApiUrl;
    // 微信应用ID
    @Value("${third.wechat.appId:wx30d8d9338e0b628f}")
    private String wechatAppId;
    // 微信应用密钥
    @Value("${third.wechat.secret:5525c99083e977c0dbc4476a9d5e2a2c}")
    private String wechatSecret;
    // 微信授权回调地址
    @Value("${third.wechat.redirectUri:https://passport.juneyaoair.com/Member/WeChatLoginCallBackTEST}")
    private String wechatRedirectUri;

    // 支付宝授权地址服务器地址
    @Value("${third.alipay.openAuthUrl:https://openauth.alipay.com}")
    private String alipayOpenAuthUrl;
    @Value("${third.alipay.openApiUrl:https://openapi.alipay.com/gateway.do}")
    private String alipayOpenApiUrl;
    @Value("${third.alipay.appId:2018070460540363}")
    private String alipayAppId;
    @Value("${third.alipay.privateKey:MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDFrDPXb2ftOYp4BglcZK2Sj5GEYuEksSs8RZRmN6+zhhNnK9PvihaI1UDxogYykM18le65vk5NW5PQ6qQYGZO0RNU71qClH14Lzp2gHedvXZH2HDHAxtw21CZf8Ye7GdYS1t9vwtItNOt3hzc5rH5dlXpJrhBx2OiKUqGmrT8oMMuXQHRAxHF0hRvpt7BuJBd1YfrEhNSwHx3yKYopCblSSuhnXR1jCpchkXb5QyI0vQVwHUuK6Y2cf0w+ELijNOMyKC9P8WOTe5yTb1u4YFCK1L0hs09nsTCWNp86wzpJGO8S0VXzrIaaZQ17Yk3OFUFrW00xBaGL7smh9YoMXngdAgMBAAECggEBAK7PU+VQc2L6APeVtw+LRWiYKIMgAqU1PNwHWG5xDqO/jUcvHjT3NGh71vwZYkuzfNWRrlV687o7o2QdLVLWw6WnPb+2VcjRDZV5ejIQYAw/foZcdq9Z7YNbaDhu267d8VAlz+EBNGegJiBFZtRNNE9Ekqm4Enjr7vHsk2JYUcMSne78GG/6VTKi1vcEkxdPB8QoRlEOT9wnZJFWNbqueFq8MkLsY7y+fUIU4OSNNGrXW6iglmYFlHdbiQccl5e8OAXgAUhR8ZjRvVC4g5/OEhHljqJkyCYOpUXPRaOkBfCgCy11jQ+E00W/WWyBH8WRv+ra+NHSsD7fPZDNVI/dju0CgYEA5g7Vzo6vYHWD4GqT4sbCU0YrWMcmmva0qwwe0yZiIeZbkWDgZJtaICmISOJaC4a+dfDR3/VxRsTswMVvEPFYkO4Kfx2P9Zo92GtALYCv9pn2brX3iDdHFCnotdCH7f05sG0sYYBjVFJZLziiRmPNNlXir5kzLT/DMGd0UrObKnMCgYEA2/Z9YxKa5fp6JsJWt+i8fho26LE4LoC8IWaFp6OzN5DgeNwLjbjMYo88ZEIt/reEwNYBRnXLjdDYYaFAo8+LTffFcGCoPo/UtKfLTN7eDicqJSFbabYf3PKiQPLJaiTg1ND4+nz3+Myybj+8OYKu9FQxqmmL8J4ryDnXt+wbXy8CgYBgBa0j69Yzcpk6oAFd1Avj6hPJmEX6xCIgioyLISgwcg+iHEdMdrMlXXBuy1s+GF2CqKNQ0QcYvLT8uOtkrPeEdnaXG+WsY1aQ8CL3WxEW4qhDCVxp1HisZ9mjmYhCw0DC7K5re8T5uu1SklbY/mr21N4q4MzFhCmaGHQq2KBZFQKBgDD9IMfj6OO3eWUQe0fSacicBgalEaIpwbyenL3NU6jYd2XzfA9ATSTWSdj6dEv6i3J3Nyl+KquXC9oSk9bn7lqbCDoTDOWCGxlxFXI1YLc+OcCzQSy2BzqqP0C/0eP1+qzrLch79K5QyJyNTVpxpI1+AuG1SBl64L1jRIwN5bq5AoGAaPsMK1ZGI/B8oHZV7Slf8W66aG1MqQxxgaPzU/bydX5R/NICuQqjx1XMjUWdlykE+DAL6lcap0X64jC2K+dw3AI37HRh7uvTH2cIyS+Et6WLAfUXsO/rd+JaIWQPF2wADgIckJE+Ll5MzJ0H6+n5UOKuNlDgTvafJDBn/6lmEq0=}")
    private String alipayPrivateKey;
    @Value("${third.alipay.publicKey:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoqnIHQkGpmtjl0GM4u00+ONZcQU7Sw75qNd/t9G2ancSvm+tm6cM8bFu12qEJdtCKJgylf8XVyfDPVo6eGAZ4Fs+5saBs0hQJ6XPy8q5QeIKRWqrn05iWCdB5q6Z1S5RSOegl3WLSyetRTExCWMwaIGhZjf9yM40ztn3t5nhgCnoSwkP8ZtDOslOSsC68zkGkDhodVP4HwFAPysIR4FzU14HR+o56JGneuFjhj9iB93XvcpxRVnOzSzoog04A336W5YySpvuJm8eu0yFrJieu1AN5nJfsB6sM+EEjFcByaungNe5vQ+G2Y2ZgcJ6WKODBBVnb9C7kX8Et8Lf0jyBewIDAQAB}")
    private String alipayPublicKey;
    // 支付宝授权回调地址
    @Value("${third.alipay.redirectUri:https://passport.juneyaoair.com/Member/AlipayLoginCallBackTEST}")
    private String alipayRedirectUri;

    @Value("${third.aesKey:aYnnI04HUsUpg3e3}")
    private String aesKey;

}
