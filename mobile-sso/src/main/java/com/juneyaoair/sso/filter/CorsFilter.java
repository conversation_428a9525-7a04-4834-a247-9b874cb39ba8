package com.juneyaoair.sso.filter;

import java.io.IOException;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.reactive.CorsUtils;

/**
 * 跨域过滤器 
 * <AUTHOR> 
 */
@Slf4j
@Component
@Order(-200)
public class CorsFilter implements Filter {
	/**
	 * 允许的请求源
	 */
	@ApolloJsonValue("${oneorder.allowOriginList:[\"*\"]}")
	private Set<String> allowOriginList;

	static final String OPTIONS = "OPTIONS";

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest) req;
		HttpServletResponse response = (HttpServletResponse) res;
		String origin = request.getHeader("Origin");
		// 如果允许跨域，则添加Access-Control-Allow-Origin响应头
		if (isAllowedOrigin(origin)) {
			response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
		}
		// 允许指定域访问跨域资源
		//response.setHeader("Access-Control-Allow-Origin", "*");
		// 允许所有请求方式
		response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
		// 有效时间
		response.setHeader("Access-Control-Max-Age", "3600");
		// 允许的header参数
		response.setHeader("Access-Control-Allow-Headers", "channelNo,Content-Type,x-requested-with,authorization");
		// 如果是预检请求，直接返回
		if (OPTIONS.equals(request.getMethod())) {
			log.info("=======================浏览器发来了OPTIONS预检请求==========");
			response.getWriter().print("");
			return;
		}
		chain.doFilter(req, res);
	}

	private boolean isAllowedOrigin(String origin) {
		// 例如，你可以检查一个白名单，看看origin是否在其中
		if(CollectionUtils.isEmpty(allowOriginList) || allowOriginList.contains("*")){
			return true;
		}
		// 返回true表示允许，false表示不允许
		return allowOriginList.contains(origin);
	}

	@Override
	public void init(FilterConfig filterConfig) {
	}

	@Override
	public void destroy() {
	}
}
