package com.juneyaoair.sso.constant;

/**
 * 外部校验接口地址常量
 * <AUTHOR>
 */
public interface ApiConstant {

    /** QQ 获取Authorization Code */
    String QQ_OAUTH_AUTHORIZE = "/oauth2.0/authorize";
    /** QQ 通过Authorization Code获取Access Token */
    String QQ_OAUTH_TOKEN = "/oauth2.0/token";
    /** QQ 获取用户OpenID */
    String QQ_OAUTH_ME = "/oauth2.0/me";
    /** QQ 获取用户信息 */
    String QQ_GET_USER_INFO = "/user/get_user_info";

    /** 微信 请求CODE */
    String WECHAT_QR_CONNECT = "/connect/qrconnect";
    /** 微信 通过code获取access_toke */
    String WECHAT_ACCESS_TOKEN = "/sns/oauth2/access_token";
    /** 微信 获取用户个人信息 */
    String WECHAT_USER_INFO = "/sns/userinfo";

    /** 支付宝授权登录地址 */
    String ALIPAY_OAUTH_AUTHORIZE = "/oauth2/publicAppAuthorize.htm";

}
