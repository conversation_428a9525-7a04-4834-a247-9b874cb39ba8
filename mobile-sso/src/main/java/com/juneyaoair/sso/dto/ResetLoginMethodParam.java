package com.juneyaoair.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 14:26
 */
@Data
@ApiModel(value = "ResetLoginMethodParam", description = "会员查询参数")
public class ResetLoginMethodParam {
    @ApiModelProperty(value = "会员号", notes = "", required = true)
    @NotBlank(message = "会员号不可为空")
    private String ffpCardNo;
}
