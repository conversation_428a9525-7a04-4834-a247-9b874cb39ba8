package com.juneyaoair.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/5 17:28
 */
@Data
@ApiModel(value = "ResetLogin",description = "重置登录密码")
public class ResetLogin extends CheckCaptchaParam {
    @ApiModelProperty(value = "新的登录密码",notes = "",required = true)
    @NotBlank(message = "密码不可为空")
    private String pwd;
}
