package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2018/7/20  16:45.
 */
@ApiModel(value = "Captcha", description = "发送验证码参数")
@Data
public class Captcha {
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = PatternCommon.GLOBAL_PHONE, message = "请输入正确的手机号")
    private String phone;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "短信类型", required = true)
    @NotNull(message = "短信类型不能为空")
    private CaptchaFuncEnum func;
}
