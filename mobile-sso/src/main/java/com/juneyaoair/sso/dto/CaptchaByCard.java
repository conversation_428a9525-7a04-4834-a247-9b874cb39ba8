package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 13:22
 */
@Data
@ApiModel(value = "CaptchaByCard", description = "发送验证码参数")
public class CaptchaByCard {
    @ApiModelProperty(value = "会员号", notes = "", required = true)
    @NotBlank(message = "会员号不可为空")
    private String ffpCardNo;
    @ApiModelProperty(value = "联系方式类型", notes = "详情参考:ContactTypeEnum", required = true)
    @NotNull(message = "联系方式类型不可为空")
    private ContactTypeEnum contactType;
    @ApiModelProperty(value = "联系方式类型", notes = "手机号格式:86-173********，邮箱格式****@juneyaoair.com", required = true)
    @NotBlank(message = "联系方式不可为空")
    private String contactValue;
}
