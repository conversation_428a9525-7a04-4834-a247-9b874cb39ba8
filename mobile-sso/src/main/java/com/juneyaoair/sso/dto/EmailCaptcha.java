package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@ApiModel(value = "EmailCaptcha", description = "发送邮箱验证码参数")
@Data
public class EmailCaptcha {
    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = PatternCommon.EMAIL, message = "请输入正确的邮箱")
    private String email;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "验证码类型", required = true)
    @NotNull(message = "验证码类型不能为空")
    private CaptchaFuncEnum func;
}
