package com.juneyaoair.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 10:24
 */
@Data
@ApiModel(value = "LoginBody",description = "登录请求体")
public class LoginBody {
    @NotBlank(message = "账号不可为空")
    @ApiModelProperty(value = "手机号/会员卡号/邮箱地址",required = true)
    private String userName;
    @NotBlank(message = "账号密码不可为空")
    @ApiModelProperty(value = "账号密码/验证码",required = true)
    private String password;
}
