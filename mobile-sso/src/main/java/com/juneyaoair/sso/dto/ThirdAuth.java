package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.common.LoginTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/21 19:26
 */
@Data
@ApiModel(value = "ThirdAuth",description = "第三方授权登录")
public class ThirdAuth {
    @ApiModelProperty(value = "第三方授权应用",notes = "目前支持QQ,WeChat,Alipay")
    private LoginTypeEnum loginType;
}
