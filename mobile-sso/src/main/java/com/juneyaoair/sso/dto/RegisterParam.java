package com.juneyaoair.sso.dto;

import com.google.common.base.Strings;
import com.juneyaoair.oneorder.common.dto.enums.RegisterTypeEnum;
import com.juneyaoair.oneorder.crm.dto.common.SalutationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/10 9:22
 */
@Data
@ApiModel(value = "RegisterParam",description = "注册业务参数")
public class RegisterParam {
    @ApiModelProperty(value = "注册方式",notes = "目前支持手机号以及邮箱注册:MOBILE,EMAIL",required = true)
    @NotNull(message = "注册方式不可为空")
    private RegisterTypeEnum registerType;
    @ApiModelProperty(value = "邮箱",required = true)
    private String email;
    @ApiModelProperty(value = "手机号格式：86-12345678910",required = true,notes = "手机号格式：86-12345678910")
    private String phone;
    @ApiModelProperty(value = "尊称",notes = "MR,MS,MISS,MRS，详情参考:SalutationEnum", required = true)
    @NotNull(message = "尊称不可为空")
    private SalutationEnum title;
    @ApiModelProperty(value = "出生日期",required = true)
    @NotBlank(message = "出生日期不可为空")
    private String birthDate;
    @ApiModelProperty(value = "证件类型",required = true)
    @NotBlank(message = "证件类型不可为空")
    private String idType;
    @ApiModelProperty(value = "证件号码",required = true)
    @NotBlank(message = "证件号码不可为空")
    private String idNumber;
    @ApiModelProperty(value = "英文姓",required = true)
    @NotBlank(message = "英文姓不可为空")
    private String lastName;
    @ApiModelProperty(value = "英文名",required = true)
    @NotBlank(message = "英文名不可为空")
    private String firstName;
    @ApiModelProperty(value = "校验码",required = true)
    @NotBlank(message = "校验码不可为空")
    private String checkCode;
    @ApiModelProperty(value = "凭据",required = true)
    private String pwd;

    @ApiModelProperty(value = "证件签发国", notes = "因私普通护照、港澳居民来往内地通行证（原回乡证）、台湾居民来往大陆通行证（原台胞证）；需要维护")
    private String idCountry;

    @ApiModelProperty(value = "证件有效期", notes = "因私普通护照、港澳居民来往内地通行证（原回乡证）、台湾居民来往大陆通行证（原台胞证）；需要维护")
    private String idExpireDate;

    @ApiModelProperty(value = "国籍", notes = "因私普通护照、港澳居民来往内地通行证（原回乡证）、台湾居民来往大陆通行证（原台胞证）；需要维护")
    private String nationality;
}
