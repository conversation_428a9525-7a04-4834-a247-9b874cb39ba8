package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 16:07
 */
@Data
@ApiModel(value = "ResetLoginByCardParam", description = "会员号重置密码参数")
public class ResetLoginByCardParam {
    @ApiModelProperty(value = "会员号", notes = "", required = true)
    @NotBlank(message = "会员号不可为空")
    private String ffpCardNo;
    @ApiModelProperty(value = "联系方式类型", notes = "详情参考:ContactTypeEnum", required = true)
    @NotNull(message = "联系方式类型不可为空")
    private ContactTypeEnum contactType;
    @ApiModelProperty(value = "验证码", notes = "")
    private String captcha;
    @ApiModelProperty(value = "新的登录密码",notes = "",required = true)
    private String pwd;
}
