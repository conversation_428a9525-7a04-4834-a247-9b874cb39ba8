package com.juneyaoair.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 10:34
 */
@Data
@ApiModel(value = "LoginResult",description = "登录结果DTO")
public class LoginResult {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "登录凭据")
    private String accessToken;

    @ApiModelProperty(value = "会员Id")
    private String ffpId;

    @ApiModelProperty(value = "会员卡号")
    private String ffpNo;

    @ApiModelProperty(value = "手机号")
    private String memberTel;

    @ApiModelProperty(value = "电子邮箱")
    private String memberEmail;

    @ApiModelProperty(value = "会员卡号")
    private String headImageUrl;

    @ApiModelProperty(value = "凭据过期时间")
    private long expiresIn;

    @ApiModelProperty(value = "登录版本，用户后期切换时采用不同的验证方式")
    private String loginVersion;

    @ApiModelProperty(value = "错误剩余次数提醒")
    private long remainCount;

    @ApiModelProperty(value = "账号是否启用状态 true:启用 false:关闭")
    private boolean accountStatus;

}
