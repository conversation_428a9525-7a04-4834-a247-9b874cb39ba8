package com.juneyaoair.sso.dto;

import com.juneyaoair.oneorder.common.dto.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 9:12
 */
@Data
@ApiModel(value = "LoginCaptchaBody",description = "验证码登录请求体")
public class LoginCaptchaBody {
    @ApiModelProperty(value = "国家区号代码",required = true)
    private String countryCode;
    @ApiModelProperty(value = "手机号/邮箱",required = true)
    @NotBlank(message = "手机号/邮箱不可为空")
    private String account;
    @ApiModelProperty(value = "验证码",required = true)
    @NotBlank(message = "验证码不可为空")
    private String captcha;
    @ApiModelProperty(value = "登录方式",required = true)
    private RegisterTypeEnum loginType;
}
