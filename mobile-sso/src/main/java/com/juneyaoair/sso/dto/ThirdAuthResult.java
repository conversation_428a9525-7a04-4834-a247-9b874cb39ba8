package com.juneyaoair.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/26 9:08
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ThirdAuthResult",description = "第三方结果")
public class ThirdAuthResult {
    @ApiModelProperty(value = "第三方授权跳转地址",notes = "目前支持QQ,WeChat,Alipay")
    private String thirdUrl;
}
