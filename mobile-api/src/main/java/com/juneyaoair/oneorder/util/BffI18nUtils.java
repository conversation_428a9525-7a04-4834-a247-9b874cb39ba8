package com.juneyaoair.oneorder.util;

import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 国际化工具类
 *
 * <AUTHOR>
 */
public class BffI18nUtils {

    private BffI18nUtils() {
    }

    /**
     * 根据语言从机场信息中获取机场名称
     *
     * @param airportInfo
     * @param language
     * @return
     */
    public static String getAirportName(ApiAirPortInfoDto airportInfo, String language) {
        if (null == airportInfo) {
            return null;
        }
        // 语言为空 默认简体中文
        if (null == language) {
            language = LanguageEnum.ZH_CN.name();
        }
        String name = getName(airportInfo.getAirportNameMap(), language);
        return StringUtils.isBlank(name) ? airportInfo.getAirPortCode() : name;
    }

    /**
     * 获取机场城市名称
     *
     * @param airportInfo 机场信息
     * @param language    语言
     * @return 城市名称
     */
    public static String getCityName(ApiAirPortInfoDto airportInfo, String language) {
        if (null == airportInfo) {
            return null;
        }
        // 语言为空 默认简体中文
        if (null == language) {
            language = LanguageEnum.ZH_CN.name();
        }
        String name = getName(airportInfo.getCityNameMap(), language);
        return StringUtils.isBlank(name) ? airportInfo.getCityCode() : name;
    }

    /**
     * 获取城市名称
     *
     * @param cityInfo 城市信息
     * @param language 语言
     * @return 城市名称
     */
    public static String getCityName(ApiCityInfoDto cityInfo, LanguageEnum language) {
        if (cityInfo == null) {
            return null;
        }
        // 语言为空 默认简体中文
        if (language == null) {
            language = LanguageEnum.ZH_CN;
        }
        String name = getName(cityInfo.getCityNameMap(), language.name());
        return StringUtils.isBlank(name) ? cityInfo.getCityCode() : name;
    }

    /**
     * @param dictMap 通过I18nDictService.fetchDictData方法获取译文字典
     * @param key
     * @param language
     * @return String
     * @description
     * <AUTHOR>
     * @date 2025/4/10 15:34
     **/
    public static String getTranslation(Map<String, Map<String, String>> dictMap, String key, LanguageEnum language) {
        if (ObjectUtils.isEmpty(dictMap)) {
            return "";
        }
        Map<String, String> languageMap = dictMap.get(key);
        if (ObjectUtils.isEmpty(languageMap)) {
            return "";
        }
        return languageMap.get(language.name());
    }

    /**
     * 获取国际化名称
     *
     * @param nameMap  key:语言 value： 值
     * @param language
     * @return
     */
    private static String getName(Map<String, String> nameMap, String language) {
        if (null == nameMap) {
            return null;
        }
        return nameMap.get(language);
    }

}
