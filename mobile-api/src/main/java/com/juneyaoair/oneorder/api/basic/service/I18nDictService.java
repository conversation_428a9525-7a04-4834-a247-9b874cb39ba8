package com.juneyaoair.oneorder.api.basic.service;

import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import java.util.Map;

public interface I18nDictService {

    /**
     * 国际化翻译
     * @param dictionaryType
     * @param key
     * @param language
     * @return
     */
    String getTranslation(I18nDictionaryTypeEnum dictionaryType, String key, String language);


    Map<String, Map<String, String>> fetchDictData(I18nDictionaryTypeEnum dictionaryType);
    
    /**
     * 新增批量获取字典数据的方法
     * @param dictionaryTypes
     * @return
     */
    Map<String, Map<String, Map<String, String>>> fetchMultipleDictData(I18nDictionaryTypeEnum... dictionaryTypes);

    /**
     * 新增从批量字典数据中获取翻译的方法
     * @param dictionaries
     * @param dictionaryType
     * @param key
     * @param language
     * @return
     */
    String getTranslationFromDict(Map<String, Map<String, Map<String, String>>> dictionaries,
                                  I18nDictionaryTypeEnum dictionaryType,
                                    String key,
                                    LanguageEnum language);
}
