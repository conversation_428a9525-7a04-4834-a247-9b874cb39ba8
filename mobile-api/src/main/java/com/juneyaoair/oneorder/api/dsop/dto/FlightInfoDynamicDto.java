package com.juneyaoair.oneorder.api.dsop.dto;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 16:52
 */
@ApiModel(value = "FlightInfoDynamicDto",description = "航班动态信息")
@Data
public class FlightInfoDynamicDto implements Comparable<FlightInfoDynamicDto> {
    @ApiModelProperty(value = "航班号")
    private String flight_no;
    @ApiModelProperty(value = "航班状态")
    private String flight_status;
    @ApiModelProperty(value = "航班日期（北京时间）",notes = "foc的flightDate表示的是北京时间")
    @SerializedName(value = "flight_date")
    private String flight_date;
    @ApiModelProperty(value = "航班日期（当地时间）",notes = "flight_date_local表示的是当地时间")
    @SerializedName(value = "flight_date_local")
    private String flight_date_local;
    @ApiModelProperty(value = "出发城市名称")
    private String departure_city;
    @ApiModelProperty(value = "出发城市三字码")
    private String departure_city_code;
    @ApiModelProperty(value = "出发城市国内国际标识", notes = "D-国内，I-国际")
    private String departure_city_type;
    @ApiModelProperty(value = "出发机场三字码")
    private String departure_airport;
    @ApiModelProperty(value = "出发机场名称")
    private String depAirPortName;
    @ApiModelProperty(value = "出发航站楼")
    private String flightHTerminal;
    @ApiModelProperty(value = "到达城市名称")
    private String arrival_city;
    @ApiModelProperty(value = "到达城市三字码")
    private String arrival_city_code;
    @ApiModelProperty(value = "到达城市国内国际标识", notes = "D-国内，I-国际")
    private String arrival_city_type;
    @ApiModelProperty(value = "出发机场三字码")
    private String arrival_airport;
    @ApiModelProperty(value = "到达机场名称")
    private String arrAirPortName;
    @ApiModelProperty(value = "到达航站楼")
    private String flightTerminal;
    @ApiModelProperty(value = "计划起飞时间（当地时间）")
    private String std;
    @ApiModelProperty(value = "实际起飞时间（当地时间）")
    private String atd;
    @ApiModelProperty(value = "预计起飞时间（当地时间）")
    private String etd;
    @ApiModelProperty(value = "计划到达时间（当地时间）")
    private String sta;
    @ApiModelProperty(value = "预计到达时间（当地时间）")
    private String eta;
    @ApiModelProperty(value = "实际到达时间（当地时间）")
    private String ata;
    @ApiModelProperty(value = "机号")
    private String ac_reg;
    @ApiModelProperty(value = "机型代码",notes = "来源于销售系统")
    private String planeModel;
    @ApiModelProperty(value = "机型代码名称",notes = "来源于销售系统")
    private String planeModelName;
    @ApiModelProperty(value = "机型图url",notes = "来源于销售系统")
    private String planModelUrl;
    @ApiModelProperty(value = "值柜台")
    private String checkinTable;
    @ApiModelProperty(value = "头等舱值机柜台")
    private String firstClassCheckinTable;
    @ApiModelProperty(value = "商务舱值机柜台")
    private String businessCheckinTable;
    @ApiModelProperty(value = "登机口")
    private String boardGate;
    @ApiModelProperty(value = "起始地天气")
    private String deptWeather;
    @ApiModelProperty(value = "起始地温度")
    private String deptTemp;
    @ApiModelProperty(value = "起始地PM2.5指数")
    private String deptpm;
    @ApiModelProperty(value = "目的地天气")
    private String destWeather;
    @ApiModelProperty(value = "目的地温度")
    private String destTemp;
    @ApiModelProperty(value = "目的地PM2.5指数")
    private String destpm;
    @ApiModelProperty(value = "起飞机位")
    private String depStandGate;
    @ApiModelProperty(value = "到达机位")
    private String arrStandGate;
    @ApiModelProperty(value = "到达行李转盘号")
    private String baggageID;
    @ApiModelProperty(value = "历史准点率")
    private String ontimeRate;
    @ApiModelProperty(value = "延误时间",notes = "单位毫秒")
    private Long delayTime;
    @ApiModelProperty(value = "延误原因")
    private String delay_reason;
    @ApiModelProperty(value = "前序航班号")
    private String pre_flight_no;
    @ApiModelProperty(value = "前序航班-出发城市中文名")
    private String pre_departure_city;
    @ApiModelProperty(value = "前序航班-出发机场三字码")
    private String pre_departure_airport;
    @ApiModelProperty(value = "前序航班-到达城市中文名")
    private String pre_arrival_city;
    @ApiModelProperty(value = "前序航班-到达机场三字码")
    private String pre_arrival_airport;
    @ApiModelProperty(value = "前序航班-出发航站楼")
    private String pre_deptTerminal;
    @ApiModelProperty(value = "前序航班-到达航站楼")
    private String pre_arrTerminal;
    @ApiModelProperty(value = "前序航班-计划起飞时间")
    private String pre_std;
    @ApiModelProperty(value = "前序航班-计划到达时间")
    private String pre_sta;
    @ApiModelProperty(value = "前序航班-预计出发时间")
    private String pre_etd;
    @ApiModelProperty(value = "前序航班-预计到达时间")
    private String pre_eta;
    @ApiModelProperty(value = "前序航班-实际起飞时间")
    private String pre_atd;
    @ApiModelProperty(value = "前序航班-实际达到时间")
    private String pre_ata;
    @ApiModelProperty(value = "前序航班-航班状态")
    private String pre_flight_status;
    @ApiModelProperty(value = "前序航班-出发机场名称")
    private String pre_depAirPortName;
    @ApiModelProperty(value = "前序航班-到达机场名称")
    private String pre_arrAirPortName;
    @ApiModelProperty(value = "前序航班是否关注",notes = "true关注,false未关注")
    private boolean pre_concert;
    @ApiModelProperty(value = "前序航班关注记录ID")
    private String pre_concertId;
    @ApiModelProperty(value = "前序航班-飞机机型")
    private String pre_planeModel;
    @ApiModelProperty(value = "前序航班-飞机机型名称")
    private String pre_planeModelName;
    @ApiModelProperty(value = "前序航班-是否跨天")
    private int pre_etTimeOver;
    @ApiModelProperty(value = "前序航班是否展示关注按钮")
    private boolean pre_showAttentionButton;
    private String airPortInfo;
    @ApiModelProperty(value = "备降地")
    private String divert_airport;
    @ApiModelProperty(value = "备降地机场名称")
    private String divert_airPortName;
    @ApiModelProperty(value = "备降地-天气")
    private String divert_weather;
    @ApiModelProperty(value = "备降地-温度")
    private String divert_temp;
    @ApiModelProperty(value = "备降地-PM2.5指数")
    private String divert_pm;
    @ApiModelProperty(value = "备降地-预计起飞时间")
    private String divert_etd;
    @ApiModelProperty(value = "备降地-实际起飞时间")
    private String divert_atd;
    @ApiModelProperty(value = "备降地-预计降落时间")
    private String divert_eta;
    @ApiModelProperty(value = "备降地-实际降落时间")
    private String divert_ata;
    @ApiModelProperty(value = "调整类型（为0计划取消）")
    private String adjust_type;
    @ApiModelProperty(value = "联程标识")
    private String is_interline;
    @ApiModelProperty(value = "经停站点三字码")
    private String stopover_station;
    @ApiModelProperty(value = "经停站-预计起飞时间")
    private String stopover_etd;
    @ApiModelProperty(value = "经停站-实际起飞时间")
    private String stopover_atd;
    @ApiModelProperty(value = "经停站-预计到达时间")
    private String stopover_eta;
    @ApiModelProperty(value = "经停站-实际到达时间")
    private String stopover_ata;
    @ApiModelProperty(value = "经停站中文名称")
    private String stopovert_airPortName;
    @ApiModelProperty(value = "经停站温度")
    private String stopovert_Temp;
    @ApiModelProperty(value = "经停站PM2.5指数")
    private String stopovert_pm;
    @ApiModelProperty(value = "经停站天气")
    private String stopovert_weather;
    @ApiModelProperty(value = "经停站行李转盘")
    private String stopovert_baggageID;
    @ApiModelProperty(value = "经停站值机柜台")
    private String stopovert_checkinTable;
    @ApiModelProperty(value = "经停站登机口")
    private String stopovert_boardGate;
    @ApiModelProperty(value = "备降点机场1")
    private String divert_airport1;
    @ApiModelProperty(value = "备降点天气1")
    private String divert_weather1;
    @ApiModelProperty(value = "备降点温度1")
    private String divert_temp1;
    @ApiModelProperty(value = "备降点PM2.5指数1")
    private String divert_pm1;
    @ApiModelProperty(value = "备降点名称1")
    private String divert_airPortName1;
    @ApiModelProperty(value = "备降点实际到达1")
    private String divert_ata1;
    @ApiModelProperty(value = "备降点预计到达1")
    private String divert_eta1;
    @ApiModelProperty(value = "备降点预计出发1")
    private String divert_etd1;
    @ApiModelProperty(value = "备降点实际出发1")
    private String divert_atd1;
    @ApiModelProperty(value = "备降点机场2")
    private String divert_airport2;
    @ApiModelProperty(value = "备降点天气2")
    private String divert_weather2;
    @ApiModelProperty(value = "备降点温度2")
    private String divert_temp2;
    @ApiModelProperty(value = "备降点PM2.5指数2")
    private String divert_pm2;
    @ApiModelProperty(value = "备降点名称2")
    private String divert_airPortName2;
    @ApiModelProperty(value = "备降点实际到达2")
    private String divert_ata2;
    @ApiModelProperty(value = "备降点预计到达2")
    private String divert_eta2;
    @ApiModelProperty(value = "备降点预计出发2")
    private String divert_etd2;
    @ApiModelProperty(value = "备降点实际出发2")
    private String divert_atd2;
    private String deptWeatherPic;
    private String destWeatherPic;
    private String stopovertWeatherPic;
    private String bridge;
    //到达口
    private String reachexit;
    @ApiModelProperty(value = "到达机位  进港靠桥")
    private String arr_bridge;
    private int depTimeZone;
    private int arrTimeZone;
    private int stopTimeZone;
    @ApiModelProperty(value = "是否关注",notes = "true关注,false未关注")
    private boolean concert;
    @ApiModelProperty(value = "关注记录ID")
    private String concertId;
    @ApiModelProperty(value = "飞行时长")
    private String flyTimeLength;
    @ApiModelProperty(value = "经停时长")
    private String stopTimeLength;
    //预计跨天
    private int etTimeOver;
    //免费行李额
    private String  baggageAllowance;
    //是否展示电子登机牌
    private boolean showBoardingPass;

    //更改时间
    private String another_etd;

    //是否展示关注按钮
    @ApiModelProperty(value = "是否展示关注按钮")
    private boolean showAttentionButton;

    @ApiModelProperty(value = "航班状态枚举值", notes = "用于存储原始的航班状态值，前端以此处理逻辑")
    private String flight_status_enum;

    @Override
    public int compareTo(FlightInfoDynamicDto o) {
        String flightDateAll = this.std;
        String oflightDateAll = o.std;
        if(StringUtils.isEmpty(flightDateAll)){
            return -1;
        }
        if(StringUtils.isEmpty(oflightDateAll)){
            return 1;
        }
        return flightDateAll.compareTo(oflightDateAll);
    }

    /**
     * 判断当前航班是否显示关注按钮
     */
    public void checkShowAttendButton(){
        Date flightDate = DateUtil.toDate(this.flight_date, DateUtil.YYYY_MM_DD_PATTERN);
        Date curDate = DateUtil.toDate(DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN);
        // 未来的航班且未关注过显示关注按钮，否则不显示
        boolean attentionButton;
        if (flightDate.getTime() >= curDate.getTime()) {
            attentionButton = true;
        } else {
            attentionButton = false;
        }
        this.showAttentionButton= attentionButton;
    }

    /**
     * 判断前序航班是否显示关注按钮
     */
    public void checkPreShowAttendButton(){
        // 如果前序航班信息不完整，不显示关注按钮
        if (StringUtils.isBlank(this.pre_flight_no) || StringUtils.isBlank(this.pre_departure_airport)
            || StringUtils.isBlank(this.pre_arrival_airport)) {
            this.pre_showAttentionButton = false;
            return;
        }

        // 使用前序航班的航班日期来判断
        String preFlightDate = this.flight_date; // 前序航班通常与主航班在同一天或前一天
        if (StringUtils.isNotBlank(preFlightDate)) {
            Date flightDate = DateUtil.toDate(preFlightDate, DateUtil.YYYY_MM_DD_PATTERN);
            Date curDate = DateUtil.toDate(DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN);
            // 未来的航班显示关注按钮，否则不显示
            if (flightDate.getTime() >= curDate.getTime()) {
                this.pre_showAttentionButton = true;
            } else {
                this.pre_showAttentionButton = false;
            }
        } else {
            this.pre_showAttentionButton = false;
        }
    }
}
