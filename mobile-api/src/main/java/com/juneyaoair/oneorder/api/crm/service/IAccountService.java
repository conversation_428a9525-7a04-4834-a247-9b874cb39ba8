package com.juneyaoair.oneorder.api.crm.service;

import com.juneyaoair.oneorder.api.crm.dto.*;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.crm.dto.response.CrmMemberBaseApiResponse;

/**
 * <AUTHOR>
 * @description 会员注册服务
 * @date 2024/10/22 16:00
 */
public interface IAccountService {
    /**
     * 会员多语言注册
     * @param crmRequestDto
     * @return
     */
    MultiLanguageRegisterAccountResultDto register(CrmRequestDto<MultiLanguageRegisterAccountReqDto> crmRequestDto);
    /**
     * @description 会员账户密码登录验证
     * <AUTHOR>
     * @date 2024/11/4 10:33
     * @param crmRequestDto
     * @return LoginPasswordCheckResultDto
     **/
    LoginPasswordCheckResultDto loginPasswordCheck(CrmRequestDto<LoginPasswordCheckReqDto> crmRequestDto);
    /**
     * @description 根据邮箱，手机号查询账户信息
     * <AUTHOR>
     * @date 2024/11/4 13:16
     * @param crmRequestDto
     * @return QueryMemberAccountResultDto
     **/
    QueryCardNoByDataTypeResultDto queryCardNoByDataType(CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto);
    /**
     * @description 会员登录密码重置
     * <AUTHOR>
     * @date 2024/11/4 13:51
     * @param crmRequestDto
     * @return QueryMemberAccountResultDto
     **/
    ResetLoginPasswordResultDto resetLoginPasswordV2(CrmRequestDto<ResetLoginPasswordReqDto> crmRequestDto);
    /**
     * @description 根据会员账户查询信息  返回值目前不全，根据实际情况补充
     * 参考文档 http://***********:8014/swagger-ui.html#!/query45controller/queryAccountByMemberIdUsingPOST
     * <AUTHOR>
     * @date 2024/11/7 9:31
     * @param bizDto
     * @param memberId 会员卡号
     * @return ResetLoginPasswordResultDto
     **/
    QueryAccountByMemberIdResultDto queryAccountByMemberId(BizDto bizDto,String memberId);


    /**
     * 根据卡号、证件、手机、邮箱查询账户信息
     * @param queryAccountInfoReqDtoCrmRequestDto
     * @return
     */
    QueryAccountInfoResDto queryAccountInfo(CrmRequestDto<QueryAccountInfoReqDto> queryAccountInfoReqDtoCrmRequestDto);
}
