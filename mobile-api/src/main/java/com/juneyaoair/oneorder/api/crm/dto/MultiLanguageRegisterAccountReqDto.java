package com.juneyaoair.oneorder.api.crm.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 会员注册类
 * @date 2024/10/22 16:40
 */
@Data
public class MultiLanguageRegisterAccountReqDto {
    /**
     * 出生日期
     */
    private String Birthday;
    /**
     * 证件号
     */
    private String CertificateNumber;
    /**
     * 证件类型(不支持身份证)
     */
    private String CertificateType;
    /**
     * 会员英文名
     */
    private String EfirstName;
    /**
     * 会员英文姓
     */
    private String ElastName;
    /**
     * 邮箱
     */
    private String Email;
    /**
     * 语言类型 (EN_US英文 JA_JP-日文..
     */
    private String LanguageType;
    /**
     * 手机号-格式：国际区号-手机号 如：852-**)
     */
    private String Mobile;
    /**
     * 国籍
     */
    private String Nationality;
    /**
     * 登录密码
     */
    private String Password;
    /**
     * 尊称代码(1-先生 2-女士 3-小姐 4-夫人)
     */
    private int SalutationCode;

    /**
     * 签发国家/地区
     */
    private String SigningAuthority;

    /**
     * 证件有效日期
     */
    private String ValidDate;
}
