package com.juneyaoair.oneorder.api.crm.constant;

/**
 * <AUTHOR>
 * @description crm系统常用请求地址
 * @date 2023/5/31 13:42
 */
public interface CrmUrlConstant {
    /**
     * 会员退出登录
     */
    String MEMBER_LOGIN_OUT = "/Member/Logout";

    /**
     * 发送验证码
     */
    String CAPTCHA_SEND = "/Captcha/Send";

    /**
     * <AUTHOR>
     * @Description 会员详情
     * @Date 9:17 2023/6/20
     **/
    String MEMBER_MEMBERDETAIL = "/Member/MemberDetail";

    /*
     * <AUTHOR>
     * @Description 积分账户查询(.net版)
     * @Date 9:30 2023/6/20
     **/
    String MILEAGE_ACCOUNTQUERY = "/Mileage/AccountQuery";

    /** 积分账户查询（java版） */
    String MILEAGE_ACCOUNT_QUERY = "/mileage/accountQuery";

    /**
     * <AUTHOR>
     * @Description 会员航段查询
     * @Date 10:52 2023/6/20
     **/
    String SEGMENT_QUERY= "/mileageDetail/currentSegmentQuery";

    /**
     * <AUTHOR>
     * @Description 会员星级查询
     * @Date 15:30 2023/6/20
     **/
    String MEMBER_STAR_QUERY = "/memberStar/memberStarQuery";

    /**
     * <AUTHOR>
     * @Description 学生认证 获取验证结果详情
     * @Date 10:59 2023/6/21
     **/
    String STUDENT_AUTH_DETAIL_QUERY= "/getStudentCertificateValidateResult";

    /**
     * <AUTHOR>
     * @Description 企业会员信息查询
     * @Date 11:10 2023/6/21
     **/
    String COMPANY_MEMBER_QUERYINFO= "/companyMember/queryInfo";

    /** 积分交易明细查询 */
    String MILEAGE_DETAIL_QUERY = "/mileageDetail/transactionDetailQuery";

    /** 累计航段明细查询 */
    String SEGMENT_DETAIL_QUERY = "/mileageDetail/currentSegmentQuery";

    /** 会员支付密码验证 */
    String CRM_ACCOUNT_PAY_PASSWORD = "/payPassword";

    /** 积分补登明细查询 */
    String RETRO_DETAIL_QUERY = "/mileage/mileageRetroRecordQuery";

    /**
     * <AUTHOR>
     * @Description CRM会员标签查询
     * @Date 11:10 2023/6/21
     **/
    String MEMBER_TAG_QUERY = "/memberTag/query";

    /**
     * <AUTHOR>
     * @Description 会员星级规则查询
     * @Date 11:10 2023/6/25
     **/
    String QUERY_MEMBER_STAR_RULE = "/memberStar/queryMemberStarRule";

    /**
     * <AUTHOR>
     * @Description 会员证件查询
     * @Date 11:10 2023/6/25
     **/
    String MEMBER_CERTIFICATE_QUERY = "/memberCertificate/query";

    String COMPENSATE_SCORE_URI = "/mileage/mileageRetro";

    /**
     * <AUTHOR>
     * @Description 支付宝实名认证接口
     * @Date 14:47 2023/9/20
     **/
    String ALIPAY_AUTH_URL = "/Member/AlipayRealName";


    //积分到期记录查询
    String QUERY_EXPIRING_SCORE = "/mileageValidity/query";

    //常用乘机人2.0
    String QUERY_COMMON_PERSON_V20="/QueryGeneralContact";//查询常用乘机人

    String BENEFICIARY_QUERY = "/beneficiary/queryByCriteria"; //查询受益人（草稿-M，激活-A，删除-D）  2020-07-30

    String COMMON_CONTACT_QUERY= "/linkman/queryAll"; //查询常用联系人  2020-05-12

    String COMMON_CONTACT_SUBMIT= "/linkman/insert"; //添加常用联系人  2020-05-12

    String COMMON_CONTACT_UPDATE= "/linkman/updateByPri"; //修改常用联系人  2020-05-12

    String COMMON_CONTACT_DELETE= "/linkman/deleteByPri"; //删除单个常用联系人  2020-05-12

    String BENEFICIARY_QUERY_BY_MEMBERID = "/beneficiary/queryByMemberId"; // 通过会员id查询各类别数量 2020-07-31

    String BENEFICIARY_ADD = "/beneficiary/add"; //受益人新增  2020-07-31

    String BENEFICIARY_ACTIVE = "/beneficiary/active"; //受益人激活  2020-07-31

    String BENEFICIARY_MODIFY = "/beneficiary/update"; //草稿受益人修改 2020-07-31

    String BENEFICIARY_DELETE = "/beneficiary/delete"; //受益人删除  2020-07-31

    String MEMBER_CUSTOMERINFO_MODIFY = "/Member/ModifyCustomerInfo";   //修改客户信息

    String MEMBER_APPLY_MEMBERKEYINFO = "/MemberKeyInfoAdjust/Apply";//提交会员修改审批

    String MERBER_CONSUM_ADDRESS_ADD = "/MemberAddress/AddMemberAddress";//新增会员地址信息信息

    String MERBER_CONSUM_ADDRESS_MODIFY = "/MemberAddress/ModifyMemberAddress";//修改会员地址信息

    String MERBER_CONSUM_CERTIFICATE_DELETE = "/MemberCertificate/DeleteMemberCertificate";//删除会员证件

    String MEMBER_CERTIFICATE_ADD = "/memberCertificate/add";

    String MEMBER_CERTIFICATE_MODIFY = "/memberCertificate/modify";

    String MEMBER_ADD_CONTACTINFO = "/MemberContact/AddMemberContact";  //添加会员联系信息

    String MERBER_UPDATE_CONTACTINFO = "/MemberContact/ModifyMemberContact";//修改会员联系信息

    String MERBER_MODIFY_CONTACT_INFO = "/memberContact/modify";//修改会员联系信息(新版)

    String MERBER_DELETE_CONTACTINFO = "/MemberContact/DeleteMemberContact";//删除会员联系信息

    String MEMBER_LOGIN_PASSWORD_RESET = "/Member/ResetLoginPassword";   //重置会员登录密码

    String MEMBER_LOGIN_PASSWORD_MODIFY = "/Member/ModifyLoginPassword"; //修改会员登录密码

    String MEMBER_CONSUME_PASSWORD_SET = "/Member/SetConsumePassword";   //设置消费密码

    String MEMBER_CONSUME_PASSWORD_MODIFY = "/Member/ModifyConsumePassword";   //修改消费密码

   String MERBER_CONSUM_ADDRESS_DELETE = "/MemberAddress/DeleteMemberAddress";//删除会员地址信息信息

    String CRM_API_FUNC087 = "/crm/Func087";//会员升级规则查询

    String SEARCH_MEMBER_RIGHT_URL = "/flightbasic/memberCenter/searchMemberRight";//查询会员权益

    String MEMBER_RIGHTS_QUERY_URL = "/MemberRights/MemberRightsQuery";//会员权益查询URL

    String MEMBERADDRESS_ADD =  "/memberAddress/add";//2023 7 27 新版的新增会员地址信息信息
    String MEMBERADDRESS_DELETE =  "/memberAddress/delete";//2023 7 27 新版的删除会员地址信息信息
    String MEMBERADDRESS_MODIFY =  "/memberAddress/modify";//2023 7 27 新版的更改会员地址信息信息

    String BASEDATA_QUERY = "/baseDataQuery";//2023 7 27 新版查询基础信息（省市等行政区域）
    //会员注册
    String REGIST_ACCOUNT_BY_MULTILANGUAGE = "/registAccountByMultiLanguage";
    //会员登录密码验证
    String LOGIN_PASSWORD_CHECK = "/loginPasswordCheck";
    //会员账户查询
    String QUERY_CARD_NO_BY_DATATYPE = "/queryCardNoByDataType";
    //重置登录密码
    String RESET_LOGIN_PASSWORD_V2 = "/member/resetLoginPasswordV2";
    //会员详情查询
    String QUERY_ACCOUNT_BY_MEMBER_ID = "/queryAccountByMemberId";
    // 查询会员私域信息
    String GET_MEMBER_PRIVATE_DOMAIN = "/activity/privateDomain/getMemberPrivateDomain";

    //联合会员报名
    String CTRIP_UNION_MEMBER_APPLY = "/activity/ctripUnionMemberApply";

    //联合会员详情查询
    String CTRIP_UNION_MEMBER_QUERY = "/activity/ctripUnionMemberQuery";

    //联合会员权益领取
    String CTRIP_UNION_MEMBER_RIGHTS_RECEIVE  ="/activity/ctripUnionMemberRightsReceive";

    //创建家庭账户
    String MEMBER_FAMILY_ACCOUNT_CREATE = "/memberFamilyAccount/create";
    //子账号接受邀请单
    String MEMBER_FAMILY_ACCOUNT_ACCEPT = "/memberFamilyAccount/invitationForm/accept";
    //审核邀请单
    String MEMBER_FAMILY_ACCOUNT_AUDIT = "/memberFamilyAccount/invitationForm/audit";
    //主账户创建邀请单
    String MEMBER_INVITATION_CREATE = "/memberFamilyAccount/invitationForm/create";
    //用于失效已超过30天的邀请单，若不存在未失效邀请单则抛异常提示(便于测试)
    String MEMBER_INVITATION_EXPIRE = "/memberFamilyAccount/invitationForm/expire";

     //主账户撤回邀请单
    String MEMBER_INVITATION_REVOKE = "/memberFamilyAccount/invitationForm/revoke";
    /**
     * 查询邀请单
     */
    String MEMBER_INVITATION_QUERY = "/memberFamilyAccount/invitationForm/query";

    /**
     * /memberFamilyAccount/invitationForm/reject
     * 子账号拒绝邀请
     */
    String MEMBER_INVITATION_REJECT= "/memberFamilyAccount/invitationForm/reject";

    /**
     * post /memberFamilyAccount/inviteDetailQuery
     * 支持查询指定家庭账户指定主账号和子账户的邀请单详情信息
     */
    String MEMBER_INVITEDETAIL_QUERY= "/memberFamilyAccount/inviteDetailQuery";

    /**
     * post /memberFamilyAccount/inviteRecordQuery
     */
    String MEMBER_INVITERECORD_QUERY= "/memberFamilyAccount/inviteRecordQuery";


    String QUERY_ACCOUNT_INFO=    "/queryAccountInfo";

    /**
     * /memberFamilyAccount/logout
     * 注销家庭账户
     */
    String MEMBER_INVITERECORD_LOGOUT= "/memberFamilyAccount/logout";


    /**
     * post /memberFamilyAccount/modifyName
     * 家庭账户名称修改
     */
    String MEMBER_INVITERECORD_MODIFYNAME= "/memberFamilyAccount/modifyName";


    /**
     *
     * 家庭账户详情查询
     */
    String MEMBER_FAMILY_ACCOUNT_QUERY_DETAIL= "/memberFamilyAccount/queryDetail";


    /**
     *post /memberFamilyAccount/relation/unbind
     * 家庭账户详情查询
     */
    String MEMBER_FAMILY_ACCOUNT_RELATION_DELAY= "/memberFamilyAccount/relation/delay";


    /**
     * post /memberFamilyAccount/relation/unbind
     */

    String MEMBER_FAMILY_ACCOUNT_RELATION_UNBIND= "/memberFamilyAccount/relation/unbind";


    String MEMBER_FAMILY_ACCOUNT_RIGHTS_GIVEVIPCARD= "/memberFamilyAccount/rights/giveVipCard";

    /**
     * 家庭账户赠卡详情
     */
    String MEMBER_FAMILY_ACCOUNT_RIGHTS_GIVEVIPCARD_DETAIL= "/memberFamilyAccount/rights/giveVipCardDetail";

    /**post /memberFamilyAccount/rights/giveVipCardRecordQuer
     * 家庭账户赠卡记录查询
     */
    String MEMBER_FAMILY_ACCOUNT_RIGHTS_GIVEVIPCARD_RECORD_QUERY= "/memberFamilyAccount/rights/giveVipCardRecordQuery";

    //post /memberFamilyAccount/add 添加家庭帐户会员
    String MEMBER_FAMILY_ACCOUNT_ADD = "/memberFamilyAccount/add";

    ///memberFamilyAccount/delete 删除家庭帐户会员
    String MEMBER_FAMILY_ACCOUNT_DELETE = "/memberFamilyAccount/delete";
}
