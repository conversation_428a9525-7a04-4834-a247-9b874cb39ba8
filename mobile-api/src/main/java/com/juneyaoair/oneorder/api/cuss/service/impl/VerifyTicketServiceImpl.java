package com.juneyaoair.oneorder.api.cuss.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.cuss.config.CussConfig;
import com.juneyaoair.oneorder.api.cuss.constant.CussConstant;
import com.juneyaoair.oneorder.api.cuss.dto.DetrMemberInfo;
import com.juneyaoair.oneorder.api.cuss.dto.TicketVerifyResponse;
import com.juneyaoair.oneorder.api.cuss.service.IVerifyTicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 客票验证服务
 * <AUTHOR>
 */
@Component
public class VerifyTicketServiceImpl extends HttpBaseServiceImpl implements IVerifyTicketService {

    @Autowired
    private CussConfig cussConfig;

    @Override
    public BaseResultDTO<TicketVerifyResponse> verifyTicket(BaseRequestDTO<DetrMemberInfo> req) {

        return invokeHttpClient(
                req, cussConfig.getCussUrl() + CussConstant.TICKET_VERIFY_PATH,
                new TypeToken<BaseResultDTO<TicketVerifyResponse>>() {
                }.getType()
        );

    }
}
