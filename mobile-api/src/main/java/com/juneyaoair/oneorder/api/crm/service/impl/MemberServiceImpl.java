package com.juneyaoair.oneorder.api.crm.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.request.crm.ModifyCustomerInfoRequest;
import com.juneyaoair.flightbasic.request.crm.PtResetLoginPasswordRequest;
import com.juneyaoair.flightbasic.request.memberCenter.ParamMemberRightsDTO;
import com.juneyaoair.flightbasic.response.crm.AddMemberAddressResponse;
import com.juneyaoair.flightbasic.response.memberCenter.MemberLevelDTO;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmResultEnum;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.api.crm.constant.VoucherTypesEnum;
import com.juneyaoair.oneorder.api.crm.dto.MemberAccountInfo;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.crm.utils.CrmUtil;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.api.order.constant.OrderUrlConstant;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.constant.CouponStateEnum;
import com.juneyaoair.oneorder.constant.EnumVoucherStates;
import com.juneyaoair.oneorder.constant.OrderCouponStateEnum;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.address.*;
import com.juneyaoair.oneorder.crm.dto.basedata.BaseDataQueryReqDto;
import com.juneyaoair.oneorder.crm.dto.basedata.BaseDataQueryResDto;
import com.juneyaoair.oneorder.crm.dto.common.*;
import com.juneyaoair.oneorder.crm.dto.request.*;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.crm.enums.MileageAccountRequestItemsEnum;
import com.juneyaoair.oneorder.mapstruct.ConvertMemberRightMapping;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.dto.*;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.*;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName MemberServiceImpl
 * @Description 会员服务实现类
 * <AUTHOR>
 * @Date 2023/6/20 9:01
 * @Version 1.0
 */
@Service
@Slf4j
public class MemberServiceImpl extends HttpBaseServiceImpl implements IMemberService {
    @Autowired
    private CrmConfig crmConfig;

    @Autowired
    private OrderConfig orderConfig;

    @Autowired
    private RedisUtil redisUtils;
    @Autowired
    private RedisConstantConfig redisConstantConfig;

    @Autowired
    private CommonService commonService;

    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;

    @Override
    public PtMemberDetail memberDetail(String channelCode, String ffpCardNo, String[] items) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = this.memberDetail(ptApiRequest, false);
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            throw MultiLangServiceException.fail("会员基本信息查询出错");
        }
        return ptCRMResponse.getData();
    }

    @Override
    public PtCRMResponse<PtMemberDetail> memberDetail(PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest, boolean useCache) {
        PtCRMResponse<PtMemberDetail> ptCRMResponse;
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_INFO + ptApiCRMRequest.getData().getCardNO());
        if (useCache) {
            String memberDetailStr = (String) redisUtils.get(key);
            if (StringUtils.isNotEmpty(memberDetailStr)) {
                Type type = new TypeToken<PtCRMResponse<PtMemberDetail>>() {
                }.getType();
                ptCRMResponse = HoAirGsonUtil.fromJson(memberDetailStr, type);
                return ptCRMResponse;
            }
        }
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_MEMBERDETAIL;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        Type type = new TypeToken<PtCRMResponse<PtMemberDetail>>() {
        }.getType();
        ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCRMResponse == null) {
            throw ServiceException.fail("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        } else {
            redisUtils.set(key, result, 10 * 60L);
        }
        return ptCRMResponse;
    }

    @Override
    public MemberRemainScoreResp queryMemberRemainScore(PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest, boolean useCache) {
        MemberRemainScoreResp memberRemainScoreResp = new MemberRemainScoreResp();
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_SCORE + ptApiCRMRequest.getData().getMemberCardNo());
        if (useCache) {
            String memberDetailStr = (String) redisUtils.get(key);
            if (StringUtils.isNotEmpty(memberDetailStr)) {
                Type type = new TypeToken<MemberRemainScoreResp>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberDetailStr, type);
            }
        }
        PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse = this.mileageAccountQuery(ptApiCRMRequest, false);
        if (ptCRMResponse.getCode() == 0) {
            MileageTotalBillSoaModel mtbs = ptCRMResponse.getData().getTotalBill();
            memberRemainScoreResp.setStatusCode(CrmResultEnum.SUC000.getResultCode());
            int availableMiles = mtbs.getAvailableMiles() == null ? 0 : mtbs.getAvailableMiles();
            int freezePoint = mtbs.getFreezeMiles() == null ? 0 : mtbs.getFreezeMiles();
            memberRemainScoreResp.setPoint(String.valueOf(availableMiles + freezePoint));
            memberRemainScoreResp.setFreezePoint(String.valueOf(freezePoint));
            redisUtils.set(key, JSON.toJSONString(ptCRMResponse), 86400L);//缓存一天
        } else {
            memberRemainScoreResp.setStatusCode(CrmResultEnum.EXCEPTION.getResultCode());
        }
        return memberRemainScoreResp;
    }

    @Override
    @Deprecated
    public PtCRMResponse<MileageAccountQueryResponse> mileageAccountQuery(PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest, boolean useCache) {
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_MILEAGE + ptApiCRMRequest.getData().getMemberCardNo());
        if (useCache) {
            String mileageAccountStr = (String) redisUtils.get(key);
            if (StringUtils.isNotEmpty(mileageAccountStr)) {
                Type type = new TypeToken<PtCRMResponse<MileageAccountQueryResponse>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(mileageAccountStr, type);
            }
        }
        PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse;
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MILEAGE_ACCOUNTQUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        Type type = new TypeToken<PtCRMResponse<MileageAccountQueryResponse>>() {
        }.getType();
        ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCRMResponse == null) {
            throw ServiceException.fail("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        } else {
            redisUtils.set(key, JSON.toJSONString(ptCRMResponse), 60L);
        }
        return ptCRMResponse;
    }

    @Override
    public MileageAccountQueryResponse mileageAccountQuery(PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.MILEAGE_ACCOUNT_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        Type type = new TypeToken<PtCRMResponse<MileageAccountQueryResponse>>() {
        }.getType();
        PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCRMResponse == null) {
            throw MultiLangServiceException.fail("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode()) {
            throw MultiLangServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse.getData();
    }

    @Override
    public MileageTotalBillSoaModel mileageTotalBillSoaModel(String channelCode, String ip, String ffpCardNo) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest = CRMReqUtil.buildCommReqNoToken(ip, channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
        mileageAccountQueryRequest.setMemberCardNo(ffpCardNo);
        mileageAccountQueryRequest.setRequestItems(new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName});
        ptApiCRMRequest.setData(mileageAccountQueryRequest);
        MileageAccountQueryResponse mileageAccountQueryResponse = mileageAccountQuery(ptApiCRMRequest);
        return mileageAccountQueryResponse.getTotalBill();
    }

    @Override
    public PtCrmMileageResponse<MemberSegmentResponse> queryMemberSegment(String memberId, String ip, String channelCode, boolean useCache) {
        String memberSegment;
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_SEGMENT + memberId);
        if (useCache) {
            memberSegment = (String) redisUtils.get(key);
            if (StringUtils.isNotBlank(memberSegment)) {
                Type type = new TypeToken<PtCrmMileageResponse<MemberSegmentResponse>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberSegment, type);
            }
        }
        PtCrmMileageResponse<MemberSegmentResponse> ptCrmMileageResponse;
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        PtCrmMileageRequest<MemberSegmentRequest> ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(ip, channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MemberSegmentRequest memberSegmentRequest = new MemberSegmentRequest();
        memberSegmentRequest.setMemberId(Integer.parseInt(memberId));
        ptCrmMileageRequest.setData(memberSegmentRequest);
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.SEGMENT_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<PtCrmMileageResponse<MemberSegmentResponse>>() {
        }.getType();
        ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCrmMileageResponse == null) {
            throw ServiceException.fail("数据转换异常");
        }
        if (0 != ptCrmMileageResponse.getCode()) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        if (null != ptCrmMileageResponse.getData()
                && CollectionUtils.isNotEmpty(ptCrmMileageResponse.getData().getSegments())) {
            redisUtils.set(key, JSON.toJSONString(ptCrmMileageResponse), 86400L);//暂时存放一天
        }
        return ptCrmMileageResponse;
    }

    @Override
    public PtCRMResponse<MemberStarQueryResp> queryMemberStar(PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequest, boolean useCache) {
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_STAR + ptApiCRMRequest.getData().getId());
        if (useCache) {
            String memberStarStr = (String) redisUtils.get(key);
            if (StringUtils.isNotBlank(memberStarStr)) {
                Type type = new TypeToken<PtCRMResponse<MemberStarQueryResp>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberStarStr, type);
            }
        }
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBER_STAR_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        Type type = new TypeToken<PtCRMResponse<MemberStarQueryResp>>() {
        }.getType();
        PtCRMResponse<MemberStarQueryResp> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        } else {
            redisUtils.set(key, JSON.toJSONString(ptCRMResponse), 3L);//暂时存放一天
        }
        return ptCRMResponse;
    }

    @Override
    public CrmMemberBaseApiResponse<MemberTagQueryResDto> queryMemberTag(CrmMemberBaseApiRequest<MemberTagQueryReqDto> memberTagQueryReqDto, boolean useCache) {
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_TAG + memberTagQueryReqDto.getData().getId());
        if (useCache) {
            String memberTagStr = (String) redisUtils.get(key);
            if (StringUtils.isNotBlank(memberTagStr)) {
                Type type = new TypeToken<CrmMemberBaseApiResponse<MemberTagQueryResDto>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberTagStr, type);
            }
        }
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBER_TAG_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(memberTagQueryReqDto,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<MemberTagQueryResDto>>() {
        }.getType();
        CrmMemberBaseApiResponse<MemberTagQueryResDto> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("数据转换异常");
        }
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getDesc());
        } else {
            redisUtils.set(key, JSON.toJSONString(crmMemberBaseApiResponse), 86400L);//暂时存放一天
        }
        return crmMemberBaseApiResponse;
    }

    @Override
    public PtCRMResponse<MemberStarQueryRulePage> queryMemberStarRule(PtApiCRMRequest<MemberStarQueryRuleReq> memberStarQueryRuleReq, boolean useCache) {

        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_RULE + memberStarQueryRuleReq.getData().getMemberLevelCode());
        if (useCache) {
            String memberLevelStr = (String) redisUtils.get(key);
            if (StringUtils.isNotBlank(memberLevelStr)) {
                Type type = new TypeToken<PtCRMResponse<MemberStarQueryRulePage>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberLevelStr, type);
            }
        }
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.QUERY_MEMBER_STAR_RULE;
        HttpResult httpResult = HttpUtil.doPostClient(memberStarQueryRuleReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<PtCRMResponse<MemberStarQueryRulePage>>() {
        }.getType();
        PtCRMResponse<MemberStarQueryRulePage> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("[查询用户星级规则请求发生错误]");
        }
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getMsg());
        } else {
            redisUtils.set(key, JSON.toJSONString(crmMemberBaseApiResponse), 86400L);//暂时存放一天
        }
        return crmMemberBaseApiResponse;
    }

    @Override
    public CrmMemberBaseApiResponse<QueryMemberCertificateResDto> toCatchMemberCertList(CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertificateRequest, boolean useCache) {
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_CENTER_MEMBER_CERT + queryCertificateRequest.getData().getMemberId());
        if (useCache) {
            String memberCertStr = (String) redisUtils.get(key);
            if (StringUtils.isNotBlank(memberCertStr)) {
                Type type = new TypeToken<CrmMemberBaseApiResponse<QueryMemberCertificateResDto>>() {
                }.getType();
                return HoAirGsonUtil.fromJson(memberCertStr, type);
            }
        }
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBER_CERTIFICATE_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(queryCertificateRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("网络异常");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<QueryMemberCertificateResDto>>() {
        }.getType();
        CrmMemberBaseApiResponse<QueryMemberCertificateResDto> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("[查询用户证件信息发生错误]");
        }
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getDesc());
        } else {
            redisUtils.set(key, JSON.toJSONString(crmMemberBaseApiResponse), 86400L);//暂时存放一天
        }
        return crmMemberBaseApiResponse;
    }

    @Override
    @SuppressWarnings("rawtypes")
    public void toCompensateScore(CrmMemberBaseApiRequest<PtScoreCompensateReq> compensateReq) {
        CrmMemberBaseApiResponse crmMemberBaseApiResponse;
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.COMPENSATE_SCORE_URI;
        HttpResult httpResult = HttpUtil.doPostClient(compensateReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("网络异常");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<QueryMemberCertificateResDto>>() {
        }.getType();
        crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw MultiLangServiceException.fail("[积分补登发生错误]");
        }
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw MultiLangServiceException.fail(crmMemberBaseApiResponse.getDesc());
        }
    }

    @Override
    public MileageRetroRecordQueryResDto toCatchRetroRecords(String ffpId, String channelNo) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        PtCrmMileageRequest<MileageRetroRecordQueryReqDto> ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MileageRetroRecordQueryReqDto memberRetroRecordQueryReq = new MileageRetroRecordQueryReqDto();
        memberRetroRecordQueryReq.setMemberId(Integer.valueOf(ffpId));
        ptCrmMileageRequest.setData(memberRetroRecordQueryReq);
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.RETRO_DETAIL_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("积分补登明细查询出错，请稍后再试！");
        }
        CrmMemberBaseApiResponse<MileageRetroRecordQueryResDto> ptCRMResponse = JSON.parseObject(result, new TypeToken<CrmMemberBaseApiResponse<MileageRetroRecordQueryResDto>>() {
        }.getType());
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getDesc());
        }
        return ptCRMResponse.getData();
    }

    @Override
    public MileageEffectiveResDto toCatchExpiringScoreV2(PtCrmMileageRequest<MileageEffectiveReqDto> mileageExpireActivityQueryReqDto) {
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.QUERY_EXPIRING_SCORE;
        HttpResult httpResult = HttpUtil.doPostClient(mileageExpireActivityQueryReqDto,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("网络异常");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<MileageEffectiveResDto>>() {
        }.getType();
        CrmMemberBaseApiResponse<MileageEffectiveResDto> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("[查询即将失效积分发生错误]");
        }
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getDesc());
        }
        return crmMemberBaseApiResponse.getData();
    }

    @Override
    public QueryGeneralContactResponse toCatchGeneralContacts(QueryGeneralContactRequest queryGeneralContactRequest) {
        String url = orderConfig.URL_FARE_API_BOOK + CrmUrlConstant.QUERY_COMMON_PERSON_V20;
        HttpResult httpResult = HttpUtil.doPostClient(queryGeneralContactRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("网络异常");
        }
        Type type = new TypeToken<QueryGeneralContactResponse>() {
        }.getType();
        QueryGeneralContactResponse queryGeneralContactResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == queryGeneralContactResponse) {
            throw ServiceException.fail("[查询常用乘机人发生错误]");
        }
        if ("90009".equals(queryGeneralContactResponse.getResultCode())) {
            return null;
        }
        if (!"1001".equals(queryGeneralContactResponse.getResultCode())) {
            throw ServiceException.fail(queryGeneralContactResponse.getErrorInfo());
        }
        return queryGeneralContactResponse;
    }

    public List<GeneralContactInfo> toCatchGeneralContactList(ChannelInfo channelInfo,String ffpId,String ffpCardNo) {
        QueryGeneralContactRequest queryGeneralContactRequest = new QueryGeneralContactRequest("V1.0", channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), "CRM", "LastBooking");
        queryGeneralContactRequest.setChannelCustomerNo(ffpId);
        queryGeneralContactRequest.setIsGMJC("N");
        QueryGeneralContactResponse queryGeneralContactResponse;
        String url = orderConfig.URL_FARE_API_BOOK + CrmUrlConstant.QUERY_COMMON_PERSON_V20;
        HttpResult httpResult = HttpUtil.doPostClient(queryGeneralContactRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("网络异常");
        }
        Type type = new TypeToken<QueryGeneralContactResponse>() {
        }.getType();
        queryGeneralContactResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (null == queryGeneralContactResponse) {
            throw MultiLangServiceException.fail("[查询常用乘机人发生错误]");
        }
        if ("90009".equals(queryGeneralContactResponse.getResultCode())) {
            return new ArrayList<>();
        }
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(queryGeneralContactResponse.getResultCode())) {
            throw MultiLangServiceException.fail(queryGeneralContactResponse.getErrorInfo());
        }
        return queryGeneralContactResponse.getGeneralContactList();
    }

    @Override
    public List<MemberBeneficiaryDTO> listEffectiveBeneficiaryInfoRecord(PtCrmMileageRequest ptCrmMileageRequest, String ip) {
        List<MemberBeneficiaryDTO> beneficiaryDTOList = listBeneficiaryInfoRecord(ptCrmMileageRequest, ip);
        // 筛选生效受益人
        if (null != beneficiaryDTOList) {
            beneficiaryDTOList = beneficiaryDTOList.stream().filter(beneficiary -> {
                Date effectiveDate = DateUtil.toDate(beneficiary.getEffectDate());
                return null != effectiveDate && effectiveDate.before(new Date());
            }).collect(Collectors.toList());
        } else {
            beneficiaryDTOList = Lists.newArrayList();
        }
        return beneficiaryDTOList;
    }

    @Override
    public List<MemberBeneficiaryDTO> listBeneficiaryInfoRecord(PtCrmMileageRequest ptCrmMileageRequest, String ip) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("服务器网络错误，查询受益人记录失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<List<MemberBeneficiaryDTO>>>() {
        }.getType();
        PtCrmMileageResponse<List<MemberBeneficiaryDTO>> responseDto = HoAirGsonUtil.fromJson(result, type);
        if (null == responseDto) {
            throw MultiLangServiceException.fail("[服务器网络错误，查询受益人记录失败]");
        }
        if (0 != responseDto.getCode()) {
            throw MultiLangServiceException.fail(responseDto.getDesc());
        }
        return responseDto.getData();
    }

    @Override
    public List<CommonContactsInfo> toCatchCommonContacts(String ffpCard, String ip, String channelCode, String channelPwd) {
        //创建查询常用联系人请求对象，调用查询常用联系人接口
        PtCrmMileageRequest<String> ptCrmMileageRequest = buildCommCrmReq(ip, channelCode, channelPwd);
        ptCrmMileageRequest.setData(ffpCard);
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.COMMON_CONTACT_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，查询常用联系人失败");
        }
        List<CommonContactsInfo> commonContactsInfoList = new ArrayList<>();

        Type type = new TypeToken<PtCrmMileageResponse<List<MemberCommonContactsInfo>>>() {
        }.getType();
        PtCrmMileageResponse<List<MemberCommonContactsInfo>> ptCrmMileageResponse;
        ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，查询常用联系人失败]");
        }
        if (0 != ptCrmMileageResponse.getCode()) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }

        if (CollectionUtils.isNotEmpty(ptCrmMileageResponse.getData())) {
            ptCrmMileageResponse.getData().forEach(memberCommonContactsInfo -> {
                CommonContactsInfo commonContactsInfo = new CommonContactsInfo();
                commonContactsInfo.setRecordId(memberCommonContactsInfo.getRecordId());
                commonContactsInfo.setName(memberCommonContactsInfo.getNameCn());
                Map<String, String> splitMap = toSplit(memberCommonContactsInfo.getMobile());
                if (null != splitMap) {
                    commonContactsInfo.setTelephoneCode(splitMap.get("telephoneCode"));
                    commonContactsInfo.setMobile(splitMap.get("telephoneNum"));
                }
                commonContactsInfo.setEmail(memberCommonContactsInfo.getEmail());
                commonContactsInfoList.add(commonContactsInfo);
            });
        }
        return commonContactsInfoList;
    }

    /**
     * @param originalTelephoneNum 原始手机号（从CRM传过来的手机号）
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Description
     * @Date 15:25 2024/6/17
     **/
    private Map<String, String> toSplit(String originalTelephoneNum) {
        HashMap<String, String> splitMap;
        try {
            if (StringUtils.isEmpty(originalTelephoneNum)) {
                throw new ServiceException("未获取到原手机号");
            }
            splitMap = new HashMap<>();
            if (originalTelephoneNum.contains("-")) {
                String[] split = originalTelephoneNum.split("-");
                splitMap.put("telephoneCode", split[0]);
                splitMap.put("telephoneNum", split[1]);
            } else {
                splitMap.put("telephoneCode", "86");
                splitMap.put("telephoneNum", originalTelephoneNum);
            }
            return splitMap;
        } catch (Exception exception) {
            log.error("手机号解析出错，错误信息：", exception);
        }
        return null;
    }

    @Override
    public PtCrmMileageResponse<Integer> toAddCommonContacts(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.COMMON_CONTACT_SUBMIT;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，添加常用联系人失败");
        }
        PtCrmMileageResponse<Integer> ptCrmMileageResponse;
        Type type = new TypeToken<PtCrmMileageResponse<Integer>>() {
        }.getType();
        ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，添加常用联系人失败]");
        }

        if (ptCrmMileageResponse.getCode() == 999966) {
            throw ServiceException.fail("该手机号被其它联系人占用，请输入新号码");
        }

        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        return ptCrmMileageResponse;
    }

    @Override
    public PtCrmMileageResponse<Boolean> toModifyCommonContacts(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.COMMON_CONTACT_UPDATE;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，修改常用联系人失败");
        }
        PtCrmMileageResponse<Boolean> ptCrmMileageResponse;
        Type type = new TypeToken<PtCrmMileageResponse<Boolean>>() {
        }.getType();
        ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，修改常用联系人失败]");
        }
        if (999966 == ptCrmMileageResponse.getCode()) {
            throw ServiceException.fail("该手机号被其它联系人占用，请输入新号码");
        }

        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        return ptCrmMileageResponse;
    }

    @Override
    public PtCrmMileageResponse<Boolean> toDeleteCommonContacts(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.COMMON_CONTACT_DELETE;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，删除常用联系人失败");
        }
        PtCrmMileageResponse<Boolean> ptCrmMileageResponse;
        Type type = new TypeToken<PtCrmMileageResponse<Boolean>>() {
        }.getType();
        ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，删除常用联系人失败]");
        }

        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        return ptCrmMileageResponse;
    }

    @Override
    public PtCrmMileageResponse<Map<String, Integer>> toCountBeneficiaryNumByMemberId(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_QUERY_BY_MEMBERID;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，统计受益人数量失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<Map<String, Integer>>>() {
        }.getType();
        PtCrmMileageResponse<Map<String, Integer>> ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，统计受益人数量失败]");
        }
        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        return ptCrmMileageResponse;
    }

    @Override
    public Integer toAddBeneficiary(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_ADD;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，添加受益人失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<Integer>>() {
        }.getType();
        PtCrmMileageResponse<Integer> ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，添加受益人失败]");
        }
        if (999962 == ptCrmMileageResponse.getCode()) {
            throw ServiceException.fail("您提交的受益人证件，与现有受益人证件重复，请修改后再提交");
        }
        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
        return ptCrmMileageResponse.getData();
    }

    @Override
    public void toActiveBeneficiary(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_ACTIVE;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，激活受益人失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<Integer>>() {
        }.getType();
        PtCrmMileageResponse<Integer> ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，激活受益人失败]");
        }
        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
    }

    @Override
    public void toModifyBeneficiary(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，修改受益人失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<Integer>>() {
        }.getType();
        PtCrmMileageResponse<Integer> ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，修改受益人失败]");
        }
        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
    }

    @Override
    public void toDeleteBeneficiary(PtCrmMileageRequest ptCrmMileageRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BENEFICIARY_DELETE;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，删除受益人失败");
        }
        Type type = new TypeToken<PtCrmMileageResponse<Integer>>() {
        }.getType();
        PtCrmMileageResponse<Integer> ptCrmMileageResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCrmMileageResponse) {
            throw ServiceException.fail("[服务器网络错误，删除受益人失败]");
        }
        if (ptCrmMileageResponse.getCode() != 0) {
            throw ServiceException.fail(ptCrmMileageResponse.getDesc());
        }
    }

    @Override
    public void toSaveCusBasicInfo(PtApiCRMRequest<PtModifyCustomerInfoRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_CUSTOMERINFO_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，保存用户基本信息失败");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，保存用户基本信息失败]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
    }

    @Override
    public PtCRMResponse applyMemberKeyInfo(PtApiCRMRequest<MemberKeyInfoReq> ptApiRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_APPLY_MEMBERKEYINFO;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，会员关键信息（上传证件照）保存失败");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，会员关键信息（上传证件照）保存失败]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse modifyCustomerInfo(PtApiCRMRequest<ModifyCustomerInfoRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_CUSTOMERINFO_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，会员非关键信息保存失败");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，会员非关键信息保存失败]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse<AddMemberAddressResponse> addCustomerAddress(PtApiCRMRequest<MemberAddressAdd> ptApiRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MERBER_CONSUM_ADDRESS_ADD;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，会员添加地址信息请求发生错误");
        }
        Type type = new TypeToken<PtCRMResponse<AddMemberAddressResponse>>() {
        }.getType();
        PtCRMResponse<AddMemberAddressResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，会员添加地址信息请求发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse modifyCustomerAddress(PtApiCRMRequest<PtMemberAddressReq> ptApiRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MERBER_CONSUM_ADDRESS_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，会员修改地址信息请求发生错误");
        }
        Type type = new TypeToken<PtCRMResponse<AddMemberAddressResponse>>() {
        }.getType();
        PtCRMResponse<AddMemberAddressResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，会员修改地址信息请求发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse deleteCertificate(PtApiCRMRequest<PtMemberCertificateDelRequest> ptApiRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MERBER_CONSUM_CERTIFICATE_DELETE;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，删除证件信息发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，删除证件信息发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public CrmMemberBaseApiResponse<MemberCertificateResDto> addMemberCertificate(CrmMemberBaseApiRequest<MemberCertificateReqDto> addCertificateRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBER_CERTIFICATE_ADD;
        HttpResult httpResult = HttpUtil.doPostClient(addCertificateRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，添加证件信息发生错误");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<MemberCertificateResDto>>() {
        }.getType();
        CrmMemberBaseApiResponse<MemberCertificateResDto> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("[服务器网络错误，添加证件信息发生错误]");
        }
        return crmMemberBaseApiResponse;
    }

    @Override
    public CrmMemberBaseApiResponse<MemberCertificateResDto> modifyMemberCertificate(CrmMemberBaseApiRequest<ModifyMemberCertificateReqDto> modifyCertificateRequest) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBER_CERTIFICATE_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(modifyCertificateRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，修改证件信息发生错误");
        }
        Type type = new TypeToken<CrmMemberBaseApiResponse<MemberCertificateResDto>>() {
        }.getType();
        CrmMemberBaseApiResponse<MemberCertificateResDto> crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == crmMemberBaseApiResponse) {
            throw ServiceException.fail("服务器网络错误，修改证件信息发生错误");
        }
        if (crmMemberBaseApiResponse.getCode() == 127007) {
            throw ServiceException.fail("修改失败，该证件被其他用户添加，请联系客服");
        }
        if (crmMemberBaseApiResponse.getCode() != 0) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getDesc());
        }
        return crmMemberBaseApiResponse;
    }

    @Override
    @SuppressWarnings("rawtypes")
    public PtCRMResponse addContact(PtApiCRMRequest<PtContactInformationReq> req) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_ADD_CONTACTINFO;
        HttpResult httpResult = HttpUtil.doPostClient(req,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，添加联系方式发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，添加联系方式发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    @SuppressWarnings("rawtypes")
    public void toModifyMemberContactInfo(CrmMemberBaseApiRequest<ModifyMemberContactReqDto> request) {
        CrmMemberBaseApiResponse crmMemberBaseApiResponse;
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MERBER_MODIFY_CONTACT_INFO;
        HttpResult result = HttpUtil.doPostClient(request, url);
        if (StringUtils.isBlank(result.getResponse())) {
            throw ServiceException.fail("服务器网络错误，修改联系方式发生错误");
        }
        crmMemberBaseApiResponse = HoAirGsonUtil.fromJson(result.getResponse(), new TypeToken<CrmMemberBaseApiResponse>() {
        }.getType());
        if (crmMemberBaseApiResponse.getCode() == 134005) {
            throw ServiceException.fail("该手机号已被占用，请输入新号码");
        }
        if (crmMemberBaseApiResponse.getCode() != 0) {
            throw ServiceException.fail(crmMemberBaseApiResponse.getDesc());
        }
    }

    @Override
    public PtCRMResponse deleteContactInformation(PtApiCRMRequest<UnbingingEmailReq> req) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MERBER_DELETE_CONTACTINFO;
        HttpResult httpResult = HttpUtil.doPostClient(req,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，删除联系方式发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，删除联系方式发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse resetLoginPassword(PtApiCRMRequest<PtResetLoginPasswordRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_LOGIN_PASSWORD_RESET;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，设置登录密码发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，设置登录密码发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse modifyLoginPassword(PtApiCRMRequest<ModifyLoginPwd> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_LOGIN_PASSWORD_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，修改登录密码发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，修改登录密码发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse setConsumePassword(PtApiCRMRequest<PtSetConsumePasswordRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_CONSUME_PASSWORD_SET;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，设置消费密码发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，设置消费密码发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse modifyConsumePassword(PtApiCRMRequest<PtResetConsumerPasswordRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_CONSUME_PASSWORD_MODIFY;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，修改消费密码发生错误");
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，修改消费密码发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public AvailCouponsResponse toCatchCoupons(CouponQueryRequest couponQueryRequest) {
        String url = orderConfig.URL_FARE_API_COUPON + orderConfig.SUB_QUERY_COUPON;
        HttpResult httpResult = HttpUtil.doPostClient(couponQueryRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("权益服务请求异常");
        }
        Type type = new TypeToken<AvailCouponsResponse>() {
        }.getType();
        AvailCouponsResponse availCouponsResponse = HoAirGsonUtil.fromJson(result, type);
        if (availCouponsResponse == null) {
            throw MultiLangServiceException.fail("查询网络出错");
        }
        if (!"1001".equals(availCouponsResponse.getResultCode())) {
            throw MultiLangServiceException.fail(availCouponsResponse.getErrorInfo());
        }
        return availCouponsResponse;
    }

    @Override
    public PtCouponProductGetResponseDto toCatchRightCoupons(PtCouponProductGetRequestDto ptCouponProductGetRequestDto) {
        String url = orderConfig.URL_FARE_API_ORDER + orderConfig.COUPON_MY_PRODUCT_V2;
        HttpResult httpResult = HttpUtil.doPostClient(ptCouponProductGetRequestDto,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("请求异常");
        }
        Type type = new TypeToken<PtCouponProductGetResponseDto>() {
        }.getType();
        PtCouponProductGetResponseDto ptCouponProductGetResponseDto;
        ptCouponProductGetResponseDto = HoAirGsonUtil.fromJson(result, type);
        if (ptCouponProductGetResponseDto == null) {
            throw MultiLangServiceException.fail("未查询到权益券，如有疑问请联系95520");
        }
        if (!"1001".equals(ptCouponProductGetResponseDto.getResultCode())) {
            throw MultiLangServiceException.fail(ptCouponProductGetResponseDto.getErrorInfo());
        }
        return ptCouponProductGetResponseDto;
    }

    @Override
    public MemberLevelInfoResp toCatchUpRule(MemberUpGradeRequest memberUpGradeRequest) {
        //TODO 此接口需要替换
        String url = crmConfig.getCrmFuncUrl() + CrmUrlConstant.CRM_API_FUNC087;
        HttpResult httpResult = HttpUtil.doPostClient(memberUpGradeRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<MemberLevelInfoResp>() {
        }.getType();
        MemberLevelInfoResp memberLevelInfoResp;
        memberLevelInfoResp = HoAirGsonUtil.fromJson(result, type);
        if (memberLevelInfoResp == null) {
            throw ServiceException.fail("查询会员升级规则出错");
        }
        if (!"000".equals(memberLevelInfoResp.getStatusCode())) {
            throw ServiceException.fail(memberLevelInfoResp.getMessage());
        }
        return memberLevelInfoResp;
    }

    @Override
    public PtCRMResponse<MemberRightsQueryResponse> queryMemberRights(PtApiCRMRequest ptApiCRMRequest) {
        String url = crmConfig.getCrmMemberUrl() + CrmUrlConstant.MEMBER_RIGHTS_QUERY_URL;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，查询用户权益发生错误");
        }
        Type type = new TypeToken<PtCRMResponse<MemberRightsQueryResponse>>() {
        }.getType();
        PtCRMResponse<MemberRightsQueryResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse) {
            throw ServiceException.fail("[服务器网络错误，查询用户权益发生错误]");
        }
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public CouponActivityResp toCatchCoupons(CouponActivityReq couponActivityReq) {
        String url = orderConfig.URL_FARE_API_BOOK + OrderUrlConstant.SUB_QUERY_COUPON_ACTIVITY;
        HttpResult httpResult = HttpUtil.doPostClient(couponActivityReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，查询用户优惠券发生错误");
        }
        Type type = new TypeToken<CouponActivityResp>() {
        }.getType();
        CouponActivityResp ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse || "1001".equals(ptCRMResponse.getResultCode())) {
            CouponActivityResp couponActivityResp = new CouponActivityResp();
            couponActivityResp.setResultCode("1002");
            return couponActivityResp;
        }
        return ptCRMResponse;
    }

    @Override
    public CouponResp toCatchRightCoupons(CouponActivityReq couponActivityReq) {
        String url = orderConfig.URL_FARE_API_PDM + OrderUrlConstant.COUPON_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(couponActivityReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，查询用户权益券发生错误");
        }
        Type type = new TypeToken<CouponResp>() {
        }.getType();
        CouponResp ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse || "10001".equals(ptCRMResponse.getResultCode())) {
            CouponResp couponActivityResp = new CouponResp();
            couponActivityResp.setResultCode("10002");
            return couponActivityResp;
        }
        return ptCRMResponse;
    }

    @Override
    public PtCrmMileageResponse<CompanyMemberQueryInfoResDto> toCatchCompanyInfo(PtCrmMileageRequest<CompanyMemberQueryInfoReqDto> companyMemberQueryInfoReq) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.COMPANY_MEMBER_QUERYINFO;
        HttpResult httpResult = HttpUtil.doPostClient(companyMemberQueryInfoReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<PtCrmMileageResponse<CompanyMemberQueryInfoResDto>>() {
        }.getType();
        PtCrmMileageResponse<CompanyMemberQueryInfoResDto> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCRMResponse == null) {
            throw ServiceException.fail("数据转换异常");
        }
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getDesc());
        }
        return ptCRMResponse;
    }


    /**
     * 通用请求头设置
     *
     * @param ip          ip地址
     * @param channelCode 渠道号
     * @return
     */
    public static PtCrmMileageRequest<String> buildCommCrmReq(String ip, String channelCode, String channelPwd) {
        PtCrmMileageRequest<String> ptCrmMileageRequest = new PtCrmMileageRequest<>();
        ptCrmMileageRequest.setChannel(channelCode);
        ptCrmMileageRequest.setChannelPwd(channelPwd);
        ptCrmMileageRequest.setClientIP(ip);
        SimpleDateFormat dateFormatGmt = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormatGmt.setTimeZone(TimeZone.getTimeZone("GMT"));
        ptCrmMileageRequest.setTimestamp(dateFormatGmt.format(new Date()));
        ptCrmMileageRequest.setVersion("V1.0");
        ptCrmMileageRequest.setOperatorUid("");
        ptCrmMileageRequest.setRandomCode("");
        return ptCrmMileageRequest;
    }

    @Override
    public CrmMemberBaseApiResponse<AddMemberAddressResDto> memberAddressAdd(CrmMemberBaseApiRequest<AddMemberAddressReqDto> req) {
        return invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBERADDRESS_ADD,
                new TypeToken<CrmMemberBaseApiResponse<AddMemberAddressResDto>>() {
                }.getType());
    }

    @Override
    public CrmMemberBaseApiResponse<DeleteMemberAddressResDto> memberAddressDelete(CrmMemberBaseApiRequest<DeleteMemberAddressReqDto> req) {
        return invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBERADDRESS_DELETE,
                new TypeToken<CrmMemberBaseApiResponse<DeleteMemberAddressResDto>>() {
                }.getType());
    }

    @Override
    public CrmMemberBaseApiResponse<ModifyMemberAddressResDto> memberAddressModify(CrmMemberBaseApiRequest<ModifyMemberAddressReqDto> req) {
        return invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.MEMBERADDRESS_MODIFY,
                new TypeToken<CrmMemberBaseApiResponse<ModifyMemberAddressResDto>>() {
                }.getType());
    }

    @Override
    public CrmMemberBaseApiResponse<BaseDataQueryResDto> baseDataQuery(CrmMemberBaseApiRequest<BaseDataQueryReqDto> req) {
        return invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.BASEDATA_QUERY,
                new TypeToken<CrmMemberBaseApiResponse<BaseDataQueryResDto>>() {
                }.getType());
    }

    @Override
    public List<MemberLevelDTOView> toQueryMemberRights(BizDto bizDto) {
        ParamMemberRightsDTO paramMemberRightsDTO = new ParamMemberRightsDTO();
        //此处渠道固定为MOBILE
        paramMemberRightsDTO.setChannelCode("MOBILE");
        BaseRequestDTO<ParamMemberRightsDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setChannelCode(bizDto.getHeadChannelCode());
        baseRequestDTO.setUserNo("10001");
        baseRequestDTO.setVersion("V1.0");
        baseRequestDTO.setIp(bizDto.getIp());
        baseRequestDTO.setRequest(paramMemberRightsDTO);
        BaseResultDTO<List<MemberLevelDTO>> catchMemberRights = flightBasicConsumerClient.toCatchMemberRights(baseRequestDTO);
        if (!"10001".equals(catchMemberRights.getResultCode())) {
            throw ServiceException.fail("会员权益查询出错");
        }

        if (CollectionUtils.isEmpty(catchMemberRights.getResult())) {
            throw ServiceException.fail("会员权益查询为空");
        }
        return ConvertMemberRightMapping.MAPPER.toConvertMemberRights(catchMemberRights.getResult());
    }

    @Override
    public AvailCouponsResponse toCatchNormalCoupons(RequestData<QueryCouponReq> requestData) {
        CouponQueryRequest couponQueryRequest = CreateQueryCouponRequest(requestData);
        couponQueryRequest.setSale(1);  //非可售
        String url = orderConfig.getURL_FARE_API_COUPON() + orderConfig.SUB_QUERY_COUPON;
        HttpResult httpResult = HttpUtil.doPostClient(couponQueryRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("网络异常");
        }
        Type type = new TypeToken<AvailCouponsResponse>() {
        }.getType();
        AvailCouponsResponse availCouponsResponse = HoAirGsonUtil.fromJson(result, type);
        if (availCouponsResponse == null) {
            throw ServiceException.fail("查询无数据");
        }
        if (!"1001".equals(availCouponsResponse.getResultCode())) {
            throw ServiceException.fail(availCouponsResponse.getErrorInfo());
        }
        return availCouponsResponse;
    }

    @Override/**/
    public PtCouponProductGetResponseDto toCatchRightCoupons(RequestData<MyCoupon> requestData) {
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = createCouponProductGetRequestDto(requestData, true);
        String url = orderConfig.getURL_FARE_API_ORDER() + orderConfig.COUPON_MY_PRODUCT_V2;
        HttpResult httpResult = HttpUtil.doPostClient(ptCouponProductGetRequestDto,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("CRM请求异常");
        }
        Type type = new TypeToken<PtCouponProductGetResponseDto>() {
        }.getType();
        PtCouponProductGetResponseDto ptCouponProductGetResponseDto;
        ptCouponProductGetResponseDto = HoAirGsonUtil.fromJson(result, type);
        if (ptCouponProductGetResponseDto == null) {
            throw ServiceException.fail("未查询到权益券，如有疑问请联系95520");
        }
        if (!"1001".equals(ptCouponProductGetResponseDto.getResultCode())) {
            throw ServiceException.fail(ptCouponProductGetResponseDto.getErrorInfo());
        }
        return ptCouponProductGetResponseDto;
    }

    @Override
    public PtMemberMilesResponse toCatchScoreChangeDetail(String startDate, String endDate, String ffpId, String channelNo) {
        if (StringUtils.isBlank(startDate)) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -1);
            startDate = new SimpleDateFormat(DateUtil.YYYYMMDD_PATTERN).format(calendar.getTime());
        }
        if (StringUtils.isBlank(endDate)) {
            endDate = new SimpleDateFormat(DateUtil.YYYYMMDD_PATTERN).format(new Date());
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MileageDetailQuery mileageDetailQuery = new MileageDetailQuery();
        mileageDetailQuery.setId(ffpId);
        mileageDetailQuery.setStartDate(startDate);
        mileageDetailQuery.setEndDate(endDate);
        mileageDetailQuery.setEntryDateSort("D");
        ptCrmMileageRequest.setData(mileageDetailQuery);
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.MILEAGE_DETAIL_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("积分明细查询出错，请稍后再试！");
        }
        Type type = new TypeToken<PtCrmMileageResponse<PtMemberMilesResponse>>() {
        }.getType();
        PtCrmMileageResponse<PtMemberMilesResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getDesc());
        }
        return ptCRMResponse.getData();
    }

    @Override
    public PtCRMResponse<MileageDetailSegmentQueryResp> toCatchSegmentDetail(String startDate, String endDate, String ffpId, String channelNo) {
        if (StringUtils.isBlank(startDate)) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -1);
            startDate = new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(calendar.getTime());
        }
        if (StringUtils.isBlank(endDate)) {
            endDate = new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(new Date());
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        PtCrmMileageRequest<MileageDetailSegmentQueryRequest> ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MileageDetailSegmentQueryRequest memberStarQueryRequest = new MileageDetailSegmentQueryRequest();
        memberStarQueryRequest.setMemberId(Integer.valueOf(ffpId));
        memberStarQueryRequest.setBeginDate(startDate);
        memberStarQueryRequest.setEndDate(endDate);
        memberStarQueryRequest.setPageSize(500);
        memberStarQueryRequest.setPageNo(1);
        ptCrmMileageRequest.setData(memberStarQueryRequest);
        String url = crmConfig.getCrmMileageOpenApiUrl() + CrmUrlConstant.SEGMENT_DETAIL_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(ptCrmMileageRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("累计航段明细查询出错，请稍后再试！");
        }
        PtCRMResponse<MileageDetailSegmentQueryResp> ptCRMResponse = JSON.parseObject(result, new TypeToken<PtCRMResponse<MileageDetailSegmentQueryResp>>() {
        }.getType());
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getInnerMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public PtCRMResponse toTakeAliPayAuth(String ffpId, String authCode, String channelCode, String channelPwd, String ip) {
        PtApiCRMRequest<Map<String, Object>> ptApiCRMRequest = CRMReqUtil.buildCommReq(ip, channelCode, channelPwd, ffpId, "");
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("Code", authCode);
        ptApiCRMRequest.setData(reqMap);
        String url = crmConfig.getCrmLoginUrl() + CrmUrlConstant.ALIPAY_AUTH_URL;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("网络异常");
        }
        return JSON.parseObject(result, PtCRMResponse.class);
    }

    @Override
    @SuppressWarnings("all")
    public CrmPhoneInfo toCatchMobileInfo(String ffpCardNo, String channelCode) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = this.memberDetail(ptApiRequest, false);
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            ServiceException.fail("会员手机号查询网络出错");
        }
        if (CollectionUtils.isEmpty(ptCRMResponse.getData().getContactInfo())) {
            ServiceException.fail("会员手机号不存在");
        }
        String crmPhone = null;
        for (MemberContactSoaModel con : ptCRMResponse.getData().getContactInfo()) {
            //手机号
            if (ContactTypeEnum.MOBILE.getCode() == con.getContactType()) {
                crmPhone = con.getContactNumber();
                break;
            }
        }
        if (StringUtils.isBlank(crmPhone)) {
            ServiceException.fail("会员手机号不存在");
        }
        CrmPhoneInfo crmPhoneInfo = new CrmPhoneInfo();
        crmPhoneInfo.setCrmPhone(crmPhone);
        // 发送短信
        String[] split = crmPhone.split("-");
        String areaId = null;
        String phone;
        if (split.length > 1) {
            areaId = split[0];
            phone = split[1];
        } else {
            phone = split[0];
        }
        crmPhoneInfo.setAreaId(areaId);
        crmPhoneInfo.setPhone(phone);
        return crmPhoneInfo;
    }

    //我的优惠券查询条件
    private CouponQueryRequest CreateQueryCouponRequest(RequestData<QueryCouponReq> requestData) {
        QueryCouponReq couponReq = requestData.getData();
        List<PtSegmentInfo> segmentInfoList = new ArrayList<>();
        String couponState = couponReq.getCouponState();
        if (CouponStateEnum.R.getState().equals(couponReq.getCouponState())) {//已领取
            couponState = CouponStateEnum.R.getState() + "," + CouponStateEnum.C.getState();
        } else if (CouponStateEnum.N.getState().equals(couponReq.getCouponState())) {//已使用
            couponState = CouponStateEnum.N.getState() + "," + CouponStateEnum.G.getState();
        }
        CouponQueryRequest couponQueryReq = new CouponQueryRequest(
                "V1.0",
                requestData.getChannelNo(),
                "10001",
                requestData.getFfpId(),
                requestData.getFfpNo(),
                couponState,
                segmentInfoList
        );
        return couponQueryReq;
    }

    /**
     * 我的权益券请求参数
     *
     * @return
     */
    private PtCouponProductGetRequestDto createCouponProductGetRequestDto(RequestData<MyCoupon> requestData, boolean showChangeCoupon) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        MyCoupon data = requestData.getData();
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = new PtCouponProductGetRequestDto("V1.0", channelInfo.getOrderChannelCode(),
                requestData.getFfpId(), requestData.getFfpNo());
        List<String> stateList = new ArrayList<>();
        /**
         * 用于区分app上权益券列表里的 可使用和已使用   可使用:Not 已使用:Used 已过期:Expired
         */
        //未使用的
        if ("R".equals(data.getCouponState())) {
            String[] stateArr = {OrderCouponStateEnum.Not.getStateCode(), OrderCouponStateEnum.Appointment.getStateCode(), OrderCouponStateEnum.Giving.getStateCode()};
            stateList = Arrays.asList(stateArr);
            ptCouponProductGetRequestDto.setAvailableStatus("Not");
        }
        if ("E".equals(data.getCouponState())) {//失效状态
            String[] stateArr = {OrderCouponStateEnum.Overdue.getStateCode(), OrderCouponStateEnum.Refund.getStateCode()};
            stateList = Arrays.asList(stateArr);
            ptCouponProductGetRequestDto.setAvailableStatus("Expired");
        } else if ("U".equals(data.getCouponState())) {//已使用
            String[] stateArr = {OrderCouponStateEnum.Used.getStateCode(), OrderCouponStateEnum.WittenOff.getStateCode(), OrderCouponStateEnum.GiveAway.getStateCode()};
            stateList = Arrays.asList(stateArr);
            ptCouponProductGetRequestDto.setAvailableStatus("Used");
        }
        List<String> voucherTypes = Lists.newArrayList();
        if (StringUtils.isNotBlank(data.getCouponSource())) {
            voucherTypes.add(data.getCouponSource());
        } else {
            voucherTypes.addAll(Arrays.asList(VoucherTypesEnum.UPGRADE.getCode(),
                    VoucherTypesEnum.BAGGAGE.getCode(),
                    VoucherTypesEnum.LOUNGE.getCode(),
                    VoucherTypesEnum.ONBOARDWIFI.getCode(),
                    VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode(),
                    VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode(),
                    VoucherTypesEnum.UNLIMITED_FLY_V2.getCode(),
                    VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode(),
                    VoucherTypesEnum.UnlimitUpgradeYear.getCode(),
                    VoucherTypesEnum.RESCHEDULE.getCode(),
                    VoucherTypesEnum.UPGRADECOUPON.getCode(),
                    VoucherTypesEnum.LOUNGECOUPON.getCode(),
                    VoucherTypesEnum.BAGGAGECOUPON.getCode(),
                    VoucherTypesEnum.THEMECOUPON.getCode()
            ));
            //主题卡类型
            voucherTypes.addAll(orderConfig.getThemeCouponList());
            voucherTypes.addAll(ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList());
            if (showChangeCoupon) {
                voucherTypes.add(VoucherTypesEnum.RESCHEDULECOUPON.getCode());
            }
        }
        List<String> couponStateList = stateList.stream().map(couponState -> {
                    EnumVoucherStates voucherStates = (EnumVoucherStates) HoEnumUtil.getEnumByString(EnumVoucherStates.class, couponState);
                    if (null != voucherStates) {
                        return voucherStates.getCode();
                    }
                    return couponState;
                }
        ).collect(Collectors.toList());
        ptCouponProductGetRequestDto.setVoucherTypes(voucherTypes);
        ptCouponProductGetRequestDto.setCouponState(couponStateList);
        ptCouponProductGetRequestDto.setPageNo(1);
        ptCouponProductGetRequestDto.setPageSize(100);
        return ptCouponProductGetRequestDto;
    }

    @Override
    public void verifyPayPassword(String channelCode, String ffpId, String payPassword) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        PtCrmMileageRequest<VerifyPayPasswordParam> crmBaseRequest = new PtCrmMileageRequest<>();
        VerifyPayPasswordParam verifyParam = new VerifyPayPasswordParam();
        verifyParam.setId(ffpId);
        verifyParam.setPassword(payPassword);
        crmBaseRequest.setClientIP(HoAirIpUtil.getLocalIp());
        crmBaseRequest.setChannel(channelInfo.getChannelCode());
        crmBaseRequest.setChannelPwd(channelInfo.getChannelPwd());
        crmBaseRequest.setData(verifyParam);
        String url = crmConfig.getCrmAccountOpenApiUrl() + CrmUrlConstant.CRM_ACCOUNT_PAY_PASSWORD;
        HttpResult httpResult = HttpUtil.doPostClient(crmBaseRequest,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("消费密码校验出错，请稍后再试！");
        }
        Type type = new TypeToken<PtCrmMileageResponse<PtMemberMilesResponse>>() {
        }.getType();
        PtCrmMileageResponse ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (0 != ptCRMResponse.getCode()) {
            throw new MultiLangServiceException(CommonErrorCode.VERIFY_PAY_PASSWORD_CHECK_FAIL, ptCRMResponse.getDesc());
        }
    }

    @Override
    public boolean checkRealState(BizDto bizDto, String ffpNo) {
        PtMemberDetail ptMemberDetail = this.memberDetail(bizDto.getHeadChannelCode(),ffpNo,new String[]{MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName});
        if(CollectionUtils.isEmpty(ptMemberDetail.getRealVerifyInfos())){
            return false;
        }
        return CrmUtil.toCheckRealNameStatus(ptMemberDetail.getRealVerifyInfos());
    }

    @Override
    public MemberAccountInfo getAccountInfo(String ffpNo, boolean useCache) {
        final String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_ACCOUNT_INFO + ffpNo);
        MemberAccountInfo accountInfo;
        // 使用缓存优先使用缓存状态
        if (useCache) {
            accountInfo = redisUtils.getObject(redisKey, MemberAccountInfo.class);
            if (null != accountInfo) {
                return accountInfo;
            }
        }
        accountInfo = new MemberAccountInfo();
        PtMemberDetail ptMemberDetail = this.memberDetail(ChannelCodeEnum.MOBILE.getChannelCode(), ffpNo,
                new String[]{MemberDetailRequestItemsEnum.BASICINFO.eName,
                    MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                    MemberDetailRequestItemsEnum.STATEINFO.eName});
        // 基础信息
        MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
        accountInfo.setName(basicInfo.getCLastName() + basicInfo.getCFirstName());
        SalutationEnum sex = SalutationEnum.formatSexCode(basicInfo.getSex());
        accountInfo.setSex(null == sex ? "" : sex.getSex());
        accountInfo.setFfpId(String.valueOf(basicInfo.getMemberId()));
        accountInfo.setFfpNo(basicInfo.getCardNO());
        accountInfo.setHeadImageUrl(basicInfo.getHeadImageUrl());
        // 状态信息
        MemberStateInfoSoaModel stateInfo = ptMemberDetail.getStateInfo();
        // 会员状态2 代表启用 未封禁
        accountInfo.setAccountStatus(2 == stateInfo.getIsClosed());
        // 联系信息
        List<MemberContactSoaModel> contactInfoTypeList = ptMemberDetail.getContactInfo();
        if (CollectionUtils.isNotEmpty(contactInfoTypeList)) {
            for (MemberContactSoaModel contactSoaModel : contactInfoTypeList) {
                if (ContactTypeEnum.MOBILE.getCode() == contactSoaModel.getContactType()) {
                    accountInfo.setMemberTel(contactSoaModel.getContactNumber());
                }
                if (ContactTypeEnum.EMAIL.getCode() == contactSoaModel.getContactType()) {
                    accountInfo.setEmail(contactSoaModel.getContactNumber());
                }
            }
        }
        redisUtils.setJSON(redisKey, accountInfo, 86400L);
        return accountInfo;
    }

    @Override
    public boolean realState(String ffpNo) {
        // 实名信息按日缓存
        final String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.MEMBER_REAL_STATE + DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD_PATTERN));
        if (redisUtils.sHasKey(redisKey, ffpNo)) {
            return true;
        }
        String[] requestItems = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtMemberDetail memberDetail  = memberDetail(ChannelCodeEnum.MOBILE.getChannelCode(), ffpNo, requestItems);
        // 会员信息resp
        if (null == memberDetail || CollectionUtils.isEmpty(memberDetail.getRealVerifyInfos())){
            log.error("查询实名信息失败，会员卡号:{} 返回信息为空", ffpNo);
            return false;
        }
        List<MemberRealNameSummarySoaModel> realInfo = memberDetail.getRealVerifyInfos();
        // 存在实名认证
        boolean status = realInfo.stream().anyMatch(realVerifyInfo -> null != realVerifyInfo && "1".equals(realVerifyInfo.getStatus()));
        if (status) {
            redisUtils.sSetAndTime(redisKey, 25 * 60 * 60L, ffpNo);
        }
        return status;
    }

}
