package com.juneyaoair.oneorder.api.order.dto.transfer;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 中转住宿申请清单
 * <AUTHOR>
 */
@Data
public class TransferHouseResult {

    @SerializedName("CheckTransferHouseList")
    @ApiModelProperty(value = "中转住宿申请清单")
    private List<TransferHouseRecord> checkTransferHouseList;

}