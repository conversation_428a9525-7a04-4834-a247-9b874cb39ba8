package com.juneyaoair.oneorder.api.cuss.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName CussConfig
 * @Description
 * <AUTHOR>
 * @Date 2023/6/27 14:19
 * @Version 1.0
 */
@Data
@Configuration
public class CussConfig {

    /** 值机选座服务 */
    @Value("${cuss.url:https://e-cuss.juneyaoair.com}")
    private String cussUrl;

}
