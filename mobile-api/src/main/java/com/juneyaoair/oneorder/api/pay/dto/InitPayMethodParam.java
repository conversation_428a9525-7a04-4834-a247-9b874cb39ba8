package com.juneyaoair.oneorder.api.pay.dto;

import com.juneyaoair.oneorder.common.dto.BizDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/27 18:38
 */
@Data
@NoArgsConstructor
@ApiModel(value = "InitPayMethodParam",description = "支付方式渲染请求参数")
public class InitPayMethodParam extends BizDto {
    @NotBlank(message = "订单类型不可为空")
    @ApiModelProperty(value = "订单类型",allowableValues = "D,I,O,GatewayUpgrade",notes = "D-国内机票订单,I-国际机票订单,O-其他,GatewayUpgrade-登机口升舱订单",required = true)
    private String orderType;
    @NotBlank(message = "收款商户不可为空")
    @ApiModelProperty(value = "商户类型",allowableValues = "JX,JN",notes = "JX-吉祥，JN-吉宁，默认不传是JX")
    private String merchantPayment;
    @ApiModelProperty(value = "出发机场")
    private String depAirport;
    @ApiModelProperty(value = "到达机场")
    private String arrAirport;
    @ApiModelProperty(value = "单程往返类型")
    private String routeType;
    @ApiModelProperty(value = "国内国际类型")
    private String interFlag;

    @ApiModelProperty(value = "支付币种，此币种数据来源于订单和当前所选币种无直接关系")
    private String currency;

    @ApiModelProperty(value = "订单币种")
    private String orderCurrency;

}
