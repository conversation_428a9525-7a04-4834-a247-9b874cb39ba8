package com.juneyaoair.oneorder.api.basic.service.impl;

import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.i18ndict.I18nDictionaryParam;
import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.basic.service.I18nDictService;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class I18nDictServiceImpl implements I18nDictService {

    @Resource
    private FlightBasicProviderClient flightBasicProviderClient;

    @Override
    public Map<String, Map<String, String>> fetchDictData(I18nDictionaryTypeEnum dictionaryType) {
        RequestData<I18nDictionaryParam> requestData = new RequestData<>();
        requestData.setOriginIp("127.0.0.1");
        requestData.setChannelNo("B2C");
        I18nDictionaryParam reqDTO = new I18nDictionaryParam();
        reqDTO.setDictionaryType(dictionaryType.name());
        reqDTO.setRefresh(false);
        requestData.setData(reqDTO);
        ResponseData<Map<String, Map<String, String>>> baseResultDTO = flightBasicProviderClient.getList(requestData);
        if (WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            return baseResultDTO.getData();
        } else {
            log.error("调用国际化字典信息接口失败，字典类型：{}", dictionaryType);
            throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public String getTranslation(I18nDictionaryTypeEnum dictionaryType, String key, String language) {
        Map<String, Map<String, String>> dictMap = this.fetchDictData(dictionaryType);
        if (dictMap != null) {
            // 根据语言枚举获取对应的语言标识
            if (null == language) {
                language = LanguageEnum.ZH_CN.name();
            }
            Map<String, String> dictionary = dictMap.get(key);
            if (dictionary != null) {
                String translations = dictionary.get(language);
                if (translations != null) {
                    return translations;
                }
            }
            return key;
        }
        return key;
    }

    @Override
    public Map<String, Map<String, Map<String, String>>> fetchMultipleDictData(I18nDictionaryTypeEnum... dictionaryTypes) {
        Map<String, Map<String, Map<String, String>>> dictionaries = new HashMap<>();

        if (dictionaryTypes != null) {
            for (I18nDictionaryTypeEnum dictType : dictionaryTypes) {
                Map<String, Map<String, String>> dict = this.fetchDictData(dictType);
                if (dict != null) {
                    dictionaries.put(dictType.name(), dict);
                }
            }
        }

        return dictionaries;
    }

    @Override
    public String getTranslationFromDict(Map<String, Map<String, Map<String, String>>> dictionaries, I18nDictionaryTypeEnum dictionaryType, String key, LanguageEnum language) {
        if (key == null || dictionaries == null) {
            return key;
        }

        Map<String, Map<String, String>> dict = dictionaries.get(dictionaryType.name());
        if (dict != null) {
            Map<String, String> translations = dict.get(key);
            if (translations != null) {
                String translation = translations.get(language.name());
                if (translation != null) {
                    return translation;
                }
            }
        }
        return key;
    }

}