package com.juneyaoair.oneorder.api.order.dto.transfer;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中转住宿申请记录
 * <AUTHOR>
 */
@Data
public class TransferHouseRecord {

    @SerializedName("CertNo")
    @ApiModelProperty(value = "证件号")
    private String certNo;

    @SerializedName("TktNo")
    @ApiModelProperty(value = "客票号")
    private String tktNo;

    @SerializedName("CouponSource")
    @ApiModelProperty(value = "权益订单类型")
    private String couponSource;

    @SerializedName("FlightDate")
    @ApiModelProperty(value = "航班日期 yyyy-MM-dd")
    private String flightDate;

    @SerializedName("FlightNo")
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @SerializedName("ApplyResult")
    @ApiModelProperty(value = "是否已申请")
    private Boolean applyResult;

    @SerializedName("OrderNo")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @SerializedName("Contact")
    @ApiModelProperty(value = "联系人名称")
    private String contact;

    @SerializedName("CreateDateTime")
    @ApiModelProperty(value = "下单时间")
    private Long createDateTime;

    @SerializedName("PhoneNo")
    @ApiModelProperty(value = "联系电话")
    private String phoneNo;

}