package com.juneyaoair.oneorder.common.util;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.common.constant.TongDunEventType;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.PlatFormEnum;
import com.juneyaoair.oneorder.common.dto.enums.SensitiveOperationEnum;
import com.juneyaoair.oneorder.config.TongDunConfig;
import com.juneyaoair.oneorder.order.constant.AirCompanyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 此类方法不要再增加，同盾新增事件统一在TongDunService类
 */
@Slf4j
@Deprecated
public class TongDunUtil {

    /**
     * @param ffpCardNo
     * @return
     */
    public static String getFfpCardNo(String ffpCardNo) {
        return subStrFromStart(ffpCardNo, AirCompanyEnum.HO.getAirCompanyCode());


    }

    /**
     * 截取某个字符串开头的字符串
     *
     * @param originStr 原始字符串
     * @param subStr    需要截取的字符串
     * @return
     */
    public static String subStrFromStart(String originStr, String subStr) {
        if(StringUtils.isBlank(originStr)){
            return "";
        }
        if (originStr.startsWith(subStr)) {
            originStr = originStr.substring(subStr.length());
        }
        return originStr;
    }

    /**
     * 同盾公共参数设置参数
     *
     * @param realChannelCode 请求渠道
     * @param platForm        平台信息  android/ios
     * @param from            表示同盾的设备指纹类型
     * @param eventType       事件类型
     * @param sign            具体事件区分标识
     * @return
     */
    public static Map<String, String> createCommonParam(String realChannelCode, String platForm, String from, String eventType, String sign, TongDunConfig config) {
        String platform;
        //按照操作系统划分密钥和事件
        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(realChannelCode) ||
            ChannelCodeEnum.G_MOBILE.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            platform = platForm;
            if (!PlatFormEnum.ANDROID.getSystemCode().equalsIgnoreCase(platForm)
                    && !PlatFormEnum.IOS.getSystemCode().equalsIgnoreCase(platForm)
                    && !PlatFormEnum.HARMONY.getSystemCode().equalsIgnoreCase(platForm)) {
                //平台设置
                platform = PlatFormEnum.ANDROID.getSystemCode();
            }
        } else if (ChannelCodeEnum.WXAPP.getChannelCode().equalsIgnoreCase(realChannelCode)
                || ChannelCodeEnum.CHECKIN.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            //平台设置
            platform = PlatFormEnum.WXAPP.getSystemCode();
            //小程序可能使用的是H5的设备指纹，此处需要更改类型
            if (PlatFormEnum.H5.getSystemCode().equalsIgnoreCase(from)) {
                //平台设置
                platform = PlatFormEnum.H5.getSystemCode();
            }
        } else if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            //平台设置
            platform = PlatFormEnum.ZFBAPP.getSystemCode();
            //小程序可能使用的是H5的设备指纹，此处需要更改类型
            if (PlatFormEnum.H5.getSystemCode().equalsIgnoreCase(from)) {
                //平台设置
                platform = PlatFormEnum.H5.getSystemCode();
            }
        } else if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(realChannelCode)
                || ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            platform = PlatFormEnum.H5.getSystemCode();
        } else if (ChannelCodeEnum.B2C.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            platform = PlatFormEnum.B2C.getSystemCode();
        } else if (ChannelCodeEnum.G_B2C.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            platform = PlatFormEnum.GLOBAL.getSystemCode();
        } else {
            log.info("未正确设置渠道:{},{},from:{}", realChannelCode, platForm, from);
            //平台设置
            throw MultiLangServiceException.fail("未正确设置渠道" + realChannelCode);
        }
        Map<String, String> params = new HashMap<>();
        //此处值填写您的合作方标识
        params.put("partner_code", config.TONGDUN_PARTNER_CODE);
        //事件类型
        params.put("event_type", eventType);
        //设置secret_key
        initSecretKey(params, platform, config);
        //此处填写策略集上的事件ID
        params.put("event_id", setEventId(platform, eventType, sign, config));
        return params;
    }

    /**
     * 初始化应用key信息
     *
     * @param params
     * @param platform
     */
    private static void initSecretKey(Map<String, String> params, String platform, TongDunConfig config) {
        if (PlatFormEnum.ANDROID.getSystemCode().equalsIgnoreCase(platform)) {
            params.put("secret_key", config.TONGDUN_ANDROID_SECRET_KEY);
        } else if (PlatFormEnum.IOS.getSystemCode().equalsIgnoreCase(platform)) {
            //此处填写对应ios密钥
            params.put("secret_key", config.TONGDUN_IOS_SECRET_KEY);
        } else if (PlatFormEnum.HARMONY.getSystemCode().equalsIgnoreCase(platform)) {
            //此处填写对应ios密钥
            params.put("secret_key", config.TONGDUN_HARMONY_SECRET_KEY);
        } else if (PlatFormEnum.WXAPP.getSystemCode().equalsIgnoreCase(platform)) {
            //此处填写对应小程序密钥
            params.put("secret_key", config.TONGDUN_XCX_SECRET_KEY);
        } else if (PlatFormEnum.ZFBAPP.getSystemCode().equalsIgnoreCase(platform)) {
            //此处填写对应小程序密钥
            params.put("secret_key", config.TONGDUN_ZFB_SECRET_KEY);
        } else if (PlatFormEnum.B2C.getSystemCode().equalsIgnoreCase(platform)) {
            params.put("secret_key", config.TONGDUN_B2C_SECRET_KEY);
        } else if (PlatFormEnum.H5.getSystemCode().equalsIgnoreCase(platform)) {
            //此处填写对应H5密钥
            params.put("secret_key", config.TONGDUN_WEB_SECRET_KEY);
        } else if(PlatFormEnum.GLOBAL.getSystemCode().equalsIgnoreCase(platform)){
            //此处填写对应国际网站密钥
            params.put("secret_key", config.TONGDUN_GLOBAL_SECRET_KEY);
        }else {
            throw ServiceException.fail("未配置的平台信息:" + platform);
        }
    }

    /**
     * 根据不同渠道加载不同的事件ID
     *
     * @param platform  设备平台信息
     * @param eventType 事件类型
     * @param sign      事件自定义唯一标识
     * @return
     */
    private static String setEventId(String platform, String eventType, String sign, TongDunConfig config) {
        String eventId = "";
        switch (platform) {
            case "android": {
                eventId = intiAndroidEventId(eventType, sign, config);
                break;
            }
            case "ios": {
                eventId = intiIosEventId(eventType, sign, config);
                break;
            }
            case "harmony": {
                eventId = intiHarmonyEventId(eventType, sign, config);
                break;
            }
            case "wxapp": {
                eventId = intiWxAppEventId(eventType, sign, config);
                break;
            }
            case "zfbapp": {
                eventId = intiZfbEventId(eventType, sign, config);
                break;
            }
            case "h5": {
                eventId = intiH5EventId(eventType, sign, config);
                break;
            }
            case "b2c": {
                eventId = initB2CEventId(eventType, sign, config);
                break;
            }
            case "global": {
                eventId = config.getGlobalEventIdMap().get(sign);
                break;
            }
            //默认不处理
            default: {
                break;
            }
        }
        if (StringUtils.isBlank(eventId)) {
            log.info("平台:{},同盾类型:{},操作类型:{},未查询到匹配的同盾事件", platform, eventType, sign);
            throw MultiLangServiceException.fail("未找到匹配的事件标识");
        }
        return eventId;
    }

    /**
     * 初始化B2C渠道事件标识ID
     *
     * @param eventType
     * @param sign
     * @param config
     * @return
     */
    private static String initB2CEventId(String eventType, String sign, TongDunConfig config) {
        //登录事件
        if (SensitiveOperationEnum.LOGIN_RISK_CTRL.name().equals(sign)) {
            return config.TONGDUN_B2C_LOGIN_EVENT_ID;
        }
        //注册事件
        if (SensitiveOperationEnum.REGISTER_RISK_CTRL.name().equals(sign)) {
            return config.TONGDUN_B2C_REGISTER_EVENT_ID;
        }
        if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            return config.TONGDUN_B2C_LOOKUP_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.B2C_WEB_ORDER_EVENT.name().equals(sign)) {
                // 官网下单购券事件
                return config.TONGDUN_B2C_ORDER_COUPON_EVENT_ID;
            }
            return config.TONGDUN_B2C_TRADE_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_B2C_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            return config.TONGDUN_B2C_MODIFY_EVENT_ID;
        }
        return config.getB2cEventIdMap().get(sign);
    }

    /**
     * 初始化H5渠道事件标识ID
     * @param eventType
     * @param sign
     * @param config
     * @return
     */
    private static String intiH5EventId(String eventType, String sign, TongDunConfig config) {
        if (TongDunEventType.REGISTER.getEventType().equals(eventType)) {
            return config.TONGDUN_WEB_REGISTER_EVENT_ID;
        } else if (TongDunEventType.LOGIN.getEventType().equals(eventType)) {
            return config.TONGDUN_WEB_LOGIN_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            return config.TONGDUN_WEB_TRADE_EVENT_ID;
        } else if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
                //值机选座查询事件
                return config.TONGDUN_H5_CHECKIN_LOOKUP_EVENT_ID;
            } else if (SensitiveOperationEnum.TICKET_RISK_CTRL.name().equals(sign)) {
                return config.TONGDUN_H5_CHECKIN_LOOKUP_EVENT_ID;
            } else {
                //航班查询事件
                return config.TONGDUN_H5_LOOKUP_EVENT_ID;
            }
        } else if (TongDunEventType.MARKETING.getEventType().equals(eventType)) {
            return config.TONGDUN_H5_MARKETING_EVENT_ID;
        } else if (TongDunEventType.CLICK.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
                return config.TONGDUN_H5_USE_SCORE_EVENT_ID;
            }
            return config.TONGDUN_H5_RESERVE_EVENT_ID;
        } else if (TongDunEventType.PAYMENT.getEventType().equals(eventType)) {
            return config.TONGDUN_H5_PAYMENT_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_H5_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
                return config.TONGDUN_H5_MODIFY_SALEPWD_EVENT_ID;
            }
        }
        return null;
    }

    /**
     * 初始化微信小程序渠道事件标识ID
     * @param eventType
     * @param sign
     * @param config
     * @return
     */
    private static String intiWxAppEventId(String eventType, String sign, TongDunConfig config) {
        if (TongDunEventType.REGISTER.getEventType().equals(eventType)) {

        } else if (TongDunEventType.LOGIN.getEventType().equals(eventType)) {
            return config.TONGDUN_XCX_LOGIN_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            return config.TONGDUN_XCX_TRADE_EVENT_ID;
        } else if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
                //值机选座查询事件
                return config.TONGDUN_XCX_CHECKIN_LOOKUP_EVENT_ID;
            } else if (SensitiveOperationEnum.TICKET_RISK_CTRL.name().equals(sign)) {
                return config.TONGDUN_XCX_CHECKIN_LOOKUP_EVENT_ID;
            } else {
                //航班查询事件
                return config.TONGDUN_XCX_LOOKUP_EVENT_ID;
            }
        } else if (TongDunEventType.MARKETING.getEventType().equals(eventType)) {
            return config.TONGDUN_XCX_MARKETING_EVENT_ID;
        } else if (TongDunEventType.CLICK.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
                return config.TONGDUN_XCX_USE_SCORE_EVENT_ID;
            }
            return config.TONGDUN_XCX_RESERVE_EVENT_ID;
        } else if (TongDunEventType.PAYMENT.getEventType().equals(eventType)) {
            return config.TONGDUN_XCX_PAYMENT_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_XCX_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
                return config.TONGDUN_XCX_MODIFY_SALEPWD_EVENT_ID;
            }
        }
        return null;
    }

    private static String intiIosEventId(String eventType, String sign, TongDunConfig config) {
        if (TongDunEventType.REGISTER.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_REGISTER_EVENT_ID;
        } else if (TongDunEventType.LOGIN.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_LOGIN_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_TRADE_EVENT_ID;
        } else if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
                //值机选座查询事件
                return config.TONGDUN_IOS_CHECKIN_LOOKUP_EVENT_ID;
            } else if (SensitiveOperationEnum.TICKET_RISK_CTRL.name().equals(sign)) {
                return config.TONGDUN_IOS_CHECKIN_LOOKUP_EVENT_ID;
            } else {
                //航班查询事件
                return config.TONGDUN_IOS_LOOKUP_EVENT_ID;
            }
        } else if (TongDunEventType.MARKETING.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_MARKETING_EVENT_ID;
        } else if (TongDunEventType.CLICK.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
                return config.TONGDUN_IOS_USE_SCORE_EVENT_ID;
            }
            return config.TONGDUN_IOS_RESERVE_EVENT_ID;
        } else if (TongDunEventType.PAYMENT.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_PAYMENT_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_IOS_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
                return config.TONGDUN_IOS_MODIFY_SALEPWD_EVENT_ID;
            }
        }
        return null;
    }

    //鸿蒙端事件ID
    private static String intiHarmonyEventId(String eventType, String sign, TongDunConfig config) {
        if (TongDunEventType.REGISTER.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_REGISTER_EVENT_ID;
        } else if (TongDunEventType.LOGIN.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_LOGIN_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_TRADE_EVENT_ID;
        } else if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
                //值机选座查询事件
                return config.TONGDUN_HARMONY_CHECKIN_LOOKUP_EVENT_ID;
            } else if (SensitiveOperationEnum.TICKET_RISK_CTRL.name().equals(sign)) {
                return config.TONGDUN_HARMONY_CHECKIN_LOOKUP_EVENT_ID;
            } else {
                //航班查询事件
                return config.TONGDUN_HARMONY_LOOKUP_EVENT_ID;
            }
        } else if (TongDunEventType.MARKETING.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_MARKETING_EVENT_ID;
        } else if (TongDunEventType.CLICK.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
                return config.TONGDUN_HARMONY_USE_SCORE_EVENT_ID;
            }
            return config.TONGDUN_HARMONY_RESERVE_EVENT_ID;
        } else if (TongDunEventType.PAYMENT.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_PAYMENT_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_HARMONY_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
                return config.TONGDUN_HARMONY_MODIFY_SALEPWD_EVENT_ID;
            }
        }
        return null;
    }

    private static String intiAndroidEventId(String eventType, String sign, TongDunConfig config) {
        if (TongDunEventType.REGISTER.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_REGISTER_EVENT_ID;
        } else if (TongDunEventType.LOGIN.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_LOGIN_EVENT_ID;
        } else if (TongDunEventType.TRADE.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_TRADE_EVENT_ID;
        } else if (TongDunEventType.LOOKUP.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
                //值机选座查询事件
                return config.TONGDUN_ANDROID_CHECKIN_LOOKUP_EVENT_ID;
            } else if (SensitiveOperationEnum.TICKET_RISK_CTRL.name().equals(sign)) {
                return config.TONGDUN_ANDROID_CHECKIN_LOOKUP_EVENT_ID;
            } else {
                //航班查询事件
                return config.TONGDUN_ANDROID_LOOKUP_EVENT_ID;
            }
        } else if (TongDunEventType.MARKETING.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_MARKETING_EVENT_ID;
        } else if (TongDunEventType.CLICK.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
                return config.TONGDUN_ANDROID_USE_SCORE_EVENT_ID;
            }
            return config.TONGDUN_ANDROID_RESERVE_EVENT_ID;
        } else if (TongDunEventType.PAYMENT.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_PAYMENT_EVENT_ID;
        } else if (TongDunEventType.SMS.getEventType().equals(eventType)) {
            return config.TONGDUN_ANDROID_SMS_EVENT_ID;
        } else if (TongDunEventType.MODIFY.getEventType().equals(eventType)) {
            if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
                return config.TONGDUN_ANDROID_MODIFY_SALEPWD_EVENT_ID;
            }
        }
        return null;
    }

    //支付宝应用端对应事件ID
    private static String intiZfbEventId(String eventType, String sign, TongDunConfig config) {
        if (SensitiveOperationEnum.CHECK_IN_TOUR.name().equals(sign)) {
            return config.TONGDUN_ZFB_CHECKIN_LOOKUP_EVENT_ID;
        }
        if (SensitiveOperationEnum.SEND_SMS_RISK_CTRL.name().equals(sign)) {
            return config.TONGDUN_ZFB_SMS_EVENT_ID;
        }
        if (SensitiveOperationEnum.USE_SALE.name().equals(sign) || SensitiveOperationEnum.ADD_BENEFICIARY.name().equals(sign)) {
            return config.TONGDUN_ZFB_USE_SCORE_EVENT_ID;
        }
        if (SensitiveOperationEnum.RESET_SALE_PWD.name().equals(sign) || SensitiveOperationEnum.MODIFY_SALE_PWD.name().equals(sign)) {
            return config.TONGDUN_ZFB_MODIFY_SALEPWD_EVENT_ID;
        }
        return null;
    }

}
