package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 查询乘机人信息V2
 * @date 2019/7/18  17:38.
 */
@Data
@NoArgsConstructor
public class QueryGeneralContactRequest {
    @JsonProperty(value = "Version")
    private String Version;
    @JsonProperty(value = "ChannelCode")
    private String ChannelCode;
    @JsonProperty(value = "UserNo")
    private String UserNo;
    @JsonProperty(value = "ChannelCustomerNo")
    private String ChannelCustomerNo;
    @JsonProperty(value = "ChannelCustomerType")
    private String ChannelCustomerType;//CRM - 常客,Other - 其它
    @JsonProperty(value = "SortType")
    private String SortType;
    @JsonProperty(value = "PassengerName")
    private String PassengerName;
    @JsonProperty(value = "PassEnNameS")
    private String PassEnNameS;
    @JsonProperty(value = "PassEnNameF")
    private String PassEnNameF;
    @JsonProperty(value = "CertNo")
    private String CertNo;
    @JsonProperty(value = "DepCity")
    private String DepCity;
    @JsonProperty(value = "ArrCity")
    private String ArrCity;
    @JsonProperty(value = "IsGMJC")
    private String IsGMJC;  //Y:军警残购票，N/null:正常购票
    @JsonProperty(value = "ContactType")
    private List<String> ContactType;// A=儿童畅飞，B=成人畅飞
    @JsonProperty(value = "PageNo")
    private int PageNo;
    @JsonProperty(value = "PageSize")
    private int PageSize;

    public QueryGeneralContactRequest(String version, String channelCode, String userNo, String channelCustomerType, String sortType){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.ChannelCustomerType = channelCustomerType;
        this.SortType = sortType;
    }
}
