package com.juneyaoair.oneorder.flight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 会员行程航班信息
 * @created 2025/06/19 14:11
 */
@Data
public class MemberTravellerFlightInfoDto {

    @ApiModelProperty(value = "市场方航班号")
    private String marketingFlightNo;

    @ApiModelProperty(value = "市场方航司Icon")
    private AirLineIcon marketingAirLineIcon;

    @ApiModelProperty(value = "承运方航班号")
    private String operationFlightNo;

    @ApiModelProperty(value = "承运方航司Icon")
    private AirLineIcon operationAirLineIcon;

    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd")
    private String flightDate;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;

    @ApiModelProperty(value = "出发城市名称")
    private String depCityName;

    @ApiModelProperty(value = "起飞机场三字码")
    private String depAirportCode;

    @ApiModelProperty(value = "起飞机场名称")
    private String depAirportName;

    @ApiModelProperty(value = "起飞机场航站楼")
    private String depTerminal;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "到达城市名称")
    private String arrCityName;

    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

    @ApiModelProperty(value = "到达机场名称")
    private String arrAirportName;

    @ApiModelProperty(value = "到达机场航站楼")
    private String arrTerminal;

    @ApiModelProperty(value = "经停机场三字码")
    private String stopAirport;

    @ApiModelProperty(value = "经停机场名称")
    private String stopAirportName;

    @ApiModelProperty(value = "经停城市名称")
    private String stopCityName;

    @ApiModelProperty(value = "国内国际标志")
    private String interFlag;

    @ApiModelProperty(value = "机型")
    private String planeType;

    @ApiModelProperty(value = "机型名称")
    private String planeTypeName;

    @ApiModelProperty(value = "起飞时间")
    private String depTime;

    @ApiModelProperty(value = "到达日期 yyyy-MM-dd")
    private String arrDate;

    @ApiModelProperty(value = "到达时间")
    private String arrTime;

    @ApiModelProperty(value = "飞行时长 单位：分钟")
    private String duration;

    @ApiModelProperty(value = "计划到达跨天天数")
    private long crossDay;


    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "旅客姓名")
    private String travellerName;

    @ApiModelProperty(value = "pnrNo")
    private String pnrNo;

    @ApiModelProperty(value = "行程序号")
    private String segNo;

    @ApiModelProperty(value = "市场方舱位")
    private String marketingCabin;

    @ApiModelProperty(value = "承运方舱位")
    private String operationCabin;

    @ApiModelProperty(value = "舱位名称")
    private String cabinName;

    /**
     * 乘客类型
     * @See com.juneyaoair.cuss.enums.ENUM_SEAT_IBE_PASSENGER_TYPE
     */
    @ApiModelProperty(value = "乘客类型 ADT：成人 CHD：儿童 UCCHD/UM：无陪儿童 INF：婴儿")
    private String passengerType;

    @ApiModelProperty(value = "座位号")
    private String seatNo;

    @ApiModelProperty(value = "客票状态 C：已值机 O:客票有效 L:已登机 F:已使用 V:已作废 E：已换开 R:已退票")
    private String couponStatus;

    @ApiModelProperty("旅服网-旅客特服 参照：ENUM_SPECIAL_PSR 旅服部分")
    private Set<String> psmSpecialSet;

    @ApiModelProperty(value = "SSR编码 SsrCodeEnum")
    private Set<String> ssrCodeSet;

    @ApiModelProperty(value = "行李限额 **K/*PC")
    private String baggageAllowance;

    @ApiModelProperty(value = "出生日期")
    private String birthdate;

    /**
     * 飞行方向  去程为G,回程为B
     */
    private String flightDirection;

    /**
     * 运价类型
     */
    private String fareType;

    @ApiModelProperty("上一张联票号")
    private String priorTicket;
    @ApiModelProperty("下一张联票号")
    private String nextTicket;
}
