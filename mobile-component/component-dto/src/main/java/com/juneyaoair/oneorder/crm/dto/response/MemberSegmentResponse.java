package com.juneyaoair.oneorder.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName MemberSegmentResponse
 * @Description CRM航段明细查询请求参数响应结果
 * <AUTHOR>
 * @Date 2020/3/17 15:33
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberSegmentResponse {

    /**
     * 航段集合
     */
    private List<Segments> Segments;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Segments {

        /**
         * 来源
         * FlightActivity：航空活动累积；
         * Adjust：航段调整
         */
        private String Source;

        /**
         * 累积航段数（航段调整时可能为负数）
         */
        private int Number;

        /**
         * 航段信息（Source为FlightActivity时有值）
         */
        private SegmentInfo SegmentInfo;

        /**
         * 备注（航段调整描述）
         */
        private String Comments;

        /**
         * 累积日期
         */
        private Date ConfirmDate;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public class SegmentInfo {

            /**
             * 到达机场三字码（SHA）
             */
            private String Destination;
            /**
             * 到达机场名称（上海虹桥）
             */
            private String DestinationName;
            /**
             * 航班日期
             */
            private String FlightDate;
            /**
             * 市场航司二字码（HO）
             */
            private String AirlineCode;
            /**
             * 市场航班号（1102）
             */
            private String FlightNumber;
            /**
             * 市场航班号后缀
             */
            private String FlightSuffix;
            /**
             * 承运航司二字码（CA）
             */
            private String OperateAirlineCode;
            /**
             * 承运航班号（1009）
             */
            private String OperateFlightNumber;
            /**
             * 承运航班号后缀
             */
            private String OperateFlightSuffix;
            /**
             * 出发机场三字码（PEX）
             */
            private String Origination;
            /**
             * 出发机场名称（北京大兴机场）
             */
            private String OriginationName;

        }

    }





}
