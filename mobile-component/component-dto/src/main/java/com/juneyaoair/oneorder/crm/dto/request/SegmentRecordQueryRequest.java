package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SegmentRecordQueryRequest
 * @Description 航段明细请求体
 * <AUTHOR>
 * @Date 2023/9/11 18:02
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SegmentRecordQueryRequest {
    @ApiModelProperty(value = "开始时间 若不传 则默认查结束时间往前一年内的累计航段")
    private String startDate;

    @ApiModelProperty(value = "结束时间 若不传 则默认为当前时间")
    private String endDate;

}
