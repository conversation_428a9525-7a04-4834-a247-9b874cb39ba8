package com.juneyaoair.oneorder.order.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by yaocf on 2018/7/23  10:46.
 * CRM证件类型
 * 证件类型顺序不可随意变动，保证NI,PP在前，其他证件在最后
 */
public enum CertificateTypeEnum {
    ID_CARD(1, "ID_CARD", "身份证", "NI", "NI"),
    PASSPORT(2, "PASSPORT", "因私普通护照", "PP", "P"),
    TAIWAN_MTP(3, "TAIWAN_MTP", "台湾居民来往大陆通行证", "MTP", "A"),
    HK_MACAO_MTP(4, "HK_MACAO_MTP", "港澳居民来往内地通行证", "ORI", "A"),
    HK_MACAO_PASS(5, "HK_MACAO_PASS", "港澳通行证", "HTPP", "A"),
    FOREIGNER_ID_CARD(6, "FOREIGNER_ID_CARD", "外国人永久居留身份证", "NIPP", "F"),
    MD_CARD(7, "MD_CARD", "残疾军人证", "GM", "NI"),
    PD_CARD(8, "PD_CARD", "残疾人民警察证", "JC", "NI"),
    OFFICER_CARD(9, "OFFICER_CARD", "军方人员证件", "MIL", "M"),
    HMT_ID_CARD(10, "HM_ID_CARD", "港澳居民居住证", "HMT", "NI"),
    TW_ID_CARD(11, "TW_ID_CARD", "台湾居民居住证", "TIC", "NI"),
    TPP(12, "TPP", "大陆居民往来台湾通行证", "TPP", "A"),
    DP(13, "DP", "驻华外交人员证", "DP", "C"),
    SAPP(14, "SAPP", "海员证", "SAPP", "G"),
    AP(15, "AP", "武警人员证件", "AP", "M"),
    BC(16, "BC", "出生医学证明", "BC", "I"),
    TI(17, "TI", "户口簿/ 临时身份证明", "TI", "I"),
    BP(18, "BUSINESS_PASSPORT", "公务护照", "BP", "P"),
    OTHER(99, "OTHER", "其他", "CC", "F"),
    UNKNOW(0, "UNKNOW", "未知", "CC", "F"),
    //以下证件会员暂时无对应的，先列出具体证件
    CCM(-1, "CCM", "文职干部证", "CCM", "M"),
    CSM(-1, "CSM", "义务兵证", "CSM", "M"),
    SCM(-1, "SCM", "士官证", "SCM", "M"),
    CPM(-1, "CPM", "文职人员证", "CPM", "M"),
    EIDM(-1, "EIDM", "职工证", "EIDM", "M"),
    APOM(-1, "APOM", "武警警官证", "APOM", "M"),
    APSM(-1, "APSM", "武警士兵证", "APSM", "M"),
    SID(-1, "SID", "十六周岁以下所持学生证", "SID", "I"),
    UU(-1, "UU", "特殊身份证、无法识别证件（其他）", "UU", "UU"),

    ;

    private int code;  //证件代码
    private String eName; //会员系统证件类型
    private String desc; //描述
    private String showCode;//统一订单证件类型 前端证件类型编码

    /**
     * 航信对应编码
     */
    private String hxCode;

    CertificateTypeEnum(int code, String eName, String desc, String showCode, String hxCode) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
        this.showCode = showCode;
        this.hxCode = hxCode;
    }

    public static CertificateTypeEnum checkType(int code) {
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            if (c.code == code) {
                return c;
            }
        }
        return null;
    }

    public static CertificateTypeEnum checkName(String eName) {
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            if (c.eName.equals(eName)) {
                return c;
            }
        }
        return null;
    }

    public static CertificateTypeEnum checkShowCode(String showCode) {
        // 前端及统一订单传入的军官证类型为MP，CRM为MIL
        if (showCode.equals("MP")) {
            return CertificateTypeEnum.OFFICER_CARD;
        }
        // 前端及统一订单传入的台湾居民居住证类型为TWP，CRM为TIC
        if (showCode.equals("TWP")) {
            return CertificateTypeEnum.TW_ID_CARD;
        }
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            if (c.showCode.equals(showCode)) {
                return c;
            }
        }
        return null;
    }

    public static int getCodeByENAME(String certificateType) {
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            if (c.eName.equals(certificateType)) {
                return c.code;
            }
        }
        return 0;
    }

    public static List<String> getShowCodeList() {
        List<String> showCodeList = new ArrayList<>();
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            showCodeList.add(c.showCode);
        }
        showCodeList.add("MP");
        showCodeList.add("TWP");
        return showCodeList;
    }

    /**
     * 排除CC的获取
     * @return
     */
    public static List<String> getShowCodeListExcludeCC() {
        List<String> showCodeList = new ArrayList<>();
        for (CertificateTypeEnum c : CertificateTypeEnum.values()) {
            if (!CertificateTypeEnum.OTHER.showCode.equals(c.showCode)) {
                showCodeList.add(c.showCode);
            }
        }
        showCodeList.add("MP");
        showCodeList.add("TWP");
        return showCodeList;
    }

    public int getCode() {
        return code;
    }

    public String geteName() {
        return eName;
    }

    public String getDesc() {
        return desc;
    }

    public String getShowCode() {
        return showCode;
    }

    public String getHxCode() {
        return hxCode;
    }
}
