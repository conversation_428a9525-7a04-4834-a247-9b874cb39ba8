package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2018/7/23  10:38
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtMemberDetailRequest {
   @JsonProperty(value = "CardNO")
   public String CardNO;  //会员卡号
   @JsonProperty(value = "CertificateType")
   public String CertificateType; //证件类型
   @JsonProperty(value = "CertificateNumber")
   public String CertificateNumber;  //证件号码
   @JsonProperty(value = "Mobile")
   public String Mobile;  //手机号码
   @JsonProperty(value = "Email")
   public String Email;  //邮箱地址
   @JsonProperty(value = "RequestItems")
   public String[] RequestItems; //请求项（不传返回所有项）
}
