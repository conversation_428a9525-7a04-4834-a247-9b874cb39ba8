package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求后端地址参数
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-12-13 17:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PtMemberAddressReq {
    private Integer RecordID; // 记录ID
    private Integer AddressType; // 地址类型
    private String PostCode; // 邮政编码
    private String CountryCode; // 国家代码
    private String ProvinceCode; // 省代码
    private String CityCode; // 城市代码
    private String Address; // 地址
    private boolean IsNormal; // 是否为常用地址
    private String Remark; // 备注
    private String Receiver; // 收件人
    private String ReceiverMobile; // 收件人手机号

}
