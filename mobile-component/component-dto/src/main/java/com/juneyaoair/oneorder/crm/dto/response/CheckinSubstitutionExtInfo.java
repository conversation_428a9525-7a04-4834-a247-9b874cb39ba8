package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 预留登机牌特有属性
 * @Date 14:32 2023/6/19
 * @return null
 **/

@Data
public class CheckinSubstitutionExtInfo {
    @SerializedName("MinMinutesAheadTakeoff")
    @ApiModelProperty(value = "最少提前分钟数")
    private int minMinutesAheadTakeoff;

    @SerializedName("MaxMinutesAheadTakeoff")
    @ApiModelProperty(value = "最大提前分钟数")
    private int maxMinutesAheadTakeoff;
}
