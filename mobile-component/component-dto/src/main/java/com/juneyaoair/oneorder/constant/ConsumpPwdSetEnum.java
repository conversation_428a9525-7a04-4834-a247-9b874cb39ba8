package com.juneyaoair.oneorder.constant;


/**
 * <AUTHOR>
 * @Description 消费密码修改类型枚举
 * @Date 8:01 2023/10/11
 **/
public enum ConsumpPwdSetEnum {
    FSET("FSET","首次设置消费密码"),
    RSET("RSET","重置消费密码")
    ;

    private final String code;
    private final String desc;


    ConsumpPwdSetEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
