package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageRecordQueryResponse
 * @Description 积分明细查询相应参数
 * <AUTHOR>
 * @Date 2019/4/9 18:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MileageRecordQueryResponse {
    @ApiModelProperty(value = "积分变动数 +表示增加，-表示减少")
    private String miles;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "记录日期 yyyy-MM-dd")
    private String recordDate;

    @ApiModelProperty(value = "失效日期 yyyy-MM-dd")
    private String expireDate;

    @ApiModelProperty(value = "备注")
    private String remark;



}
