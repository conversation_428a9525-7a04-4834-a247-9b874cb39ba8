package com.juneyaoair.oneorder.crm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2018/7/20  10:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PtApiCRMRequest<T> {
    @JsonProperty(value = "Data")
    private T Data;
    @JsonProperty(value = "Channel")
    private String Channel;
    @JsonProperty(value = "ChannelPwd")
    private String ChannelPwd;
    @JsonProperty(value = "Operator")
    private String Operator;
    @JsonProperty(value = "Header")
    private Header Header;
    @JsonProperty(value = "requestId")
    private String requestId;
}
