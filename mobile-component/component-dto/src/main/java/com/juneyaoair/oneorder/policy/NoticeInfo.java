package com.juneyaoair.oneorder.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 隐私政策信息
 * @date 2019/8/9  15:19.
 */
@Data
@ApiModel(value = "NoticeInfo",description = "条款明细")
public class NoticeInfo {
    @ApiModelProperty(value = "icon图标")
    private String iconUrl;
    @ApiModelProperty(value = "条款名称")
    private String title;
    @ApiModelProperty(value = "条款地址")
    private String url;
    @ApiModelProperty(value = "富文本文件ID")
    private String ntInfoId;
    @ApiModelProperty(value = "条款排序")
    private int orderNum;
    @ApiModelProperty(value = "最后一次更新的时间戳")
    private Long updateTimestamp;
}
