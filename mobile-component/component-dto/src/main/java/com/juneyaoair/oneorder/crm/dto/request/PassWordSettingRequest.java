package com.juneyaoair.oneorder.crm.dto.request;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName PassWordSettingRequest
 * @Description 设置消费密码请求体
 * <AUTHOR>
 * @Date 2024/8/7 8:26
 * @Version 1.0
 */
public class PassWordSettingRequest {

    /**
     * <AUTHOR>
     * @Description 验证码
     * @Date 8:30 2024/8/7
     **/
    @NotEmpty(message = "短信验证码不可为空")
    private String verifyCode;

    /**
     * <AUTHOR>
     * @Description 消费密码
     * @Date 8:30 2024/8/7
     **/
    @NotEmpty(message = "消费密码不可为空")
    private String consumePwd;

    /**
     * <AUTHOR>
     * @Description 确认消费密码
     * @Date 8:30 2024/8/7
     **/
    @NotEmpty(message = "确认消费密码不可为空")
    private String confirmedPwd;

    public PassWordSettingRequest() {
    }

    public PassWordSettingRequest(String verifyCode, String consumptionPassWord, String confirmedConsumptionPwd) {
        this.verifyCode = verifyCode;
        this.consumePwd = consumptionPassWord;
        this.confirmedPwd = confirmedConsumptionPwd;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getConsumePwd() {
        return consumePwd;
    }

    public void setConsumePwd(String consumePwd) {
        this.consumePwd = consumePwd;
    }

    public String getConfirmedPwd() {
        return confirmedPwd;
    }

    public void setConfirmedPwd(String confirmedPwd) {
        this.confirmedPwd = confirmedPwd;
    }
}
