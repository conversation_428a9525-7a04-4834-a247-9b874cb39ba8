package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "AvailCouponsResponse",description = "优惠券响应")
public class AvailCouponsResponse {
    @ApiModelProperty(value = "结果代码")
    private String ResultCode;

    @ApiModelProperty(value = "结果描述")
    private String ErrorInfo;

    @ApiModelProperty(value = "可用优惠券列表")
    private List<AvailCoupon> AvailCouponList;

    @ApiModelProperty(value = "不可使用优惠券列表")
    private List<AvailCoupon> unusableCouponList;
}
