package com.juneyaoair.oneorder.common.dto.enums;

/**
 * @ClassName AliPayAuthStatusEnum
 * @Description AliPay认证状态枚举类
 * <AUTHOR>
 * @Date 2024/7/18 8:55
 * @Version 1.0
 */
public enum AliPayAuthStatusEnum {
    AUTHED("AUTHED", "已认证"),
    NON_AUTHED("NON_AUTHED", "未认证"),

    AUTHED_FAILED("AUTHED_FAILED", "认证失败");

    private final String code;
    private final String desc;

    AliPayAuthStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
