package com.juneyaoair.oneorder.flight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/20 9:44
 */
@Data
@ApiModel(value = "FlightNotice",description = "航班通知请求体")
public class FlightNotice {
    @ApiModelProperty(value = "起始地城市三字码",required = true)
    @NotBlank(message = "起始地城市不能为空")
    private String depCity;
    @ApiModelProperty(value = "目的地城市三字码",required = true)
    @NotBlank(message = "目的地城市不能为空")
    private String arrCity;
    @ApiModelProperty(value = "航班日期yyyy-MM-dd")
    private String flightDate;
    @ApiModelProperty(value = "返程航班日期yyyy-MM-dd")
    private String backFlightDate;
}
