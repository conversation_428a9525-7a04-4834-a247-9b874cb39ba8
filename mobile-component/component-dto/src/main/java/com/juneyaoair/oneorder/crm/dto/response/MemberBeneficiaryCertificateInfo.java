package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 积分受益人证件信息
 * @Date 15:13 2023/8/22
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberBeneficiaryCertificateInfo {

    @NotBlank(message="证件类型不能为空")
    @ApiModelProperty(value = "证件类型")
    private String ctype;

    @ApiModelProperty(value = "证件号")
    private String cnumber;

    @ApiModelProperty(value = "签发国 国家二字码")
    private String signingCountry;

    @ApiModelProperty(value = "签发国名称")
    private String signingCountryName;

    @ApiModelProperty(value = "过期时间 yyyy-MM-dd")
    private String expireDate;

    @ApiModelProperty(value = "过期描述")
    private String expireDesc;

    @ApiModelProperty(value = "是否已过期 true-已过期")
    @JsonProperty(value = "isExpired")
    private boolean isExpired;

}
