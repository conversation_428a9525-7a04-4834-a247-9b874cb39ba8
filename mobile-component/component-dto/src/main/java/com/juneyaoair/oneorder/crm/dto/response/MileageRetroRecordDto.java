package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageRetroRecordDto
 * @Description
 * <AUTHOR>
 * @Date 2023/9/12 8:02
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageRetroRecordDto {
    @ApiModelProperty(value = "定级积分")
    private Integer ClubMiles;

    @ApiModelProperty(value = "目的地")
    private String Destination;

    @ApiModelProperty(value = "航班日期")
    private String FlightDate;

    @ApiModelProperty(value = "市场航司")
    private String MarketAirlineCode;

    @ApiModelProperty(value = "市场航班号")
    private String MarketFlightNumber;

    @ApiModelProperty(value = "市场航班号后缀")
    private String MarketFlightSuffix;

    @ApiModelProperty(value = "会员卡号")
    private String MemberCardNo;

    @ApiModelProperty(value = "会员Id ")
    private Integer MemberId;

    @ApiModelProperty(value = "承运航司")
    private String OperateAirlineCode;

    @ApiModelProperty(value = "操作时间")
    private String OperateDate;

    @ApiModelProperty(value = "承运航班号 ")
    private String OperateFlightNumber;

    @ApiModelProperty(value = "承运航班号后缀")
    private String OperateFlightSuffix;

    @ApiModelProperty(value = "操作者")
    private String OperateUserId;

    @ApiModelProperty(value = "始发地")
    private String Origination;

    @ApiModelProperty(value = "异常原因")
    private String ReasonCode;

    @ApiModelProperty(value = "记录Id")
    private Integer RecordId;

    @ApiModelProperty(value = "记录来源")
    private Integer RecordSource;

    @ApiModelProperty(value = "定级航段")
    private int Segment;

    @ApiModelProperty(value = "乘机记录来源 = ['MAN', 'MOT']")
    private String SendingSourceCode;

    @ApiModelProperty(value = "最后操作时间")
    private String UpdateDate;

    @ApiModelProperty(value = "最后操作者")
    private String UpdateUserId;

    @ApiModelProperty(value = "验证状态")
    private String VerifyState;

}
