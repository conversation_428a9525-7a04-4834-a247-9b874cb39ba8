package com.juneyaoair.oneorder.crm.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MemberRightsResponse
 * @Description 会员可用权益响应（普通权益）
 * <AUTHOR>
 * @Date 2019/8/1 15:17
 **/
@Data
@NoArgsConstructor
public class MemberRightsResponse {

    @ApiModelProperty(value = "会员等级编码")
    private String memberLevelCode;

    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;

    @ApiModelProperty(value = "水印")
    private String watermark;

    @ApiModelProperty(value = "排序")
    private Integer serialNumber;

    @ApiModelProperty(value = "权益列表")
    private List<MemberRightsDTOView> memberRights;

}
