package com.juneyaoair.oneorder.flight.dto;

import com.juneyaoair.oneorder.common.dto.BizDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 航班动态请求参数
 * @date 2023/7/13 9:20
 */
@Data
@ApiModel(value = "FlightDynamicParam",description = "航班动态请求参数")
public class FlightDynamicParam extends BizDto {
    @NotBlank(message = "航班查询类型不可为空")
    @ApiModelProperty(value = "航班查询类型，airline-按航线查询，flightNo-按航班号查询",example = "airline/flightNo",allowableValues = "airline,flightNo",notes = "airline-按航线查询，flightNo-按航班号查询")
    private String queryType;
    @NotBlank(message = "航班日期不可为空")
    @ApiModelProperty(value = "航班日期",notes = "日期格式：yyyy-MM-dd",example = "2023-11-16")
    private String flightDate;
    @ApiModelProperty(value = "航班号，flightNo查询时不可为空",notes = "flightNo查询时不可为空",example = "HO1111")
    private String flightNo;
    @ApiModelProperty(value = "出发城市，airline查询时不可为空",notes = "airline查询时不可为空",example = "SHA")
    private String depCityCode;
    @ApiModelProperty(value = "到达城市，airline查询时不可为空",notes = "airline查询时不可为空",example = "SYX")
    private String arrCityCode;
    @ApiModelProperty(value = "出发机场",hidden = true)
    private String depAirport;
    @ApiModelProperty(value = "到达机场",hidden = true)
    private String arrAirport;
}
