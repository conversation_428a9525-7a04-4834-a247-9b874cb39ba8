package com.juneyaoair.oneorder.airtransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 接送机航班信息
 * @created 2024/3/28 9:30
*/
@Data
public class AirTransferFlightInfo {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "出发机场三字码")
    private String depAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

    @ApiModelProperty(value = "出发机场名")
    private String depAirportName;

    @ApiModelProperty(value = "到达机场名")
    private String arrAirportName;

    @ApiModelProperty(value = "出发城三字码")
    private String depCityCode;

    @ApiModelProperty(value = "到达城三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "出发城市名")
    private String depCityName;

    @ApiModelProperty(value = "到达城市名")
    private String arrCityName;

    @ApiModelProperty(value = "机型名称")
    private String planeTypeName;

    @ApiModelProperty(value = "起飞时间 HH:MM")
    private String depDateTime;

    @ApiModelProperty(value = "到达时间 HH:MM")
    private String arrDateTime;

    @ApiModelProperty(value = "出发航站楼")
    private String depAirportTerminal;

    @ApiModelProperty(value = "到达航站楼")
    private String arrAirportTerminal;

    @ApiModelProperty(value = "飞行时长 单位：分钟")
    private Integer duration;

    @ApiModelProperty(value = "公司图标")
    private String airIcon;

    /**
     * 判断该航班是否在供应商网络内，
     */
    @ApiModelProperty(value = "航班是否在供应商网络内")
    private Boolean isInn;

    /**
     * 航线类型:I-国际,D-国内
     */
    private String AirlineType;
}
