package com.juneyaoair.oneorder.order.dto;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "PassengerInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtPassengerInfo {
	private int PassengerID; //乘客ID保存在统一订单数据库中的ID
	private int PassengerNO; //乘客序号第一位乘客为0开始，第二位为1，依次增加1
	private String PassengerName; //乘客姓名
	private String PassengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
	private String PassengerIdentity; //乘客身份VIP，教师，警殘，革伤  留学生（STU）
	private String PassengerIdentityUrl; //乘客身份资质证明url
	private String CertType; //证件类型身份证:NI,护照:PP,其它证件:CC
	private String CertNo; //证件号码
	private String FfCardNo; //常客卡号HO+卡号
	private String CountryTelCode; //手机号国际区号默认值86
	private String HandphoneNo; //手机号
	private double TicketPrice; //票面价为Price.RSP销售参考价加总
	private double PricePaid; //实际支付票价当有积分支付时的实际支Nationality付票价是Price.PriceValue - 积分抵用金额
	private double YQTax; //燃油费
	private double CNTax; //建设税
	private double OtherTax; //其它税费
	private double QFee; //Q费
	private String AdtNameToInf; //婴儿绑定的成人姓名如果乘客类型为婴儿，则必填
	private String Birthdate; //出生日期yyyy-MM-dd儿童、婴儿和国际票必填
	private String Sex; //性别国际票必填
	private String Nationality; //国籍国际票必填
	private String BelongCountry; //发证国国际票必填
	private String CertValidity; //证件有效期yyyy-MM-dd国际票必填
	private String IsSaveCommon;
	private String IsBuyInsurance; //是否购买保险
	private List<PtInsuranceInfo> InsuranceList; //保险信息列表
	private String SaCardNo;//星盟卡号 XX+卡号
	//2017-12-15 新增
	private String GjCertType;//军警证件类型  //选择军警证件类时使用 NI
	private String GjCertNo;//军警证件号码
	private String IsMeal;//是否选择餐食  Y-购买   N-未购买
	private List<PtMealProduct> MealProductList;//餐食信息列表  选择时只能按人航段，如果两个说明，按顺序为两个航段
	private List<PtWeightProduct> WeightProductList;//逾重行李列表 选择时只能按人航段，如果两个说明，按顺序为两个航段
	private List<QSegmentInfo> QSegmentInfoList; //航段Q费

	private boolean IsUseScore; //是否使用积分 true - 使用,false - 不使用
	private boolean IsGiftScore; //是否赠送积分 true - 赠送,false - 不赠送
	private List<PtScoreUse> ScoreUseInfoList; //积分使用信息列表
	private PtScoreGift ScoreGiftInfo; //积分赠送信息
	private List<InternatTaxInfo> OtherTaxList;//国际税费信息列表
	private String UnlimitedFlyCardNo;// 无限畅飞卡卡号


	/*
		2021-11-19
	 */
	private String TicketNo;//客票号
	private String Pnr;//订单号
	/**
	 * 产品卡号
	 */
	private String VoucherNo;
	/**
	 * 产品类型
	 */
	private String VoucherType;
	/**
	 * 主题卡时必填：具体主题卡代码
	 */
	private String VoucherResource;


	public String getSaCardNo() {
		return SaCardNo;
	}

	public void setSaCardNo(String saCardNo) {
		SaCardNo = saCardNo;
	}

	public int getPassengerNO() {
		return PassengerNO;
	}
	public void setPassengerNO(int passengerNO) {
		PassengerNO = passengerNO;
	}
	public String getPassengerName() {
		return PassengerName;
	}
	public void setPassengerName(String passengerName) {
		PassengerName = passengerName;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public String getPassengerIdentity() {
		return PassengerIdentity;
	}
	public void setPassengerIdentity(String passengerIdentity) {
		PassengerIdentity = passengerIdentity;
	}
	public String getCertType() {
		return CertType;
	}
	public void setCertType(String certType) {
		CertType = certType;
	}
	public String getCertNo() {
		return CertNo;
	}
	public void setCertNo(String certNo) {
		CertNo = certNo;
	}
	public String getFfCardNo() {
		return FfCardNo;
	}
	public void setFfCardNo(String ffCardNo) {
		FfCardNo = ffCardNo;
	}
	public String getCountryTelCode() {
		return CountryTelCode;
	}
	public void setCountryTelCode(String countryTelCode) {
		CountryTelCode = countryTelCode;
	}
	public String getHandphoneNo() {
		return HandphoneNo;
	}
	public void setHandphoneNo(String handphoneNo) {
		HandphoneNo = handphoneNo;
	}
	public double getTicketPrice() {
		return TicketPrice;
	}
	public void setTicketPrice(double ticketPrice) {
		TicketPrice = ticketPrice;
	}
	public double getPricePaid() {
		return PricePaid;
	}
	public void setPricePaid(double pricePaid) {
		PricePaid = pricePaid;
	}
	public double getYQTax() {
		return YQTax;
	}
	public void setYQTax(double yQTax) {
		YQTax = yQTax;
	}
	public double getCNTax() {
		return CNTax;
	}
	public void setCNTax(double cNTax) {
		CNTax = cNTax;
	}
	public double getOtherTax() {
		return OtherTax;
	}
	public void setOtherTax(double otherTax) {
		OtherTax = otherTax;
	}
	public double getQFee() {
		return QFee;
	}
	public void setQFee(double qFee) {
		QFee = qFee;
	}
	public String getAdtNameToInf() {
		return AdtNameToInf;
	}
	public void setAdtNameToInf(String adtNameToInf) {
		AdtNameToInf = adtNameToInf;
	}
	public String getBirthdate() {
		return Birthdate;
	}
	public void setBirthdate(String birthdate) {
		Birthdate = birthdate;
	}
	public String getSex() {
		return Sex;
	}
	public void setSex(String sex) {
		Sex = sex;
	}
	public String getNationality() {
		return Nationality;
	}
	public void setNationality(String nationality) {
		Nationality = nationality;
	}
	public String getBelongCountry() {
		return BelongCountry;
	}
	public void setBelongCountry(String belongCountry) {
		BelongCountry = belongCountry;
	}
	public String getCertValidity() {
		return CertValidity;
	}
	public void setCertValidity(String certValidity) {
		CertValidity = certValidity;
	}
	public String getIsBuyInsurance() {
		return IsBuyInsurance;
	}
	public void setIsBuyInsurance(String isBuyInsurance) {
		IsBuyInsurance = isBuyInsurance;
	}
	public List<PtInsuranceInfo> getInsuranceList() {
		return InsuranceList;
	}
	public void setInsuranceList(List<PtInsuranceInfo> insuranceList) {
		InsuranceList = insuranceList;
	}

	public String getIsSaveCommon() {
		return IsSaveCommon;
	}

	public void setIsSaveCommon(String isSaveCommon) {
		IsSaveCommon = isSaveCommon;
	}

	public String getGjCertType() {
		return GjCertType;
	}

	public void setGjCertType(String gjCertType) {
		GjCertType = gjCertType;
	}

	public String getGjCertNo() {
		return GjCertNo;
	}

	public void setGjCertNo(String gjCertNo) {
		GjCertNo = gjCertNo;
	}

	public String getIsMeal() {
		return IsMeal;
	}

	public void setIsMeal(String isMeal) {
		IsMeal = isMeal;
	}

	public List<PtMealProduct> getMealProductList() {
		return MealProductList;
	}

	public void setMealProductList(List<PtMealProduct> mealProductList) {
		MealProductList = mealProductList;
	}

	public List<PtWeightProduct> getWeightProductList() {
		return WeightProductList;
	}

	public void setWeightProductList(List<PtWeightProduct> weightProductList) {
		WeightProductList = weightProductList;
	}

	public int getPassengerID() {
		return PassengerID;
	}

	public void setPassengerID(int passengerID) {
		PassengerID = passengerID;
	}

	public List<QSegmentInfo> getQSegmentInfoList() {
		return QSegmentInfoList;
	}

	public void setQSegmentInfoList(List<QSegmentInfo> QSegmentInfoList) {
		this.QSegmentInfoList = QSegmentInfoList;
	}

	public boolean isUseScore() {
		return IsUseScore;
	}

	public void setUseScore(boolean useScore) {
		IsUseScore = useScore;
	}

	public boolean isGiftScore() {
		return IsGiftScore;
	}

	public void setGiftScore(boolean giftScore) {
		IsGiftScore = giftScore;
	}

	public List<PtScoreUse> getScoreUseInfoList() {
		return ScoreUseInfoList;
	}

	public void setScoreUseInfoList(List<PtScoreUse> scoreUseInfoList) {
		ScoreUseInfoList = scoreUseInfoList;
	}

	public PtScoreGift getScoreGiftInfo() {
		return ScoreGiftInfo;
	}

	public void setScoreGiftInfo(PtScoreGift scoreGiftInfo) {
		ScoreGiftInfo = scoreGiftInfo;
	}

	public List<InternatTaxInfo> getOtherTaxList() {
		return OtherTaxList;
	}

	public void setOtherTaxList(List<InternatTaxInfo> otherTaxList) {
		OtherTaxList = otherTaxList;
	}

	public String getUnlimitedFlyCardNo() {
		return UnlimitedFlyCardNo;
	}

	public void setUnlimitedFlyCardNo(String unlimitedFlyCardNo) {
		UnlimitedFlyCardNo = unlimitedFlyCardNo;
	}

	public String getPassengerIdentityUrl() {
		return PassengerIdentityUrl;
	}

	public void setPassengerIdentityUrl(String passengerIdentityUrl) {
		PassengerIdentityUrl = passengerIdentityUrl;
	}
    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getPnr() {
        return Pnr;
    }

    public void setPnr(String pnr) {
        Pnr = pnr;
    }

	public String getVoucherNo() {
		return VoucherNo;
	}

	public void setVoucherNo(String voucherNo) {
		VoucherNo = voucherNo;
	}

	public String getVoucherType() {
		return VoucherType;
	}

	public void setVoucherType(String voucherType) {
		VoucherType = voucherType;
	}

	public String getVoucherResource() {
		return VoucherResource;
	}

	public void setVoucherResource(String voucherResource) {
		VoucherResource = voucherResource;
	}
}
