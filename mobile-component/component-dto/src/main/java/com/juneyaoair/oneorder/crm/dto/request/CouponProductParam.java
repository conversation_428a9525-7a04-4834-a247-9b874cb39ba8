package com.juneyaoair.oneorder.crm.dto.request;

import com.juneyaoair.oneorder.page.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 查看权益券
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CouponProductParam extends PageParam {

    @ApiModelProperty(value = "Coupon:权益劵 Card:权益卡")
    private String ruleModel;

    @NotBlank(message = "权益券类型不能为空")
    @ApiModelProperty(value = "权益券类型")
    private String voucherTypes;

    @ApiModelProperty(value = "权益券状态（默认:Not）可使用:Not 已使用:Used 已过期:Expired")
    private List<String> couponState;

    @ApiModelProperty(value = "权益券状态（默认:Not）可使用:Not 已使用:Used 已过期:Expired")
    private String availableStatus;

    @Valid
    @ApiModelProperty(value = "使用限定条件")
    private CouponProductCondition couponProductCondition;

}
