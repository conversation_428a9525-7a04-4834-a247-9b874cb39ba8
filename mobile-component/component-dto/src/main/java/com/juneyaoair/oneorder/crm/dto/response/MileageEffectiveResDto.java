package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MileageEffectiveResDto
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 9:50
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageEffectiveResDto {
    @JsonProperty(value = "EffectiveList")
    private List<MileageEffective> EffectiveList;

    @JsonProperty(value = "Total")
    private Integer Total;//总数
}
