package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageRetroRecordQueryReqDto
 * @Description 会员积分补登信息查询请求体
 * <AUTHOR>
 * @Date 2023/9/12 8:33
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageRetroRecordQueryReqDto {
    @ApiModelProperty(value = "会员ID")
    @JsonProperty(value = "MemberId")
    private Integer MemberId;
}
