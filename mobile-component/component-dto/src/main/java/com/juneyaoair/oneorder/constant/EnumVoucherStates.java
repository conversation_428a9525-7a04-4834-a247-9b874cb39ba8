package com.juneyaoair.oneorder.constant;

/**
 * 产品管理平台状态对接
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/03/01
 */
public enum EnumVoucherStates {
    /// 可使用
    Available("Available", "Not"),
    /// 已过期
    Expired("Expired", "Overdue"),
    /// 已退款
    Refunded("Refunded", "Refund"),
    // 未生效
    Inactivate("Inactivate", "Apply"),
    // 已使用
    Used("Used", "Used"),
    // 已核销
    WittenOff("WittenOff", "WittenOff"),
    // 已取消
    Canceled("Canceled", "Cancel"),
    // 转赠中
    Giving("Giving", "Giving"),
    // 已预约
    Appointment("Appointment", "Appointment"),
    // 已转赠
    GiveAway("GiveAway", "GiveAway"),
    ;

    private final String code;
    private final String orderCode;

    @Override
    public String toString() {
        return orderCode;
    }

    public String getCode() {
        return code;
    }

    public String getOrderCode() {
        return orderCode;
    }

    EnumVoucherStates(String code, String orderCode) {
        this.code = code;
        this.orderCode = orderCode;
    }

    public static String getOrderCodeByCode(String code) {
        for (EnumVoucherStates e : EnumVoucherStates.values()) {
            if (e.getCode().equals(code)) {
                return e.getOrderCode();
            }
        }
        return null;
    }
}
