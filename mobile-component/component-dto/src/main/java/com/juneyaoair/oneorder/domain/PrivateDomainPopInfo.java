package com.juneyaoair.oneorder.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 私域运营弹窗提示信息
 * <AUTHOR>
 */
@Data
public class PrivateDomainPopInfo {

    @ApiModelProperty(value = "是否需要弹窗")
    private boolean pop;

    @ApiModelProperty(value = "描述")
    private String message;

    @ApiModelProperty(value="获客链接名")
    private String linkName;

    @ApiModelProperty(value="获客链接地址")
    private String linkUrl;

}
