package com.juneyaoair.oneorder.order.dto;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2;
import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.juneyaoair.oneorder.tools.utils.DateUtil.dateToString;
import static com.juneyaoair.oneorder.tools.utils.DateUtil.toDate;

@Data
public class MemberCertificateResModel {
    @ApiModelProperty(value = "证件号码")
    private String CertificateNumber;
    @ApiModelProperty(value = "证件类型")
    private String CertificateType;
    @ApiModelProperty(value = "备注")
    private String Comments;
    @ApiModelProperty(value = "客户ID")
    private Integer Id;
    @ApiModelProperty(value = "是否认证（Y：已认证）")
    private String IsIdentification;
    @ApiModelProperty(value = "操作日期")
    private String OperateDate;
    @ApiModelProperty(value = "操作员")
    private String OperateUserId;
    @ApiModelProperty(value = "记录id")
    private Integer RecordId;
    @ApiModelProperty(value = "签发国家/地区")
    private String SigningAuthority;
    @ApiModelProperty(value = "状态")
    private String Status;
    @ApiModelProperty(value = "更新日期")
    private String UpdateDate;
    @ApiModelProperty(value = "更新人")
    private String UpdateUserId;
    @ApiModelProperty(value = "证件有效期")
    private String ValidDate;
    @ApiModelProperty(value ="是否存在飞行累积记录(Y:是，N:否)")
    private String IsAccumulated;



    // 转为.net会员系统 MemberCertificateSoaModelV2
    public MemberCertificateSoaModelV2 convertToMemberCertificateSoaModelV2() {
        MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = new MemberCertificateSoaModelV2();
        memberCertificateSoaModelV2.setCertificateNumber(this.CertificateNumber);
        memberCertificateSoaModelV2.setCertificateType(CertificateTypeEnum.getCodeByENAME(this.CertificateType));
        memberCertificateSoaModelV2.setRecordId(this.RecordId);
        memberCertificateSoaModelV2.setRemark(this.Comments);
        memberCertificateSoaModelV2.setOperateDate(this.OperateDate);
        memberCertificateSoaModelV2.setUpdateDate(this.UpdateDate);
        memberCertificateSoaModelV2.setVerify("Y".equals(this.IsIdentification));
        memberCertificateSoaModelV2.setSigningAuthority(this.SigningAuthority);
        memberCertificateSoaModelV2.setValidDate(StringUtils.isNotEmpty(this.ValidDate)?
                dateToString(toDate(this.ValidDate,"yyyy-MM-dd HH:mm:ss"),"yyyy-MM-dd"):"");
        memberCertificateSoaModelV2.setIsAccumulated(this.IsAccumulated);
        return memberCertificateSoaModelV2;
    }

}
