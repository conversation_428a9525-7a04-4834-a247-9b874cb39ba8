package com.juneyaoair.oneorder.order.dto;

import com.juneyaoair.oneorder.crm.dto.response.QueryCommonPersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName ModifyPersonV2
 * @Description
 * <AUTHOR>
 * @Date 2023/9/8 17:12
 * @Version 1.0
 */
@Data
public class ModifyPersonV2 {
    @ApiModelProperty(value = "true新增，false 合并,null不处理")
    private Boolean addFlag;

    @NotNull(message = "乘客信息不能为空")
    @ApiModelProperty(value = "乘机人信息")
    private QueryCommonPersonInfo commonPersonInfo;

    @ApiModelProperty(value = "是否删除,true-删除，false/null-修改")
    private Boolean isRemove;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "主题卡,不支持修改")
    private String modified;

    @ApiModelProperty(value = "航班日期--最后一段行程日期")
    private String flightDate;
}
