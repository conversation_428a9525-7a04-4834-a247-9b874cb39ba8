package com.juneyaoair.oneorder.flight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description 会员行程客票信息
 * @created 2025/06/19 14:11
 */
@Data
public class MemberTravellerTicketInfo {

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "旅客姓名")
    private String travellerName;

    @ApiModelProperty(value = "pnrNo")
    private String pnrNo;

    @ApiModelProperty(value = "行程序号")
    private String segNo;

    @ApiModelProperty(value = "市场方舱位")
    private String marketingCabin;

    @ApiModelProperty(value = "承运方舱位")
    private String operationCabin;

    @ApiModelProperty(value = "舱位名称")
    private String cabinName;

    /**
     * 乘客类型
     * @See com.juneyaoair.cuss.enums.ENUM_SEAT_IBE_PASSENGER_TYPE
     */
    @ApiModelProperty(value = "乘客类型 ADT：成人 CHD：儿童 UCCHD/UM：无陪儿童 INF：婴儿")
    private String passengerType;

    @ApiModelProperty(value = "座位号")
    private String seatNo;

    @ApiModelProperty(value = "客票状态 C：已值机 O:客票有效 L:已登机 F:已使用 V:已作废 E：已换开 R:已退票")
    private String couponStatus;

    @ApiModelProperty(value = "SSR编码 SsrCodeEnum")
    private Set<String> ssrCodeSet;

    @ApiModelProperty(value = "行李限额 **K/*PC")
    private String baggageAllowance;

    @ApiModelProperty(value = "出生日期")
    private String birthdate;

    @ApiModelProperty(value = "上一张联票号")
    private String priorTicket;

    @ApiModelProperty(value = "下一张联票号")
    private String nextTicket;

}
