package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FamilyAccountInviteModel {
    /**
     *家庭账户id ,
     */
    @JsonProperty("FamilyAccountId")
    private Integer  FamilyAccountId;
    /**
     *邀请状态(当前会员不是主账户时有值)(待确认-Send 已过期-Expire 已撤回-Revoke 待审核-Accept 已拒绝-Reject 邀请成功-AuditPass 审核不通过-AuditReject) ,
     */
    @JsonProperty("InviteStatus")
    private String InviteStatus;
    /**
     *邀请时间(当前会员不是主账户时有值)
     */
    @JsonProperty("InviteTime")
    private String InviteTime;
    /**
     * 会员卡号
     */
    @JsonProperty("MemberId")
    private String MemberId;
    /**
     *会员姓名（已脱敏）
     */
    @JsonProperty("MemberName")
    private String MemberName;
    /**
     *家庭成员类型(M-主账号本人、P-主账户父母、S-主账户配偶、C-主账户子女、O-其他) ,
     */
    @JsonProperty("MemberType")
    private String MemberType;
    /**
     *备注（目前返回审核不通过原因，支持扩展）
     */
    @JsonProperty("Remark")
    private String Remark;
}
