package com.juneyaoair.oneorder.crm.dto.common;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/23  13:24.
 */
@Data
public class MemberCertificateSoaModelV2 {
    @SerializedName("RecordId")
    private long recordId;
    @SerializedName("CertificateType")
    private int certificateType;
    @SerializedName("CertificateNumber")
    private String certificateNumber;
    @SerializedName("Remark")
    private String remark;


    /**
     * 是否是实名认证的证件
     */
    @SerializedName("IsVerify")
    private boolean isVerify;
    @SerializedName("OperateDate")
    private String operateDate;
    @SerializedName("UpdateDate")
    private String updateDate;
    private String record;//recordId加密后得信息
    /**
     * 证件有效期
     */
    @SerializedName(value = "ValidDate")
    private String validDate;

    /**
     * 是否存在飞行累积记录(Y:是，N:否)
     */
    private String isAccumulated;

    /**
     * 签发国
     */
    @SerializedName("SigningAuthority")
    private String signingAuthority;
    /**
     * 签发国名称
     */
    private String signingAuthorityName;

}
