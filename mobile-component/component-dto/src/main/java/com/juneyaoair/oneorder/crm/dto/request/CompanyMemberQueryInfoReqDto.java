package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CompanyMemberQueryInfoReqDto
 * @Description CRM 企业会员信息查询 请求参数
 * <AUTHOR>
 * @Date 2020/6/19 17:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyMemberQueryInfoReqDto {

    /**
     * 会员id
     */
    @JsonProperty(value = "MemberId")
    private Integer MemberId;

}
