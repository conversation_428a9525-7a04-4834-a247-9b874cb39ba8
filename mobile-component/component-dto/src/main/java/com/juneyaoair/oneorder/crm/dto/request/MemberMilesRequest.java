package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberMilesRequest
 * @Description CRM积分明细查询请求参数
 * <AUTHOR>
 * @Date 2019/4/19 13:29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberMilesRequest {

    /**
     * 会员卡号
     */
    private String MemberCardNO;

    /**
     * 查询开始时间
     * yyyyMMdd
     */
    private String BeginDate;

    /**
     * 查询结束时间
     * yyyyMMdd
     */
    private String EndDate;

    /**
     * 操作员
     */
    private String ClientCode;

    /**
     * ClientCode+ MemberCardNO+Key(Md5加密)
     */
    private String Signature;

}
