package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberAddressInfo
 * @Description 会员地址信息（会员编辑用）
 * <AUTHOR>
 * @Date 2023/9/6 8:57
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberAddressInfo {

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "省/州")
    private String province;

    @ApiModelProperty(value = "城县")
    private String town;

    @ApiModelProperty(value = "地址/信息号")
    private String detailAddress;

    @ApiModelProperty(value = "邮编")
    private String zipCode;

    @ApiModelProperty(value = "地址记录ID")
    private String record;

}
