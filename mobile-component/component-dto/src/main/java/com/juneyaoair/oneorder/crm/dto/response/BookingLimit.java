package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.oneorder.order.dto.DateLimit;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 优惠券限制字段
 * @Date 14:34 2023/6/19
 * @return null
 **/

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingLimit {
    @SerializedName("FlightType")
    @ApiModelProperty(value = "航程类型 OW:限单程 RT:限往返（含缺口程） 为空代表不限制")
    private String flightType;

    @SerializedName("PassengerType")
    @ApiModelProperty(value = "旅客类型 ADT:限成人 CHD:限儿童 INF:限婴儿 多个以逗号分隔 为空代表不限制")
    private String passengerType;

    @SerializedName("MinAdults")
    @ApiModelProperty(value = "最小预定成人数，为0代表不限制")
    private int minAdults;

    @SerializedName("MaxAdults")
    @ApiModelProperty(value = "最大预定成人数，为0代表不限制")
    private int maxAdults;

    @SerializedName("MinTicketPrice")
    @ApiModelProperty(value = "最低票面价，为空代表不限制")
    private double minTicketPrice;

    @SerializedName("MaxTicketPrice")
    @ApiModelProperty(value = "最高票面价，为空代表不限制")
    private double maxTicketPrice;

    @SerializedName("SelfUse")
    @ApiModelProperty(value = "本人使用限制 0:不限制 1：限本人使用")
    private int selfUse;

    @SerializedName("RebateBase")
    @ApiModelProperty(value = "折扣基础价格 Y:经济舱全价票面价为基础 为空表示当前舱位票面价为基础")
    private String rebateBase;

    @SerializedName("购买日期限制 为空即无限制")
    @ApiModelProperty(value = "票价")
    private List<DateLimit> bookingDates;

    @SerializedName("出行日期限制 为空即无限制")
    @ApiModelProperty(value = "票价")
    private List<DateLimit> travelDates;

    @SerializedName("FlightRoutes")
    private List<BlackList> flightRoutes;

    @SerializedName("FlightNos")
    @ApiModelProperty(value = "航班号")
    private BlackList flightNos;

    @SerializedName("Cabins")
    @ApiModelProperty(value = "仓位")
    private BlackList cabins;

    @SerializedName("FlightDates")
    @ApiModelProperty(value = "航班日期范围")
    private List<DateLimit> flightDates;

}
