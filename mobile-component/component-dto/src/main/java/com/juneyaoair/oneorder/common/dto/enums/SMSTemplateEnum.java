package com.juneyaoair.oneorder.common.dto.enums;

import com.alibaba.fastjson2.JSONArray;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;

/**
 * 短信模板枚举
 */
public enum SMSTemplateEnum {
    SMS000194(new JSONArray(LanguageEnum.ZH_CN),new JSONArray(CaptchaFuncEnum.REGISTER,CaptchaFuncEnum.LOGIN,CaptchaFuncEnum.RESET_LOGIN,CaptchaFuncEnum.THIRD_PARTY_BIND)),
    SMS000251(new JSONArray(LanguageEnum.EN_US),new JSONArray(CaptchaFuncEnum.LOGIN)),
    SMS000252(new JSONArray(LanguageEnum.EN_US),new JSONArray(CaptchaFuncEnum.REGISTER)),
    SMS000253(new JSONArray(LanguageEnum.EN_US),new JSONArray(CaptchaFuncEnum.RESET_LOGIN)),

    SMS000254(new JSONArray(LanguageEnum.JA_JP),new JSONArray(CaptchaFuncEnum.REGISTER)),
    SMS000255(new JSONArray(LanguageEnum.JA_JP),new JSONArray(CaptchaFuncEnum.LOGIN)),
    SMS000256(new JSONArray(LanguageEnum.JA_JP),new JSONArray(CaptchaFuncEnum.RESET_LOGIN)),

    SMS000257(new JSONArray(LanguageEnum.ZH_HK),new JSONArray(CaptchaFuncEnum.REGISTER)),
    SMS000258(new JSONArray(LanguageEnum.ZH_HK),new JSONArray(CaptchaFuncEnum.LOGIN)),
    SMS000259(new JSONArray(LanguageEnum.ZH_HK),new JSONArray(CaptchaFuncEnum.RESET_LOGIN)),
    //其余语言后续补充
    ;
    /**
     * 语言清单 来源 LanguageEnum
     * */
    private final JSONArray languageArr;
    /**
     * 场景清单 来源 SmsFuncEnum
     */
    private final JSONArray funcArr;

    SMSTemplateEnum(JSONArray languageArr, JSONArray funcArr) {
        this.languageArr = languageArr;
        this.funcArr = funcArr;
    }

    /**
     * 获取语言
     * @param language
     * @return
     */
    public static SMSTemplateEnum getSMSTemplate(LanguageEnum language, CaptchaFuncEnum func) {
        for (SMSTemplateEnum smsTemplateEnum : values()) {
            if (smsTemplateEnum.languageArr.contains(language) && smsTemplateEnum.funcArr.contains(func)) {
                return smsTemplateEnum;
            }
        }
        return null;
    }
}
