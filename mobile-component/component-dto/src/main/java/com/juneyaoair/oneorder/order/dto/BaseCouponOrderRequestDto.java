package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName BaseCouponOrderRequestDto
 * @Description 订单产品请求统一订单参数——基础信息
 * <AUTHOR>
 * @Date 2019/10/28 14:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseCouponOrderRequestDto<T> {

    @SerializedName("Version")
    private String version;
    @SerializedName("ChannelCode")
    private String channelCode;
    @SerializedName("FfpId")
    private String ffpId;
    @SerializedName("FfpCardNo")
    private String ffpCardNo;
    @SerializedName("RequestIp")
    private String requestIp;
    @SerializedName("Request")
    private T request;
    @SerializedName("UserNo")
    private String userNo;//渠道工作人员账号
    @SerializedName("SearchType")
    private String searchType;
}
