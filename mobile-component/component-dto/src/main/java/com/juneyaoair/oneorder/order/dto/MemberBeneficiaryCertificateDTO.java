package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @ClassName MemberQueryBeneficiaryResponse
 * @Description 积分受益人列表查询相应参数
 * <AUTHOR>
 * @Date 2019/7/30 15:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "MemberBeneficiaryCertificateDTO")
@XmlAccessorType(XmlAccessType.FIELD)
public class MemberBeneficiaryCertificateDTO {

    /**
     * 类型
     */
    @JsonProperty(value = "Ctype")
    private Integer Ctype;

    /**
     * 证件号
     */
    @JsonProperty(value = "Cnumber")
    private String Cnumber;

    /**
     * 签发国 国家二字码
     */
    @JsonProperty(value = "SigningCountry")
    private String SigningCountry;

    /**
     * 过期时间 yyyy-MM-dd
     */
    @JsonProperty(value = "ExpireDate")
    private String ExpireDate;

}
