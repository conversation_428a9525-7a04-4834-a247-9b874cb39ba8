package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

/**
 * @ClassName ModifyMemberContactReqDto
 * @Description 修改手机号请求体（Java版本）
 * <AUTHOR>
 * @Date 2024/5/10 9:42
 * @Version 1.0
 */
@Builder
public class ModifyMemberContactReqDto {

    @ApiModelProperty(value = "联系类型", notes = "1:手机号 2:家庭电话 3:单位电话 4:传真 5:邮箱 6:B2C用户名 7:微信号 8:其他 9:B2C用户")
    @JsonProperty(value = "ContactType")
    private Integer ContactType;

    @ApiModelProperty(value = "联系方式的值")
    @JsonProperty(value = "ContactValue")
    private String ContactValue;

    @ApiModelProperty(value = "会员Id")
    @JsonProperty(value = "MemberId")
    private Integer MemberId;

    @ApiModelProperty(value = "记录Id")
    @JsonProperty(value = "RecordId")
    private Integer RecordId;

    @ApiModelProperty(value = "备注")
    @JsonProperty(value = "Remark")
    private String Remark;

    public ModifyMemberContactReqDto() {
    }

    public ModifyMemberContactReqDto(Integer contactType, String contactValue, Integer memberId, Integer recordId, String remark) {
        ContactType = contactType;
        ContactValue = contactValue;
        MemberId = memberId;
        RecordId = recordId;
        Remark = remark;
    }

    public Integer getContactType() {
        return ContactType;
    }

    public void setContactType(Integer contactType) {
        ContactType = contactType;
    }

    public String getContactValue() {
        return ContactValue;
    }

    public void setContactValue(String contactValue) {
        ContactValue = contactValue;
    }

    public Integer getMemberId() {
        return MemberId;
    }

    public void setMemberId(Integer memberId) {
        MemberId = memberId;
    }

    public Integer getRecordId() {
        return RecordId;
    }

    public void setRecordId(Integer recordId) {
        RecordId = recordId;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }
}
