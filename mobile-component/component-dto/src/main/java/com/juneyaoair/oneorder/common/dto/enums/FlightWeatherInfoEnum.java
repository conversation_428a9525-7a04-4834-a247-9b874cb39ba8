package com.juneyaoair.oneorder.common.dto.enums;

public enum FlightWeatherInfoEnum {
    YJX("雨夹雪", "https://mediaws.juneyaoair.com/upload/flightWeather/yujiaxue.png"),
    BFY("暴风雨", "https://mediaws.juneyaoair.com/upload/flightWeather/baofengyu.png"),
    BFX("暴风雪", "https://mediaws.juneyaoair.com/upload/flightWeather/baofengxue.png"),
    XZF("旋转风", "https://mediaws.juneyaoair.com/upload/flightWeather/xuanzhuanfeng.png"),
    CBF("尘暴风", "https://mediaws.juneyaoair.com/upload/flightWeather/chenbaofeng.png"),
    LZY("雷阵雨", "https://mediaws.juneyaoair.com/upload/flightWeather/leizhenyu.png"),
    <PERSON><PERSON><PERSON>("大雨", "https://mediaws.juneyaoair.com/upload/flightWeather/dayu.png"),
    <PERSON><PERSON>("中雨", "https://mediaws.juneyaoair.com/upload/flightWeather/zhongyu.png"),
    <PERSON><PERSON>("小雨", "https://mediaws.juneyaoair.com/upload/flightWeather/xiaoyu.png"),
    ZHY("阵雨", "https://mediaws.juneyaoair.com/upload/flightWeather/zhenyu.png"),
    BY("暴雨", "https://mediaws.juneyaoair.com/upload/flightWeather/baoyu.png"),
    FC("浮尘", "https://mediaws.juneyaoair.com/upload/flightWeather/fuchen.png"),
    XX("小雪", "https://mediaws.juneyaoair.com/upload/flightWeather/xiaoxue.png"),
    ZX("中雪", "https://mediaws.juneyaoair.com/upload/flightWeather/zhongxue.png"),
    DX("大雪", "https://mediaws.juneyaoair.com/upload/flightWeather/daxue.png"),
    BX("暴雪", "https://mediaws.juneyaoair.com/upload/flightWeather/baoxue.png"),
    DY("多云", "https://mediaws.juneyaoair.com/upload/flightWeather/duoyun.png"),
    X("雪", "https://mediaws.juneyaoair.com/upload/flightWeather/xue.png"),
    W("雾", "https://mediaws.juneyaoair.com/upload/flightWeather/wu.png"),
    Y("雨", "https://mediaws.juneyaoair.com/upload/flightWeather/yu.png"),
    Q("晴", "https://mediaws.juneyaoair.com/upload/flightWeather/qing.png"),
    YIN("阴", "https://mediaws.juneyaoair.com/upload/flightWeather/yin.png"),
    NULL_WEATHER("N/A", "https://mediaws.juneyaoair.com/upload/flightWeather/NA.png");

    private String weatherDescription;
    private String weatherPictureUrl;

    FlightWeatherInfoEnum(String weatherDescription, String weatherPictureUrl) {
        this.weatherDescription = weatherDescription;
        this.weatherPictureUrl = weatherPictureUrl;
    }

    public static FlightWeatherInfoEnum formatWeatherStatus(String code) {
        for (FlightWeatherInfoEnum c : FlightWeatherInfoEnum.values()) {
            if (c.weatherDescription.equals(code)) {
                return c;
            }
        }
        return NULL_WEATHER;
    }

    public String getWeatherPictureUrl() {
        return weatherPictureUrl;
    }

    public String getWeatherDescription() {
        return weatherDescription;
    }
}
