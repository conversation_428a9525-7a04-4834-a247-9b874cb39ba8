package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageEffectiveReqDto
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 9:47
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageEffectiveReqDto {
    @JsonProperty(value = "StartDate")
    private String StartDate;

    @JsonProperty(value = "EndDate")
    private String EndDate;

    @JsonProperty(value = "Id")
    private Integer Id;//会员ID

    @JsonProperty(value = "PageNum")
    private Integer PageNum;

    @JsonProperty(value = "PageSize")
    private Integer PageSize;
}
