package com.juneyaoair.oneorder.disney;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@Data
@Getter
@Setter
public class ProductOrderBookReq {
    @ApiModelProperty(value = "渠道订单号")
    @NotBlank(message = "渠道订单号不能为空")
    private String channelOrderNo;


    @ApiModelProperty(value = "总金额")
    @NotNull(message = "总金额不能为空")
    private BigDecimal saleAmount;

    /**
     * 联系人
     */
    private String linkerName;

    /**
     * 联系人手机
     */
    private String handPhone;


    /**
     * 积分
     */
    private String score;

    @ApiModelProperty(value = "优惠劵号")
    private String couponNo;

    @ApiModelProperty(value = "优惠劵抵扣金额")
    private int couponAmount;

    @ApiModelProperty(value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Valid
    @ApiModelProperty(value = "产品列表")
    private List<CouponProductInfoListDTO> productItemList;

    @ApiModelProperty(value = "迪士尼plu")
    private String disneyPlu;

}
