package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-09-03 14:10
 */
@Data
public class OnboardProduct {

    @SerializedName("Specifications")
    private String specifications; //规格
    @SerializedName("SpecRemark")
    private String  specRemark; //规格备注 meal_reserve  餐食预定   meal_pay   餐食付费升级 2020-10-12
    @SerializedName("Company")
    private String company; //单位
    @SerializedName("Weight")
    private String weight; //重量
    @SerializedName("PlaceOfOrigin")
    private String placeOfOrigin; //产地
    @SerializedName("Brand")
    private String brand; //品牌
    @SerializedName("ProDescription")
    private String proDescription; //产品描述
    @SerializedName("ProImage")
    private String proImage;  //  多个图片在一起，需前端自己拆分
    @SerializedName("QualityDate")
    private String qualityDate; //保质期

}
