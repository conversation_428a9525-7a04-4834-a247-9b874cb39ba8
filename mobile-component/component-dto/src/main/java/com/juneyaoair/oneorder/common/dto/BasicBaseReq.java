package com.juneyaoair.oneorder.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 基础服务基础请求类
 * @date 2019/5/15  16:16.
 */
@Data
@NoArgsConstructor
public class BasicBaseReq<T> {
    private String channelCode;
    private String ip;
    private String version;
    private String userNo;
    private T request;
    private String serviceCode;
    private String ffpId;
    private String ffpCardNo;

    public BasicBaseReq(String version, String channelCode){
        this.version = version;
        this.channelCode = channelCode;
    }
    public BasicBaseReq(String version, String channelCode, String ip){
        this.version = version;
        this.channelCode = channelCode;
        this.ip = ip;
    }
}
