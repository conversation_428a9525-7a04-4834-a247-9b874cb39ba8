package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 升舱特有属性
 * @Date 14:35 2023/6/19
 * @return null
 **/

@Data
public class UpgradeExtInfo {
    @SerializedName("ApplyAheadDays")
    @ApiModelProperty(value = "提前申请天数  0表示可即订即用")
    private int applyAheadDays;
}
