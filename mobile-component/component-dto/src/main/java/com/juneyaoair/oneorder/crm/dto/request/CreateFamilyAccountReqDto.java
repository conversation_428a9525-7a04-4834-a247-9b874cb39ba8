package com.juneyaoair.oneorder.crm.dto.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class CreateFamilyAccountReqDto {
    /**
     * 家庭账户名称
     */
    @NotEmpty(message = "家庭账户名称不可为空")
    private String  FamilyAccountName;

    /**
     * 主账户会员卡号
     */
//    @NotEmpty(message = "主账户会员卡号不可为空")
    private String  MainMemberId;
}
