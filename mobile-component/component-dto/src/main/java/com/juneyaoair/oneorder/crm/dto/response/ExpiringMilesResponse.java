package com.juneyaoair.oneorder.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ExpiringMilesResponse
 * @Description CRM即将过期积分查询响应参数
 * <AUTHOR>
 * @Date 2019/4/23 10:51
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExpiringMilesResponse {

    /**
     * 即将过期积分
     */
    private List<ExpiringMilesDetail> MilesDetail;

    /**
     * 状态，000为成功
     */
    private String StatusCode;

    /**
     * 接口返回提示信息；成功返回成功
     */
    private String Message;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class ExpiringMilesDetail{

        /**
         * 即将过期积分
         */
        private String Miles;

        /**
         * 失效日期
         * yyyy/MM/dd HH:mm:ss
         */
        private String ExpireDate;

    }

}
