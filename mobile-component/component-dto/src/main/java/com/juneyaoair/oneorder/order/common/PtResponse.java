package com.juneyaoair.oneorder.order.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/5  19:42.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtResponse<T> {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String OrderNo;
    private String ChannelOrderNo;
    private String FfpId;
    private String ResultCode;
    private String ErrorInfo;
    private List<ScheduledDetails> ScheduledDetails;
    private T Result;
}
