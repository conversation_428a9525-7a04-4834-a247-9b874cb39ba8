package com.juneyaoair.oneorder.disney;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BookedTicketDto {
    @ApiModelProperty(value = "客票号")
    private String ticketNo;

    /*PVG*/
    @ApiModelProperty(value = "起始机场")
    private String depAirport;

    /*SHA*/
    @ApiModelProperty(value = "到达机场")
    private String arrAirport;

    /*A-X-J-Y*/
    @ApiModelProperty(value = "仓位")
//    @NotEmpty(message = "预定客票信息-舱位为空")
    private String cabin;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "乘客姓名")
    private String passengerName;

    /*ADT*/
    @ApiModelProperty(value = "乘客类型")
    @JsonProperty(value = "PassengerType")
    private String passengerType;

    /*D-国内 I-国际*/
    @ApiModelProperty(value = "航线类型")
    private String airlineType;

    /*OW-单车 RT-来回程*/
    @ApiModelProperty(value = "航程类型")
    private String routeType;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "手机国家码")
    private String phoneCountryCode;

    @ApiModelProperty(value = "手机号码")
    private String phoneNo;
}
