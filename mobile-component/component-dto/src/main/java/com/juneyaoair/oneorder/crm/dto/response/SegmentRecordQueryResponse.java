package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SegmentRecordQueryResponse
 * @Description 航段明细返回体
 * <AUTHOR>
 * @Date 2023/9/11 17:58
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SegmentRecordQueryResponse {
    @ApiModelProperty(value = "航段变化")
    private String segmentVariable;

    @ApiModelProperty(value = "出发地-目的地")
    private String segment;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "出行日期")
    private String flightDate;
}
