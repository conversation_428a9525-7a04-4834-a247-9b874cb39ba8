package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "MemberCommonContactsInfo")
@XmlAccessorType(XmlAccessType.FIELD)
@NoArgsConstructor
@Data
public class MemberCommonContactsInfo {
	@JsonProperty(value = "RecordId")
	private Integer RecordId; //常用联系人信息ID,主键自动生成


	@JsonProperty(value = "Email")
	private String Email; //联系人邮箱

	@JsonProperty(value = "NameCn")
	private String NameCn;//联系人姓名(中文名)

	@JsonProperty(value = "NameEn")
	private String NameEn; //联系人英文名

	@JsonProperty(value = "Mobile")
	private String Mobile;//联系人电话

	@JsonProperty(value = "MemberId")
	private String MemberId; //会员卡号

	public MemberCommonContactsInfo(Integer recordId, String email, String nameCn, String mobile, String memberId) {
		RecordId = recordId;
		Email = email;
		NameCn = nameCn;
		Mobile = mobile;
		MemberId = memberId;
	}
}