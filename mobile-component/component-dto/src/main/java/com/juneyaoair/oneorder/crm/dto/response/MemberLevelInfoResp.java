package com.juneyaoair.oneorder.crm.dto.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2019-3-28 16:36
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class MemberLevelInfoResp {
    /**
     * 接口返回码，”000”表示调用成功，其他表示调用失败
     */
    private String StatusCode;
    /**
     * 接口返回提示信息；成功返回成功
     */
    private String Message;

    private List<MemberLevelUpgradeRuleArray> MemberLevelUpgradeRuleArray;

    public MemberLevelInfoResp(String statusCode, String message){
        this.StatusCode = statusCode;
        this.Message = message;
    }
}
