package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description  我的权益券查看
 * @date 2019/2/28  17:23.
 */
@Data
@NoArgsConstructor
public class PtCouponProductGetRequestDto {
    @JsonProperty(value = "Version")
    private String Version;
    @JsonProperty(value = "ChannelCode")
    private String ChannelCode;
    @JsonProperty(value = "FfpId")
    private String FfpId;
    @JsonProperty(value = "IdNbr")
    private String IdNbr;  //证件号
    @JsonProperty(value = "FfpCardNo")
    private String FfpCardNo;
    /**
     * Coupon  权益劵
     * Card   权益卡
     */
    @JsonProperty(value = "RuleModel")
    private String RuleModel;
    @JsonProperty(value = "VoucherNos")
    private List<String> VoucherNos; //权益凭证号
    @JsonProperty(value = "VoucherTypes")
    private List<String> VoucherTypes;
    @JsonProperty(value = "CouponState")
    private List<String> CouponState;//券状态
    /**
     * 用于区分app上权益券列表里的 可使用和已使用   可使用:Not 已使用:Used 已过期:Expired
     */
    @JsonProperty(value = "AvailableStatus")
    private String AvailableStatus;
    @JsonProperty(value = "SingleBookConditions")
    private List<SingleBookCondition> SingleBookConditions;
    @JsonProperty(value = "PageNo")
    private Integer PageNo ;
    @JsonProperty(value = "PageSize")
    private Integer PageSize ;
    @JsonProperty(value = "PassengerName")
    private String PassengerName; //旅客姓名 2021-04-13 升舱卡仅限本人使用

    public PtCouponProductGetRequestDto(String version, String channelCode){
        this.Version = version;
        this.ChannelCode = channelCode;
    }
    public PtCouponProductGetRequestDto(String version, String channelCode, String ffpId, String ffpCardNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.FfpId = ffpId;
        this.FfpCardNo = ffpCardNo;
    }
}
