package com.juneyaoair.oneorder.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class DetrSegmentDetail {
    @ApiModelProperty()
    public String airLine;
    @ApiModelProperty(value = "登机口")
    public String boardingNo;
    @ApiModelProperty("座位等级")
    public String cabin;
    @ApiModelProperty(value = "出发城市名称")
    public String depCityName;
    @ApiModelProperty(value = "出发机场码")
    public String depAirportCode;
    @ApiModelProperty(value = "出发机场名称")
    public String depAirportName;
    @ApiModelProperty(value = "出发机场航站楼")
    public String depAirportTerminal;
    @ApiModelProperty(value = "出发时刻")
    public String depTime;
    @ApiModelProperty(value = "到达城市名称")
    public String arrCityName;
    @ApiModelProperty(value = "到达机场码")
    public String arrAirportCode;
    @ApiModelProperty(value = "到达机场名称")
    public String arrAirportName;
    @ApiModelProperty("到达航站楼")
    public String arrAirportTerminal;
    @ApiModelProperty(value = "到达时刻")
    public String arrTime;
    @ApiModelProperty(value = "航班号")
    public String flightNo;
    @ApiModelProperty(value = "市场方航空公司代码")
    public String marketingAirline;//市场方航空公司代码
    @ApiModelProperty(value = "承运方航空公司代码")
    public String operationAirline;//承运方航空公司代码
    @ApiModelProperty(value = "pnrNo")
    public String pnrNo;//pnrNo
    @ApiModelProperty(value = "航段序号")
    public int segmentIndex;
    @ApiModelProperty(value = "航段状态")
    public String segmentStatus;//航段状态
    @ApiModelProperty(value = "客票状态(行程单状态)",example = "" +
            "客票有效，可以使用   OPEN FOR USE\n" +
            "客票已使用   USED/FLOWN\n" +
            "退票申请    REFUND APPLICATION\n" +
            "客票已退票   REFUNDED\n" +
            "客票已换开到其它电子票上   EXCHANGED\n" +
            "挂起   SUSPENDED\n" +
            "作废   VOID\n" +
            "客票已换开为飞行中断旅客舱单   FIM EXCH\n" +
            "客票已换开为纸票   PRINT EXCH\n" +
            "航变   AIRP CNTL/HO\n" +
            "值机   CHECKED IN\n" +
            "客票与PNR断连   OPEN\n" +
            "离港   LIFT/BOARDED")
    public String ticketStatus;//客票状态
    @ApiModelProperty("客票状态用于展示")
    public String ticketStatusDesc;//客票状态用于展示
    @ApiModelProperty("航段类型")
    public int type;// 航段类型

    @ApiModelProperty(value = "行程跨天数,大于0时使用",example = "1")
    public int days;
    @ApiModelProperty(value = "行李件数")
    private int baggagePiece;
    @ApiModelProperty(value = "行李重量")
    private int baggageWeight;
    @ApiModelProperty(value = "行李重量单位")
    private String baggageWeightUnit;

    public String depCityCode;
    public String arrCityCode;
}
