package com.juneyaoair.oneorder.ticket;

import com.juneyaoair.oneorder.cuss.TaxInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel
@Data
public class FlightItineraryInfo {
    @ApiModelProperty(value = "货币类型")
    private String currencyType;
    @ApiModelProperty(value = "票面总价的货币类型")
    private String currencyTypeTotal;
    @ApiModelProperty("电子客票类型")
    public int eticketType;//电子客票类型
    @ApiModelProperty("票价")
    public Double fare;//票价
    @ApiModelProperty("出票航空公司")
    public String issueAirline;//出票航空公司
    @ApiModelProperty("出票城市")
    public String issueCity;//出票城市
    @ApiModelProperty("出票日期")
    public String issueDate;//出票日期
    @ApiModelProperty("终点城市")
    public String dstCity;//终点城市
    @ApiModelProperty("始发城市")
    public String orgCity;//始发城市
    @ApiModelProperty("旅客姓名")
    public String passengerName;//旅客姓名
    @ApiModelProperty("旅客类型")
    public int passengerType;//旅客类型
    @ApiModelProperty("支付方式")
    public String payMethod;//支付方式
    @ApiModelProperty("航段数")
    public int segmentCount;//航段数
    @ApiModelProperty("税款金额")
    public double tax;//税款金额
    @ApiModelProperty("票号")
    public String ticketNo;//票号
    @ApiModelProperty(value = "连续票号")
    private String followTicketNo;
    @ApiModelProperty("客票总金额")
    public Double totalAmount;//客票总金额
    @ApiModelProperty("证件类型")
    public String idType;//证件类型
    @ApiModelProperty("证件号码")
    public String idNo;//证件号码
    @ApiModelProperty("证件号码脱敏展示")
    public String idNoDesc;//证件号码
    @ApiModelProperty("邮箱")
    public String email;//邮箱
    @ApiModelProperty("航段info")
    public List<DetrSegmentDetail> detrSegmentDetailList;
    @ApiModelProperty(value = "转换后的税费列表")
    private List<TaxInfo> taxInfoList;
    @ApiModelProperty(value = "IT客票标记")
    private boolean iT;
    @ApiModelProperty(value = "行李件数")
    private int baggagePiece;
    @ApiModelProperty(value = "行李重量")
    private int baggageWeight;
    @ApiModelProperty(value = "行李重量单位")
    private String baggageWeightUnit;
    @ApiModelProperty(value = "签注信息")
    private  String signingInfo;
    @ApiModelProperty(value = "航协记录编号")
    private String iataNo;
    @ApiModelProperty(value = "旅行编码")
    private String tourCode;
}
