package com.juneyaoair.oneorder.constant;

/**
 * <AUTHOR>
 * @description 优惠券状态枚举
 * @date 2019/7/4  9:11.
 */
public enum CouponStateEnum {

    R("R","已领取"),
    N("N","已使用"),
    E("E","已过期"),
    C("C","赠送中"),
    G("G","已赠送");

    private String state;
    private String desc;

    CouponStateEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public String getDesc() {
        return desc;
    }
}
