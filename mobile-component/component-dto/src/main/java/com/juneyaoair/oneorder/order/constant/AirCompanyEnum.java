package com.juneyaoair.oneorder.order.constant;

/**
 * <AUTHOR>
 * @description
 * @date 2019/1/10  10:06.
 */
public enum AirCompanyEnum {
    HO("HO","吉祥"),
    MU("MU","东航");
    private  String airCompanyCode;
    private  String airCompanyName;
    AirCompanyEnum(String airCompanyCode, String airCompanyName){
        this.airCompanyCode = airCompanyCode;
        this.airCompanyName = airCompanyName;
    }
    //航司类型检验
    public static AirCompanyEnum checkAirCompanyEnum(String v){
        for (AirCompanyEnum c: AirCompanyEnum.values()) {
            if (c.airCompanyCode.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getAirCompanyCode() {
        return airCompanyCode;
    }

    public String getAirCompanyName() {
        return airCompanyName;
    }

}
