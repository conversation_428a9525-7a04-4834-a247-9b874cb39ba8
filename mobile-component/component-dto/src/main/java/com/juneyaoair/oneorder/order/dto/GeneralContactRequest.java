package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.oneorder.crm.dto.common.GeneralContactInfo;
import com.juneyaoair.oneorder.order.common.PtBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新增乘机人请求
 * @date 2019/8/7  10:17.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GeneralContactRequest extends PtBaseRequest {
    @ApiModelProperty(value = "增加标记")
    @JsonProperty("AddFlag")
    @SerializedName("AddFlag")
    private Boolean addFlag;

    @ApiModelProperty(value = "乘机人列表")
    @JsonProperty("GeneralContactList")
    @SerializedName("GeneralContactList")
    private List<GeneralContactInfo> generalContactList;

    @ApiModelProperty(value = "是否删除标记")
    @JsonProperty("IsRemove")
    @SerializedName("IsRemove")
    private Boolean isRemove;

    @ApiModelProperty(value = "国内国际航线 true/null代表国际航线需要完全检验证件信息  false代表国内航线部分检验")
    @JsonProperty("InterFlag")
    @SerializedName("InterFlag")
    private Boolean interFlag;

    @ApiModelProperty(value = "航线类型 D：国内 I：国际 R:港澳台 null：未知")
    @JsonProperty("SegmentType")
    @SerializedName("SegmentType")
    private String segmentType;

    public GeneralContactRequest(String version, String channelCode, String userNo){
        super(version,channelCode,userNo);
    }
}
