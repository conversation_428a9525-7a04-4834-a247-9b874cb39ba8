package com.juneyaoair.oneorder.flight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 会员行程航班信息
 * @created 2025/06/19 14:11
 */
@Data
public class MemberTravellerFlightInfo {

    @ApiModelProperty(value = "市场方航班号")
    private String marketingFlightNo;

    @ApiModelProperty(value = "市场方航司Icon")
    private AirLineIcon marketingAirLineIcon;

    @ApiModelProperty(value = "承运方航班号")
    private String operationFlightNo;

    @ApiModelProperty(value = "承运方航司Icon")
    private AirLineIcon operationAirLineIcon;

    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd")
    private String flightDate;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;

    @ApiModelProperty(value = "出发城市名称")
    private String depCityName;

    @ApiModelProperty(value = "起飞机场三字码")
    private String depAirportCode;

    @ApiModelProperty(value = "起飞机场名称")
    private String depAirportName;

    @ApiModelProperty(value = "起飞机场航站楼")
    private String depTerminal;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "到达城市名称")
    private String arrCityName;

    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

    @ApiModelProperty(value = "到达机场名称")
    private String arrAirportName;

    @ApiModelProperty(value = "到达机场航站楼")
    private String arrTerminal;

    @ApiModelProperty(value = "经停机场三字码")
    private String stopAirport;

    @ApiModelProperty(value = "经停机场名称")
    private String stopAirportName;

    @ApiModelProperty(value = "经停城市名称")
    private String stopCityName;

    @ApiModelProperty(value = "国内国际标志")
    private String interFlag;

    @ApiModelProperty(value = "机型")
    private String planeType;

    @ApiModelProperty(value = "机型名称")
    private String planeTypeName;

    @ApiModelProperty(value = "起飞时间")
    private String depTime;

    @ApiModelProperty(value = "到达日期 yyyy-MM-dd")
    private String arrDate;

    @ApiModelProperty(value = "到达时间")
    private String arrTime;

    @ApiModelProperty(value = "飞行时长 单位：分钟")
    private String duration;

    @ApiModelProperty(value = "计划到达跨天天数")
    private long crossDay;

    @ApiModelProperty(value = "旅客清单")
    private List<MemberTravellerTicketInfo> travellerList;
}
