package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EquityUnitsUsage {
    @ApiModelProperty(value = "凭证号")
    @SerializedName(value="EquityNo")
    private String equityNo;

    @ApiModelProperty(value = "航班号")
    @SerializedName(value="FlightNo")
    private String flightNo;

    @ApiModelProperty(value = "出发机场")
    @SerializedName(value="DepCity")
    private String depCity;

    @ApiModelProperty(value = "到达机场")
    @SerializedName(value="ArrCity")
    private String arrCity;

    @ApiModelProperty(value = "起飞时间")
    @SerializedName(value="DepTime")
    private String depTime;

    @ApiModelProperty(value = "到达时间")
    @SerializedName(value="ArrTime")
    private String arrTime;

    @ApiModelProperty(value = "舱位")
    @SerializedName(value="Cabin")
    private String cabin;
}
