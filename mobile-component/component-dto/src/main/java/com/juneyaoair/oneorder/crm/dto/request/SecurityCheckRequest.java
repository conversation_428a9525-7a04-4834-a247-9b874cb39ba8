package com.juneyaoair.oneorder.crm.dto.request;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName SecurityCheckRequest
 * @Description 安全性检查类型区分
 * <AUTHOR>
 * @Date 2024/8/8 14:29
 * @Version 1.0
 */
public class SecurityCheckRequest {

    @NotEmpty(message = "安全检查类型不可为空")
    private String securityType;

    public SecurityCheckRequest() {
    }

    public SecurityCheckRequest(String securityType) {
        this.securityType = securityType;
    }

    public String getSecurityType() {
        return securityType;
    }

    public void setSecurityType(String securityType) {
        this.securityType = securityType;
    }

}
