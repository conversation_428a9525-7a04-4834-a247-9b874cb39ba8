package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/2/20 9:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberVerifyAccountResp {

    @ApiModelProperty(value = "接口使用凭证")
    private String proof;

    @ApiModelProperty(value = "接口使用时间")
    private String time;

    @ApiModelProperty(value = "接口使用手机号")
    private String userName;
}
