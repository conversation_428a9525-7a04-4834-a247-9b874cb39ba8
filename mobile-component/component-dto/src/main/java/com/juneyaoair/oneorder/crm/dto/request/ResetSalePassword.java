package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * Created by yaocf on 2018/7/26  11:18.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ResetSalePassword {
    @NotBlank(message="消费密码不能为空")
    @ApiModelProperty(value = "消费密码")
    private String consumePwd;

    @NotBlank(message="确认消费密码不能为空")
    @ApiModelProperty(value = "确认消费密码")
    private String confirmedPwd;
}
