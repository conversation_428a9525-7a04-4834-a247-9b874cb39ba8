package com.juneyaoair.oneorder.ticket;

import lombok.Data;

import java.util.List;

@Data
public class TicketInfoQueryResponse {
    //旅客类型，取值为：1. ADT：成人2. CHD：有人陪伴儿童3. UM：无人陪伴儿童
    private String traveller_type;
    //旅客姓名，先取中文名称；若中文名称为空，则取英文名、英文姓拼接；若还空则取证件信息-英文名、英文姓拼接
    private String traveller_name;
    //英文姓
    private String traveller_surname;
    //英文名
    private String traveller_givenname;
    //性别，M：男性，F：女性，若性别为空，则取证件信息-性别；若再为空，则取身份证号判断，15位：尾数是奇数为M，偶数则为F，18位：第17位是奇数为M，偶数则为F
    private String traveller_gender;
    //证件信息-证件类型：1. NI：身份证 2. PSPT：护照
    private String traveller_document_type;
    //证件信息-证件号码
    private String traveller_number;
    //客票信息-票号
    private String ticket_ticketnumber;
    //市场航班-航空公司二字代码
    private String segment_car_airlinecode;
    //市场航班-航班号，开头不含0
    private String segment_car_flightnumber;
    //承运航班-航空公司二字代码
    private String segment_opt_airlinecode;
    //承运航班-航班号
    private String segment_opt_flightnumber;
    //离港信息-起飞机场三字代码
    private String segment_depa_airportcode;
    //离港信息-离港日期
    private String segment_depa_date;
    //离港信息-离港时间
    private String segment_depa_time;
    // 到港信息-到达机场三字代码
    private String segment_arrival_airportcode;
    //到港信息-到港日期
    private String segment_arrival_date;
    //到港信息-到港时间
    private String segment_arrival_time;
    //舱位信息-主舱位
    private String segment_compartment;
    //舱位信息-子舱位，舱位信息-子舱位为空，则取舱位信息-主舱位
    private String segment_clazz;
    private String booking_gdsrecordlocator;
    private String booking_recordlocator;
    //证件信息-证件类型：1. NI：身份证 2. PSPT：护照
    private String traveller_document_type2;
    //证件信息-证件号码
    private String traveller_number2;
    //座位号
    private String operation_location;
    //登机号
    private String operation_boardingnumber;
    //旅客联系方式
    private String traveller_phone_ctcm;
    private String traveller_phone_ctct;
    private String traveller_email;
    //值机类型
    private String operation_checkintype;
    private String operation_checkinpid;
    //行李明细（List格式）
    private List<BaggageInfo> baggage_json;
    private String hdr_stamp;
    private String hdr_event;
    private String hdr_event_name;
    private String hdr_subevent;
    private String hdr_uptm;
    private String hdr_newval;
    private String hdr_oldval;
    private String traveller_birthdate;
    private String traveller_depa_flag;
    private String traveller_transfer_flag;
    //值机状态值机状态 0：未值机,1：已值机,2：未登机,3:值机拉下
    private String traveller_checkin_flag;
    //登机状态
    private String traveller_boarded_flag;
    private String traveller_checkout_flag;
    private String traveller_trfd_flag;
    private String mp_open_id;
    private String red_list_flag;
    //乘机次数
    private String total_cnt;
    private String code_list_flag;
    private String cust_type_flag;
    private String business_cust_flag;
    //会员id
    private String member_id;
    //会员卡号
    private String member_card_no;
    //会员等级code
    private String member_level_code;
    //会员等级描述，例：福卡
    private String member_level_des;
    //会员标识
    private String member_flag;
    //出票日期, 格式：yyyy-MM-dd hh:mm
    private String ticket_out_time;
    //行李总数量
    private String checkincount_total;
    //行李总重量
    private String checkinweight_total;
    private String traveller_inftticketnumber;
    //经停站
    private String b_airport;
    //值机时间
    private String operation_checkindate;
    private String clazz_flag;
    private String segment_clazz_issue;
    private String passengertype;
    // 换开标识 1：是，0：否
    private String exchange_flag;
    private String operation_checkinagent;
    private String clast_name;
    private String cfirst_name;
    //国际国内标识
    private String ticket_tickettype;
    private String especially_flag;
    private String especially_desc;
    // 出发航站楼
    private String segment_depa_terminal;
    // 到达航站楼
    private String segment_arr_terminal;
    //B2C升舱标志
    private String b2c_upgrade_flag;
    //机型
    private String aircraft_type;
    //免费行李额
    private String segment_baggageallowance;
    private String  emd_baggage    ;         //收费行李重量
    private String  emd_baggage_unit;         //收费行李重量单位

    private String  segment_couponnumber; //航段号
    //票状态
    private String  segment_couponstatus;
}
