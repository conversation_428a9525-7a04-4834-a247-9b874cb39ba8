package com.juneyaoair.oneorder.disney;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DisneyGuestInfoDto {

    @ApiModelProperty(value = "迪士尼门票子产品编号")
    private String subPlu;

    @ApiModelProperty(value = "乘客姓名")
    private String passengerName;

    @ApiModelProperty(value = "乘客类型")
    private String passengerType;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "手机国家码")
    private String phoneCountryCode;

    @ApiModelProperty(value = "手机号码")
    private String phoneNo;
}
