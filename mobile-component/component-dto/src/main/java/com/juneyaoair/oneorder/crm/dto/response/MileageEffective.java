package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageEffective
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 9:49
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageEffective {
    @JsonProperty(value = "ExpireDate")
    private String ExpireDate;

    @JsonProperty(value = "Miles")
    private Integer Miles;
}
