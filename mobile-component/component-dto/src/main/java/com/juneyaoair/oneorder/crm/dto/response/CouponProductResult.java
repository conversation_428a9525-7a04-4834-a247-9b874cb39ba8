package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 权益券信息
 * <AUTHOR>
 */
@Data
public class CouponProductResult {

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "权益券号")
    private String voucherNo;

    @ApiModelProperty(value = "权益券状态")
    private String voucherState;

    @ApiModelProperty(value = "权益券类型")
    private String voucherType;

    @ApiModelProperty(value = "生效时间")
    private String activateTime;

    @ApiModelProperty(value = "过期时间")
    private String expireTime;

    @ApiModelProperty(value = "权益券是否限制本人使用  1:是  0:否")
    private int selfUse;

    @ApiModelProperty(value = "不可用原因")
    private String unAvailableMsg;

    @ApiModelProperty(value = "是否可以使用")
    private boolean availableStatus;

    @ApiModelProperty(value = "预定限制")
    private CouponProductBookingLimit bookingLimit;

    @ApiModelProperty(value = "资源使用方式说明")
    private String useMode;

    @ApiModelProperty(value = "资源使用方式说明")
    private CouponProductVoucherDetailBaggageExt baggageExt;

}
