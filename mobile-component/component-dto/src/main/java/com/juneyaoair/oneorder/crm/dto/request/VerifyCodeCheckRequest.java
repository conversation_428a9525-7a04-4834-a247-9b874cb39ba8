package com.juneyaoair.oneorder.crm.dto.request;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName VerifyCodeCheckRequest
 * @Description 验证码校验请求体
 * <AUTHOR>
 * @Date 2024/8/6 21:34
 * @Version 1.0
 */
public class VerifyCodeCheckRequest {

    /**
     * <AUTHOR>
     * @Description 验证码类型
     * @Date 21:34 2024/8/6
     **/
    @NotEmpty(message = "验证码类型不可为空")
    private String type;

    /**
     * <AUTHOR>
     * @Description 验证码
     * @Date 21:34 2024/8/6
     **/
    @NotEmpty(message = "验证码不可为空")
    private String verifyCode;

    public VerifyCodeCheckRequest() {
    }

    public VerifyCodeCheckRequest(String type, String verifyCode) {
        this.type = type;
        this.verifyCode = verifyCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }
}
