package com.juneyaoair.oneorder.common.dto;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;

/**
 * <AUTHOR>
 */
public interface RequestInterface {

    /**
     * 渠道号
     * @return
     */
    String getChannelNo();

    /**
     * 会员号
     * @return
     */
    String getFfpId();

    /**
     * 会员卡号
     * @return
     */
    String getFfpNo();

    /**
     * 来源IP
     * @return
     */
    String getOriginIp();

    /**
     * 语言
     * @return
     */
    LanguageEnum getLanguage();

    /**
     * 是否登录
     *      true:   已登录
     *      false： 未登录
     * @return
     */
    boolean isLoginFlag();

}
