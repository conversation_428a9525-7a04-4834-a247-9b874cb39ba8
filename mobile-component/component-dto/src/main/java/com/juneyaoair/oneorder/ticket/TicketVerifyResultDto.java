package com.juneyaoair.oneorder.ticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/9 13:44
 */
@Data
public class TicketVerifyResultDto {
    @ApiModelProperty("票号")
    private String ticketNo;
    @ApiModelProperty("乘客姓名")
    private String passengerName;
    @ApiModelProperty("证件号")
    private String certNo;
    @ApiModelProperty("销售方航空公司名称")
    private String operatingAirlineName;
    @ApiModelProperty("总价币种")
    private String totalCurrency;
    @ApiModelProperty("总价")
    private String total;
    @ApiModelProperty("票面价")
    private String fare;
    @ApiModelProperty("税费币种")
    private String taxCurrency;
    @ApiModelProperty("机建燃油")
    private String yqcn;
    @ApiModelProperty("其他税费")
    private String otherTax;
    @ApiModelProperty(value = "国内、国际地区标志，true-国际地区",notes = "true-国际地区")
    private boolean interFlag;
    @ApiModelProperty("航班信息清单")
    private List<TicketFlight> flightList;
}
