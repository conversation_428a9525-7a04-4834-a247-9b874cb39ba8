package com.juneyaoair.oneorder.bigdate.request;

import lombok.Data;

/**
 * @program: mobile
 * @description 大数据请求接口 /traveller/ticket/info
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2019-07-23 14:14
 **/
@Data
public class TicketInfoQueryRequest {
    /**
     * 航班开始时间
     */
    private String flightStartDate;
    /**
     * 航班结束时间
     */
    private String flightEndDate;
    /**
     * 姓名
     */
    private String travellerName;
    //证件号
    private String travellerNumber;
    //票号
    private String ticketNumber;
    //加密串
    private String signature;
    /**
     * 航班号
     */
    private String airlineCodeFlightNumber;
    /**
     *起始地
     */
    private String segmentDepaAirportCode;
    /**
     * 目的地
     */
    private String segmentArrivalAirportCode;

    private String  segment_baggageallowance ;  //行李限额
    private String  emd_baggage    ;         //收费行李重量
    private String  emd_baggage_unit;         //收费行李重量单位

}
