package com.juneyaoair.oneorder.cuss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/25 16:00
 */
@Data
@ApiModel(value = "税费实体")
public class TaxInfo {
    @ApiModelProperty(value = "税费代码")
    private String taxCode;
    @ApiModelProperty(value = "币种")
    private String taxCurrencyType;
    @ApiModelProperty(value = "税费金额")
    private Double taxAmount;
}
