package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberCenterRequest
 * @Description 会员中心查询通用请求类
 * <AUTHOR>
 * @Date 2019/7/31 15:40
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberCenterRequest<T> {

    /**
     * 渠道码
     * 必填
     */
    private String channelCode;

    /**
     * IP
     * 必填
     */
    private String ip;

    /**
     * 版本号
     * 必填
     */
    private String version;

    /**
     * 用户ID
     */
    private String userNo;

    /**
     * 请求实体
     */
    private T request;

    /**
     * 接口编码
     */
    private String serviceCode;

}
