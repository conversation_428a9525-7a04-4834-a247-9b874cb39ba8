package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 随身wifi特有属性
 * @Date 14:33 2023/6/19
 * @return null
 **/

@Data
public class WIFIExtInfo {
    @ApiModelProperty(value = "设备押金")
    @SerializedName("Deposit")
    private Double deposit;

    @ApiModelProperty(value = "提前预定天数")
    @SerializedName("AdvanceDays")
    private Integer advanceDays;

    @ApiModelProperty(value = "提前预定小时数")
    @SerializedName("AdvanceHours")
    private Integer advanceHours;

    @ApiModelProperty(value = "最小预定天数")
    @SerializedName("MinBookDays")
    private Integer minBookDays;
}
