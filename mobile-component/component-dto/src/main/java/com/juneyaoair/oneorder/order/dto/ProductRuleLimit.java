package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/30  8:44.
 */
@Data
public class ProductRuleLimit {
    @SerializedName("AirLine")
    private List<AirlineLimit> airLine;
    @SerializedName("FlightNo")
    private FlightNoLimit flightNo;
    @SerializedName("Cabin")
    private List<String> cabin;//适用舱位
    @SerializedName("AircraftModel")
    private List<String> aircraftModel;
    @SerializedName("CityCode")
    private List<String> cityCode;
    @SerializedName("AirportCode")
    private List<String> airportCode;
    @SerializedName("FlightDate")
    private FlightDateLimit flightDate;
    @SerializedName("FlightType")
    private String flightType;
}
