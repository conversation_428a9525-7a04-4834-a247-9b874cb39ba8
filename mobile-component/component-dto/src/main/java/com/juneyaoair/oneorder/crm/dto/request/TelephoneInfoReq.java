package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2018/12/10 16:42
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TelephoneInfoReq {

    @ApiModelProperty(value = "手机号")
    private String telephone;

    @ApiModelProperty(value = "记录ID")
    private String record;

    @NotNull(message="验证码不能为空")
    @ApiModelProperty(value = "验证码")
    private String veriCode;

    @NotBlank(message = "短信类型不能为空")
    @ApiModelProperty(value = "短信类型")
    private String type;

    @ApiModelProperty(value = "之前步骤接口凭证")
    private MemberVerifyAccountResp memberVerifyAccountResp;

}
