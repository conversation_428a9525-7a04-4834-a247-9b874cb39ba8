package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@Data
@Setter
@Getter
public class MileageDetailSegmentQueryResp {

    @JsonProperty("Count")
    private Integer Count;
    @JsonProperty("Segments")
    private List<SegmentsDTO> Segments;

    @NoArgsConstructor
    @Data
    public static class SegmentsDTO {
        @JsonProperty("Comments")
        private String Comments;
        @JsonProperty("ConfirmDate")
        private String ConfirmDate;
        @JsonProperty("Number")
        private Integer Number;
        @JsonProperty("SegmentInfo")
        private SegmentInfoDTO SegmentInfo;
        @JsonProperty("Source")
        private String Source;

        @NoArgsConstructor
        @Data
        public static class SegmentInfoDTO {
            @JsonProperty("AirlineCode")
            private String AirlineCode;
            @JsonProperty("Destination")
            private String Destination;
            @JsonProperty("DestinationName")
            private String DestinationName;
            @JsonProperty("FlightDate")
            private String FlightDate;
            @JsonProperty("FlightNumber")
            private String FlightNumber;
            @JsonProperty("FlightSuffix")
            private String FlightSuffix;
            @JsonProperty("OperateAirlineCode")
            private String OperateAirlineCode;
            @JsonProperty("OperateFlightNumber")
            private String OperateFlightNumber;
            @JsonProperty("OperateFlightSuffix")
            private String OperateFlightSuffix;
            @JsonProperty("Origination")
            private String Origination;
            @JsonProperty("OriginationName")
            private String OriginationName;
        }
    }
}
