package com.juneyaoair.oneorder.common.dto.enums;

/**
 * <AUTHOR>
 * @description 国内国际/地区枚举
 * @date 2023/6/25 10:15
 */
public enum InterFlagEnum {
    D("D","国内"),
    I("I","国际/地区");

    private String code;
    private String desc;

    InterFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }
}
