package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "SegmentInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtSegmentInfo {
	
	private int SegNO; //旅行顺序
	private String FlightDirection; //飞行方向
	private String FlightNo; //航班号
	private String DepDateTime; //航班起飞时间
	private String ArrDateTime; //航班到达时间
	private String DepCity; //起飞城市三字码
	private String ArrCity; //到达城市三字码
	private String DepAirport; //起飞机场三字码
	private String ArrAirport; //到达机场三字码
	private String Cabin; //舱位
	private String CabinClass; //舱位等级
	private boolean IsCodeShare; //是否共享航班
	private String CarrierFlightNo; //承运航班号
	private String MealCode; //餐食代码
	private boolean IsSeatedOnPlane; //是否可以机上订位
	private String PlaneStyle; //机型
	private String DepTerm; //起飞航站楼
	private String ArrTerm; //到达航站楼
	private int StopNumber; //经停次数
	private String UpgradeCouponCode;//升舱券/改期券
	private Boolean UpgradeFlag; //升舱改期航段标记
	private double UpgradeTicketPrice;//升舱费
	private Boolean VoluntarilyChangeFlag;//资源改期标记 非自愿传false
	private String CouponCode;//改期券
	private String UpgradeCard;// 是否是无限升舱卡 Y

	//2021-11-30 如果是儿童则跟随成人
	private String PassengerType;
	public int getSegNO() {
		return SegNO;
	}
	public void setSegNO(int segNO) {
		SegNO = segNO;
	}
	public String getFlightDirection() {
		return FlightDirection;
	}
	public void setFlightDirection(String flightDirection) {
		FlightDirection = flightDirection;
	}
	public String getFlightNo() {
		return FlightNo;
	}
	public void setFlightNo(String flightNo) {
		FlightNo = flightNo;
	}
	public String getDepDateTime() {
		return DepDateTime;
	}
	public void setDepDateTime(String depDateTime) {
		DepDateTime = depDateTime;
	}
	public String getArrDateTime() {
		return ArrDateTime;
	}
	public void setArrDateTime(String arrDateTime) {
		ArrDateTime = arrDateTime;
	}
	public String getDepCity() {
		return DepCity;
	}
	public void setDepCity(String depCity) {
		DepCity = depCity;
	}
	public String getArrCity() {
		return ArrCity;
	}
	public void setArrCity(String arrCity) {
		ArrCity = arrCity;
	}
	public String getDepAirport() {
		return DepAirport;
	}
	public void setDepAirport(String depAirport) {
		DepAirport = depAirport;
	}
	public String getArrAirport() {
		return ArrAirport;
	}
	public void setArrAirport(String arrAirport) {
		ArrAirport = arrAirport;
	}
	public String getCabin() {
		return Cabin;
	}
	public void setCabin(String cabin) {
		Cabin = cabin;
	}
	public String getCabinClass() {
		return CabinClass;
	}
	public void setCabinClass(String cabinClass) {
		CabinClass = cabinClass;
	}
	public boolean GetIsCodeShare() {
		return IsCodeShare;
	}
	public void setIsCodeShare(boolean isCodeShare) {
		IsCodeShare = isCodeShare;
	}
	public String getCarrierFlightNo() {
		return CarrierFlightNo;
	}
	public void setCarrierFlightNo(String carrierFlightNo) {
		CarrierFlightNo = carrierFlightNo;
	}
	public String getMealCode() {
		return MealCode;
	}
	public void setMealCode(String mealCode) {
		MealCode = mealCode;
	}
	public boolean getIsSeatedOnPlane() {
		return IsSeatedOnPlane;
	}
	public void setIsSeatedOnPlane(boolean isSeatedOnPlane) {
		IsSeatedOnPlane = isSeatedOnPlane;
	}
	public String getPlaneStyle() {
		return PlaneStyle;
	}
	public void setPlaneStyle(String planeStyle) {
		PlaneStyle = planeStyle;
	}
	public String getDepTerm() {
		return DepTerm;
	}
	public void setDepTerm(String depTerm) {
		DepTerm = depTerm;
	}
	public String getArrTerm() {
		return ArrTerm;
	}
	public void setArrTerm(String arrTerm) {
		ArrTerm = arrTerm;
	}
	public int getStopNumber() {
		return StopNumber;
	}
	public void setStopNumber(int stopNumber) {
		StopNumber = stopNumber;
	}

	public String getUpgradeCouponCode() {
		return UpgradeCouponCode;
	}

	public void setUpgradeCouponCode(String upgradeCouponCode) {
		UpgradeCouponCode = upgradeCouponCode;
	}

	public Boolean getUpgradeFlag() {
		return UpgradeFlag;
	}

	public void setUpgradeFlag(Boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}

	public double getUpgradeTicketPrice() {
		return UpgradeTicketPrice;
	}

	public void setUpgradeTicketPrice(double upgradeTicketPrice) {
		UpgradeTicketPrice = upgradeTicketPrice;
	}

	public Boolean getVoluntarilyChangeFlag() {
		return VoluntarilyChangeFlag;
	}

	public void setVoluntarilyChangeFlag(Boolean voluntarilyChangeFlag) {
		VoluntarilyChangeFlag = voluntarilyChangeFlag;
	}

	public String getCouponCode() {
		return CouponCode;
	}

	public void setCouponCode(String couponCode) {
		CouponCode = couponCode;
	}

	public String getUpgradeCard() {
		return UpgradeCard;
	}

	public void setUpgradeCard(String upgradeCard) {
		UpgradeCard = upgradeCard;
	}

	public String getPassengerType() {
		return PassengerType;
	}

	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
}