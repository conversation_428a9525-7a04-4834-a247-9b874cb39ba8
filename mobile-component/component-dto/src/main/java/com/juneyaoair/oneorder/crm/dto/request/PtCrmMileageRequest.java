package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PtCrmMileageRequest
 * @Description  java 微服务 CRM接口通用请求参数
 * <AUTHOR>
 * @Date 2020/3/17 15:43
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtCrmMileageRequest<T> {
    /**
     * 业务数据
     */
    @JsonProperty(value = "Data")
    private T Data;
    /**
     * 渠道名称
     */
    @JsonProperty(value = "Channel")
    private String Channel;
    /**
     * 渠道密码
     */
    @JsonProperty(value = "ChannelPwd")
    private String ChannelPwd;
    /**
     * 请求IP
     */
    @JsonProperty(value = "ClientIP")
    private String ClientIP;
    /**
     * 版本号
     */
    @JsonProperty(value = "Version")
    private String Version;
    /**
     * 业务随机码
     * SgdsR2fi1，用于错误排查或业务追踪
     */
    @JsonProperty(value = "RandomCode")
    private String RandomCode;
    /**
     * 操作者  可不传（常旅客后台除外）
     */
    @JsonProperty(value = "OperatorUid")
    private String OperatorUid;
    /**
     * 时间戳（默认为系统当前时间）
     */
    @JsonProperty(value = "Timestamp")
    private String Timestamp;

    /**
     * 加密数据
     */
    @JsonProperty(value = "Encryptor")
    private String Encryptor;
    /**
     * 签名数据
     */
    @JsonProperty(value = "Signature")
    private String Signature;

    public PtCrmMileageRequest(String channel, String channelPwd, String version) {
        Channel = channel;
        ChannelPwd = channelPwd;
        Version = version;
    }
}
