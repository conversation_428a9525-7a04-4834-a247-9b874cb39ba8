package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 权益凭证
 * @Date 14:33 2023/6/19
 * @return null
 **/

@Data
public class VoucherInfo {
    @ApiModelProperty(value = "权益凭证号")
    private String VoucherNo;

    @ApiModelProperty(value = "凭证状态")
    private String VoucherState;

    @ApiModelProperty(value = "品类（同资源品类）")
    private String VoucherType;

    @ApiModelProperty(value = "生效时间")
    private String ActivateTime;

    @ApiModelProperty(value = "过期时间")
    private String ExpireTime;

    @ApiModelProperty(value = "权益信息")
    private ResourceInfo VoucherDetail;

    @ApiModelProperty(value = "预定限制")
    private com.juneyaoair.oneorder.crm.dto.response.BookingLimit BookingLimit;

    @ApiModelProperty(value = "设备押金")
    private EquityUnitsUsage EquityUnitsUsage;

    @ApiModelProperty(value = "规则备注")
    private String RuleRemark;

    @ApiModelProperty(value = "是否可转赠 1-可转赠  0-不可转赠")
    private int IsGiving;

    @ApiModelProperty(value = "不可用原因")
    private String UnAvailableMsg;

    @ApiModelProperty(value = "是否可以使用")
    private boolean AvailableStatus;

    @ApiModelProperty(value = "返回权益券是否可直接查看订单信息")
    private String Display;

    @ApiModelProperty(value = "贵宾休息室剩余可取消预约次数")
    private int BookCancelCount;

    @ApiModelProperty(value = "订单号")
    private String MainOrderNo;

    @ApiModelProperty(value = "渠道订单号")
    private String ChannelOrderNo;

    @ApiModelProperty(value = "表示无限升舱卡  yes - 无限升舱卡， 其他情况视为普通升舱卡")
    private String UnLimitedUpgrade;

    @ApiModelProperty(value = "绑定截至日期")
    private String LimitBindingDate;

    @ApiModelProperty(value = "绑定状态 no-未绑定")
    private String LimitBindingStatus;

    @ApiModelProperty(value = "记录是否已过绑定有效期  有效:Effective  失效:invalid")
    private String UpgradeBindingValidity;

    @ApiModelProperty(value = "儿童中文姓名")
    private String ChildCnName;

    @ApiModelProperty(value = "儿童英文姓")
    private String ChildElastName;

    @ApiModelProperty(value = "儿童英文名")
    private String ChildEfirstName;

    @ApiModelProperty(value = "儿童身份证号")
    private String ChildIdNumber;

    @ApiModelProperty(value = "儿童出生日期")
    private String ChildBirthDate;

    @ApiModelProperty(value = "成人姓名")
    private String AdultName;

    @ApiModelProperty(value = "成人身份证号")
    private String AdultIdNumber;

    @ApiModelProperty(value = "成人英文姓")
    private String AdultElastName;

    @ApiModelProperty(value = "成人英文名")
    private String AdultEfirstName;

    @ApiModelProperty(value = "证件类型")
    private String CertType;

    @ApiModelProperty(value = "绑定类型  ADT:绑定本人  CHD:绑定儿童")
    private String BindingType;

    @ApiModelProperty(value = "受赠人会员ID")
    private String ReceiveMemberId;

    @ApiModelProperty(value = "受赠人会员卡号")
    private String ReceiveMemberCard;

    @ApiModelProperty(value = "受赠人券码")
    private String ReveiveVoucherNo;

    @ApiModelProperty(value = "是否他人赠送标识   1:是  0:否")
    private Integer ReceiveStatus;

    @ApiModelProperty(value = "使用状态")
    private String UsedStatus;

    @ApiModelProperty(value = "权益券是否限制本人使用  1:是  0:否")
    private int SelfUse;

    @ApiModelProperty(value = "下单类型")
    private String SubOrderType;

    private String RescheduleType;

    private String AdvanceHour;
}
