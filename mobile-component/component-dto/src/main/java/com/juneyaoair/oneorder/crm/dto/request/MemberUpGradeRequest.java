package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2019-2-27 15:49
 * @description：后端会员等级req
 * @modified By：
 * @version: $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberUpGradeRequest {
    @JsonProperty(value = "MemberCardNO")
    private String MemberCardNO;

    @JsonProperty(value = "ClientCode")
    private String ClientCode;

    @JsonProperty(value = "Signature")
    private String Signature;
}
