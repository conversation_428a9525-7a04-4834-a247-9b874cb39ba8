package com.juneyaoair.oneorder.common.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class BaseReq <T>{
    @NotBlank(message = "渠道用户号不能为空")
    @ApiModelProperty(value = "渠道号", name = "channelCode", example = "MOBILE", required = true, allowableValues = "MOBILE,B2C")
    public String channelCode;
    @NotNull(message = "客户端版本不能为空")
    @ApiModelProperty(value = "客户端版本号", name = "clientVersion", example = "5.6.0", required = true)
    public String clientVersion;//当前发起请求的客户端版本
    @ApiModelProperty(value = "操作平台", name = "platformInfo", example = "android", required = true)
    public String platformInfo;//android ios
    @ApiModelProperty(value = "编译版本号", name = "versionCode", example = "56000", required = true)
    public String versionCode;//编译版本号
    @Valid
    public T request;//业务参数
}
