package com.juneyaoair.oneorder.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName StudentAuthDTO
 * @Description 学生认证 详情参数
 * <AUTHOR>
 * @Date 2020/5/9 14:46
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentAuthDTO {
    /**
     * 学校名称
     */
    @NotBlank(message="学校名称不能为空")
    private String schoolName;

    /**
     * 当前学籍
     */
    @NotBlank(message="当前学籍不能为空")
    private String schoolRoll;

    /**
     * 入学时间
     */
    @NotBlank(message="入学时间不能为空")
    private String beginDate;

    /**
     * 预计毕业时间
     */
    @NotBlank(message="预计毕业时间不能为空")
    private String endDate;

    /**
     * 学生证照片路径
     */
    @NotBlank(message="学生证照片路径不能为空")
    private String studentCardImg;

    /**
     * 个人资料照片路径
     */
    @NotBlank(message="个人资料照片路径不能为空")
    private String studentInfoImg;

    /**
     * 注册记录照片路径
     */
    @NotBlank(message="注册记录照片路径不能为空")
    private String studentRecordImg;

}
