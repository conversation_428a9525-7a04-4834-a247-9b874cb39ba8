package com.juneyaoair.oneorder.common.dto;

import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 每日许可校验
 * @created 2023/11/15 17:24
 */
@Data
public class CheckDayLicense {

    @ApiModelProperty(value = "校验Key")
    private String key;

    @ApiModelProperty(value = "校验类型")
    private String checkLicense;

    @ApiModelProperty(value = "校验不通过提示文案")
    private String message;

    @ApiModelProperty(value = "错误编码", notes = "用户国际化根据编码翻译文案,使用时确认相关翻译文案齐全")
    private String errorCode;

    public CheckDayLicense() {
    }

    /**
     * 不建议直接使用，建议校验类型通过枚举传入
     * @param key
     * @param checkLicense
     * @param message
     */
    @Deprecated
    public CheckDayLicense(String key, String checkLicense, String message) {
        this.key = key;
        this.checkLicense = checkLicense;
        this.message = message;
    }

    public CheckDayLicense(String key, CheckLicenseFuncEnum checkLicense, String message) {
        this.key = key;
        this.checkLicense = checkLicense.name();
        this.message = message;
    }

    public CheckDayLicense(String key, CheckLicenseFuncEnum checkLicense, String errorCode,String message) {
        this.key = key;
        this.checkLicense = checkLicense.name();
        this.errorCode = errorCode;
        this.message = message;
    }

    public CheckDayLicense(String key, CheckLicenseFuncEnum checkLicense, String subType, String errorCode, String message) {
        this.key = key;
        this.checkLicense = checkLicense.name() + "_" + subType;
        this.errorCode = errorCode;
        this.message = message;
    }

}
