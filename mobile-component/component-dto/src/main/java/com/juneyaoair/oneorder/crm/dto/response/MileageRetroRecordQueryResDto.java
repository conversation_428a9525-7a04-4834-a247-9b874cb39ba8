package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MileageRetroRecordQueryResDto
 * @Description 补登返回体
 * <AUTHOR>
 * @Date 2023/9/12 8:01
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageRetroRecordQueryResDto {
    @ApiModelProperty(value = "补登记录")
    private List<MileageRetroRecordDto> RetroRecords;
}
