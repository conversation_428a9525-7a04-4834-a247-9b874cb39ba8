package com.juneyaoair.oneorder.amap.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 高德地图-输入提示返回结果
 * @created 2024/4/8 16:02
 */
@Data
public class AmapInputTipsResult {

    @ApiModelProperty(value = "状态 0表示失败 1表示成功")
    private String status;

    @ApiModelProperty(value = "返回状态说明，status为0时，info返回错误原因，否则返回“OK”")
    private String info;

    @ApiModelProperty(value = "状态说明,10000代表正确,详情参阅info状态表")
    private String infocode;

    @ApiModelProperty(value = "返回结果总数目")
    private String count;

    @ApiModelProperty(value = "城市名称 直辖市则显示直辖市名称；局域网网段内IP或者非法IP或国外IP则返回空")
    private List<AmapTip> tips;

}
