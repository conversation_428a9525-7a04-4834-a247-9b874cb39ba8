package com.juneyaoair.oneorder.disney;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: hcb
 * @Date: 2024/1/11 16:10
 * @Description: DisneyItemDto
 * @Version 1.0.0
 */
@Data
public class DisneyItemDto {

    @ApiModelProperty(value = "渠道订单号")
    private String sourceID;

    @ApiModelProperty(value = "预占SessionId")
    private String sessionID;

    private String disneyPlu;
    @ApiModelProperty(value = "同行游客信息")
    private List<DisneyGuestInfoDto> guestInfoList;
}