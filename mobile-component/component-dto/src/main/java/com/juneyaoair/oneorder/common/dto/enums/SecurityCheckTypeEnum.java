package com.juneyaoair.oneorder.common.dto.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName SecurityCheckTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 14:34
 * @Version 1.0
 */
public enum SecurityCheckTypeEnum {
    FSET("FSET", "首次设置消费密码或忘记消费密码"),
    RESET("RESET", "修改消费密码");
    private final String code;
    private final String desc;

    SecurityCheckTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @param v
     * @return com.juneyaoair.oneorder.common.dto.enums.SecurityCheckTypeEnum
     * <AUTHOR>
     * @Description 根据code过滤符合条件的SecurityCheckTypeEnum
     * @Date 14:37 2024/8/8
     **/
    public static SecurityCheckTypeEnum toFilterSecurityCheckType(String v) {
        if (StringUtils.isEmpty(v)) {
            return null;
        }
        for (SecurityCheckTypeEnum c : SecurityCheckTypeEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }
}
