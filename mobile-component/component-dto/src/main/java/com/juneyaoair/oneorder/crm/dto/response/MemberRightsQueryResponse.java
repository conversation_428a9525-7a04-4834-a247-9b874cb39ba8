package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.oneorder.crm.dto.common.MemberRightsRuleSoaModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MemberRightsQueryResponse
 * @Description 会员权益响应
 * <AUTHOR>
 * @Date 2019/9/5 16:58
 **/
@Data
@NoArgsConstructor
public class MemberRightsQueryResponse {

    @SerializedName("RightsInfos")
    List<MemberRightsRuleSoaModel> rightsInfos;

}
