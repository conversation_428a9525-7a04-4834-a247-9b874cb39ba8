package com.juneyaoair.oneorder.airport;

import java.math.BigDecimal;
import java.util.List;

public class SimplifyCityPO {
    public List<DSTDurationPO> dstDurationList;
    public Integer dstOffset;
    public String cityCode;
    public String cityName;
    public String cityEName;
    public String countryCode;
    public String cityPinYin;
    public BigDecimal provinceId;
    public String provinceName;
    public String isInternational;

    public SimplifyCityPO(List<DSTDurationPO> dstDurationList, Integer dstOffset, String cityCode, String cityName, String cityEName, String countryCode, String cityPinYin, BigDecimal provinceId, String provinceName, String isInternational) {
        this.dstDurationList = dstDurationList;
        this.dstOffset = dstOffset;
        this.cityCode = cityCode;
        this.cityName = cityName;
        this.cityEName = cityEName;
        this.countryCode = countryCode;
        this.cityPinYin = cityPinYin;
        this.provinceId = provinceId;
        this.provinceName = provinceName;
        this.isInternational = isInternational;
    }

    public SimplifyCityPO() {
    }

    public List<DSTDurationPO> getDstDurationList() {
        return this.dstDurationList;
    }

    public Integer getDstOffset() {
        return this.dstOffset;
    }

    public String getCityCode() {
        return this.cityCode;
    }

    public String getCityName() {
        return this.cityName;
    }

    public String getCityEName() {
        return this.cityEName;
    }

    public String getCountryCode() {
        return this.countryCode;
    }

    public String getCityPinYin() {
        return this.cityPinYin;
    }

    public BigDecimal getProvinceId() {
        return this.provinceId;
    }

    public String getProvinceName() {
        return this.provinceName;
    }

    public String getIsInternational() {
        return this.isInternational;
    }

    public void setDstDurationList(List<DSTDurationPO> dstDurationList) {
        this.dstDurationList = dstDurationList;
    }

    public void setDstOffset(Integer dstOffset) {
        this.dstOffset = dstOffset;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public void setCityEName(String cityEName) {
        this.cityEName = cityEName;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public void setCityPinYin(String cityPinYin) {
        this.cityPinYin = cityPinYin;
    }

    public void setProvinceId(BigDecimal provinceId) {
        this.provinceId = provinceId;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public void setIsInternational(String isInternational) {
        this.isInternational = isInternational;
    }

}
