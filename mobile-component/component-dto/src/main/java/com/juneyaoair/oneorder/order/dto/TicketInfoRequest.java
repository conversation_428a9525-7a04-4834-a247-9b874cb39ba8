package com.juneyaoair.oneorder.order.dto;

import lombok.Data;

/**
 * 客票信息查询请求类
 */
@Data
public class TicketInfoRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String TicketNo;
    /**
     * 旅客姓名
     **/
    private String PassengerName;
    private String CertNo;
    private String CertType;
    /**
     * 查询类型，是否可升舱或改期
     * UPGRADE升舱，CHANGE改期，默认空兼容之前查询，不处理
     */
    private String QueryType;

    public TicketInfoRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }
}
