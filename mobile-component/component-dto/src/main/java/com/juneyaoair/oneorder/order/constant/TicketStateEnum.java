package com.juneyaoair.oneorder.order.constant;

/**
 * 票状态
 */
public class TicketStateEnum {
    //客票状态
    public static final String OPEN_FOR_USE = "OPEN FOR USE";   /// 客票有效，可以使用
    public static final String USED_FLOWN = "USED/FLOWN"; /// 客票已使用
    public static final String REFUND_APPLICATION = "REFUND APPLICATION";/// 退票申请（统一订单平台内部状态航信系统没有此状态，退票已申请在退票处理流程中）
    public static final String REFUNDED = "REFUNDED"; /// 客票已退票
    public static final String EXCHANGED = "EXCHANGED"; /// 客票已换开到其它电子票上
    public static final String SUSPENDED = "SUSPENDED";/// 挂起
    public static final String VOID = "VOID";         /// 作废
    public static final String FIM_EXCH = "FIM EXCH";/// 客票已换开为飞行中断旅客舱单
    public static final String PRINT_EXCH = "PRINT EXCH";/// 客票已换开为纸票

    private TicketStateEnum(){}
    /**
     * 客票已值机
     */
    public static final String CHECKED_IN = "CHECKED IN";
    //航变
    public static final String AIRP_CNTL = "AIRP CNTL/HO";
}
