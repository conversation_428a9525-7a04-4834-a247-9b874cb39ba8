package com.juneyaoair.oneorder.domain;

/**
 * 免打扰类型
 * <AUTHOR>
 */
public enum NotDisturbDayType {

    /** 描述 */
    DAY_30(30, "30天免打扰"),
    ;

    /** 描述 */
    private final int day;
    private final String desc;

    NotDisturbDayType(int day, String desc) {
        this.day = day;
        this.desc = desc;
    }

    public int getDay() {
        return day;
    }

    public String getDesc() {
        return desc;
    }

}
