package com.juneyaoair.oneorder.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 私域运营弹窗提示信息
 * <AUTHOR>
 */
@Data
public class PrivateDomainInfo {

    @ApiModelProperty(value = "旅客归属地域城市码")
    private String travellerCityCode;

    @ApiModelProperty(value = "旅客归属地域城市名")
    private String travellerCityName;

    @ApiModelProperty(value = "私域高价值旅客")
    private boolean privateHighValueTraveller;

    @ApiModelProperty(value = "是否是吉祥航空会员")
    private boolean juneyaoairMember;

    @ApiModelProperty(value = "是否实名")
    private boolean realName;

    @ApiModelProperty(value = "是否更新授权微信")
    private boolean authorizedWechat;

    @ApiModelProperty(value = "是否添加企业微信好友")
    private boolean addWecom;

    @ApiModelProperty(value = "是否免打扰用户")
    private boolean noDisturbingUser;

    @ApiModelProperty(value = "是否当日可营销私域高价值客户")
    private boolean markertingDayUser;

    @ApiModelProperty(value="获客链接名")
    private String linkName;

    @ApiModelProperty(value="获客链接地址")
    private String linkUrl;

}
