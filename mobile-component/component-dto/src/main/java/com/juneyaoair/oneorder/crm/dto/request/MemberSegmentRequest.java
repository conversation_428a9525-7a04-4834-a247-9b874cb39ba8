package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberSegmentRequest
 * @Description CRM航段明细查询请求参数
 * <AUTHOR>
 * @Date 2020/3/17 14:53
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberSegmentRequest {

    /**
     * 会员卡号
     */
    @JsonProperty(value = "MemberId")
    private int MemberId;

}
