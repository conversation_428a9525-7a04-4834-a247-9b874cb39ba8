package com.juneyaoair.oneorder.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "PageResult",description = "分页传输对象")
public class PageResult<T> {

    @ApiModelProperty("数据行列表")
    private List<T> rows;

    @ApiModelProperty("总记录数")
    private long total;

    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @ApiModelProperty("每页记录数")
    private Integer pageSize;

    public PageResult(List<T> rows, long total, int pageNum, int pageSize) {
        this.rows = rows;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

}
