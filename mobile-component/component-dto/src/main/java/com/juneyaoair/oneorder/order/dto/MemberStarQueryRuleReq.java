package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 会员星级规则请求信息
 * @Version: V1.0
 * created by ZhangJingShuang on 2021/12/27 14:33
 */
@Data
public class MemberStarQueryRuleReq {
     //会员级别
     @JsonProperty(value = "MemberLevelCode")
     private String MemberLevelCode;
     //会员星级
     @JsonProperty(value = "MemberStarCode")
     private String MemberStarCode;
     //页码
     @JsonProperty(value = "PageIndex")
     private Integer PageIndex;
     //页大小
     @JsonProperty(value = "PageSize")
     private Integer PageSize;
     //记录id
     @JsonProperty(value = "RecordId")
     private Integer RecordId;
     //星级名称
     @JsonProperty(value = "StarName")
     private String StarName;
}