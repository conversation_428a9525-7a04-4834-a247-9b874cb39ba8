package com.juneyaoair.oneorder.order.dto;

import com.juneyaoair.oneorder.crm.dto.response.QueryCommonPersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 新增或修改请求业务参数
 * @date 2019/8/7  10:33.
 */
@Data
@NoArgsConstructor
public class ModifyPerson {
    private Boolean addFlag;// true新增，false 合并,null不处理
    @NotNull(message = "乘客信息不能为空")
    private QueryCommonPersonInfo commonPersonInfo;
    private Boolean isRemove;//是否删除
    private String depCityCode; //出发城市三字码
    private String arrCityCode; //到达城市三字码
    private String modified;//主题卡,不支持修改
    @ApiModelProperty(value = "航班日期--最后一段行程日期")
    private String flightDate;
}
