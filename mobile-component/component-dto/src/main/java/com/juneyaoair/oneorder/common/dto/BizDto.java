package com.juneyaoair.oneorder.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 14:02
 */
@Data
@NoArgsConstructor
@ApiModel(value = "BizDto",description = "业务通用流水号")
public class BizDto {
    @ApiModelProperty(value = "请求流水号",notes = "客户端随机生成，建议不超过32位，用于日志追踪",required = true)
    private String bizSeq;
    @ApiModelProperty(value = "客户端IP地址",hidden = true)
    private String ip;
    @ApiModelProperty(value = "渠道号",hidden = true,notes = "此渠道为客户端发起渠道,来源于请求头")
    private String headChannelCode;
    @ApiModelProperty(value = "客户端版本号",hidden = true,notes = "客户端请求发起版本")
    private String headClientVersion;
    @ApiModelProperty(value = "客户端编译版本",hidden = true,notes = "客户端编译版本")
    private String headVersionCode;
    @ApiModelProperty(value="操作平台")
    private String platformInfo;
    @ApiModelProperty(value="设备指纹来源",hidden = true)
    private String from;

    public BizDto(String ip, String headChannelCode) {
        this.ip = ip;
        this.headChannelCode = headChannelCode;
    }
}
