package com.juneyaoair.oneorder.crm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/7/20  10:01.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Header {
    private String ClientIP;   //客户端IP
    private String ClientVersion;  //客户端版本
    private String Token;   //登录凭证
    private Long MemberId;  //登录会员Id
    private Long Timestamp;  //时间戳（默认为系统当前时间）
}
