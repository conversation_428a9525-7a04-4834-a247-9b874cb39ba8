package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @ClassName LoungeCouponExt
 * <AUTHOR>
 * @Description
 * @Date 2020-12-25 14:36
 **/
@Data
public class LoungeCouponExt {

    /**
     * 航站楼
     */
    @SerializedName("AirportTerminal")
    private String airportTerminal;
    /**
     * 休息室位置
     */
    @SerializedName("LoungeAddress")
    private String loungeAddress;

    /**
     * 营业开始时间 HH:mm
     */
    @SerializedName("StartDate")
    private String startDate;
    /**
     * 营业结束时间 HH:mm
     */
    @SerializedName("EndDate")
    private String endDate;
    /**
     * 特别提示
     */
    @SerializedName("SpecialInformation")
    String specialInformation;
    /**
     * 规格
     */
    @SerializedName("Specifications")
    String specifications;
}
