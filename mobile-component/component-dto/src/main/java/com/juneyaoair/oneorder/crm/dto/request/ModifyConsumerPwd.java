package com.juneyaoair.oneorder.crm.dto.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 修改消费密码
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-12-10 17:30
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ModifyConsumerPwd {
    @NotBlank(message="旧密码不能为空")
    @ApiModelProperty(value = "旧消费密码")
    private String oldPassword;

    @NotBlank(message="新密码不能为空")
    @ApiModelProperty(value = "新消费密码")
    private String newPassword;

    @ApiModelProperty(value = "确认消费密码")
    private String confirmedPassword;
}
