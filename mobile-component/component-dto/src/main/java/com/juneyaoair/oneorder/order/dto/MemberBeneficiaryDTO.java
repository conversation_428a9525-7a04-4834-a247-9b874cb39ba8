package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName MemberQueryBeneficiaryResponse
 * @Description 积分受益人列表查询相应参数
 * <AUTHOR>
 * @Date 2019/7/30 15:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "MemberBeneficiaryDTO")
@XmlAccessorType(XmlAccessType.FIELD)
public class MemberBeneficiaryDTO {

    /**
     * 记录id
     */
    @JsonProperty(value = "RecordId")
    private Integer RecordId;

    /**
     * 会员id
     */
    @JsonProperty(value = "MemberId")
    private Integer MemberId;

    /**
     * 性别
     * 男-M，女-F, 其他-O
     */
    @JsonProperty(value = "Sex")
    private String Sex;

    /**
     * 生日 yyyy-MM-dd
     */
    @JsonProperty(value = "Birthday")
    private String Birthday;

    /**
     * 英文姓
     */
    @JsonProperty(value = "ELastName")
    private String ELastName;
    /**
     * 英文名
     */
    @JsonProperty(value = "EFirstName")
    private String EFirstName;
    /**
     * 中文姓 不包含特殊字符的中文姓名
     */
    @JsonProperty(value = "CLastName")
    private String CLastName;
    /**
     * 中文名  不包含特殊字符的中文姓名
     */
    @JsonProperty(value = "CFirstName")
    private String CFirstName;

    /**
     * 国籍
     * 国家二字码
     */
    @JsonProperty(value = "Nationality")
    private String Nationality;

    /**
     * 生效日期
     */
    @JsonProperty(value = "EffectDate")
    private String EffectDate;

    /**
     * 状态
     */
    @JsonProperty(value = "Status")
    private String Status;

    /**
     * 备注
     */
    @JsonProperty(value = "Comments")
    private String Comments;

    /**
     * 证件信息
     */
    @JsonProperty(value = "Certificate")
    private List<MemberBeneficiaryCertificateDTO> Certificate;

}
