package com.juneyaoair.oneorder.crm.dto.response;

import com.juneyaoair.oneorder.crm.dto.common.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/23  11:29.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtMemberDetail {
    private MemberBasicInfoSoaModel BasicInfo;//基本信息
    private MemberStateInfoSoaModel StateInfo;//状态信息
    private List<MemberCertificateSoaModelV2> CertificateInfo;//证件信息
    private List<MemberContactSoaModel> ContactInfo; //联系信息
    private List<MemberAddressSoaModel> AddressInfos; //AddressInfos
    private List<MemberRealNameSummarySoaModel> RealVerifyInfos;  //实名认证信息
    /**
     * 第三方账号信息
     */
    private List<MemberThirdpartySoaModel> ThirdpartyInfos;
}

