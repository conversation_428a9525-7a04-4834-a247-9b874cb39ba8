package com.juneyaoair.oneorder.crm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2018/7/20  16:03
 * 对CRM的返回结果
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtCRMResponse<T> {
    private T Data;
    /**
     * 请求是否成功
     */
    private boolean IsSuccess;
    /**
     * 异常码，0为成功
     */
    private Integer Code;
    /**
     * 异常信息
     */
    private String Msg;
    /**
     * 内部异常信息
     */
    private String InnerMsg;
}
