package com.juneyaoair.oneorder.ticket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 改期规则
 * @date 2018/12/4  21:02.
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties({"flightFlag","timeConditionStart","timeConditionEnd","leftTime","leftTimeUnit","originalLeftTimeUnit","rightTime","rightTimeUnit","originalRightTimeUnit"})
public class ChangeAndRefundRule {
    private String desc;//描述  如 起飞前2小时前   起飞前2小时后
    private Double changeFee;//改期费用
    private String ruleDesc;//规则描述


    private int flightFlag; //用于标记起飞前后
    private String timeConditionStart;
    private String timeConditionEnd;

    private String leftTime;
    private String leftTimeUnit;
    private String originalLeftTimeUnit;

    private String rightTime;
    private String rightTimeUnit;
    private String originalRightTimeUnit;

    public ChangeAndRefundRule(String desc, Double changeFee, String ruleDesc){
        this.desc=desc;
        this.changeFee=changeFee;
        this.ruleDesc=ruleDesc;
    }
}
