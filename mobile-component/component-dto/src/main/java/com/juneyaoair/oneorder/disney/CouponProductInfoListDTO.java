package com.juneyaoair.oneorder.disney;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
public class CouponProductInfoListDTO {

    @ApiModelProperty(value = "产品ID")
    @NotBlank(message = "产品ID不能为空")
    private String productId;

    @ApiModelProperty(value = "产品类型")
    @NotBlank(message = "产品类型不能为空")
    private String productType;

    @ApiModelProperty(value = "销售价格")
    @NotNull(message = "销售价格不能为空")
    private BigDecimal salePrice;
    /*CNY*/
    @ApiModelProperty(value = "币种")
    private String currencyCode;

    @ApiModelProperty(value = "销售数量")
    @NotNull(message = "销售数量不能为空")
    private Double saleCount;


    @Valid
    @ApiModelProperty(value = "预定客票信息")
    private BookedTicketDto bookedTicketInfo;

    @Valid
    @ApiModelProperty(value = "迪士尼客票信息")
    private DisneyItemDto disneyItemDto;

}
