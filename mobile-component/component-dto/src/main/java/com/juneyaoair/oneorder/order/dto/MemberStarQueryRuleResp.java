package com.juneyaoair.oneorder.order.dto;

import lombok.Data;

/**
 * @Description: 会员星级规则信息
 * @Version: V1.0
 * created by ZhangJingShuang on 2021/12/27 14:33
 */
@Data
public class MemberStarQueryRuleResp {
    //规则生效日
    private String EffectiveDate;
    //规则失效日
    private String ExpireDate;
    //会员级别
    private String MemberLevelCode;
    //会员星级
    private String MemberStarCode;
    //记录id
    private String RecordId;
    //最大航段数
    private String SegmentMax;
    //最小航段数
    private String SegmentMin;
}
/*
MemberStarRuleModel {
    EffectiveDate (string, optional): 规则生效日 ,
    ExpireDate (string, optional): 规则失效日 ,
    MemberLevelCode (string, optional): 会员级别 ,
    MemberStarCode (string, optional): 会员星级 ,
    OperateDate (string, optional): 操作日期 ,
    OperateUserId (string, optional): 操作人 ,
    RecordId (integer, optional): 记录id ,
    SegmentMax (integer, optional): 最大航段数 ,
    SegmentMin (integer, optional): 最小航段数 ,
    StarName (string, optional): 星级名称 ,
    UpdateDate (string, optional): 更新日期 ,
    UpdateUserId (string, optional): 更新人
}
 */