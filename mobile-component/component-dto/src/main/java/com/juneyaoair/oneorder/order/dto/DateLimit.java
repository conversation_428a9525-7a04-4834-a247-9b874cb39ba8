package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 日期限制值
 * @Date 14:34 2023/6/19
 * @return null
 **/

@Data
public class DateLimit {
    @SerializedName("0：不适用条件 1：适用条件 多条日期范围可能会有交集，以不适用优先。例：2018-11-11至2019-06-30适用但 2019-02-01至2019-02-20不适用")
    @ApiModelProperty(value = "航班号")
    private int limitType;

    @SerializedName("StartDate")
    @ApiModelProperty(value = "起始日期 为空即无限制")
    private String startDate;

    @SerializedName("EndDate")
    @ApiModelProperty(value = "结束日期 为空即无限制")
    private String endDate;
}
