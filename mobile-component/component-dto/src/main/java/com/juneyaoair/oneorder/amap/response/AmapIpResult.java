package com.juneyaoair.oneorder.amap.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 高德地图-IP定位返回
 * @created 2024/4/8 15:34
 */
@Data
public class AmapIpResult {

    @ApiModelProperty(value = "状态 0表示失败 1表示成功")
    private String status;

    @ApiModelProperty(value = "返回状态说明，status为0时，info返回错误原因，否则返回“OK”")
    private String info;

    @ApiModelProperty(value = "状态说明,10000代表正确,详情参阅info状态表")
    private String infocode;

    @ApiModelProperty(value = "省份名称 直辖市显示直辖市名称；局域网IP网段则返回“局域网”；非法IP以及国外IP则返回空")
    private String province;

    @ApiModelProperty(value = "城市名称 直辖市则显示直辖市名称；局域网网段内IP或者非法IP或国外IP则返回空")
    private String city;

    @ApiModelProperty(value = "城市的adcode编码")
    private String adcode;

    @ApiModelProperty(value = "所在城市矩形区域范围（所在城市范围的左下右上对标对）")
    private String rectangle;

}
