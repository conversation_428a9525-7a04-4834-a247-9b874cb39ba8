package com.juneyaoair.oneorder.crm.dto.common;

/**
 * <AUTHOR>
 * @description 尊称枚举
 * @date 2024/10/10 9:22
 */
public enum SalutationEnum {

    UNKNOW(0, "UNKNOW", "未知",""),
    MR(1, "MR", "先生","Male"),
    MS(2, "MS", "女士","Female"),
    MISS(3, "MISS", "小姐","Female"),
    MRS(4, "MRS", "夫人","Female");
    private int code;
    private String eName;
    private String desc;
    private String sex;

    SalutationEnum(int code, String eName, String desc, String sex) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
        this.sex = sex;
    }

    public String geteName() {
        return eName;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getSex() {
        return sex;
    }

    public static SalutationEnum formatSexCode(int code){
        for (SalutationEnum c: SalutationEnum.values()) {
            if (c.code==code) {
                return c;
            }
        }
        return null;
    }

}
