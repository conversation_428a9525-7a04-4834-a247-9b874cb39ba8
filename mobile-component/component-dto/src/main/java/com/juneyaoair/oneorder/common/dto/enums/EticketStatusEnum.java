package com.juneyaoair.oneorder.common.dto.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum EticketStatusEnum {
    OPEN_FOR_USE("OPEN FOR USE", "开放使用"),
    USED_FLOWN("USED/FLOWN", "已使用"),
    REFUNDED("REFUNDED", "已退票"),
    EXCHANGED("EXCHANGED", "已换开"),
    SUSPENDED("SUSPENDED", "挂起"),
    VOID("VOID", "已作废"),
    CHECKED_IN("CHECKED IN", "已值机"),
    OPEN("OPEN", "航程待确认"),
    LIFT_BOARDED("LIFT/BOARDED", "已登机"),
    NONE("NONE", "未知");

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    EticketStatusEnum(String code, String desc) {
        this.code =code;
        this.desc =desc;
    }

    public static EticketStatusEnum getEnumByCode(String code){
        return s_map.getOrDefault(code,NONE);
    }

    private static Map<String,EticketStatusEnum> s_map = new HashMap<>();


    static {
        for (EticketStatusEnum value : EticketStatusEnum.values()) {
            if (StringUtils.isBlank(value.code)) {
                continue;
            }
            s_map.put(value.code,value);
        }
    }
}
