package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 行李额度特有属性
 * @Date 14:33 2023/6/19
 * @return null
 **/

@Data
public class BaggageExtInfo {
    @SerializedName("BaggageUnit")
    private String baggageUnit;
    @SerializedName("BaggageValue")
    private int baggageValue;
    @SerializedName("BaggageSize")
    private String baggageSize;

    @SerializedName("IsIntl")
    @ApiModelProperty(value = "国内DOMESTIC;国际INTL")
    private String isIntl;

    @SerializedName("ProductName")
    private String productName;
}
