package com.juneyaoair.oneorder.common.dto;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 基础请求参数 极验非必须
 * @date 2024/8/12 9:08
 */
@Data
public class RequestDataDto<T> implements GeetestInterface, RequestInterface {

    @NotBlank(message = "{channelNo.notNull}")
    @ApiModelProperty(value = "渠道号", hidden = true)
    private String channelNo;

    @ApiModelProperty(value = "当前所选币种，来源于网站语言切换")
    private String currency;

    @NotNull(message = "{language.notNull}")
    @ApiModelProperty(value = "语言", hidden = true)
    private LanguageEnum language;

    @ApiModelProperty(value = "会员ID",hidden = true)
    private String ffpId;

    @ApiModelProperty(value = "会员卡号",hidden = true)
    private String ffpNo;

    @ApiModelProperty(value = "来源IP", hidden = true)
    private String originIp;

    @ApiModelProperty(value = "客户端版本", hidden = true)
    private String clientVersion;

    @ApiModelProperty(value = "设备指纹信息")
    private String blackBox;

    @ApiModelProperty(value="极验流水号")
    private String geetest_challenge;

    @ApiModelProperty(value="极验验证串")
    private String geetest_validate;

    @ApiModelProperty(value="极验时间戳")
    private String geetest_seccode;

    @ApiModelProperty(value = "极验使用CLIENT_TYPE,web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式", allowableValues = "web,h5,native",
            notes = "web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式",
            example = "web,h5,native")
    private String client_type;

    @ApiModelProperty(value="是否通过token有效期校验 true：通过 false:未通过", hidden = true)
    private boolean loginFlag;

    @Valid
    @ApiModelProperty(value = "数据")
    private T data;

}
