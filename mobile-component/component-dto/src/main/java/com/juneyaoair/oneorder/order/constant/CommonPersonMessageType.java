package com.juneyaoair.oneorder.order.constant;

public enum CommonPersonMessageType {

    UNLIMITED_FLY_V2_PAY("UNLIMITED_FLY_V2_PAY", "畅飞卡2.0需付费购买"),
    UNLIMITED_FLY_V2_NOT_PURCHASED("UNLIMITED_FLY_V2_NOT_PURCHASED", "畅飞卡2.0未购买"),
    NOT_ALLOWED("NOT_ALLOWED", "弹框提示且不允许客人选择")
    ;

    CommonPersonMessageType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
