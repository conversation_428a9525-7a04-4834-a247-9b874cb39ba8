package com.juneyaoair.oneorder.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 基础请求参数
 * @date 2024/8/12 9:08
 */
@Data
public class BaseRequestData<T> {

    @NotBlank(message = "渠道不能为空")
    @ApiModelProperty(value = "渠道编号 参照：ChannelEnum")
    private String channelNo;

    @NotBlank(message = "来源IP不能为空")
    @ApiModelProperty(value = "来源IP")
    private String originIp;

    @Valid
    @ApiModelProperty(value = "数据")
    private T data;

}
