package com.juneyaoair.oneorder.crm.dto.response;

import com.juneyaoair.oneorder.crm.dto.common.MemberLevelChangeSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MileageExpireBillSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MileagePeriodBillSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MileageTotalBillSoaModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MileageAccountQueryResponse
 * @Description 积分账户查询响应参数
 * <AUTHOR>
 * @Date 2020/3/6 16:53
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageAccountQueryResponse {
    /**
     * 总账信息
     */
    private MileageTotalBillSoaModel TotalBill;
    /**
     * 时间段账单信息
     */
    private MileagePeriodBillSoaModel PeriodBill;

    /**
     * 积分到期账单
     */
    private List<MileageExpireBillSoaModel> ExpireBills;
    /**
     * 会员升降级信息
     */
    private MemberLevelChangeSoaModel LevelChange;

}
