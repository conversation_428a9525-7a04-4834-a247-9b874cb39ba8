package com.juneyaoair.oneorder.crm.dto.request;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FamilyAccountInviteQueryReqDto {
    /**
     * 查询截止日期 ,
     */
    private String EndDate;
    /**
     *家庭账户id(默认为空，如果不为空则查询指定家庭账户id) ,
     */
    private String FamilyAccountId;
    /**
     *邀请类型(1-发出邀请 0-收到邀请) ,
     */
    private String InviteType;
    /**
     *会员卡号 ,
     */
    private String MemberId;
    /**
     * 查询截止日期 ,
     */
    private String StartDate;
}
