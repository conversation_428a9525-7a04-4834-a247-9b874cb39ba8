package com.juneyaoair.oneorder.ticket;

import com.juneyaoair.oneorder.common.dto.enums.EticketStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/9 13:44
 */
@Data
public class TicketFlight {
    @ApiModelProperty("子舱位")
    private String cabin;
    @ApiModelProperty(value = "舱位等级名称")
    private String cabinName;
    @ApiModelProperty("航班号")
    private String flightNo;
    @ApiModelProperty("航班日期")
    private String flightDate;
    @ApiModelProperty("出发城市名称")
    private String depCityName;
    @ApiModelProperty(value = "出发机场名称")
    private String depAirportName;
    @ApiModelProperty(value = "出发机场航站楼")
    private String depAirportTerminal;
    @ApiModelProperty("到达城市名称")
    private String arrCityName;
    @ApiModelProperty(value = "到达机场名称")
    private String arrAirportName;
    @ApiModelProperty(value = "到达机场航站楼")
    private String arrAirportTerminal;
    @ApiModelProperty("出发时刻")
    private String depTime;
    @ApiModelProperty("到达时刻")
    private String arrTime;
    /**
     * @see EticketStatusEnum
     **/
    @ApiModelProperty("客票状态 参照：EticketStatusEnum")
    private String ticketStatus;
    @ApiModelProperty(value = "周几")
    private String week;
    @ApiModelProperty(value = "退款规则；国内客票使用",notes = "国内客票使用")
    private List<ChangeAndRefundRule> refundRule;
    @ApiModelProperty(value = "改期规则；国内客票使用",notes = "国内客票使用")
    private List<ChangeAndRefundRule> changeRule;
    @ApiModelProperty(value = "退款规则描述文案；国际/地区客票使用",notes = "国际/地区客票使用")
    private String refundRuleDesc;
    @ApiModelProperty(value = "改期规则描述文案；国际/地区客票使用",notes = "国际/地区客票使用")
    private String changeRuleDesc;

}
