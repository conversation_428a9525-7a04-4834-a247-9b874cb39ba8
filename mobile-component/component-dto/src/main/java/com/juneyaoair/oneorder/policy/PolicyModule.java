package com.juneyaoair.oneorder.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/8/9  15:31.
 */
@Data
@ApiModel(value = "PolicyModule",description = "模块信息")
public class PolicyModule {
    @ApiModelProperty(value = "模块名称")
    private String moduleName;
    @ApiModelProperty(value = "排序号")
    private int orderNum;
    @ApiModelProperty(value = "隐私政策明细列表")
    private List<NoticeInfo> noticeInfoList;
}
