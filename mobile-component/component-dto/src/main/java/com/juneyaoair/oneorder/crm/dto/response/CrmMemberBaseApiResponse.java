package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrmMemberBaseApiResponse<T> {
    @ApiModelProperty(value = "结果代码（0:成功，其他:失败）")
    private Integer Code;
    @ApiModelProperty(value = "响应业务实体")
    private T Data;
    @ApiModelProperty(value = "结果描述")
    private String Desc;
    @ApiModelProperty(value = "响应加密业务实体")
    private String Encryptor;
    @ApiModelProperty(value = "签名")
    private String Signature;
}
