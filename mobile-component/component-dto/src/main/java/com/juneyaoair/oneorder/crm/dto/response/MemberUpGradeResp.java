package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2019-3-1 14:10
 * @description：给前端的升级积分返回值
 * @modified By：
 * @version: $
 */
@Data
public class MemberUpGradeResp {

    @ApiModelProperty(value = "新级别名称")
    private String newLevelName;

    @ApiModelProperty(value = "新级别编码")
    private String newLevelCode;

    @ApiModelProperty(value = "升级所需积分")
    private int levelUPMile;

    @ApiModelProperty(value = "升级所需航段")
    private int levelUPSegment;

    @ApiModelProperty(value = "截止日期（格式yyyy-mm-dd）")
    private String endDate;

    @ApiModelProperty(value = "升级所需积分百分比")
    private String upMilePercent;

    @ApiModelProperty(value = "升级所需航段百分比")
    private String upSegmentPercent;

    @ApiModelProperty(value = "定级积分")
    private int nextLevelMile;

    @ApiModelProperty(value = "定级航段")
    private int nextLevelSegment;
}
