package com.juneyaoair.oneorder.order.dto;


import lombok.Data;


/**
 * <AUTHOR>
 * @description 升舱改期请求是使用的航班信息
 * @date 2018/11/22  10:14.
 */
@Data
public class FlightInfo {
    private String id;
    private String flightDirection; //飞行方向去程为G,回程为B
    private String flightNo; //航班号
    private String depDateTime; //航班起飞时间yyyy-MM-dd HH:mm
    private String arrDateTime; //航班到达时间yyyy-MM-dd HH:mm
    private String flightDate; //
    private String depCity; //起飞城市三字码
    private String arrCity; //到达城市三字码
    private String depCityName; //起飞城市
    private String arrCityName; //到达城市
    private String depAirport; //起飞机场三字码
    private String arrAirport; //到达机场三字码
    private String depAirportName; //起飞机场
    private String arrAirportName; //到达机场
    private boolean codeShare; //是否共享航班
    private String carrierNo; //航班号
    private String fType; //机型
    private String mealCode; //餐食代码
    private boolean aSR;
    private int stopNumber; //经停次数
    private String depTerm; //起飞航站楼
    private String arrTerm; //到达航站楼
    private boolean etkt; //是否电子客票
    private double yQTax; //燃油费国内航程时该字段有效,国际时该字段无效值为-1
    private double cNTax; //建设税国内航程时该字段有效,国际时该字段无效值为-1
    private double duration; //飞行时长
    private String upCouponCode;//升舱优惠券
    private String ticketStatus;//客票状态
    private String cabinCode;
    /**
     * 票面价
     */
    private double priceValue;
    /**
     * 升舱票面差价费用
     */
    private double upgradeTicketPrice;
    /**
     * 升舱标记
     */
    private Boolean upgradeFlag;



    public String createSinaParam(){
        return this.flightNo+this.flightDate;
    }
}
