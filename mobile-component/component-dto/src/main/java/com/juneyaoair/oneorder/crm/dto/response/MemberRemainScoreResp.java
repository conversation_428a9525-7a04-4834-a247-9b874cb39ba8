package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 15:21 2018/9/13
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemberRemainScoreResp {
    private String resultCode;
    private String errorInfo;
    private String message;

    @ApiModelProperty(value = "000-成功，其他失败")
    private String statusCode;

    @ApiModelProperty(value = "总积分 = 可用积分+冻结")
    private String point;

    @ApiModelProperty(value = "冻结积分")
    private String freezePoint;

    @ApiModelProperty(value = "可用积分")
    private String usablePoint;

    @ApiModelProperty(value = "是否开通免密支付")
    private boolean smallExemptPwdStatus;

    @ApiModelProperty(value = "积分免密支付额度 默认20")
    private int freeScoreLimit;

}
