package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberCertificateReqDto {

    @ApiModelProperty(value = "证件号码")
    @JsonProperty(value = "CertificateNumber")
    private String CertificateNumber;

    @JsonProperty(value = "CertificateType")
    private Integer CertificateType;

    @ApiModelProperty(value = "会员id")
    @JsonProperty(value = "MemberId")
    private Integer MemberId;

    @ApiModelProperty(value = "备注")
    @JsonProperty(value = "Remark")
    private String Remark;

}
