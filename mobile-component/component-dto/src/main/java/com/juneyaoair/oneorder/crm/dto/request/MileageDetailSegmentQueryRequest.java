package com.juneyaoair.oneorder.crm.dto.request;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Data
@Getter
@Setter
public class MileageDetailSegmentQueryRequest {

    @JsonProperty("BeginDate")
    private String BeginDate;
    @JsonProperty("EndDate")
    private String EndDate;
    @JsonProperty("MemberId")
    private Integer MemberId;
    @JsonProperty("PageNo")
    private Integer PageNo;
    @JsonProperty("PageSize")
    private Integer PageSize;
}
