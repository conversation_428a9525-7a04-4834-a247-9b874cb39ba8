package com.juneyaoair.oneorder.amap.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 高德地图-输入提示返回结果
 * @created 2024/4/8 16:02
 */
@Data
public class AmapTip {

    @ApiModelProperty(value = "返回数据ID")
    private String id;

    @ApiModelProperty(value = "tip名称")
    private String name;

    @ApiModelProperty(value = "所属区域 省+市+区（直辖市为“市+区”）")
    private String district;

    @ApiModelProperty(value = "区域编码（六位区县编码）")
    private String adcode;

    @ApiModelProperty(value = "tip中心点坐标")
    private String location;

    @ApiModelProperty(value = "详细地址")
    private String address;

}
