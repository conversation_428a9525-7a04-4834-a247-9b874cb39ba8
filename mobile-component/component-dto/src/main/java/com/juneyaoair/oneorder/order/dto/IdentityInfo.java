package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@XmlRootElement(name = "IdentityInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class IdentityInfo {
	private String IdNo; //证件号DETR:,F提取到的信息，有证件信息、行程单和常客等等信息都在这里
	private String IdType; //证件类型

	private Date Birthdate;//出生日期
	private String Sex;//性别
	private String Nationality;//国籍国际票必填
	private String BelongCountry;//发证国国际票必填
	private Date CertValidity;//证件有效期yyyy-MM-dd国际票必填

	public String getIdNo(){
		return IdNo;
	}
	public void setIdNo(String IdNo){
		this.IdNo=IdNo;
	}
	public String getIdType(){
		return IdType;
	}
	public void setIdType(String IdType){
		this.IdType=IdType;
	}

	public Date getBirthdate() {
		return Birthdate;
	}

	public void setBirthdate(Date birthdate) {
		Birthdate = birthdate;
	}

	public String getSex() {
		return Sex;
	}

	public void setSex(String sex) {
		Sex = sex;
	}

	public String getNationality() {
		return Nationality;
	}

	public void setNationality(String nationality) {
		Nationality = nationality;
	}

	public String getBelongCountry() {
		return BelongCountry;
	}

	public void setBelongCountry(String belongCountry) {
		BelongCountry = belongCountry;
	}

	public Date getCertValidity() {
		return CertValidity;
	}

	public void setCertValidity(Date certValidity) {
		CertValidity = certValidity;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("IdentityInfo ["); 
		sb.append("IdNo="+IdNo+",");
		sb.append("IdType="+IdType);
		sb.append("]");
		return sb.toString();
	}
}