package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 畅飞卡绑定记录
 * <AUTHOR>
 * @Description
 * @create 2020-07-31 14:25
 */
@Data
public class UnlimitedFlyBindRecord {

    /**
     * 券号
     */
    @SerializedName("VoucherNo")
    private String voucherNo;

    /**
     * 绑定状态
     * yes | no
     */
    @SerializedName("BindStatus")
    private String bindStatus;

    /**
     * 绑定日期
     * yyyy-MM-dd
     */
    @SerializedName("BindingDate")
    private String bindingDate;

    /**
     * 券有效期开始时间
     * yyyy-MM-dd
     */
    @SerializedName("StartDate")
    private String startDate;

    /**
     * 绑定截至日期
     * yyyy-MM-dd
     */
    @SerializedName("LimitBindingDate")
    private String limitBindingDate;

    /**
     * 记录是否已过绑定有效期
     * Effective  有效
     * invalid     失效
     */
    @SerializedName("BindingValidity")
    private String bindingValidity;

    /**
     * 儿童中文姓名
     */
    @SerializedName("ChildCnName")
    private String childCnName;

    /**
     * 儿童英文姓名
     */
    @SerializedName("ChildEnName")
    private String childEnName;

    /**
     * 儿童身份证号
     */
    @SerializedName("ChildIdNumber")
    private String childIdNumber;

    /**
     * 记录noshow是否已达上限   yes：是
     */
    @SerializedName(value="NowShowStatus")
    private String noShowStatus;

}
