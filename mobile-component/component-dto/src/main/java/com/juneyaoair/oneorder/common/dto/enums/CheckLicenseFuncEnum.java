package com.juneyaoair.oneorder.common.dto.enums;

/**
 * <AUTHOR>
 * @description 关键功能频次控制枚举，具体频次值在apollo的checkLicense
 * @date 2024/11/6 10:24
 */
public enum CheckLicenseFuncEnum {

    SERVICE_REGISTER_IP("注册功能IP控制"),
    SERVICE_REGISTER_IP_ERROR("注册功能IP错误控制"),
    SERVICE_REGISTER_ACCOUNT("注册功能账户控制"),
    SERVICE_REGISTER_ACCOUNT_ERROR("注册功能账户错误控制"),

    SERVICE_LOGIN_IP("登录功能IP控制"),
    SERVICE_LOGIN_IP_ERROR("登录功能IP错误控制"),
    SERVICE_LOGIN_ACCOUNT("登录功能账户控制"),
    SERVICE_LOGIN_ACCOUNT_ERROR("登录功能账户错误控制"),

    SERVICE_SEND_SMS_IP("发送短信IP控制"),
    SERVICE_SEND_SMS_ACCOUNT("发送短信账户控制"),

    SERVICE_SEND_EMAIL_IP("发送邮件IP控制"),
    SERVICE_SEND_EMAIL_ACCOUNT("发送邮件账户控制"),

    REGISTER_SERVICE_SEND_EMAIL_IP("会员注册发送邮件账户控制"),
    REGISTER_SERVICE_SEND_EMAIL_ACCOUNT("会员注册发送邮件账户控制"),

    RESET_LOGIN_IP("重置登录密码IP控制"),
    RESET_LOGIN_IP_ERROR("重置登录密码IP错误控制"),
    RESET_LOGIN_ACCOUNT("重置登录密码账户控制"),
    RESET_LOGIN_ACCOUNT_ERROR("重置登录密码账户错误控制"),

    TRAVELLER_TRIP_IP("行程查询IP控制"),
    TRAVELLER_TRIP_FFP("行程查询会员控制"),
    TRAVELLER_TRIP_NO_DATA_IP("行程查询无行程控制"),
    TRAVELLER_TRIP_NO_DATA_FFP("行程查询无行程控制"),

    EMD_CANCEL_SEAT_IP("取消选座验证码发送IP控制"),
    EMD_CANCEL_SEAT_CONTACT("取消选座验证码发送联系方式控制"),

    VERIFY_TICKET_FFP_WHITE("客票验真白名单控制"),
    VERIFY_TICKET_IP("客票验真IP控制"),
    VERIFY_TICKET_FFP("客票验真会员控制"),

    ITINERARY_IP("行程确认单IP控制"),
    ITINERARY_FFP("行程确认单会员控制"),

    ITINERARY_SEND_IP("行程确认单发送IP控制"),
    ITINERARY_SEND_FFP("行程确认单发送会员控制"),

    TRAVEL_CONFIRMATION_FORM_IP("行程确认单验证码IP控制"),
    TRAVEL_CONFIRMATION_FORM_EMAIL("行程确认单验证码邮箱控制"),
    TRAVEL_CONFIRMATION_FORM_FFP("行程确认单验证码会员控制"),

    GLOBAL_SEAT_MAP_IP("国际官网座位图查询IP控制"),
    GLOBAL_RESERVE_SEAT_IP("国际官网值机选座IP控制"),
    GLOBAL_GET_SEAT_CONTACT_INFO_IP("国际官网查询选座联系方式信息IP控制"),
    GLOBAL_SEAT_SEND_VERIFY_CODE_IP("国际官网取消选座验证码次数IP控制"),
    GLOBAL_CANCEL_SEAT_IP("国际官网取消选座操作IP控制"),
    GLOBAL_CANCEL_SEAT_ORDER_IP("国际官网取消选座操作IP控制"),
    GLOBAL_REFUND_SEAT_ORDER_IP("国际官网优选座位退单操作IP控制"),
    GLOBAL_ADD_PEER_IP("国际官网添加同行人操作IP控制"),
    GLOBAL_GET_SEAT_CHECK_INFO_IP("国际官网添加同行人操作IP控制"),
    QUERY_COUPON_ORDER_REFUND_INFO_NO_LOGIN("查询权益订单退款详情NoLogin"),
    QUERY_COUPON_ORDER_DETAIL_NO_LOGIN("查询我的权益订单详情NoLogin"),

    SEND_VERIFY_CODE("发送验证码"),

    ;
    private final String desc;

    CheckLicenseFuncEnum(String desc) {
        this.desc = desc;
    }
}
