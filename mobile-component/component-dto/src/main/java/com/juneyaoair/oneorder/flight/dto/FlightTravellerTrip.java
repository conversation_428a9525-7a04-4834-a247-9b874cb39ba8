package com.juneyaoair.oneorder.flight.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FlightTravellerTrip {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 航班到达日期
     */
    private String flightArrDate;

    /**
     * 航班出发日期
     */
    private String flightDateOrg;

    /**
     * 英文航班日期
     */
    private String flightDateEn;

    /**
     * 出发时刻
     */
    private String depDateTime;

    /**
     * 出发时间时区
     */
    private Integer depDateLocalTimeZone;

    /**
     * 北京出发时间
     */
    private String depDateChinaTime;
    /**
     * 到达时刻
     */
    private String arrDateTime;

    /**
     * 到达时间时区
     */
    private Integer arrDateLocalTimeZone;

    /**
     * 北京到达时间
     */
    private String arrDateChinaTime;

    /**
     * 出发城市
     */
    private String depCity;
    /**
     * 到达城市
     */
    private String arrCity;

    /**
     * 出发机场
     */
    private String depAirport;

    /**
     * 到达机场
     */
    private String arrAirport;

    /**
     * 舱位
     */
    private String cabinCode;

    /**
     * 舱位数量
     */
    private String cabinNum;
    /**
     * 出发城市名称
     */
    private String depCityName;
    /**
     * 到达城市名称
     */
    private String arrCityName;
    /**
     *出发机场名称
     */
    private String depAirportName;
    /**
     * 到达机场名称
     */
    private String arrAirportName;

    private String depAirportTerminal;

    private String arrAirportTerminal;

    private String stopAirport;

    private String stopAirportName;

    @ApiModelProperty("机型")
    private String planeType;

    @ApiModelProperty("机型名称")
    private String planeTypeName;

    private Long duration;
}
