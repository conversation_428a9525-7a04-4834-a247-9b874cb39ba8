package com.juneyaoair.oneorder.common.dto;

import com.juneyaoair.oneorder.geetest.dto.GeetestClient;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 9:30
 */
@Data
@ApiModel(value = "GeetestDto",description = "极验验证参数")
public class GeetestDto extends GeetestClient implements GeetestInterface {
    @NotBlank(message = "流水信息不可为空")
    @ApiModelProperty(value="流水号",name="geetest_challenge",example="1111111",required=true)
    private String geetest_challenge;
    @NotBlank(message = "验证信息不可为空")
    @ApiModelProperty(value="验证串",name="geetest_validate",example="11111",required=true)
    private String geetest_validate;
    @NotBlank(message = "时间戳不可为空")
    @ApiModelProperty(value="时间戳",name="geetest_seccode",example="111111",required=true)
    private String geetest_seccode;
}
