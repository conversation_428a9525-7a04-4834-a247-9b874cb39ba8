package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ScoreGift")
@XmlAccessorType(XmlAccessType.FIELD)

public class PtScoreGift {
	private String ScoreGiftRuleID; //赠送规则ID
	private int GiftPercentage; //赠送比例计算结果进行四舍五入
	private int GiftScore; //赠送积分"在航班查询时为0，赠送需要根据实际支付价来计算，如果使用了积分需要扣除积分抵扣金额后再计算
	private String Remark; //赠送说明

	public String getScoreGiftRuleID(){
		return ScoreGiftRuleID;
	}
	public void setScoreGiftRuleID(String ScoreGiftRuleID){
		this.ScoreGiftRuleID=ScoreGiftRuleID;
	}
	public int getGiftPercentage(){
		return GiftPercentage;
	}
	public void setGiftPercentage(int GiftPercentage){
		this.GiftPercentage=GiftPercentage;
	}
	public int getGiftScore(){
		return GiftScore;
	}
	public void setGiftScore(int GiftScore){
		this.GiftScore=GiftScore;
	}
	public String getRemark(){
		return Remark;
	}
	public void setRemark(String Remark){
		this.Remark=Remark;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("ScoreGift ["); 
		sb.append("ScoreGiftRuleID="+ScoreGiftRuleID+",");
		sb.append("GiftPercentage="+GiftPercentage+",");
		sb.append("GiftScore="+GiftScore+",");
		sb.append("Remark="+Remark);
		sb.append("]");
		return sb.toString();
	}
}