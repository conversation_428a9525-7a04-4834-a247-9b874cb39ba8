package com.juneyaoair.oneorder.crm.dto.response;

import java.util.List;

public class CtripUnionMemberInfoAppResDto {
    /**
     * 携程会员级别
     */
    private String CtripMemberLevelCode;
    /**
     * 携程会员uid
     */
    private String CtripMemberUid;
    /**
     * 携程积分余额
     */
    private String CtripMiles;

    private boolean IsCtripMember;

    private String MemberName;

    public String getMemberName() {
        return MemberName;
    }

    public void setMemberName(String memberName) {
        MemberName = memberName;
    }

    private String MemberLevel;//会员级别描述  福卡
    private String MemberLevelCode;//会员级别代码

    public String getMemberLevel() {
        return MemberLevel;
    }

    public void setMemberLevel(String memberLevel) {
        MemberLevel = memberLevel;
    }

    public String getMemberLevelCode() {
        return MemberLevelCode;
    }

    public void setMemberLevelCode(String memberLevelCode) {
        MemberLevelCode = memberLevelCode;
    }

    public boolean isCtripMember() {
        return IsCtripMember;
    }

    public void setCtripMember(boolean ctripMember) {
        IsCtripMember = ctripMember;
    }

    /**
     * 权益明细列表
     */
    private List<CtripUnionMemberRightsDetailModel> RightsDetailList;

    public String getCtripMemberLevelCode() {
        return CtripMemberLevelCode;
    }

    public void setCtripMemberLevelCode(String ctripMemberLevelCode) {
        CtripMemberLevelCode = ctripMemberLevelCode;
    }

    public String getCtripMemberUid() {
        return CtripMemberUid;
    }

    public void setCtripMemberUid(String ctripMemberUid) {
        CtripMemberUid = ctripMemberUid;
    }

    public String getCtripMiles() {
        return CtripMiles;
    }

    public void setCtripMiles(String ctripMiles) {
        CtripMiles = ctripMiles;
    }

    public List<CtripUnionMemberRightsDetailModel> getRightsDetailList() {
        return RightsDetailList;
    }

    public void setRightsDetailList(List<CtripUnionMemberRightsDetailModel> rightsDetailList) {
        RightsDetailList = rightsDetailList;
    }
}
