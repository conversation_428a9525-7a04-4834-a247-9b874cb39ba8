package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2019-3-1 14:13
 * @description：返回给前端的保级返回值
 * @modified By：
 * @version: $
 */
@Data
public class MemberProGradeResp {
    @ApiModelProperty(value = "当前级别名称")
    private String currentLevelName;

    @ApiModelProperty(value = "当前级别Code")
    private String currentLevelCode;

    @ApiModelProperty(value = "当前级别编码")
    private String currentLevel;

    @ApiModelProperty(value = "会员星级")
    private int memberStar;

    @ApiModelProperty(value = "新级别名称")
    private String newLevelName;

    @ApiModelProperty(value = "截止日期（格式yyyy-mm-dd）")
    private String endDate;

    @ApiModelProperty(value = "有效剩余时间")
    private int timeLeft;

    @ApiModelProperty(value = "星级失效日期（格式yyyy-mm-dd）")
    private String starExpireDate;

    @ApiModelProperty(value = "保级所需积分百分比")
    private String proMilePercent;

    @ApiModelProperty(value = "保级所需航段百分比")
    private String proSegmentPercent;

    @ApiModelProperty(value = "保级所需积分")
    private int proMile;

    @ApiModelProperty(value = "保级所需航段（续级标准一共所需要的航段）")
    private int proSegment;

    @ApiModelProperty(value = "保级还需航段（续级标准航段减去失效日之前实际拥有的航段）")
    private int levelContinueSegment;

    @ApiModelProperty(value = "定级积分")
    private int gradingMile;

    @ApiModelProperty(value = "定级航段")
    private int gradingSegment;

    @ApiModelProperty(value = "是否敬医卡")
    private Boolean isDoctorCard;

    @ApiModelProperty(value = "敬医金卡  会员中心描述信息  享金卡基础权益并同时享有同行权益复制特权")
    private String doctorCardDesc;
}
