package com.juneyaoair.oneorder.order.dto;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@Data
public class PtTicketInfoReq {
	private String Version; // 接口版本号
	private String ChannelCode; // 渠道用户号
	private String UserNo; // 渠道工作人员号
	private String TicketNo; // 电子客票号
	private String PassengerName;//姓名
	private boolean IsGetIdentityInfos; // 是否读取证件信息
	private String QueryType;

	public PtTicketInfoReq() {
		super();
	}

	public PtTicketInfoReq(String version, String channelCode, String userNo, String ticketNo, boolean isGetIdentityInfos) {
		this.Version = version;
		this.ChannelCode = channelCode;
		this.UserNo = userNo;
		this.TicketNo = ticketNo;
		this.IsGetIdentityInfos = isGetIdentityInfos;
	}
	public String getVersion() {
		return Version;
	}

	public void setVersion(String version) {
		Version = version;
	}

	public String getChannelCode() {
		return ChannelCode;
	}

	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}

	public String getUserNo() {
		return UserNo;
	}

	public void setUserNo(String userNo) {
		UserNo = userNo;
	}	
	public String getTicketNo() {
		return TicketNo;
	}

	public void setTicketNo(String ticketNo) {
		TicketNo = ticketNo;
	}

	public boolean isIsGetIdentityInfos() {
		return IsGetIdentityInfos;
	}

	public void setIsGetIdentityInfos(boolean isGetIdentityInfos) {
		IsGetIdentityInfos = isGetIdentityInfos;
	}

	public String getQueryType() {
		return QueryType;
	}

	public void setQueryType(String queryType) {
		QueryType = queryType;
	}

	@Override
	public String toString() {
		return "TicketInfoReq [version=" + Version + ", channelCode=" + ChannelCode + ", userNo=" + UserNo + ", ticketNo=" + TicketNo + ", isGetIdentityInfos=" + IsGetIdentityInfos + "]";
	};
}
