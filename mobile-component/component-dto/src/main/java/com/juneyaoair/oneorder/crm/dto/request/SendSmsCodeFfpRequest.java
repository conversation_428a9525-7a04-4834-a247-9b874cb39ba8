package com.juneyaoair.oneorder.crm.dto.request;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName SendSmsCodeFfpRequest
 * @Description 短信发送验证码
 * <AUTHOR>
 * @Date 2024/7/23 14:44
 * @Version 1.0
 */

@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SendSmsCodeFfpRequest extends GeetestDto {

    @NotEmpty(message = "短信类型不能为空")
    @ApiModelProperty(value = "短信类型")
    private String type;

}
