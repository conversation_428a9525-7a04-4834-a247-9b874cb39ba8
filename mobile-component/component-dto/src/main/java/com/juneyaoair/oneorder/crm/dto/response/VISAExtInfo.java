package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VISAExtInfo {
    @ApiModelProperty(value = "签证类型")
    private String VisaType;

    @ApiModelProperty(value = "是否需要面试")
    private int Interview;

    @ApiModelProperty(value = "入境次数")
    private String EntryTimes;

    @ApiModelProperty(value = "最大停留时间")
    private String MaxStayDays;

    @ApiModelProperty(value = "有效期限")
    private String ExpiryDate;

    @ApiModelProperty(value = "办理预计工作日")
    private String ExpectedDays;

    @ApiModelProperty(value = "受理范围")
    private String AcceptanceRange;

    @ApiModelProperty(value = "受理时间")
    private String AcceptanceTime;

    @ApiModelProperty(value = "办理流程")
    private String ApplyProcess;

    @ApiModelProperty(value = "提前预定天数")
    private int AdvanceDays;

    @ApiModelProperty(value = "办理流程数组")
    private String[] processes;

    @ApiModelProperty(value = "截止收材料时间")
    private int endCollectDays;

    @ApiModelProperty(value = "预定须知和重要条款")
    private String ReservationNotes;

    @ApiModelProperty(value = "拒签全退")
    private String RefundAll;

    @ApiModelProperty(value = "办理方式")
    private String ApplyWay;
}
