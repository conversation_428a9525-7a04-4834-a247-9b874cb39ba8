package com.juneyaoair.oneorder.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MemberMilesResponse
 * @Description CRM 积分明细查询：会员综合积分查询（Func058）响应结果
 * <AUTHOR>
 * @Date 2019/4/19 10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberMilesResponse {

    /**
     * 响应编码
     */
    private String StatusCode;

    /**
     * 信息
     */
    private String Message;

    /**
     * 明细
     */
    private List<MilesDetail> MilesDetail;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class MilesDetail {

        /**
         * 积分变动类型
         */
        private String MilesType;

        /**
         * 积分（以+ - 来代表加减积分，例+1）
         */
        private String Miles;

        /**
         * 日期
         * yyyy-MM-dd
         */
        private String ActiveDate;

        /**
         * 描述
         */
        private String Description;

        /**
         * 失效日期
         */
        private String ExpireDate;

    }

}
