package com.juneyaoair.oneorder.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CompanyMemberQueryInfoResDto
 * @Description CRM 企业会员信息查询 响应结果
 * <AUTHOR>
 * @Date 2020/6/19 17:10
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyMemberQueryInfoResDto {

    /**
     * 企业信息
     */
    private CompanyInfoVO CompanyInfo;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class CompanyInfoVO {
        /**
         * 企业id
         */
        private Integer CompanyId;
        /**
         * 企业名称
         */
        private String CompanyName;
        /**
         * 协议生效日期
         */
        private String EffectiveDate;
        /**
         * 协议失效日期
         */
        private String ExpireDate;

    }

    /**
     * 企业会员状态：0-未绑定 1-已验证待激活 2-已绑定
     */
    private Integer Status;

    /**
     * 验证信息
     */
    private VerifyInfoVO VerifyInfo;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class VerifyInfoVO {
        /**
         * 企业信息（客户验证时填写的信息）
         */
        private String CompanyInfo;
        /**
         * 企业邮箱
         */
        private String Email;

    }

}
