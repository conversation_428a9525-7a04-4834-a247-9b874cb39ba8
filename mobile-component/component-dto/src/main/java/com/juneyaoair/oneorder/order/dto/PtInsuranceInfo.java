package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "InsuranceInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtInsuranceInfo {
	private String InsuranceCode; //保险种类代码
	private String InsuranceName; //保险种类名称
	private int InsuranceNumber; //保险份数
	private String InsuranceState; //保险状态
	private double InsuranceAmount; //保险金额  保险金额 = 份数 * 单价 * 航段数
	private String InsuranceBillNo; //保险单号
	public String getInsuranceCode() {
		return InsuranceCode;
	}
	public void setInsuranceCode(String insuranceCode) {
		InsuranceCode = insuranceCode;
	}
	public int getInsuranceNumber() {
		return InsuranceNumber;
	}
	public void setInsuranceNumber(int insuranceNumber) {
		InsuranceNumber = insuranceNumber;
	}
	public double getInsuranceAmount() {
		return InsuranceAmount;
	}
	public void setInsuranceAmount(double insuranceAmount) {
		InsuranceAmount = insuranceAmount;
	}

	public String getInsuranceName() {
		return InsuranceName;
	}

	public void setInsuranceName(String insuranceName) {
		InsuranceName = insuranceName;
	}

	public String getInsuranceState() {
		return InsuranceState;
	}

	public void setInsuranceState(String insuranceState) {
		InsuranceState = insuranceState;
	}

	public String getInsuranceBillNo() {
		return InsuranceBillNo;
	}

	public void setInsuranceBillNo(String insuranceBillNo) {
		InsuranceBillNo = insuranceBillNo;
	}
}