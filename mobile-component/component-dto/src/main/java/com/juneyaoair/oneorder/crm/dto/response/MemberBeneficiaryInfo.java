package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName MemberBeneficiaryInfo
 * @Description 积分受益人对象
 * <AUTHOR>
 * @Date 2019/7/30 15:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberBeneficiaryInfo {
    @ApiModelProperty(value = "记录id")
    private Integer recordId;

    @NotEmpty(message="性别不能为空")
    @ApiModelProperty(value = "性别  男-M，女-F, 其他-O")
    @JsonProperty(value = "sex")
    private String sex;

    @NotEmpty(message="生日不能为空")
    @ApiModelProperty(value = "生日 yyyy-MM-dd")
    @JsonProperty(value = "birthday")
    private String birthday;

    @NotEmpty(message="英文姓不能为空")
    @ApiModelProperty(value = "英文姓")
    @JsonProperty(value = "eLastName")
    private String eLastName;

    @NotEmpty(message="英文名不能为空")
    @JsonProperty(value = "eFirstName")
    @ApiModelProperty(value = "英文名")
    private String eFirstName;

    @ApiModelProperty(value = "中文姓")
    @JsonProperty(value = "cLastName")
    private String cLastName;

    @ApiModelProperty(value = "中文名")
    @JsonProperty(value = "cFirstName")
    private String cFirstName;

    @NotEmpty(message="国籍不能为空")
    @JsonProperty(value = "nationality")
    @ApiModelProperty(value = "国籍 国家二字码")
    private String nationality;

    @ApiModelProperty(value = "国籍")
    private String nationalityName;

    @Valid
    @Size(min = 1,message = "至少添加一个证件信息")
    @ApiModelProperty(value = "证件信息")
    private List<MemberBeneficiaryCertificateInfo> certificate;

    @ApiModelProperty(value = "距离生效日期还有几天")
    private Integer day;

    @ApiModelProperty(value = "生效日期")
    private String effectDate;

    @ApiModelProperty(value = "备注")
    private String comments;
}
