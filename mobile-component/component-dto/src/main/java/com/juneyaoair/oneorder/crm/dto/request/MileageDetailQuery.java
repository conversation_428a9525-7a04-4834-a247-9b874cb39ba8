package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MemberMilesRequest
 * @Description CRM积分明细查询请求参数
 * <AUTHOR>
 * @Date 2019/4/19 13:29
 **/
@Data
public class MileageDetailQuery {

    /**
     * 会员id
     */
    @JsonProperty(value = "Id")
    private String Id;

    /**
     * 入账开始日期 YYYYMMDD
     */
    @JsonProperty(value = "StartDate")
    private String StartDate ;

    /**
     * 入账结束日期 YYYYMMDD
     */
    @JsonProperty(value = "EndDate")
    private String EndDate;

    /**
     * 时间排序 A-升序 D-降序
     */
    @JsonProperty(value = "EntryDateSort")
    private String EntryDateSort;

    /**
     * 入账类型 add-新增 reduce-扣减 ,
     */
    @JsonProperty(value = "EntryType")
    private String EntryType;

}
