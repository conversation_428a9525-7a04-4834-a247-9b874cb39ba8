package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrmMemberBaseApiRequest<T> {

    @ApiModelProperty(value = "请求业务实体")
    @JsonProperty(value = "Data")
    private T Data;

    @ApiModelProperty(value = "渠道名称")
    @JsonProperty(value = "Channel")
    private String Channel;

    @ApiModelProperty(value = "渠道密码")
    @JsonProperty(value = "ChannelPwd")
    private String ChannelPwd;

    @ApiModelProperty(value = "客户端ip")
    @JsonProperty(value = "ClientIp")
    private String ClientIp;

    @ApiModelProperty(value = "版本号")
    @JsonProperty(value = "Version")
    private String Version;

    @ApiModelProperty(value = "请求随机码")
    @JsonProperty(value = "RandomCode")
    private String RandomCode;

    @ApiModelProperty(value = "操作者")
    @JsonProperty(value = "OperatorUid")
    private String OperatorUid;

    @ApiModelProperty(value = "时间戳,默认为系统当前时间")
    @JsonProperty(value = "Timestamp")
    private String Timestamp;

    @ApiModelProperty(value = "加密数据")
    @JsonProperty(value = "Encryptor")
    private String Encryptor;

    @ApiModelProperty(value = "签名数据")
    @JsonProperty(value = "Signature")
    private String Signature;

}
