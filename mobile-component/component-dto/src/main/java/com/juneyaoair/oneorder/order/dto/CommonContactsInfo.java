package com.juneyaoair.oneorder.order.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonContactsInfo {
	private Integer recordId; //常用联系人信息ID,主键自动生成

	private String name;//联系人姓名

	private String mobile;//联系人电话

	private String email; //联系人邮箱

	private Boolean isOwn;//是否本人标记

	private int priority;//优先级调整(将本人排在最前面)

	private String telephoneCode;//国际区号
}