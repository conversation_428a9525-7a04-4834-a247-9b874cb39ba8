package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 资源信息
 * @Date 14:49 2023/6/19
 * @return null
 **/

@Data
public class ResourceInfo {
    @SerializedName("ResourceId")
    private String resourceId;

    @SerializedName("VendorMessageId")
    private String vendorMessageId;

    @SerializedName("ResourceType")
    private String resourceType;

    @SerializedName("ResourceName")
    private String resourceName;

    @SerializedName("Description")
    private String description;
    @SerializedName("RightValidityDays")

    @ApiModelProperty(value = "资源有效天数")
    private int rightValidityDays;

    @SerializedName("StandardPrice")
    @ApiModelProperty(value = "标准售价（参考价值）")
    private Double standardPrice;

    @SerializedName("LoungeExt")
    private LoungeExtInfo loungeExt;

    @SerializedName("UpgradeExt")
    private UpgradeExtInfo upgradeExt;

    @SerializedName(value = "BaggageExt",alternate = "BaggageExtInfo")
    private BaggageExtInfo baggageExt;

    @SerializedName("CheckinSubstitutionExt")
    private CheckinSubstitutionExtInfo checkinSubstitutionExt;

    @SerializedName("Tags")
    private List<String> tags;

    @SerializedName("Imgs")
    @ApiModelProperty(value = "资源图片列表")
    private List<String> imgs;

    @ApiModelProperty(value = "资源使用方式说明")
    @SerializedName("UseMode")
    private String useMode;

    @ApiModelProperty(value = "购买须知")
    @SerializedName("PurchaseNotes")
    private String purchaseNotes;

    @SerializedName("RefundRules")
    @ApiModelProperty(value = "提前申请天数  0表示可即订即用")
    private String refundRules;//退款规则

    @SerializedName("OnboardWifiExt")
    private OnboardWifiExtInfo onboardWifiExt;

    @ApiModelProperty(value = "随身wifi特有属性")
    @SerializedName("WIFIExt")

    private WIFIExtInfo wifiExt;
    @ApiModelProperty(value = "境外电话卡特有属性")
    @SerializedName("PhoneCardExt")
    private PhoneCardExtInfo phoneCardExt;

    @ApiModelProperty(value = "接送机特有属性")
    @SerializedName("VISAExt")
    private VISAExtInfo visaExt;

    @ApiModelProperty(value = "供应商ID")
    @SerializedName("VendorID")
    private String vendorID;

    @ApiModelProperty(value = "供应商名称")
    @SerializedName("VendorName")
    private String vendorName;

    @ApiModelProperty(value = "供应商名称")
    @SerializedName("ProductName")
    private String productName;


    public Double getStandardPrice(){
        return this.standardPrice==null?0D:this.standardPrice;
    }
}
