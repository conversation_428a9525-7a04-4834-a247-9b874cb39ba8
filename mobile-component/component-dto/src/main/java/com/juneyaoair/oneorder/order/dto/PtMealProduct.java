package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/12/15.
 */
@XmlRootElement(name = "MealProduct")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtMealProduct {
    private String DepAirport;//起飞机场三字码
    private String ArrAirport;//到达机场三字码
    private String FlightNo;//航班号
    private int MealProductId;//套餐Id  餐食唯一Id
    private String MealCode;//套餐编号  餐食唯一编号
    private String MealName;//餐食名称
    private String MealClass;//餐食分类 A,B,C,D
    private String MealType;//餐食类型 正餐D,点心R
    private String MealBrief;//餐食简介
    private String MealDescription;//套餐说明
    private String MealPictureUrls;//餐食图片地址
    private double MealPrice;//套餐价格
    private String Remark;//备注

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public int getMealProductId() {
        return MealProductId;
    }

    public void setMealProductId(int mealProductId) {
        MealProductId = mealProductId;
    }

    public String getMealCode() {
        return MealCode;
    }

    public void setMealCode(String mealCode) {
        MealCode = mealCode;
    }

    public String getMealName() {
        return MealName;
    }

    public void setMealName(String mealName) {
        MealName = mealName;
    }

    public String getMealClass() {
        return MealClass;
    }

    public void setMealClass(String mealClass) {
        MealClass = mealClass;
    }

    public String getMealType() {
        return MealType;
    }

    public void setMealType(String mealType) {
        MealType = mealType;
    }

    public String getMealBrief() {
        return MealBrief;
    }

    public void setMealBrief(String mealBrief) {
        MealBrief = mealBrief;
    }

    public String getMealDescription() {
        return MealDescription;
    }

    public void setMealDescription(String mealDescription) {
        MealDescription = mealDescription;
    }

    public String getMealPictureUrls() {
        return MealPictureUrls;
    }

    public void setMealPictureUrls(String mealPictureUrls) {
        MealPictureUrls = mealPictureUrls;
    }

    public double getMealPrice() {
        return MealPrice;
    }

    public void setMealPrice(double mealPrice) {
        MealPrice = mealPrice;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }
}
