package com.juneyaoair.oneorder.flight.dto;

import com.juneyaoair.oneorder.order.dto.FlightInfo;
import com.juneyaoair.oneorder.order.dto.PtPassengerInfo;
import com.juneyaoair.oneorder.order.dto.SegmentShow;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Setter
@Getter
public class MemberTravellerFlightResp {
    /**
     * 客票对应的航段展示信息
     */
    private List<SegmentShow> segmentShowList;
    /**
     * 乘客信息
     */
    private List<PtPassengerInfo> passengerInfoList;

    private List<FlightInfo> flightInfoList;

    /**
     * 客票号
     */
    private String ticketNo;
    /**
     * 航程类型  单程：OW；往返：RT（RT时请注意飞行方向）
     */
    private String routeType;
    /**
     * 国内国际标识  国内-D 国际-I
     */
    private String interFlag;

    private String fareType;

    private String flightDate;

    private String sign;

    private boolean ifUp;

    /**
     * 是否为中转航班
     */
    private boolean isTransfer;
    /**
     * 获取对应的待签名字段
     * @return
     */
    public String createSinaParam(){
        String params="";
        if (!CollectionUtils.isEmpty(flightInfoList)){
            params = this.ticketNo+flightInfoList.get(0).createSinaParam();
        }
        return params;
    }
}
