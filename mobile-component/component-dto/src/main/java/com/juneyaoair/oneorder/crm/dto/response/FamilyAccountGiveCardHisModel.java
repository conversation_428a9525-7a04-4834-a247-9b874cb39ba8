package com.juneyaoair.oneorder.crm.dto.response;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class FamilyAccountGiveCardHisModel {
    /**
     * 家庭账户id
     */
    private String FamilyAccountId;
    /**
     * 赠卡级别代码 金卡、白金卡...对应的数字级别 ,
     */
    private String GiveLevelCode;

    private String GiveLevelName;
    /**
     * 赠卡级别周期 例如：3
     */
    private String GiveLevelPeriod;
    /**
     * 赠卡级别周期类型 D-天，M-月 ,
     */
    private String GiveLevelPeriodType;
    /**
     * 主账户id
     */
    private String MainId;
    /**
     * 主账户会员卡号 ,
     */
    private String MainMemberId;
    /**
     * 主账户会员姓名
     */
    private String MainName;
    /**
     * 子账户id
     */
    private String SubId;
    /**
     * 子账户会员卡号
     */
    private String SubMemberId;
    /**
     * 子账户会员姓名
     */
    private String SubName;
    /**
     *交易时间
     */
    private String TradeTime;
}
