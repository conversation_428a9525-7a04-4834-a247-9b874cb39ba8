package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 修改客户信息（请求）
 * @Date 16:52 2023/9/12
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtModifyCustomerInfoRequest {
    private String CLastName;   //中文姓
    private String CFirstName;  //中文名
    private String ELastName;   //英文姓
    private String EFirstName;  //英文名
    private String SLastName;  //特殊姓
    private String SFirstName;//特殊名
    private String Sex;
    private String SalutationCode;
    private Long Birthday;
    private String CompanyName; //单位名称
    private String Department;  //部门
    private String Post;    //职务
    private String HeadImageUrl;    //头像Url
    private String Referrer;  //推荐人
    private String Nationality;  //国籍
    private String Comments;  //备注
}
