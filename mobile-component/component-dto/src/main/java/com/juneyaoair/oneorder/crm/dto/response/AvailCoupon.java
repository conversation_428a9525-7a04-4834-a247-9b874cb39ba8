package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "AvailCoupon",description = "优惠券信息")
public class AvailCoupon {
    @ApiModelProperty(value = "优惠券序号")
    private String CouponNo;

    @ApiModelProperty(value = "部分券需要密码")
    private String Password;

    @ApiModelProperty(value = "优惠券类型")
    private String CouponType;

    @ApiModelProperty(value = "优惠券折扣率")
    private Double CouponRebate;

    @ApiModelProperty(value = "优惠券面值")
    private int CouponPrice;

    @ApiModelProperty(value = "=TicketOrder打包下单  =CouponOrder单独下单")
    private String SubOrderType;

    @ApiModelProperty(value = "子活动描述")
    private String Remark;

    @ApiModelProperty(value = "优惠券状态 R-已领取 E-已过期 N-已使用 C-赠送中 G-已赠送;权益券状态 可使用:Not 已使用:Used 已核销:WittenOff 已赠送:GiveAway 已过期:Expired 已退款：Refund")
    private String CouponState;

    @ApiModelProperty(value = "升舱券二级分类  UpgradeUnlimited")
    private String couponSubType;

    @ApiModelProperty(value = "绑定状态 U-待绑定 B-已绑定 F-绑定失效")
    private String couponBindState;//绑定状态

    @ApiModelProperty(value = "使用平台")
    private String ChannelNm;

    @ApiModelProperty(value = "使用时段")
    private String UsedStEndDt;

    @ApiModelProperty(value = "使用限制")
    private String UsedLimit;

    @ApiModelProperty(value = "航班时段")
    private String SegmentStAndEndDt;

    @ApiModelProperty(value = "舱位限制")
    private String CabinLimit;

    @ApiModelProperty(value = "航段限制")
    private String AirSegLimit;

    @ApiModelProperty(value = "优惠券活动名称")
    private String CouponActivityName;

    @ApiModelProperty(value = "优惠券来源 HO机票  PJ锦江 PZ中粮 PT度假(淘旅行)  PA安飞士")
    private String CouponSource;

    @ApiModelProperty(value = "APP端logo地址")
    private String APPLogoUrl;

    @ApiModelProperty(value = "在状态为G的情况下表示  被赠送者的领取日期")
    private String ReveiceTime;

    @ApiModelProperty(value = "赠送的优惠券领取卡号")
    private String ReveiceCardNo;

    @SerializedName("BookingLimit")
    @ApiModelProperty(value = "预定限制")
    private BookingLimit bookingLimit;

    @SerializedName("FlightStartDate")
    @ApiModelProperty(value = "航班限制时间起始")
    private String flightStartDate;

    @SerializedName("FlightEndDate")
    @ApiModelProperty(value = "航班限制时间结束")
    private String flightEndDate;

    //以下为自定义字段
    @ApiModelProperty(value = "优惠券来源名称  机票 度假")
    private String couponSourceName;

    @ApiModelProperty(value = "页面样式")
    private String couponStyle;

    @ApiModelProperty(value = "优惠券名称 如10元优惠券")
    private String couponName;

    @ApiModelProperty(value = "是否可以转赠  Y")
    private String isGive;

    private String receiveInfo;

    @ApiModelProperty(value = "优惠券状态名称")
    private String couponStateName;

    @ApiModelProperty(value = "是否展示二维码角标")
    private boolean showQRcode;

    @ApiModelProperty(value = "二维码数据")
    private String qrcodeData;

    @ApiModelProperty(value = "使用方式")
    private String useMode;

    @ApiModelProperty(value = "适用范围")
    private String applyScope;

    @ApiModelProperty(value = "国内DOMESTIC;国际INTL")
    private String airPortCode;

    @ApiModelProperty(value = "其他适用航线")
    private List<List<String>> otherApplyRoutes;

    @ApiModelProperty(value = "使用开始时间 yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty(value = "使用结束时间 yyyy-MM-dd")
    private String endData;

    @ApiModelProperty(value = "逾重单位")
    private int overweight;

    @ApiModelProperty(value = "逾重单位单位")
    private String unit;

    @ApiModelProperty(value = "预约航班时间 yyyy-MM-dd")
    private String applyFlightDate;

    @ApiModelProperty(value = "预约航班号")
    private String applyFlightNo;

    @ApiModelProperty(value = "是否已预约使用")
    private boolean hasApply;

    @ApiModelProperty(value = "固包类子券信息(赠送)")
    private List<VoucherInfo> vouchers;

    @ApiModelProperty(value = "是否为固包权益券")
    private boolean packageFlag;

    @ApiModelProperty(value = "是否为赠送中的权益券")
    private boolean givingFlag;

    @ApiModelProperty(value = "是否展示二维码")
    private boolean codeFlag;

    @ApiModelProperty(value = "是否可用")
    private Boolean IsAvailable;

    @ApiModelProperty(value = "标记是否往返特惠优惠券")
    private Boolean roundTripFlag;

    @ApiModelProperty(value = "赠送券时的加密信息")
    private String sign;

    @ApiModelProperty(value = "是否即将过期   未使用状态，且过期日期距现在小于5天")
    private boolean expiring;

    @ApiModelProperty(value = "不可使用原因")
    private String unAvailableMsg;

    @ApiModelProperty(value = "是否可以取消预约  true 可以取消预约")
    private Boolean isCancel;

    @ApiModelProperty(value = "贵宾休息室剩余可取消预约次数")
    private int bookCancelCount;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "是否显示查看订单按钮")
    private Boolean isDisplay;

    @ApiModelProperty(value = "是否是无限升舱卡")
    private boolean unlimitedUpClass;//

    @ApiModelProperty(value = "是否无限升舱卡15周年版，和原本无限升舱卡一致，区分不同权益券券面使用")
    private boolean unlimitedUpV2Flag;

    @ApiModelProperty(value = "是否已绑定")
    private boolean bound;


    @SerializedName("UseDate")
    @ApiModelProperty(value = "使用时间")
    private String useDate;

    @ApiModelProperty(value = "畅飞卡2.0 权益券列表增加绑定信息描述")
    private String bindInfoDesc;

    @ApiModelProperty(value = "使用状态")
    private String UsedStatus;

    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderNo;

    @ApiModelProperty(value = "是否他人赠送标识   true:是  false:否")
    private boolean receiveStatus;

    @ApiModelProperty(value = "权益券是否限制本人使用  1:是  0:否")
    private int selfUse;

    @ApiModelProperty(value = "优惠券抵扣类型 CHD100:代表儿童100%免票")
    private String discountType;

    private String RescheduleType;

    @ApiModelProperty(value = "航班起飞几小时前可用")
    private String   AdvanceHour;

    @ApiModelProperty(value = "到期信息提示")
    private Boolean expiration;

    @ApiModelProperty(value = "总类型")
    private String totalType;

    @ApiModelProperty(value = "可用日期")
    private String availDate;//

    @ApiModelProperty(value = "预约按钮是否显示")
    private Boolean isBookHoloShow =false;

    /**
     * 获取bookingLimit中最小成人数
     * @return
     */
    public int getMinAdults() {
        if (null == bookingLimit){
            return 0;
        }
        return bookingLimit.getMinAdults();
    }
}


