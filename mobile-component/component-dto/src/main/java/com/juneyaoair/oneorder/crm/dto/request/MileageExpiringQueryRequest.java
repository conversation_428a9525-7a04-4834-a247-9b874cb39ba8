package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageExpiringQuery
 * @Description CRM查询即将过期积分请求参数
 * <AUTHOR>
 * @Date 2019/4/22 16:49
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MileageExpiringQueryRequest {

    /**
     * 会员卡号
     */
    private String MemberCardNO;

    /**
     * 截止时间（格式yyyymmdd）
     */
    private String EndDate;

    /**
     * 操作员 MOBILE
     */
    private String ClientCode;

    /**
     * ClientCode+ MemberCardNO+Key(Md5加密)
     * 注：key是clientPassword
     */
    private String Signature;

}
