package com.juneyaoair.oneorder.flight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Segment",description = "航段信息")
public class Segment {
    @ApiModelProperty(value = "飞行方向",allowableValues = "G,B")
    private String flightDirection;
    @ApiModelProperty(value = "起始地城市三字码",required = true)
    private String depCity;
    @ApiModelProperty(value = "目的地城市三字码",required = true)
    private String arrCity;
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期yyyy-MM-dd",required = true)
    private String flightDate;
    @ApiModelProperty(value = "出发机场三字码")
    private String depAirport;
    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirport;
}
