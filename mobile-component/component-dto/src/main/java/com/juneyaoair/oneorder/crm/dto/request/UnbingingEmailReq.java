package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;


/**
 * 解绑邮箱
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-12-11 14:03
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UnbingingEmailReq{
    @ApiModelProperty(value = "此值不用传")
    private String recordId;
    @ApiModelProperty(value = "传此值即可 后台根据此值加密后赋值给recordId")
    private String record;

    @NotEmpty(message="验证码不能为空")
    @ApiModelProperty(value = "验证码")
    private String veriCode;

    @ApiModelProperty(value = "email地址")
    @NotEmpty(message="邮箱地址不能为空")
    private String email;

    @NotEmpty(message="邮箱类型不能为空")
    @ApiModelProperty(value = "邮箱类型 emailverycode-绑定邮箱发送验证码,memberLogoutEmail-注销账号邮箱验证码")
    private String type;
}
