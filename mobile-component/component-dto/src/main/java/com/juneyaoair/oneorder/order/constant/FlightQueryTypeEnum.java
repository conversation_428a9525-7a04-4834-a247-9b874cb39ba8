package com.juneyaoair.oneorder.order.constant;

/**
 * <AUTHOR>
 * @description 航班查询类别枚举
 * @date 2019/4/23  14:15.
 */
public enum FlightQueryTypeEnum {
    NORMAL("NORMAL","默认查询"),
    POLICE_REMNANTS("POLICE_REMNANTS","军残警残"),
    NO_ACCOMPANY_CHILD("NO_ACCOMPANY_CHILD","无陪儿童"),
    CHANGE("CHANGE","改期查询"),
    NOTVOLUNTARYCHANGE("NOTVOLUNTARYCHANGE","非自愿改期"),
    PACKAGE_CABIN("PACKAGE_CABIN","一舱多价查询"),
    FREE_TICKET("FREE_TICKET", "免票兑换查询"),
    AWARD_FLY_FREE_TICKET("AWARD_FLY_FREE_TICKET","奖励飞免票"),
    STUDENT("STUDENT","留学生运价"),
    THEME_CARD("THEME_CARD","主题卡查询"),
    YJ_YS("YJ_YS","国内拥军价格");



    private String type;
    private String desc;

    FlightQueryTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    //校验类型
    public static FlightQueryTypeEnum checkEnum(String v){
        for (FlightQueryTypeEnum c: FlightQueryTypeEnum.values()) {
            if (c.type.equals(v)) {
                return c;
            }
        }
        return null;
    }

}
