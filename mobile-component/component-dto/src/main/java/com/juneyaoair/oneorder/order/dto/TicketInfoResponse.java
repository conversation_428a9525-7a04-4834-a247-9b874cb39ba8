package com.juneyaoair.oneorder.order.dto;

import com.juneyaoair.oneorder.crm.dto.response.MemberSegmentResponse;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "TicketInfoResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class TicketInfoResponse {
	private boolean IsHasMoreTax; //是否有更多税需要DETR:X提取的税项
	private boolean IsIT; //是否IT票
	private String EqviuCurrencyType; //等值支付货币
	private String EqviuFare; //等值支付金额
	private String ETicketType; //电子客票类型
	private String CurrencyType; //货币类型
	private String DstCity; //终点城市
	private String ExchangeInfo; //改签信息
	private Double Fare; //票价
	private String FareCompute; //票价计算信息
	private String FollowTicketNo; //后续票号
	private String ISI; //ISI信息
	private String IssueAirline; //出票航空公司
	private String OrgCity; //始发城市
	private String OriginalIssue; //OI信息
	private String PassengerName; //旅客姓名
	private String PayMethod; //支付方式
	private String SigningInfo; //签注信息
	private Double Tax; //税款金额
	private String TicketNo; //票号
	private Double TotalAmount; //客票总金额
	private String TourCode; //旅游代码
	private String CurrencyTypeTotal; //票面总价的货币类型
	private String IsReceiptPrinted; //是否已打印T4（发票）联
	private String Remark; //原始主机返回信息
	private String InfantBirthday; //无人陪伴儿童年龄yyyy-MM-dd
	private String PassengerType; //旅客类型
	private String UnaccompaniedChildAge; //无人陪伴儿童年龄
	private String IataNo; //出票的Iata号
	private String IssueDate; //出票时间yyyy-MM-dd
	private String PassengerID; //
	private String IsFromDomestic; /// 是否国内起飞
	private String InterFlag ;// 是否国际航班
	private List<SegmentInfo> SegmentInfoList; //航段信息列表
	private List<IdentityInfo> IdentityInfoList; //证件信息列表
	private List<TaxInfo> TaxInfoList; //税费信息列表
	private Double CN;
	private Double YQ;
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	private String fareType;//判断航班组合类型
	private String ChangeInvFlag;// Y/N 是否做过非自愿改期 Y时退票不校验客票状态

	public TicketInfoResponse() {
	}

	public boolean isHasMoreTax() {
		return IsHasMoreTax;
	}

	public void setHasMoreTax(boolean hasMoreTax) {
		IsHasMoreTax = hasMoreTax;
	}

	public boolean isIT() {
		return IsIT;
	}

	public void setIT(boolean IT) {
		IsIT = IT;
	}

	public String getEqviuCurrencyType() {
		return EqviuCurrencyType;
	}

	public void setEqviuCurrencyType(String eqviuCurrencyType) {
		EqviuCurrencyType = eqviuCurrencyType;
	}

	public String getEqviuFare() {
		return EqviuFare;
	}

	public void setEqviuFare(String eqviuFare) {
		EqviuFare = eqviuFare;
	}

	public String getETicketType() {
		return ETicketType;
	}

	public void setETicketType(String ETicketType) {
		this.ETicketType = ETicketType;
	}

	public String getCurrencyType() {
		return CurrencyType;
	}

	public void setCurrencyType(String currencyType) {
		CurrencyType = currencyType;
	}

	public String getDstCity() {
		return DstCity;
	}

	public void setDstCity(String dstCity) {
		DstCity = dstCity;
	}

	public String getExchangeInfo() {
		return ExchangeInfo;
	}

	public void setExchangeInfo(String exchangeInfo) {
		ExchangeInfo = exchangeInfo;
	}

	public Double getFare() {
		return Fare;
	}

	public void setFare(Double fare) {
		Fare = fare;
	}

	public String getFareCompute() {
		return FareCompute;
	}

	public void setFareCompute(String fareCompute) {
		FareCompute = fareCompute;
	}

	public String getFollowTicketNo() {
		return FollowTicketNo;
	}

	public void setFollowTicketNo(String followTicketNo) {
		FollowTicketNo = followTicketNo;
	}

	public String getISI() {
		return ISI;
	}

	public void setISI(String ISI) {
		this.ISI = ISI;
	}

	public String getIssueAirline() {
		return IssueAirline;
	}

	public void setIssueAirline(String issueAirline) {
		IssueAirline = issueAirline;
	}

	public String getOrgCity() {
		return OrgCity;
	}

	public void setOrgCity(String orgCity) {
		OrgCity = orgCity;
	}

	public String getOriginalIssue() {
		return OriginalIssue;
	}

	public void setOriginalIssue(String originalIssue) {
		OriginalIssue = originalIssue;
	}

	public String getPassengerName() {
		return PassengerName;
	}

	public void setPassengerName(String passengerName) {
		PassengerName = passengerName;
	}

	public String getPayMethod() {
		return PayMethod;
	}

	public void setPayMethod(String payMethod) {
		PayMethod = payMethod;
	}

	public String getSigningInfo() {
		return SigningInfo;
	}

	public void setSigningInfo(String signingInfo) {
		SigningInfo = signingInfo;
	}

	public Double getTax() {
		return Tax;
	}

	public void setTax(Double tax) {
		Tax = tax;
	}

	public String getTicketNo() {
		return TicketNo;
	}

	public void setTicketNo(String ticketNo) {
		TicketNo = ticketNo;
	}

	public Double getTotalAmount() {
		return TotalAmount;
	}

	public void setTotalAmount(Double totalAmount) {
		TotalAmount = totalAmount;
	}

	public String getTourCode() {
		return TourCode;
	}

	public void setTourCode(String tourCode) {
		TourCode = tourCode;
	}

	public String getCurrencyTypeTotal() {
		return CurrencyTypeTotal;
	}

	public void setCurrencyTypeTotal(String currencyTypeTotal) {
		CurrencyTypeTotal = currencyTypeTotal;
	}

	public String getIsReceiptPrinted() {
		return IsReceiptPrinted;
	}

	public void setIsReceiptPrinted(String isReceiptPrinted) {
		IsReceiptPrinted = isReceiptPrinted;
	}

	public String getRemark() {
		return Remark;
	}

	public void setRemark(String remark) {
		Remark = remark;
	}

	public String getInfantBirthday() {
		return InfantBirthday;
	}

	public void setInfantBirthday(String infantBirthday) {
		InfantBirthday = infantBirthday;
	}

	public String getPassengerType() {
		return PassengerType;
	}

	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}

	public String getUnaccompaniedChildAge() {
		return UnaccompaniedChildAge;
	}

	public void setUnaccompaniedChildAge(String unaccompaniedChildAge) {
		UnaccompaniedChildAge = unaccompaniedChildAge;
	}

	public String getIataNo() {
		return IataNo;
	}

	public void setIataNo(String iataNo) {
		IataNo = iataNo;
	}

	public String getIssueDate() {
		return IssueDate;
	}

	public void setIssueDate(String issueDate) {
		IssueDate = issueDate;
	}

	public String getPassengerID() {
		return PassengerID;
	}

	public void setPassengerID(String passengerID) {
		PassengerID = passengerID;
	}

	public String getIsFromDomestic() {
		return IsFromDomestic;
	}

	public void setIsFromDomestic(String isFromDomestic) {
		IsFromDomestic = isFromDomestic;
	}

	public String getInterFlag() {
		return InterFlag;
	}

	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}

	public List<SegmentInfo> getSegmentInfoList() {
		return SegmentInfoList;
	}

	public void setSegmentInfoList(List<SegmentInfo> segmentInfoList) {
		SegmentInfoList = segmentInfoList;
	}

	public List<IdentityInfo> getIdentityInfoList() {
		return IdentityInfoList;
	}

	public void setIdentityInfoList(List<IdentityInfo> identityInfoList) {
		IdentityInfoList = identityInfoList;
	}

	public List<TaxInfo> getTaxInfoList() {
		return TaxInfoList;
	}

	public void setTaxInfoList(List<TaxInfo> taxInfoList) {
		TaxInfoList = taxInfoList;
	}

	public String getResultCode() {
		return ResultCode;
	}

	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}

	public String getErrorInfo() {
		return ErrorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}

	public Double getCN() {
		return CN;
	}

	public void setCN(Double CN) {
		this.CN = CN;
	}

	public Double getYQ() {
		return YQ;
	}

	public void setYQ(Double YQ) {
		this.YQ = YQ;
	}

	public String getFareType() {
		return fareType;
	}

	public void setFareType(String fareType) {
		this.fareType = fareType;
	}

	public String getChangeInvFlag() {
		return ChangeInvFlag;
	}

	public void setChangeInvFlag(String changeInvFlag) {
		ChangeInvFlag = changeInvFlag;
	}

	@Data
	public static class SegmentInfo{
		public String FlightNo; //航班编号
		public String DepTime; //起飞时间yyyy-MM-dd HH:mi


		public String ArrAirportTerminal; //到达航站楼
		public String DepAirportTerminal; //起飞航站楼
		public String ArrAirportCode; //到达机场代码
		public String ArrAirportName;
		public String ArrCityName;
		public String ArrCityCode;
		public String BaggageWeight; //允许携带的行李重量
		public String BaggagePiece; //允许携带的行李件数
		public String Cabin; //预订舱位
		public String DepAirportCode; //起飞机场代码
		public String DepAirportName;
		public String DepCityName;
		public String DepCityCode;
		public String StartValidityDate; //有效期起始时间(null表示无起始日期或情况不明)yyyy-MM-dd HH:mi
		public String EndValidityDate; //有效期终止时间(null表示无终止日期或情况不明)yyyy-MM-dd HH:mi
		public String PnrNo; //PNR编号(ICS编号)
		public String CrsPnrNo; //代理人系统PNR编号
		public String CrsType; //代理人系统代码
		public String Rate; //适用的运价类型（如YB为Y舱B类运价）FireBase
		public String TicketStatus; //客票状态
		public String Airline; //
		public String BoardingNo; //旅客在已飞行航段中的登机牌号
		public String SegmentStatus; //航段状态
		public String StopType; //停留原因停留原因 O 正常 X 中转联程
		public String Type; //航段类型
		public boolean IsFPC; //
		public String OperationAirline; //承运方航空公司代码
		public String MarketingAirline; //市场方航空公司代码
		public String ArrTime; //到达时间yyyy-MM-dd HH:mm
		public int SegmentIndex; //电子票票面航段序号
		public String McoNumber; //MCO单号
		public String BaggageWeightUnit; //行李重量单位
		public String SeatStatus; //座位状态 2021-09-14
	}
}