package com.juneyaoair.oneorder.crm.dto.request;


import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class CreateFamilyCreateReqDto {
    /**
     * 主账户会员卡号
     */
//    @NotEmpty(message = "主账户会员卡号不可为空")
    private String MainMemberId;
    /**
     * 关系类型 父母-Parent、配偶-Spouse、子女-Child ,
     */
    @NotEmpty(message = "关系类型不可为空")
    private String RelationType;

    /**
     * 子账户会员卡号 ,
     */
    @NotEmpty(message = " 子账户会员卡号不可为空")
    private String SubMemberId;

    /**
     * 子账户姓名
     */
    @NotEmpty(message = " 子账户姓名不可为空")
    private String  SubName;


    private Boolean IsAdult;
}
