package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PtScoreCompensateReq
 * @Description 积分补登三方请求体
 * <AUTHOR>
 * @Date 2023/6/27 16:13
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PtScoreCompensateReq {
    @JsonProperty(value = "MemberCardNo")
    private String MemberCardNo;

    @JsonProperty(value = "FlightRecord")
    private FlightRecord FlightRecord;
}
