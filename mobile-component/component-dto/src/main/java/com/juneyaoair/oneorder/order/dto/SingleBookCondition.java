package com.juneyaoair.oneorder.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/2/13 14:35
 */
@Data
public class SingleBookCondition {

    @ApiModelProperty(value = "使用开始时间 格式：yyyy-MM-dd HH:mm:ss")
    private String UseStartDate;
    
    @ApiModelProperty(value = "使用结束时间 格式：yyyy-MM-dd HH:mm:ss")
    private String UseEndDate;
    
    @ApiModelProperty(value = "航程类型 OW")
    private String FlightType;

    @ApiModelProperty(value = "航班日期  yyyy-MM-dd HH:mm:ss")
    private String FlightDate;

    @ApiModelProperty(value = "出发机场三字码")
    private String DepAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String ArrAirportCode;

    @ApiModelProperty(value = "出发城市三字码")
    private String DepCityCode;

    @ApiModelProperty(value = "到达城市三字码")
    private String ArrCityCode;

    @ApiModelProperty(value = "出发城市")
    private String DepCity;

    @ApiModelProperty(value = "到达城市")
    private String ArrCity;

    @ApiModelProperty(value = "航班号")
    private String FlightNo;

    @ApiModelProperty(value = "舱位代码")
    private String Cabin;

    @ApiModelProperty(value = "适用机型")
    private String AircraftType;

}
