package com.juneyaoair.oneorder.crm.dto.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class FamilyAccountInviteDetailModel {
    /**
     * 邀请状态(待确认-Send 已过期-Expire 已撤回-Revoke 待审核-Accept 已拒绝-Reject 邀请成功-AuditPass 审核不通过-AuditReject) ,
     */
    @JsonProperty("InviteStatus")
    private String InviteStatus;

    /**
     * 出生日期
     */
    @JsonProperty("Birthday")
    private String Birthday;
    /**
     * 邀请时间
     */
    @JsonProperty("InviteTime")
    private String InviteTime;
    /**
     * 主账户卡号
     */
    @JsonProperty("MainMemberId")
    private String MainMemberId;
    /**
     * 主账户姓名(已脱敏) ,
     */
    @JsonProperty("MainMemberName")
    private String MainMemberName;
    /**
     * 家庭成员类型(P-主账户父母、S-主账户配偶、C-主账户子女、O-其他)
     */
    @JsonProperty("MemberType")
    private String MemberType;
    /**
     * 子账户卡号
     */
    @JsonProperty("SubMemberId")
    private String SubMemberId;
    /**
     *子账户姓名(已脱敏)
     */
    @JsonProperty("SubMemberName")
    private String SubMemberName;

}
