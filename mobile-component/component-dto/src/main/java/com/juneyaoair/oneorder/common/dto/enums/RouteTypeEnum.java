package com.juneyaoair.oneorder.common.dto.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 15:32
 */
public enum RouteTypeEnum {
    OW("OW","单程"),
    RT("RT","往返");

    private String code;
    private String desc;

    RouteTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }
}
