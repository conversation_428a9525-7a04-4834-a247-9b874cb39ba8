package com.juneyaoair.oneorder.crm.dto.response;

import com.juneyaoair.oneorder.crm.dto.common.CertType;
import com.juneyaoair.oneorder.crm.dto.common.GeneralContactInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 查询乘机人信息V2
 * @date 2019/7/18  18:35.
 */
@Data
public class QueryGeneralContactResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ChannelCustomerNo;
    private String ChannelCustomerType;
    private String SortType; //  LastBooking - 最近预订时间从近到远，BookingNum - 预订次数从大到小，Create － 创建时间从近到远
    @ApiModelProperty("常用乘机人信息列表")
    private List<GeneralContactInfo> GeneralContactList;//常用乘机人信息列表
    @ApiModelProperty("证件类型字典")
    private List<CertType> CertTypeList;//证件类型字典
    @ApiModelProperty("使用的证件类型字典")
    private List<CertType> UseCertTypeList;//使用的证件类型字典
    private String PageNo;
    private String PageSize;
    private String Count;
    private String ResultCode;
    private String ErrorInfo;
}
