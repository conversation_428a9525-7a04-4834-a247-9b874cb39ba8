package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ScoreUse")
@XmlAccessorType(XmlAccessType.FIELD)

public class PtScoreUse {
	private String ScoreUseRuleID; //使用规则ID
	private int UseScore; //扣减积分
	private int Deductibls; //抵扣金额
	private double PricePaid; //实际支付票价PricePaid = PriceValue - Deductibls
	private double PriceValue; //票价
	private String Remark; //使用说明

	public String getScoreUseRuleID(){
		return ScoreUseRuleID;
	}
	public void setScoreUseRuleID(String ScoreUseRuleID){
		this.ScoreUseRuleID=ScoreUseRuleID;
	}
	public int getUseScore(){
		return UseScore;
	}
	public void setUseScore(int UseScore){
		this.UseScore=UseScore;
	}
	public int getDeductibls(){
		return Deductibls;
	}
	public void setDeductibls(int Deductibls){
		this.Deductibls=Deductibls;
	}
	public double getPricePaid(){
		return PricePaid;
	}
	public void setPricePaid(double PricePaid){
		this.PricePaid=PricePaid;
	}
	public double getPriceValue(){
		return PriceValue;
	}
	public void setPriceValue(double PriceValue){
		this.PriceValue=PriceValue;
	}
	public String getRemark(){
		return Remark;
	}
	public void setRemark(String Remark){
		this.Remark=Remark;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("ScoreUse ["); 
		sb.append("ScoreUseRuleID="+ScoreUseRuleID+",");
		sb.append("UseScore="+UseScore+",");
		sb.append("Deductibls="+Deductibls+",");
		sb.append("PricePaid="+PricePaid+",");
		sb.append("PriceValue="+PriceValue+",");
		sb.append("Remark="+Remark);
		sb.append("]");
		return sb.toString();
	}
}