package com.juneyaoair.oneorder.order.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ResourceBuyCouponEnjoy
 * <AUTHOR>
 * @Description
 * @Date 2021-04-25 9:59
 **/
@Data
public class ResourceBuyCouponEnjoy {
    /**
     * 图片
     */
    @NotBlank(message = "图片不可为空")
    @SerializedName("Img")
    private String img;
    /**
     * 主标题
     */
    @NotBlank(message = "主标题不可为空")
    @SerializedName("MainTitle")
    private String mainTitle;
    /**
     * 子标题
     */
    @NotBlank(message = "子标题不可为空")
    @SerializedName("Subtitle")
    private String subtitle;
    /**
     * 排序
     */
    @NotBlank(message = "排序不可为空")
    @SerializedName("Sequence")
    private String sequence;
}
