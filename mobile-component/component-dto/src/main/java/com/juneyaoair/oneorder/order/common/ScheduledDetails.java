package com.juneyaoair.oneorder.order.common;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author:zhaochengyang
 * @Version:10.0
 * @Date:2022/1/14
 */

@AllArgsConstructor
@Data
public class ScheduledDetails {

    private String ResourceType;
    private String EffectiveStartTime;

    private String EffectiveEndTime;
    private List<IneffectiveSchedules>  IneffectiveSchedules;
}
