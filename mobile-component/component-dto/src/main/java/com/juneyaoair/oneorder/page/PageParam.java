package com.juneyaoair.oneorder.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PageParam",description = "分页")
public class PageParam {

    @NotNull(message = "当前页码不能为空")
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;

    @NotNull(message = "每页记录数不能为空")
    @ApiModelProperty("每页记录数")
    private Integer pageSize;

}
