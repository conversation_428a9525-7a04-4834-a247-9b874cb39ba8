package com.juneyaoair.oneorder.common.dto.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2019/10/30  13:04.
 */
public enum PlatFormEnum {
    IOS("ios","苹果"),
    ANDROID("android","安卓"),
    HARMONY("harmony","鸿蒙"),
    H5("h5","H5页面"),
    WXAPP("wxapp","微信小程序"),
    ZFBAPP("zfbapp","支付宝小程序"),
    B2C("b2c","中文官网"),
    GLOBAL("global","国际网站"),
    ;
    private String systemCode;
    private String systemName;

    PlatFormEnum(String systemCode, String systemName) {
        this.systemCode = systemCode;
        this.systemName = systemName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public String getSystemName() {
        return systemName;
    }
}
