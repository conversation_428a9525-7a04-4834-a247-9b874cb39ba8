package com.juneyaoair.oneorder.geetest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 14:37
 */
@ApiModel(value = "GeetestClient", description = "极验初始化参数")
@Data
public class GeetestClient {
    /**
     * @see com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum
     */
    @ApiModelProperty(value = "极验使用场景", allowableValues = "sms,login,common,prePayBaggage", required = true,
            example = "common:公用 sms：短信 login：登录 prePayBaggage:额外行李")
    @NotBlank(message = "使用场景不能为空")
    private String scene;
    @ApiModelProperty(value = "极验使用CLIENT_TYPE,web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式", allowableValues = "web,h5,native",
            notes = "web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式",
            required = true, example = "web,h5,native")
    @NotBlank(message = "CLIENT_TYPE传递类型不能为空")
    private String client_type;
}
