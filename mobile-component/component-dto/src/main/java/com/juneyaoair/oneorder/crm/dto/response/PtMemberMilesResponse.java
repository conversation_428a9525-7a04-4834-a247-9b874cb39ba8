package com.juneyaoair.oneorder.crm.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * @ClassName MemberMilesDetail
 * @Description CRM 积分明细查询：会员综合积分查询（Func058）响应结果
 * <AUTHOR>
 * @Date 2019/4/19 10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtMemberMilesResponse {

    @ApiModelProperty(value = "积分明细")
    private List<MemberMilesDetail> transactionDetailDtoList;

    @Data
    public static class MemberMilesDetail {

        @ApiModelProperty(value = "入账类型 add-新增 reduce-扣减")
        private String EntryType;

        @ApiModelProperty(value = "描述")
        private String Description;

        @ApiModelProperty(value = "入账时间")
        private String EntryDate;

        @ApiModelProperty(value = "积分失效日期")
        private String ExpireDate;

        @ApiModelProperty(value = "积分数")
        private String Miles;

        @ApiModelProperty(value = "交易类型")
        private String TransactionType;
    }

}
