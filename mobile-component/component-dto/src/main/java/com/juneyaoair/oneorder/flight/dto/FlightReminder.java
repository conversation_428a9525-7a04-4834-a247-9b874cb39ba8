package com.juneyaoair.oneorder.flight.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 航班提醒通知
 * @date 2020/2/13  15:07.
 */
@Data
@ApiModel(value = "FlightReminder",description = "航班提醒")
@JsonIgnoreProperties({"startDate","endDate","suitRoute","tripTypeList"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlightReminder {
    @ApiModelProperty(value = "提醒标题")
    private String title;
    @ApiModelProperty(value = "提醒内容")
    private String reminderContent;
    @ApiModelProperty(value = "生效开始时间",notes = "格式：yyyy-MM-dd HH:mm:ss")
    private String startDate;
    @ApiModelProperty(value = "生效结束时间",notes = "格式：yyyy-MM-dd HH:mm:ss")
    private String endDate;
    @ApiModelProperty(value = "适用航线")
    private List<String> suitRoute;
    @ApiModelProperty(value = "国内国际地区区分",allowableValues = "D,I")
    private List<String> tripTypeList;
    @ApiModelProperty(value = "更新日期")
    private String updateTime;
}
