package com.juneyaoair.oneorder.order.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2017/7/26.
 */
@Data
@NoArgsConstructor
public class PtBaseRequest {

    @JsonProperty("Version")
    private String Version;

    @JsonProperty("ChannelCode")
    private String ChannelCode;

    @JsonProperty("UserNo")
    private String UserNo;

    public PtBaseRequest(String version, String channelCode, String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
    }
}
