package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 黑/白名单值
 * @Date 14:34 2023/6/19
 * @return null
 **/

@Data
public class BlackList {

    @SerializedName("ListType")
    @ApiModelProperty(value = "黑/白名单")
    private String listType;

    @SerializedName("ListValue")
    @ApiModelProperty(value = "名单值  航线名单：PVG-HKG,PVG-*,PVG-DOMESTIC,PVG-INTL等 航班号：HO1121,HO1127等 舱位：F,P,A等")
    private List<String> listValue;
}
