package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MemberBeneficiaryRequest
 * @Description CRM受益人列表查询请求参数
 * <AUTHOR>
 * @Date 2020/7/30 16:25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberBeneficiaryRequest {

    @JsonProperty(value = "MemberId")
    private Integer MemberId; //会员卡号
    @JsonProperty(value = "Status")
    private List<String> Status; // 受益人状态  草稿-M，激活-A，删除-D
    @JsonProperty(value = "BeneficIds")
    private List<Integer> BeneficIds; // 激活受益人id集合
    @JsonProperty(value = "BeneficId")
    private Integer BeneficId; //删除激活受益人id

    public MemberBeneficiaryRequest(Integer memberId, List<String> status, List<Integer> beneficIds) {
        MemberId = memberId;
        Status = status;
    }

    public MemberBeneficiaryRequest(Integer memberId) {
        MemberId = memberId;
    }

    public MemberBeneficiaryRequest(Integer memberId, Integer beneficId) {
        MemberId = memberId;
        BeneficId = beneficId;
    }

    public MemberBeneficiaryRequest(Integer memberId, List<Integer> beneficIds) {
        MemberId = memberId;
        BeneficIds = beneficIds;
    }
}
