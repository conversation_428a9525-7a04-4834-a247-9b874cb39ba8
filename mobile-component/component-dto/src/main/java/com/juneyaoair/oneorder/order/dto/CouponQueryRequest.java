package com.juneyaoair.oneorder.order.dto;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
public class CouponQueryRequest {
    @JsonProperty(value = "Version")
    private String Version; // 接口版本号10
    @JsonProperty(value = "ChannelCode")
    private String ChannelCode; // 渠道用户号B2C,CC等
    @JsonProperty(value = "UserNo")
    private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
    @JsonProperty(value = "FfpId")
    private String FfpId;
    @JsonProperty(value = "FfpCardNo")
    private String FfpCardNo;
    @JsonProperty(value = "CouponState")
    private String CouponState;  //R-已领取,U-已使用, E-已过期
    @JsonProperty(value = "CouponSource")
    private String CouponSource;//优惠券分类
    @JsonProperty(value = "Sale")
    private int Sale;   //0：全部 1：仅非可售 2：仅可售
    /**
     * 是否包含有效但不可用的优惠券
     */
    @JsonProperty(value = "IsContainUnavailable")
    private boolean IsContainUnavailable;
    /**
     * 航距
     */
    @JsonProperty(value = "AirlineMileage")
    private int AirlineMileage;

    @JsonProperty(value = "SegmentInfoList")
    private List<PtSegmentInfo> SegmentInfoList;
    /**
     * 旅客信息 为空表示暂未知
     */
    @JsonProperty(value = "PassengerInfoList")
    private List<PtPassengerInfo> PassengerInfoList;

    public CouponQueryRequest() {
    }

    public CouponQueryRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public CouponQueryRequest(String version, String channelCode, String userNo, String ffpId, String ffpCardNo, String couponState, List<PtSegmentInfo> segmentInfoList) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        FfpId = ffpId;
        FfpCardNo = ffpCardNo;
        CouponState = couponState;
        SegmentInfoList = segmentInfoList;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public String getCouponState() {
        return CouponState;
    }

    public void setCouponState(String couponState) {
        CouponState = couponState;
    }

    public List<PtSegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<PtSegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }

    public String getCouponSource() {
        return CouponSource;
    }

    public void setCouponSource(String couponSource) {
        CouponSource = couponSource;
    }

    public int getSale() {
        return Sale;
    }

    public void setSale(int sale) {
        Sale = sale;
    }

    public List<PtPassengerInfo> getPassengerInfoList() {
        return PassengerInfoList;
    }

    public void setPassengerInfoList(List<PtPassengerInfo> passengerInfoList) {
        PassengerInfoList = passengerInfoList;
    }

    public int getAirlineMileage() {
        return AirlineMileage;
    }

    public void setAirlineMileage(int airlineMileage) {
        AirlineMileage = airlineMileage;
    }

    public boolean isContainUnavailable() {
        return IsContainUnavailable;
    }

    public void setContainUnavailable(boolean containUnavailable) {
        IsContainUnavailable = containUnavailable;
    }
}
