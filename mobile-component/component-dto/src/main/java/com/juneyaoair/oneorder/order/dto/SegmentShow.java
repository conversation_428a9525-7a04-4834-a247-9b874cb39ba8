package com.juneyaoair.oneorder.order.dto;


import lombok.Data;


/**
 * <AUTHOR>
 * @description 升舱时航段显示信息
 * @date 2019/8/28  15:15.
 */
@Data
public class SegmentShow {
    /**
     * 旅行顺序  第一段为从0开始，第二段为1，依次增加
     */
    private int segNo;

    /**
     * PNR记录编号
     */
    private String pnrNo;
    /**
     * 飞行方向  去程为G,回程为B
     */
    private String flightDirection;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班出发日期
     * yyyy-MM-dd
     */
    private String flightDate;
    /**
     * 航班到达日期
     */
    private String flightArrDate;
    /**
     * 原始票面价
     */
    private double priceValue;
    /**
     * 周几
     */
    private String flightWeek;
    /**
     *起飞时间
     */
    private String deptTime;
    /**
     * 到达时间
     */
    private String arrTime;
    /**
     * 出发城市三字码
     */
    private String deptCityCode;
    /**
     * 出发城市名称
     */
    private String deptCityName;
    /**
     * 到达城市三字码
     */
    private String arrCityCode;
    /**
     * 到达城市名称
     */
    private String arrCityName;
    /**
     * 起飞机场三字码
     */
    private String deptAirPortCode;
    /**
     * 到达机场三字码
     */
    private String arrAirPortCode;
    /**
     * 出发机场名称
     */
    private String deptAirPortName;
    /**
     * 到达机场名称
     */
    private String arrAirPortName;
    /**
     * 出发机场航站楼
     */
    private String deptTerminal;
    /**
     * 到达机场航站楼
     */
    private String arrTerminal;
    /**
     * 飞行时长
     */
    private long flightTime;
    /**
     * 表示间隔天数
     */
    private int days;
    /**
     * 经停城市
     */
    private String stopCity;
    /**
     * 经停城市名称
     */
    private String stopCityName;
    /**
     * 当前舱位
     */
    private String cabin;
    /**
     * 舱位等级描述  经济舱   头等舱
     */
    private String cabinClassName;

    /**
     * 是否可升舱
     */
    private boolean ifUp;

    /**
     * 舱位是否售罄
     */
    private boolean cabinSoldOut;

    /**
     * 不可升舱原因
     */
    private String notUpReason;
    /**
     * 优惠券数量
     */
    private int couponNum;
    /**
     *是否需要购买券
     */
    private boolean buyCoupon;
    /**
     * 选中的升舱航段
     */
    private boolean selectUpFlag;
    /**
     * 使用的升舱优惠券
     */
    private String upCouponCode;

    /**
     * 是否是无限升舱卡
     */
    private boolean unlimitedUpClass;

    private String planeType;//机型

    /**
     * 无限升舱卡航段大于3段
     */
    private boolean unlimitedCardFlightOver3;

    private String ticketStatus;

}
