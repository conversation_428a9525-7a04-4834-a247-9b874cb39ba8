package com.juneyaoair.oneorder.crm.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CouponProductCondition {

    @ApiModelProperty(value = "航程类型 OW")
    private String flightType;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期  格式：yyyy-MM-dd")
    private String flightDate;

    @ApiModelProperty(value = "出发机场三字码")
    private String depAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

}
