package com.juneyaoair.oneorder.crm.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求后端修改消费密码
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-12-10 17:39
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PtResetConsumerPasswordRequest {
    private boolean IsVerify;  //是否验证
    private String Account;  //手机号或邮箱
    private String Captcha;  //短信验证码或者邮件验证码
    private String NewPassword;  //新密码
    private String OldPassword;  //是否自动生成新密码
}
