package com.juneyaoair.oneorder.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName StudentAuthDetailQueryResponse
 * @Description 学生认证 详情参数
 * <AUTHOR>
 * @Date 2020/5/8 17:46
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentAuthDetailQueryResponse extends StudentAuthDTO {
    /**
     * 会员id
     */
    private Integer memberId;

    /**
     * 认证状态
     */
    private String authStatus;  //认证结果  UNKNOW = 未知  REJECT = 认证失败  PROCESSING = 等待审核 EXPIRED = 认证失效 APPROVED = 已认证

    /**
     * 认证结果说明
     */
    private String authDesc;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 失效原因
     */
    private String expiredReason;
}
