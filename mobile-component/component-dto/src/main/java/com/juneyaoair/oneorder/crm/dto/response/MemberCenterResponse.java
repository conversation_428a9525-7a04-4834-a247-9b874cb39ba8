package com.juneyaoair.oneorder.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberCenterResponse
 * @Description 会员中心查询通用响应结果
 * <AUTHOR>
 * @Date 2019/7/31 16:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberCenterResponse<T> {

    /**
     * 渠道号
     */
    private String channelCode;

    /**
     * 结果编码
     */
    private String resultCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 版本号
     */
    private String version;

    /**
     * 用户ID
     */
    private String userNo;

    /**
     * 接口编码
     */
    private String servieCode;

    /**
     * 响应结果
     */
    private T result;

}
