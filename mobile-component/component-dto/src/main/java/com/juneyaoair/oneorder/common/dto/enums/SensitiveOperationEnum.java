package com.juneyaoair.oneorder.common.dto.enums;

/**
 * <AUTHOR>
 * @description  账户敏感操作枚举
 * @date 2023/11/7 9:52
 */
public enum SensitiveOperationEnum {
    /** operation desc */
    RESET_SALE_PWD("重置消费密码"),
    MODIFY_SALE_PWD("修改消费密码"),
    USE_SALE("使用积分"),
    ADD_BENEFICIARY("增加受益人"),
    AUTH("实名"),
    CHECK_IN_TOUR("值机选座行程查询"),
    TICKET_RISK_CTRL("客票查询"),
    TRADE_RISK_CTRL("机票交易下单"),
    UPGRADE_BUY_RISK_CTRL("无限升舱下单"),
    SEND_SMS_RISK_CTRL("发送短信"),
    REGISTER_RISK_CTRL("注册账户"),
    QUERY_FLIGHT_RISK_CTRL("航班查询"),
    CREATE_DCEPAY_RISK_CTRL("德付通支付参数"),
    CREATE_COUPON_RISK_CTRL("优惠券/权益领取"),
    B2C_WEB_ORDER_EVENT( "B2C下单购券事件"),
    LOGIN_RISK_CTRL("账户密码登录"),
    SEND_EMAIL_CTRL("发送邮件"),
    ;
    private final String desc;

    SensitiveOperationEnum(String desc) {
        this.desc = desc;
    }
    public String getDesc() {
        return desc;
    }
}
