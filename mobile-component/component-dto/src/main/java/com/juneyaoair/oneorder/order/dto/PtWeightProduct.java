package com.juneyaoair.oneorder.order.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/12/15.
 */
@XmlRootElement(name = "WeightProduct")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtWeightProduct {
    private String WeightName;//逾重产品名称

    private String WeightRemark;//逾重产品说明

    private String FlightNo;//航班号

    private int WeightId;//逾重产品Id

    private double WeightAmount;//逾重产品金额

    private String Currency;//币种

    private double WeightNum;//逾重产品重量

    private String WeightUnit;//逾重产品单位

    private String CouponCode;//优惠券代码

    private String ArrAirport;//到达机场三字码

    private String DepAirPort;//出发机场三字码

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getDepAirPort() {
        return DepAirPort;
    }

    public void setDepAirPort(String depAirPort) {
        DepAirPort = depAirPort;
    }

    public String getWeightName() {
        return WeightName;
    }

    public void setWeightName(String weightName) {
        WeightName = weightName;
    }

    public String getWeightRemark() {
        return WeightRemark;
    }

    public void setWeightRemark(String weightRemark) {
        WeightRemark = weightRemark;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public int getWeightId() {
        return WeightId;
    }

    public void setWeightId(int weightId) {
        WeightId = weightId;
    }

    public double getWeightAmount() {
        return WeightAmount;
    }

    public void setWeightAmount(double weightAmount) {
        WeightAmount = weightAmount;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public double getWeightNum() {
        return WeightNum;
    }

    public void setWeightNum(double weightNum) {
        WeightNum = weightNum;
    }

    public String getWeightUnit() {
        return WeightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        WeightUnit = weightUnit;
    }

    public String getCouponCode() {
        return CouponCode;
    }

    public void setCouponCode(String couponCode) {
        CouponCode = couponCode;
    }
}
