package com.juneyaoair.oneorder.order.dto;

/**
 * Created by yaocf on 2016/8/30.
 */

import java.util.Date;

/**
 * app用户登陆信息
 */
public class AppCustomer {
    private String recordId;
    private String  ffp_card_no;//卡号   //memberID
    private long ffp_id;//乘客ID   //id
    private String customer_name;//乘客姓名
    private String createTime;//首次登陆时间
    private Date last_recent_login_time;  //倒数第二次登录
    private Date last_login_time;//最近一次登陆时间
    private String last_deviceId;//最近一次登陆设备号
    private String clientVersion;//当前发起请求的客户端版本

    public AppCustomer() {
    }

    public AppCustomer(String ffp_card_no, long ffp_id) {
        this.ffp_card_no = ffp_card_no;
        this.ffp_id = ffp_id;
    }

    public AppCustomer(String ffp_card_no, long ffp_id, String customer_name) {
        this.ffp_card_no = ffp_card_no;
        this.ffp_id = ffp_id;
        this.customer_name = customer_name;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getFfp_card_no() {
        return ffp_card_no;
    }

    public void setFfp_card_no(String ffp_card_no) {
        this.ffp_card_no = ffp_card_no;
    }

    public long getFfp_id() {
        return ffp_id;
    }

    public void setFfp_id(long ffp_id) {
        this.ffp_id = ffp_id;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public Date getLast_login_time() {
        return last_login_time;
    }

    public void setLast_login_time(Date last_login_time) {
        this.last_login_time = last_login_time;
    }

    public String getLast_deviceId() {
        return last_deviceId;
    }

    public void setLast_deviceId(String last_deviceId) {
        this.last_deviceId = last_deviceId;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Date getLast_recent_login_time() {
        return last_recent_login_time;
    }

    public void setLast_recent_login_time(Date last_recent_login_time) {
        this.last_recent_login_time = last_recent_login_time;
    }

    @Override
    public String toString() {
        return "AppCustomer{" +
                "recordId='" + recordId + '\'' +
                ", ffp_card_no='" + ffp_card_no + '\'' +
                ", ffp_id=" + ffp_id +
                ", customer_name='" + customer_name + '\'' +
                ", last_recent_login_time='" + last_recent_login_time + '\'' +
                ", last_login_time=" + last_login_time +
                ", last_deviceId='" + last_deviceId + '\'' +
                ", clientVersion='" + clientVersion + '\'' +
                '}';
    }
}
