package com.juneyaoair.oneorder.constant;

/**
 * Created by yaocf on 2018/8/1  10:36.
 * 订单优惠券状态
 */
public enum OrderCouponStateEnum {
    Apply("Apply","申请"),
    Not("Not","未使用"),
    Cancel("Cancel","已取消"),
    Used("Used","已使用"),
    ApplyRefund("ApplyRefund","申请退款"),
    Refund("Refund","已退款"),
    Giving("Giving","赠送中"),
    GiveAway("GiveAway","已赠送"),
    Overdue("Overdue","已过期"),
    WittenOff("WittenOff","已核销"),
    Appointment("Appointment","已预约");
    private String stateCode;
    private String desc;
    OrderCouponStateEnum(String stateCode, String desc)
    {
        this.stateCode = stateCode;
        this.desc = desc;
    }

    public String getStateCode() {
        return stateCode;
    }

    public String getDesc() {
        return desc;
    }

    //校验类型
    public static OrderCouponStateEnum getEnum(String v){
        for (OrderCouponStateEnum c: OrderCouponStateEnum.values()) {
            if (c.stateCode.equals(v)) {
                return c;
            }
        }
        return null;
    }
}
