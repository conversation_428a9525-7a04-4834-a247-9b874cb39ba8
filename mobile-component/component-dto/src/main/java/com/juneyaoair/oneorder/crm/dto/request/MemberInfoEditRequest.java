package com.juneyaoair.oneorder.crm.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberInfoEditRequest
 * @Description 会员编辑请求体
 * <AUTHOR>
 * @Date 2023/9/6 8:54
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberInfoEditRequest{

    @ApiModelProperty(value = "用户是否已实名")
    private boolean realName;

    @ApiModelProperty(value = "会员基本信息")
    private MemberBasicInfo memberBasicInfo;

    @ApiModelProperty(value = "会员地址信息")
    private MemberAddressInfo memberAddressInfo;

}
