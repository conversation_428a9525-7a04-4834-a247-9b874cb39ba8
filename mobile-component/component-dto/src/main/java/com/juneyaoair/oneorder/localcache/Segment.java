package com.juneyaoair.oneorder.localcache;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @Author: caolei
 * @Description: 航线信息
 * @Date: 2022/4/8 15:07
 * @Modified by:
 */
@Data
public class Segment {
    @ApiModelProperty(value = "城市清单")
    private Set<String> cityCodeSet;
    @ApiModelProperty(value = "null：未知  CN:国内 TW：台湾 HK：香港  MO：澳门 其他：国外")
    private String segmentCode;
    @ApiModelProperty(value = "航信类型 D：国内 I：国际 R:港澳台 null：未知")
    private String segmentType;

    /**
     * 是否国际航线
     * @return
     */
    public boolean isInternal() {
        return "I".equals(segmentType) || "R".equals(segmentType);
    }
}