package com.juneyaoair.oneorder.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.oneorder.crm.dto.common.GeneralContactCertInfoV2;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName QueryCommonPersonInfoV2
 * @Description
 * <AUTHOR>
 * @Date 2023/9/8 17:14
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryCommonPersonInfoV2 {
    @ApiModelProperty(value = "常用旅客联系信息ID,主键自动生成")
    private Integer commonContactId;
    @ApiModelProperty(value = "渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID，其它时为自编号")
    private String channelCustomerNo;
    @ApiModelProperty(value = "渠道客户类型,CRM - 常客,Other - 其它")
    private String channelCustomerType;
    @ApiModelProperty(value = "乘客姓名 英文姓名可以根据/分出姓和名")
    private String passengerName;
    @ApiModelProperty(value = "乘客类型 ADT － 成人，CHD － 儿童，INF － 婴儿")
    private String passengerType;
    @ApiModelProperty(value = "常客卡号 HO+卡号")
    private String ffCardNo;
    @ApiModelProperty(value = "手机号国际区号")
    private String countryTelCode;
    @ApiModelProperty(value = "手机号")
    private String handphoneNo;
    @ApiModelProperty(value = "出生日期yyyy-MM-dd")
    private String birthdate;
    @ApiModelProperty(value = "性别")
    private String sex;
    @ApiModelProperty(value = "国籍")
    private String nationality;
    @ApiModelProperty(value = "国籍名称")
    private String nationalityName;
    @ApiModelProperty(value = "发证国")
    private String belongCountry;
    @ApiModelProperty(value = "证件有效期yyyy-MM-dd")
    private String certValidity;
    @ApiModelProperty(value = "最后预订时间yyyy-MM-dd HH:mm:ss")
    private String lastBookingTime;
    @ApiModelProperty(value = "预订次数")
    private String bookingNum;
    @ApiModelProperty(value = "机票订单创建时间格式:yyyy-MM-dd HH:mm:ss")
    private String createDatetime;
    @ApiModelProperty(value = "国际标识")
    private String interFlag;
    @ApiModelProperty(value = "星盟卡号 XX+卡号")
    private String saCardNo;
    @ApiModelProperty(value = "乘机人类型 A儿童畅飞卡 B成人畅飞卡")
    private String contactType;
    @ApiModelProperty(value = "乘客英文姓")
    private String passEnNameS;
    @ApiModelProperty(value = "乘客英文名")
    private String passEnNameF;
    @ApiModelProperty(value = "是否军残警残")
    private String isGmJC;
    @ApiModelProperty(value = "是否本人标记")
    private Boolean isOwn;
    @ApiModelProperty(value = "是否受益人标记")
    private Boolean isBeneficiary;
    @ApiModelProperty(value = "优先级调整")
    private int priority;

    @Valid
    @Size(min = 1, message = "至少添加一个证件信息")
    @ApiModelProperty(value = "证件信息列表")
    private List<GeneralContactCertInfoV2> contactCertList;

    @ApiModelProperty(value = "错误提示信息")
    private String alertMessage;

    @ApiModelProperty(value = "提示类型")
    private String messageType;

    @JsonIgnore
    @ApiModelProperty(value = "畅飞卡2.0类型 UnlimitedFly:吉祥畅飞卡2.0 UnlimitedFlySF:吉祥畅飞卡2.0 春运版")
    private String unlimitedCardType;

    @ApiModelProperty(value = "是否拥军优属")
    private Boolean isYjYs;
}
