package com.juneyaoair.oneorder.order.dto;

public class TaxInfo {
	private Double TaxAmount; //税项金额
	private String TaxCode; //税项代码
	private String TaxCurrencyType; //税项货币类型

	public Double getTaxAmount(){
		return TaxAmount;
	}
	public void setTaxAmount(Double TaxAmount){
		this.TaxAmount=TaxAmount;
	}
	public String getTaxCode(){
		return TaxCode;
	}
	public void setTaxCode(String TaxCode){
		this.TaxCode=TaxCode;
	}
	public String getTaxCurrencyType(){
		return TaxCurrencyType;
	}
	public void setTaxCurrencyType(String TaxCurrencyType){
		this.TaxCurrencyType=TaxCurrencyType;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("TaxInfo ["); 
		sb.append("TaxAmount="+TaxAmount+",");
		sb.append("TaxCode="+TaxCode+",");
		sb.append("TaxCurrencyType="+TaxCurrencyType);
		sb.append("]");
		return sb.toString();
	}
}