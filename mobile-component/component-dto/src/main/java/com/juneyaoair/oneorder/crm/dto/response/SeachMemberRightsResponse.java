package com.juneyaoair.oneorder.crm.dto.response;

import com.juneyaoair.oneorder.crm.dto.common.MemberCompanyRightsResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberDoctorRightsResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberRightsResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberStarRightsResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 会员可用权益响应（包含普通权益、星级权益）
 * @Date 19:01 2023/7/15
 **/
@Data
@NoArgsConstructor
public class SeachMemberRightsResponse {

    @ApiModelProperty(value = "会员权益列表")
    private List<MemberRightsResponse> memberRightsResponse;

    @ApiModelProperty(value = "会员星级权益")
    private MemberStarRightsResponse memberStarRightsResponse;

    @ApiModelProperty(value = "敬医权益")
    private MemberDoctorRightsResponse memberDoctorRightsResponse;

    @ApiModelProperty(value = "企业专属权益")
    private MemberCompanyRightsResponse memberCompanyRightsResponse;



}
