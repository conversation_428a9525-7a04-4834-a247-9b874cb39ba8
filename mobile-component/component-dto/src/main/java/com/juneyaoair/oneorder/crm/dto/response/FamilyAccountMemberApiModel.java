package com.juneyaoair.oneorder.crm.dto.response;

import com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FamilyAccountMemberApiModel {

    private String DetailRecordId;

    /**
     * 账户状态(Y-正常 N-异常)
     */
    private String  AccountStatus;
    /**
     * 关联关系生效日期(当前会员不是主账户时有值)
     */
    private String  EffectiveDate;
    /**
     *关联关系失效日期(当前会员不是主账户时有值) ,
     */
    private String ExpireDate;
    /**
     * 会员卡号
     */
    private String  MemberId;
    /**
     * 会员姓名（已脱敏）
     */
    private String  MemberName;
    /**
     *家庭成员类型(M-主账号本人、P-主账户父母、S-主账户配偶、C-主账户子女、O-其他)
     */
    private String  MemberType;

    private Boolean IsOwn;

    private Boolean IsGeneral;

    private Boolean  IsBeneficiary;

    private  Boolean IsAdult;

    private Boolean IsExpireDate;

    private String MemberLevel;//会员级别描述  福卡

    private String MemberLevelCode;//会员级别代码

    private String  LevelExpireDate;

    private List<MemberFamilyCertificateDto>  certificateSoaModelList;
}
