package com.juneyaoair.oneorder.crm.dto.response;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PhoneCardExtInfo {

    @SerializedName("Days")
    @ApiModelProperty(value = "适用天数")
    private String days;

    @SerializedName("AdvanceDays")
    @ApiModelProperty(value = "提前预定天数")
    private Integer advanceDays;

    @SerializedName("AdvanceHours")
    @ApiModelProperty(value = "提前预定小时数")
    private Integer advanceHours;

    @SerializedName("MinBookDays")
    @ApiModelProperty(value = "最小预定天数")
    private Integer minBookDays;

    @SerializedName("CountryId")
    @ApiModelProperty(value = "国家编码")
    private String countryId;
}
