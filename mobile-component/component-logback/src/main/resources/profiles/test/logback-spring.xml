<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="3 seconds">
    <property resource="application.properties"/>
    <property name="LOG_PATH" value="/opt/log/"/>
    <property name="appName" value="${spring.application.name:-bff-service}"/>
    <property name="env" value="${spring.profiles.active}"/>

    <!-- 输出到控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5p) --- [${appName},%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId},%X{X-Span-Export}] [%t] %logger{39} : %msg%n</Pattern>
        </encoder>
    </appender>

    <!-- 流水日志 -->
    <appender name="flowlog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${appName}/flowlog/flowlog.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}/flowlog/flowlog-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-6p --- [%15.15t] [${appName}][trace=%X{X-B3-TraceId}] %-40.40logger{39} : %msg%n</Pattern>
        </layout>
    </appender>


    <!-- 业务日志 -->
    <appender name="apilog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${appName}/apilog/api.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}/apilog/api-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%msg%n</Pattern>
        </layout>
    </appender>

    <!-- 度量日志 -->
    <appender name="metriclog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${appName}/metriclog/metric.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}/metriclog/metriclog-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%msg%n</Pattern>
        </layout>
    </appender>


    <logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
    <logger name="org.apache.catalina.util.LifecycleBase" level="ERROR"/>
    <logger name="org.apache.coyote.http11.Http11NioProtocol" level="ERROR"/>
    <logger name="org.apache.sshd.common.util.SecurityUtils" level="ERROR"/>
    <logger name="org.apache.tomcat.util.net.NioSelectorPool" level="ERROR"/>
    <logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="ERROR"/>
    <logger name="org.hibernate.validator.internal.util.Version" level="ERROR"/>
    <logger name="org.springframework.kafka" level="ERROR"/>
    <logger name="org.apache.kafka" level="ERROR"/>
    <logger name="org.mongodb" level="ERROR"/>

    <!--elk接入的度量日志-->
    <logger name="com.juneyaoair.metriclog" level="info" additivity="false">
        <appender-ref ref="metriclog"/>
    </logger>
    <!--elk接入的日志-->
    <logger name="com.juneyaoair.apilog" level="info" additivity="false">
        <appender-ref ref="apilog"/>
    </logger>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="flowlog"/>
    </root>
</configuration>