<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.juneyaoair.oneorder</groupId>
        <artifactId>apiMobile</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>mobile-component</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>component-jpasskit</module>
        <module>component-security</module>
        <module>component-exception</module>
        <module>component-util</module>
        <module>component-dto</module>
        <module>component-core</module>
        <module>component-logback</module>
        <module>component-aspect</module>
        <module>service-support</module>
        <module>component-swagger</module>
        <module>component-i18n</module>
        <module>component-base</module>
        <module>component-constant</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>