package com.juneyaoair.mobile.exception;

import com.google.common.collect.Maps;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/6 9:08
 */
public abstract class AppException extends RuntimeException {
    private final ErrorCode error;
    /** 异常描述是否已经国际化 */
    private Boolean global;
    private final Map<String, Object> data = Maps.newHashMap();

    protected AppException(ErrorCode error, Map<String, Object> data) {
        super(format(error.getCode(), error.getMessage(), data));
        this.error = error;
        if (!MapUtils.isEmpty(data)) {
            this.data.putAll(data);
        }

    }

    protected AppException(ErrorCode error, String message, Map<String, Object> data) {
        super(message);
        this.error = error;
        if (!MapUtils.isEmpty(data)) {
            this.data.putAll(data);
        }
    }

    protected AppException(ErrorCode error, String message, Boolean global, Map<String, Object> data) {
        super(message);
        this.error = error;
        this.global = global;
        if (!MapUtils.isEmpty(data)) {
            this.data.putAll(data);
        }
    }

    private static String format(String code, String message, Map<String, Object> data) {
        return String.format("[%s]%s:%s.", code, message, MapUtils.isEmpty(data) ? "" : data.toString());
    }

    public ErrorCode getError() {
        return this.error;
    }

    public Map<String, Object> getData() {
        return this.data;
    }

    public Boolean getGlobal() {
        return global;
    }

}