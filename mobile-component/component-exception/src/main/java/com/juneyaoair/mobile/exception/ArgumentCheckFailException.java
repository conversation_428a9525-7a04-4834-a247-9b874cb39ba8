package com.juneyaoair.mobile.exception;

import com.google.common.collect.ImmutableMap;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 参数检验异常
 * @date 2023/5/26 15:48
 */
public class ArgumentCheckFailException extends AppException {

    public ArgumentCheckFailException(Map<String, Object> data) {
        super(CommonErrorCode.REQUEST_VALIDATION_FAILED,CommonErrorCode.REQUEST_VALIDATION_FAILED.getMessage(), data);
    }

    public ArgumentCheckFailException(String cause) {
        super(CommonErrorCode.REQUEST_VALIDATION_FAILED, cause, ImmutableMap.of("detail", cause));
    }
}
