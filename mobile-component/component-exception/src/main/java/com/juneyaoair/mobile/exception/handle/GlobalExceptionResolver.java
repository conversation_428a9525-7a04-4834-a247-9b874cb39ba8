package com.juneyaoair.mobile.exception.handle;

import brave.propagation.TraceContext;
import cn.dev33.satoken.sso.exception.SaSsoException;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.juneyaoair.bff.util.HoLogUtil;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.mobile.exception.PlatServiceException;
import com.juneyaoair.mobile.exception.util.ServletUtils;
import com.juneyaoair.oneorder.common.exception.BffServiceException;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.oneorder.log.dto.BffLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/26 15:34
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionResolver {

    @Value("${spring.application.name}")
    private String serverName;
    @Resource
    private LocaleUtil localeUtil;

    @ExceptionHandler({ArgumentCheckFailException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handleAppException(ArgumentCheckFailException ex) {
        ResponseData representation = createResponseData(ex.getError(), ex.getMessage(), null);
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    @ExceptionHandler({ServiceException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handleServiceException(ServiceException ex) {
        ResponseData representation = createResponseData(ex.getError(), ex.getMessage(), null);
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    @ExceptionHandler({BffServiceException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> bffServiceException(BffServiceException ex, HttpServletRequest request) {
        Map<String, Object> errMap = null;
        if (StringUtils.isNotBlank(ex.getMessage())) {
            errMap = new HashMap<>();
            errMap.put("detail", ex.getMessage());
        }
        ResponseData representation = new ResponseData(ex.getErrorCode().name(), ex.getErrorCode().getStatus(), localeUtil.getTips(ex.getErrorCode().name()), errMap);
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    @ExceptionHandler({MultiLangServiceException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handleMultiLangServiceException(MultiLangServiceException ex, HttpServletRequest request) {
        ResponseData representation = createResponseData(ex.getError(), ex.getMessage(), ex.getGlobal());
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    @ExceptionHandler({PlatServiceException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handlePlatServiceException(PlatServiceException ex, HttpServletRequest request) {
        ErrorCode errorCode = ex.getError();
        String errorMsg = ex.getMessage();
        ResponseData responseData = new ResponseData(errorCode.getCode(), errorCode.getStatus(), ex.getMessage(), null, ImmutableMap.of("detail", errorMsg));
        return new ResponseEntity(responseData, HttpStatus.OK);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ResponseData> httpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.error("请求URI:{},异常信息:", request.getRequestURI(), e);
        Map<String, Object> errMap = new HashMap<>();
        errMap.put("error", "httpMessageNotReadableException");
        ResponseData representation = new ResponseData(CommonErrorCode.REQUEST_VALIDATION_FAILED.getCode(), CommonErrorCode.REQUEST_VALIDATION_FAILED.getStatus(), localeUtil.getTips(CommonErrorCode.REQUEST_VALIDATION_FAILED.name()), errMap);
        return new ResponseEntity<>(representation, HttpStatus.OK);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ResponseData> methodValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        String errorMessage = objectError.getDefaultMessage();
        ResponseData representation = createResponseData(CommonErrorCode.REQUEST_VALIDATION_FAILED, errorMessage, null);
        return new ResponseEntity<>(representation, HttpStatus.OK);
    }

    @ExceptionHandler(value = BindException.class)
    public ResponseEntity<ResponseData> bindException(BindException be, HttpServletRequest request) {
        log.error("请求URI:{},异常信息:", request.getRequestURI(), be);
        List<ObjectError> allErrors = be.getBindingResult().getAllErrors();
        List<String> errorMessage = Lists.newArrayList();
        for (ObjectError error : allErrors) {
            if (StringUtils.isNotBlank(error.getDefaultMessage())) {
                errorMessage.add(error.getDefaultMessage());
            }
        }
        ResponseData representation = createResponseData(CommonErrorCode.REQUEST_VALIDATION_FAILED, Joiner.on(",").join(errorMessage), null);
        return new ResponseEntity<>(representation, HttpStatus.OK);
    }

    @ExceptionHandler({SaSsoException.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handleSaSsoException(SaSsoException ex, HttpServletRequest request) {
        String traceId = getTraceId(request);
        ResponseData representation = new ResponseData(CommonErrorCode.SYSTEM_ERROR.getCode(), CommonErrorCode.SYSTEM_ERROR.getStatus(), ex.getMessage(), null, null, traceId);
        BffLogDTO resBffLogDTO = createResLogDTO(traceId, request, CommonErrorCode.SYSTEM_ERROR, JSON.toJSONString(representation));
        HoLogUtil.accdetailLogger.info(JSON.toJSONString(resBffLogDTO));
        saveMetricLog(request, CommonErrorCode.SYSTEM_ERROR);
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    @ExceptionHandler({Exception.class})
    @ResponseBody
    public ResponseEntity<ResponseData> handleException(Exception ex, HttpServletRequest request) {
        String exMsg = getExMsg(ex);
        log.error("请求URI:{},异常定位：{} 异常信息：", request.getRequestURI(), exMsg, ex);
        String traceId = getTraceId(request);
        ResponseData representation = new ResponseData(CommonErrorCode.SYSTEM_ERROR.getCode(), CommonErrorCode.SYSTEM_ERROR.getStatus(), localeUtil.getTips(CommonErrorCode.SYSTEM_ERROR.name()), null, null, traceId);
        BffLogDTO resBffLogDTO = createResLogDTO(traceId, request, CommonErrorCode.SYSTEM_ERROR, JSON.toJSONString(representation));
        HoLogUtil.accdetailLogger.info(JSON.toJSONString(resBffLogDTO));
        saveMetricLog(request, CommonErrorCode.SYSTEM_ERROR);
        return new ResponseEntity(representation, HttpStatus.OK);
    }

    private void saveMetricLog(HttpServletRequest request, ErrorCode errorCode) {
        //渠道信息
        String channelCode = ServletUtils.getChannelCode(request);
        //请求方法地址
        String requestURL = request.getRequestURI();
        String clientIp = HoAirIpUtil.getIpAddr(request);
        HoLogUtil.saveMetricLog(clientIp, channelCode, requestURL, errorCode.getCode(), errorCode.getMessage());

    }

    //因为没实现ServiceException的toString 所以这里先自己定义ex的message
    private static String getExMsg(Exception ex) {
        StringBuilder exMsg = new StringBuilder();
        if (ex.getStackTrace() != null && ex.getStackTrace().length > 0 && ex.getStackTrace()[0] != null) {
            exMsg.append(ex.getStackTrace()[0].getClassName()).append(":").append(ex.getStackTrace()[0].getLineNumber());
            exMsg.append(System.lineSeparator());
        }
        if (ex.getStackTrace() != null && ex.getStackTrace().length > 1 && ex.getStackTrace()[1] != null) {
            exMsg.append(ex.getStackTrace()[1].getClassName()).append(":").append(ex.getStackTrace()[1].getLineNumber());
            exMsg.append(System.lineSeparator());
        }
        if (ex.getStackTrace() != null && ex.getStackTrace().length > 2 && ex.getStackTrace()[2] != null) {
            exMsg.append(ex.getStackTrace()[2].getClassName()).append(":").append(ex.getStackTrace()[2].getLineNumber());
            exMsg.append(System.lineSeparator());
        }
        return exMsg.toString();
    }

    private String getTraceId(HttpServletRequest request) {
        String traceId = null;
        TraceContext traceContext = (TraceContext) request.getAttribute(TraceContext.class.getName());
        if (null != traceContext) {
            traceId = StringUtils.isNotBlank(traceContext.traceIdString()) ? traceContext.traceIdString() : null;
        }
        return traceId;
    }

    private BffLogDTO createResLogDTO(String traceId, HttpServletRequest request, ErrorCode errorCode, String message) {
        BffLogDTO log = new BffLogDTO();
        log.setTraceId(traceId);
        log.setChannelCode(ServletUtils.getChannelCode(request));
        log.setClientVersion(ServletUtils.getHeader(request,SecurityConstants.HEAD_CLIENT_VERSION));
        log.setPath(request.getRequestURI());
        log.setClientIp(HoAirIpUtil.getIpAddr(request));
        log.setHost(HoAirIpUtil.getLocalIp());
        log.setServiceName(serverName);
        log.setTimestamp(HoLogUtil.getCurrentDateStr(HoLogUtil.FORMAT_DATE_TIME_ELK));
        log.setMessage(message);
        log.setType("Response");
        log.setErrorCode(errorCode.getCode());
        log.setErrorMess(errorCode.getMessage());
        log.setInterval(0);
        return log;
    }

    /**
     * 创建返回对象
     * @param errorMsg
     * @param errorCode
     * @return
     */
    private ResponseData<Object> createResponseData(ErrorCode errorCode, String errorMsg, Boolean global){
        // 语言为简体中文 message使用原始值 否则使用翻译值
        String message;
        // 不存在异常描述 按响应编码获取国际化文案
        if (StringUtils.isBlank(errorMsg)) {
            message = localeUtil.getTips(errorCode.getCode());
        }
        // 语言为中文返回异常描述
        else if (Locale.SIMPLIFIED_CHINESE.equals(LocaleContextHolder.getLocale())) {
            message = errorMsg;
        }
        // 异常标识异常描述已经进行了国际化
        else if (Boolean.TRUE.equals(global)) {
            message = errorMsg;
        }
        // 其他 按响应编码获取国际化文案
        else {
            message = localeUtil.getTips(errorCode.getCode());
        }
        return new ResponseData<>(errorCode.getCode(), errorCode.getStatus(), message, null, ImmutableMap.of("detail", errorMsg));
    }

}
