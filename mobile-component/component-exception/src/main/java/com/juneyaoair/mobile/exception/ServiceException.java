package com.juneyaoair.mobile.exception;

import com.google.common.collect.ImmutableMap;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 业务服务异常 此服务异常逐步废弃，建议使用MultiLangServiceException
 * @date 2023/5/26 15:19
 * 使用支持国际化的异常 MultiLangServiceException 替代
 */
@Deprecated
public class ServiceException extends AppException {
    public ServiceException(String message) {
        super(CommonErrorCode.BUSINESS_ERROR, message, StringUtils.isBlank(message) ? ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public ServiceException(ErrorCode errorCode, String message) {
        super(errorCode, message, StringUtils.isBlank(message) ? ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    protected ServiceException(ErrorCode error, String message, Map<String, Object> data) {
        super(error, message, data);
    }

    public static ServiceException fail(ErrorCode errorCode) {
        return fail(errorCode, errorCode.getMessage(), ImmutableMap.of("detail", errorCode.getMessage()));
    }

    public static ServiceException fail(ErrorCode errorCode, String message) {
        return fail(errorCode, message, StringUtils.isBlank(message) ?
                ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public static ServiceException fail(String message) {
        return fail(CommonErrorCode.BUSINESS_ERROR, message,StringUtils.isBlank(message) ?
                ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    /**
     * @param errorCode
     * @param message 对外显示
     * @param detailMap 对内显示
     * @return
     */
    public static ServiceException fail(ErrorCode errorCode, String message, Map<String,Object> detailMap) {
        throw new ServiceException
                (new ErrorCode() {
                    @Override
                    public int getStatus() {
                        return errorCode.getStatus();
                    }

                    @Override
                    public String getMessage() {
                        return message;
                    }

                    @Override
                    public String getCode() {
                        return errorCode.getCode();
                    }
                }, message, detailMap);
    }

}
