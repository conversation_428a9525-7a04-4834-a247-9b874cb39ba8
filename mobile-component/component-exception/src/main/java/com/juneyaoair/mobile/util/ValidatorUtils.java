package com.juneyaoair.mobile.util;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * @Author: caolei
 * @Description: 参数校验器
 * @Modified by:
 */
public class ValidatorUtils {

    private ValidatorUtils(){
    }

    private static final ValidatorFactory FACTORY = Validation.buildDefaultValidatorFactory();

    /**
     * 参数校验
     * @param obj
     * @param <T>
     */
    public static <T> void validate(T obj){
        Set<ConstraintViolation<T>> violations = FACTORY.getValidator().validate(obj);
        if (!CollectionUtils.isEmpty(violations)) {
            throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, violations.iterator().next().getMessage());
        }
    }

}
