package com.juneyaoair.mobile.exception;

import com.google.common.collect.ImmutableMap;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 业务服务异常 多语言改造，逐步替换原有ServiceException 此异常是中断服务流程
 * @date 2023/5/26 15:19
 */
public class MultiLangServiceException extends AppException {

    private Object obj = null;

    public MultiLangServiceException(String message) {
        super(CommonErrorCode.SYSTEM_ERROR, StringUtils.isBlank(message) ? "抱歉！系统出现意外！" : message, StringUtils.isBlank(message) ? ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public MultiLangServiceException(ErrorCode errorCode) {
        super(errorCode, errorCode.getMessage(),ImmutableMap.of("detail", errorCode.getMessage()));
    }

    public MultiLangServiceException(ErrorCode errorCode, String message) {
        super(errorCode, message, StringUtils.isBlank(message) ? ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public MultiLangServiceException(ErrorCode errorCode, String message, Boolean global) {
        super(errorCode, message, global, StringUtils.isBlank(message) ? ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public MultiLangServiceException(ErrorCode error, Map<String, Object> data) {
        super(error,error.getMessage(), data);
    }

    public MultiLangServiceException(ErrorCode error, Object obj, Map<String, Object> data) {
        super(error,error.getMessage(), data);
        this.obj = obj;
    }

    public static MultiLangServiceException fail(String message) {
        return fail(CommonErrorCode.BUSINESS_ERROR, message, StringUtils.isBlank(message) ?
                ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public static MultiLangServiceException fail(ErrorCode errorCode) {
        return fail(errorCode, errorCode.getMessage(), ImmutableMap.of("detail", errorCode.getMessage()));
    }

    public static MultiLangServiceException fail(ErrorCode errorCode, String message) {
        return fail(errorCode, message, StringUtils.isBlank(message) ?
                ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }

    public static MultiLangServiceException fail(ErrorCode errorCode, String message, Object obj) {
        return fail(errorCode, message, obj, StringUtils.isBlank(message) ?
                ImmutableMap.of("detail", "抱歉！系统出现意外！") : ImmutableMap.of("detail", message));
    }


    /**
     * @param errorCode
     * @param message   对外显示
     * @param detailMap 对内显示
     * @return
     */
    public static MultiLangServiceException fail(ErrorCode errorCode, String message, Map<String, Object> detailMap) {
        throw new MultiLangServiceException
                (new ErrorCode() {
                    @Override
                    public int getStatus() {
                        return errorCode.getStatus();
                    }

                    @Override
                    public String getMessage() {
                        return message;
                    }

                    @Override
                    public String getCode() {
                        return errorCode.getCode();
                    }
                }, detailMap);
    }

    /**
     * @param errorCode
     * @param message   自定义对外显示
     * @param obj       异常时返回的结构体
     * @param detailMap 对内显示
     * @return
     */
    public static MultiLangServiceException fail(ErrorCode errorCode, String message, Object obj, Map<String, Object> detailMap) {
        throw new MultiLangServiceException
                (new ErrorCode() {
                    @Override
                    public int getStatus() {
                        return errorCode.getStatus();
                    }

                    @Override
                    public String getMessage() {
                        return message;
                    }

                    @Override
                    public String getCode() {
                        return errorCode.getCode();
                    }
                }, obj, detailMap);
    }

    public Object getObj() {
        return obj;
    }
}
