package com.juneyaoair.oneorder.common.security.utils;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @description 客户端工具类
 * @date 2023/6/29 13:56
 */
public class ServletUtils {

    private static final String UTF8 = "UTF-8";

    /**
     * 获取请求投中指定参数值
     * @param request
     * @param name
     * @return
     */
    public static String getHeader(HttpServletRequest request, String name) {
        String value = request.getHeader(name);
        if (StringUtils.isBlank(value))
        {
            return StringUtils.EMPTY;
        }
        return urlDecode(value);
    }

    /**
     * 内容解码
     *
     * @param str 内容
     * @return 解码后的内容
     */
    public static String urlDecode(String str) {
        try
        {
            return URLDecoder.decode(str, UTF8);
        }
        catch (UnsupportedEncodingException e)
        {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 获取渠道号
     * 渠道号 channelCode > ChannelNo > channel_code
     * @param request
     * @return
     */
    public static String getChannelCode(HttpServletRequest request) {
        String channelCode = ServletUtils.getHeader(request, SecurityConstants.HEAD_CHANNEL_CODE);
        if (StringUtils.isBlank(channelCode)){
            channelCode = ServletUtils.getHeader(request, SecurityConstants.HEAD_CHANNEL_NO);
        }
        if (StringUtils.isBlank(channelCode)){
            channelCode = ServletUtils.getHeader(request, SecurityConstants.HEAD_CAR_CHANNEL_CODE);
        }
        return channelCode;
    }

    /**
     * 基于请求头获取语言信息
     * @param request
     * @return
     */
    public static LanguageEnum getLanguage(HttpServletRequest request) {
        String lang = ServletUtils.getHeader(request, SecurityConstants.LANGUAGE);
        return LanguageEnum.getLanguage(lang);
    }

}
