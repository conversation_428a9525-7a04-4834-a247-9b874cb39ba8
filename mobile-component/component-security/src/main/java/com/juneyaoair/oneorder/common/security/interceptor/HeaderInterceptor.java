package com.juneyaoair.oneorder.common.security.interceptor;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.security.utils.HttpRequestUtils;
import com.juneyaoair.oneorder.common.security.utils.ServletUtils;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * @date 2023/7/21 16:28
 */
@Order(1)
@Slf4j
public class HeaderInterceptor implements AsyncHandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (request == null) {//for sonar check
            return false;
        }
        if (!(handler instanceof HandlerMethod))
        {
            return true;
        }
        SecurityContextHolder.setClientVersion(ServletUtils.getHeader(request, SecurityConstants.HEAD_CLIENT_VERSION));
        SecurityContextHolder.setVersionCode(ServletUtils.getHeader(request, SecurityConstants.HEAD_VERSION_CODE));
        String channelCode = ServletUtils.getChannelCode(request);
        SecurityContextHolder.setChannelCode(channelCode);
        SecurityContextHolder.setVersion(ServletUtils.getHeader(request, SecurityConstants.HEAD_VERSION));
        SecurityContextHolder.setOriginIp(HttpRequestUtils.getIpAddress(request));
        LanguageEnum language = ServletUtils.getLanguage(request);
        SecurityContextHolder.setLanguage(language);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        SecurityContextHolder.remove();
    }
}
