<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.juneyaoair</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.3</version>
    </parent>

    <groupId>com.juneyaoair.oneorder</groupId>
    <artifactId>apiMobile</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <description>新版BFF服务层</description>

    <!-- 子模块 -->
    <modules>
        <module>mobile-gateway</module>
        <module>mobile-auth</module>
        <module>mobile-sso</module>
        <module>mobile-component</module>
        <module>mobile-config</module>
        <module>mobile-service</module>
        <module>mobile-api</module>
        <module>mobile-hocar-gateway</module>
        <module>hocar-gateway2b</module>
        <module>hocar-auth2b</module>
    </modules>

    <!-- 统一属性管理 -->
    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <oneorder.mybatis>1.0.1-SNAPSHOT</oneorder.mybatis>
        <oneorder.client.version>1.0.0-SNAPSHOT</oneorder.client.version>
        <spring.cloud.version>Hoxton.SR9</spring.cloud.version>
        <spring.cloud.alibaba.version>2.2.5.RELEASE</spring.cloud.alibaba.version>
        <spring.cloud.sleuth.version>2.2.6.RELEASE</spring.cloud.sleuth.version>
        <spring.boot.version>2.3.8.RELEASE</spring.boot.version>
        <elastic-job.version>2.1.5</elastic-job.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <apollo.version>1.6.1</apollo.version>
        <gson.version>2.8.6</gson.version>
        <fastjson.version>2.0.26</fastjson.version>
        <fastjson2.version>2.0.33</fastjson2.version>
        <base-component-bom.version>1.3.3</base-component-bom.version>
        <compilerArgument>-Xlint:deprecation,unchecked</compilerArgument>
        <lombok.version>1.18.18</lombok.version>
        <jetcache.version>2.5.2</jetcache.version>
        <joda-time.version>2.10.10</joda-time.version>
        <servlet.version>3.1.0</servlet.version>
        <cxf.verssion>3.4.3</cxf.verssion>
        <discovery.version>6.10.0</discovery.version>
        <poi.version>3.17</poi.version>
        <kafka.version>2.6.6</kafka.version>
        <jasypt.version>2.0.0</jasypt.version>
        <kaptcha.version>0.0.9</kaptcha.version>
        <curator.version>5.0.0</curator.version>
        <knife4j.version>2.0.9</knife4j.version>
        <caffeine.version>2.9.0</caffeine.version>
        <hutool.version>5.7.14</hutool.version>
        <zxing.version>3.3.3</zxing.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <flying-saucer-pdf.version>9.1.20</flying-saucer-pdf.version>
        <zipkin.brave.version>5.12.7</zipkin.brave.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <pagehelper.version>5.3.1</pagehelper.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <jjwt.version>0.10.0</jjwt.version>
        <saToken.version>1.39.0</saToken.version>
        <dsop.version>5.0.2</dsop.version>
        <flightBasicConsumer.client.version>1.0.1-SNAPSHOT</flightBasicConsumer.client.version>
        <flightBasicProvider.client.version>1.0.1-SNAPSHOT</flightBasicProvider.client.version>
        <smsModel.version>1.0.0</smsModel.version>
        <tinyurl.client.version>1.0.0-SNAPSHOT</tinyurl.client.version>
        <json.lib.version>2.3</json.lib.version>
        <jsch.version>0.1.55</jsch.version>
        <commons.net.version>3.3</commons.net.version>
        <javax.version>2.3.1</javax.version>
        <cuss.client.version>2.0.5-SNAPSHOT</cuss.client.version>
        <forest.version>1.5.26</forest.version>
        <jakarta.validation.version>2.0.2</jakarta.validation.version>
        <asm.version>9.1</asm.version>
        <commons-email.version>1.5</commons-email.version>
        <javax.mail.verson>1.5.6</javax.mail.verson>

    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>asm</artifactId>
            <groupId>org.ow2.asm</groupId>
            <version>${asm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${javax.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>1.10</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- ########################pom依赖版本控制#########################################################-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-sleuth</artifactId>
                <version>${spring.cloud.sleuth.version}</version>
            </dependency>
            <!-- ########################pom依赖版本控制#########################################################-->

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <!-- 引入elastic-job-lite核心模块 -->
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-core</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <!-- 使用springframework自定义命名空间时引入 -->
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-spring</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <!-- 下面三个引用解决当前版本elastic-job一些bug-->
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>2.12.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.22</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--增强swagger-bootstrap-ui-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- 项目版本控制 ################################################-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>21.1.0.0</version>
            </dependency>
            <dependency>
                <groupId>cn.easyproject</groupId>
                <artifactId>orai18n</artifactId>
                <version>12.1.0.2.0</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.22</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <!-- 选座系统 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.travelsky</groupId>
                <artifactId>ebuildapi</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>${cxf.verssion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.miwurster</groupId>
                <artifactId>spring-data-influxdb</artifactId>
                <version>1.8</version>
            </dependency>
            <!--Sftp-->
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
            <!--Zip4j-->
            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>7.1.4</version>
            </dependency>
         <!--   <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>common-ftp</artifactId>
                <version>1.1.0</version>
            </dependency>-->

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.2</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.3.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.axet</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.12</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-email</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.64</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.64</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-kotlin</artifactId>
                <version>2.12.3</version>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.4</version>
            </dependency>

            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-spring</artifactId>
                <version>1.3-groovy-2.5</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>1.3-groovy-2.5</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.5.8</version>
                <type>pom</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.197</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>io.zipkin.brave</groupId>
                <artifactId>brave</artifactId>
                <version>${zipkin.brave.version}</version>
            </dependency>

            <!-- 项目版本控制 ################################################-->
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>order-client</artifactId>
                <version>${oneorder.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.travelsky</groupId>
                        <artifactId>ebuildClient-S1</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-restresult</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-tools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>fare-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>fare-client</artifactId>
                <version>${oneorder.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.juneyaoair</groupId>
                        <artifactId>flightbasic-dto</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-restresult</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>product-client</artifactId>
                <version>${oneorder.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>bundle-client</artifactId>
                <version>${oneorder.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-mybatis</artifactId>
                <version>${oneorder.mybatis}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-restresult</artifactId>
                <version>${oneorder.mybatis}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-restresult</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.juneyaoair.oneorder</groupId>
                        <artifactId>component-tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-tools</artifactId>
                <version>${oneorder.mybatis}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-aspect</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>mobile-config-apollo</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>mobile-config-redis</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-exception</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-util</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-logback</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-swagger</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>mobile-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-constant</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-dto</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-security</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-core</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-i18n</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>component-base</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>flightbasic-dto</artifactId>
                <version>${flightBasicConsumer.client.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.juneyaoair</groupId>
                        <artifactId>cuss-dto-booking</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>flightbasic-consumer-client</artifactId>
                <version>${flightBasicConsumer.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.juneyaoair</groupId>
                        <artifactId>flightbasic-dto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>flightbasic-provider-client</artifactId>
                <version>${flightBasicProvider.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.juneyaoair</groupId>
                        <artifactId>flightbasic-dto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>flying-saucer-pdf</artifactId>
                <version>${flying-saucer-pdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.oneorder</groupId>
                <artifactId>service-support</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.dsop</groupId>
                <artifactId>sdk-java</artifactId>
                <version>${dsop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>smsModel</artifactId>
                <version>${smsModel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.horder</groupId>
                <artifactId>tinyurl-client</artifactId>
                <version>${tinyurl.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>${json.lib.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons.net.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.horder</groupId>
                <artifactId>horder-api-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>horder-config-apollo</artifactId>
                        <groupId>com.juneyaoair.horder</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>horder-config-kafka</artifactId>
                        <groupId>com.juneyaoair.horder</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>horder-config-mybatis</artifactId>
                        <groupId>com.juneyaoair.horder</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>horder-config-redis</artifactId>
                        <groupId>com.juneyaoair.horder</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>horder-component-log</artifactId>
                        <groupId>com.juneyaoair.horder</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${saToken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${saToken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${saToken.version}</version>
            </dependency>
            <!-- Sa-Token插件：权限缓存与业务缓存分离 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-alone-redis</artifactId>
                <version>${saToken.version}</version>
            </dependency>
            <!-- Http请求工具（在模式三的单点注销功能下用到，如不需要可以注释掉） -->
            <dependency>
                <groupId>com.dtflys.forest</groupId>
                <artifactId>forest-spring-boot-starter</artifactId>
                <version>${forest.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-email</artifactId>
                <version>${commons-email.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>dbtest</id>
            <properties>
                <profiles.active>dbtest</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 预发布环境 -->
            <id>uat</id>
            <properties>
                <profiles.active>uat</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>


    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!--打包时先排除profiles文件夹-->
                <excludes>
                    <exclude>profiles/**</exclude>
                </excludes>
                <!--不加这个就会报错，对于多配置文件的这种，必须设成true-->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/profiles/${profiles.active}</directory>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.5</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <verbose>true</verbose>
                    <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <!--<offline>true</offline>-->
                    <useNativeGit>true</useNativeGit>
                    <gitDescribe>
                        <skip>false</skip>
                    </gitDescribe>
                    <includeOnlyProperties>
                        <include>git.branch</include>
                        <include>git.build</include>
                        <include>git.commit.id</include>
                        <include>git.commit.time</include>
                        <include>git.commit.user</include>
                        <include>git.remote.origin.url</include>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <!--maven 灵活版本，多节点打包-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <skip>true</skip>
                    <encoding>UTF-8</encoding>
                    <!--<compilerArgs>
                        <arg>-bootclasspath</arg>
                        <arg>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</arg>
                    </compilerArgs>-->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
<!--            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M3</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttc</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                    <outputDirectory/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.7.0.1746</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
            <!-- true表示开启该远程仓库中release版本的下载，下同 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- true表示开启该远程仓库中snapshots版本的下载，下同 -->
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>


    <!-- 配置私服仓库 -->
    <distributionManagement>
        <repository>
            <!-- 注意：这里的id一定要和setting.xml文件中server下的id保持一致，下同 -->
            <id>releases</id>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>