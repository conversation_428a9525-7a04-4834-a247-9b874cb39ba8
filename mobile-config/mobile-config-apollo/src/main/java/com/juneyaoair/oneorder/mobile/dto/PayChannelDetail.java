package com.juneyaoair.oneorder.mobile.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PayChannelDetail {
    /**
     * 支付方式
     */
    private String method;
    /**
     * 支付网关号
     */
    private String gateway;
    /**
     * 支付网关类型
     */
    private String gatewayType;
    /**
     * 适用币种
     */
    private String currency;
    /**
     * 支付密钥
     */
    private String key;

    /**
     * epay支付密钥
     */
    private String epayKey;
    /**
     * 支付渠道
     */
    private String payChannel;
    /**
     * 同步异步状态位 true-表示异步 false-同步
     */
    private boolean async;
    /**
     * 适用的订单类型
     */
    private List<String> suitOrderType;
    /**
     * 卡密信息
     */
    private String cardInfo;
    /**
     * 活动优惠参数,目前只支持支付宝
     */
    private String promoParam;
    /**
     * 返回的模式，正常情况无需处理 qrcode-返回二维码流
     */
    private String mode;

    /**
     * <AUTHOR>
     * @Description 是否需要传送银行卡信息
     * @Date 8:56 2024/4/23
     **/
    private boolean needCardInfo;
}
