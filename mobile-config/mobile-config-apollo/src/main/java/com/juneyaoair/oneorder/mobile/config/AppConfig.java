package com.juneyaoair.oneorder.mobile.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.dto.EmailContent;
import com.juneyaoair.oneorder.mobile.dto.PayChannelDetail;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 应用通用配置 此配置对应application空间
 * @date 2023/6/19 9:42
 */
@Slf4j
@Data
@Configuration
public class AppConfig {
    /**
     * 渠道信息
     */
    @Value("${channel.map:}")
    private String channelSet;
    /**
     * 支付渠道配置
     */
    @Value("${payChannel.map:}")
    private String payChannelSet;

    @ApolloJsonValue("${checkLicense.map:{}}")
    private Map<String, Integer> checkLicenseMap;

    /**
     * @description 新的邮件模板配置
     * <AUTHOR>
     * @date 2024/10/30 16:13
     **/
    @ApolloJsonValue("${emailTemplate.hash:{}}")
    private Map<String, EmailContent> emailTemplateHash;

    @Value("${hoAesKey:D03u+bkkkW2JbN1uPjDYEA==}")
    private String hoAesKey;

    /** BFF SM4加解密 key和偏移量IV */
    @Value("${bffSmKey:vvn1airE1rzc1OKm}")
    private String bffSmKey;
    @Value("${bffSmIv:4hIhjunvPiWyaoLn}")
    private String bffSmIv;

    /**
     * 舱位等级关系配置
     **/
    @ApolloJsonValue("${cabinClass.map:{\"J\":\"J,C,D,A,R,I\",\"Y\":\"Y,B,M,U,H,Q,V,W,S,T,Z,E,G,K,L,N,X\"}}")
    private Map<String, String> cabinClass;

    /**
     * 获取渠道配置
     *
     * @return
     */
    public Map<String, ChannelInfo> getChannelSetMap() {
        try {
            Type type = new TypeToken<Map<String, ChannelInfo>>() {
            }.getType();
            return HoAirGsonUtil.fromJson(channelSet, type);
        } catch (Exception e) {
            log.error("{},ChannelInfo转换异常:", channelSet, e);
            return null;
        }
    }

    /**
     * 获取支付配置
     *
     * @return
     */
    public Map<String, Map<String, List<PayChannelDetail>>> getPayChannelDetailMap() {
        try {
            Type type = new TypeToken<Map<String, Map<String, List<PayChannelDetail>>>>() {
            }.getType();
            return HoAirGsonUtil.fromJson(payChannelSet, type);
        } catch (Exception e) {
            log.error("{},payChannelSet转换异常:", payChannelSet, e);
            return null;
        }
    }

    /**
     * 根据舱位代码获取舱位等级
     *
     * @param cabinCode 舱位代码，用于查询舱位等级
     * @return 对应的舱位等级，如果找不到或输入无效则返回空字符串
     */
    public String transferCabinClass(String cabinCode) {
        // 检查舱位代码是否为空或空白，如果是，则返回空字符串
        if (StringUtils.isBlank(cabinCode)) {
            return "";
        }
        // 检查舱位等级映射是否为空，如果是，则返回空字符串
        if (ObjectUtils.isEmpty(cabinClass)) {
            return "";
        }
        try {
            // 遍历舱位等级映射，寻找与舱位代码匹配的舱位等级
            for (Map.Entry<String, String> entry : cabinClass.entrySet()) {
                // 如果当前映射值中包含舱位代码，则返回对应的舱位等级键
                if (entry.getValue().indexOf(cabinCode) > -1) {
                    return entry.getKey();
                }
            }
            // 如果遍历结束没有找到匹配项，则返回空字符串
            return "";
        } catch (Exception e) {
            // 记录异常信息，用于调试和错误跟踪
            log.error("舱位代码获取舱位等级发生异常:", e);
            // 发生异常时返回空字符串
            return "";
        }
    }

}
