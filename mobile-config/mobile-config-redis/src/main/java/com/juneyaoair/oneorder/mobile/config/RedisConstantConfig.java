package com.juneyaoair.oneorder.mobile.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 常用redis缓存配置
 * @date 2023/6/9 16:47
 */
@Configuration
public class RedisConstantConfig {

    private static final String BFF_PREFIX = "BFF:";

    /**
     * redis环境变量前缀
     */
    @Value("${redis.env:}")
    private String redisEnv;

    private String getRedisEnv() {
        if (redisEnv == null || "pro".equals(redisEnv)) {
            return "";
        } else {
            return redisEnv + ":";
        }
    }

    /**
     * 获取本项目自身缓存key
     *
     * @param key
     * @return
     */
    public  String getLocalRedisKey(String key) {
        return BFF_PREFIX + getRedisEnv() + key;
    }

    /**
     * 获取API项目的缓存
     *
     * @param key
     * @return
     */
    public String getAPIRedisKey(String key) {
        return "API:" + getRedisEnv() + key;
    }
    /**
     * B2C 低价缓存 Hash key (REDIS 0)
     */
    public final String REDIS0_ONEORDER_LOWPRICE_B2C = "OneOrder:LowPrice:B2C";

    /**
     * 城市基础信息 hash
     */
    public final String REDIS_CITY_INFO_HASH = "cityInfoHash";
    /**
     * 错误城市信息
     */
    public final String REDIS_CITY_INFO_ERROR = "errorCityInfo:";
    /**
     * 机场基础信息 hash
     */
    public static final String REDIS_AIRPORT_INFO_HASH = "airportInfoHash";

    /**
     * 错误城市信息
     */
    public final String REDIS_AIRPORT_INFO_ERROR = "errorAirportInfo:";
    /**
     * 会员中心会员航段
     */
    public final String MEMBER_CENTER_MEMBER_SEGMENT = "memberCenter:memberSegment:";

    /**
     * 会员中心会员信息缓存 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_INFO = "memberCenter:memberInfo:";

    /**
     * 会员中心会员积分 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_SCORE = "memberCenter:memberScore:";

    /**
     * 会员中心会员星级 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_STAR = "memberCenter:memberStar:";

    /**
     * 会员标签查询 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_TAG = "memberCenter:memberTag:";

    /**
     * 会员标签规则查询 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_RULE = "memberCenter:memberRule:";

    /**
     * 积分账户查询 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_MILEAGE = "memberCenter:memberScore:";

    /**
     * 积分证件查询 2021-01-21
     */
    public final String MEMBER_CENTER_MEMBER_CERT = "memberCenter:memberCert:";
    /** 会员实名状态 */
    public final String MEMBER_REAL_STATE = "member:realState:";

    /**
     * 会员账号信息
     */
    public final String MEMBER_ACCOUNT_INFO = "member:accountInfo:";

    /**
     * <AUTHOR>
     * @Description 支付宝认证缓存key
     * @Date 8:59 2024/7/18
     **/
    public final String ALIPAY_AUTH_KEY = "ALIPAYAUTH:";

    /**
     * <AUTHOR>
     * @Description 同盾校验缓存key
     * @Date 20:44 2024/8/6
     **/
    public final String TONGDUN_AUTH_KEY = "TONGDUNAUTH:";

    /**
     * <AUTHOR>
     * @Description 首次设置消费密码是否已通过安全性检查
     * @Date 12:53 2024/8/7
     **/
    public final String SECURITY_CHECK_KEY = "securityCheck:";

    /**
     * <AUTHOR>
     * @Description 修改消费密码是否已通过安全检查
     * @Date 12:53 2024/8/7
     **/
    public final String RESET_SECURITY_CHECK_KEY = "resetSecurityCheck:";

    /**
     * <AUTHOR>
     * @Description 基于会员发送短信的次数缓存
     * @Date 13:49 2024/7/24
     **/
    public final String SEND_VERIFY_CODE_BY_CARDNO = "sendVerifyCodeFfpCount:";

    public final String SMS_REDIS = "SMS:";

    /**
     * 国家缓存
     */
    public final String COMMON_COUNTRIES = "common:countrys";
    /**
     * 消息平台TOKEN缓存
     */
    public static final String MESSAGE_PLATFORM_TOKEN = "B2CTASK:messagePlatform:token:";
    /**
     * 共飞城市
     */
    public static final String COMMON_FLYING_CITY = "common:commonCity";

    /**
     * 查看会员权益 会员权益集合对象
     */
    public static final String NEW_MEMBER_RIGHTS_KEY = "new_member_rights";
    /**
     * 淘旅行热门度假产品
     */
    public static final String TAOLXPACKAGE = "B2C:TaolxPackage:QueryHot";
    /** 许可校验 */
    public static final String CHECK_LICENSE = "checkLicense:";
    /** 选座相关短信 */
    public static final String SMS_SEAT = "SMS:smsSeat:";
    /** 绑定三次登录信息 */
    public static final String SMS_BIND_THIRD_PARTY_LOGIN = "SMS:bindThirdPartyLogin:";
    /** 验证码 */
    public static final String SEND_VERIFY_CODE = "VERIFY_CODE:";

    /** 选座座位图使用客票信息缓存 */
    public static final String SELECT_SEAT_INFO_CACHE = BFF_PREFIX + "SEAT:SELECT_SEAT_INFO_CACHE:";
    /** 订单积分后置数据缓存 */
    public static final String ORDER_POINT_POST = BFF_PREFIX + "ORDER:POINT_POST:";

    /**
     * 短信验证码缓存目录
     */
    public static final String SMS_CAPTCHA_DIR = "SMS:Captcha:";
    /**
     * 邮箱验证码缓存目录
     */
    public static final String EMAIL_CAPTCHA_DIR = "EMAIL:Captcha:";


    public static final String TICKET_VERY_DIR = "ticketVeryNew:";

    /** 三方登录 */
    public static final String THIRD_PARTY_LOGIN = "thirdPartyLogin:";

    /**
     * 创建客票关键信息
     * @param funcDir 功能模块
     * @param tktNo
     * @return
     */
    public static String createTicketVeryInfo(String funcDir,String tktNo) {
        tktNo = tktNo.replace("-", "");
        return  funcDir +"tktNo:" + tktNo;
    }

}
