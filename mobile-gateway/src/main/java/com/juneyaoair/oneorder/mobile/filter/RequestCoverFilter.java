package com.juneyaoair.oneorder.mobile.filter;

import com.alibaba.fastjson2.JSONObject;
import com.juneyaoair.oneorder.common.errorcode.BffErrorCode;
import com.juneyaoair.oneorder.common.exception.BffServiceException;
import com.juneyaoair.oneorder.mobile.constant.HeaderConstant;
import com.juneyaoair.oneorder.mobile.constant.WSEnum;
import com.juneyaoair.oneorder.mobile.context.GatewayContext;
import com.juneyaoair.oneorder.mobile.utils.HttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 请求内容存储 处理请求内容 内容放在gatewayContext中
 */

/**
 * <AUTHOR>
 *
 * <p>
 * 请求内容存储 处理请求内容 内容放在gatewayContext中 此filter需要先执行
 * </p>
 */
@Slf4j
public class RequestCoverFilter implements WebFilter {
    @Autowired
    private ServerCodecConfigurer serverCodecConfigurer;
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        log.info("RequestCoverFilter执行");
        try {
            /**
             * save request path and serviceId into gateway context
             */
            ServerHttpRequest request = exchange.getRequest();
            log.debug("HttpMethod:{},Url:{}", request.getMethod(), request.getURI().getRawPath());
            GatewayContext gatewayContext = new GatewayContext(request);
            /// 注意，因为webflux的响应式编程 不能再采取原先的编码方式 即应该先将gatewayContext放入exchange中，否则其他地方可能取不到
            /**
             * save gateway context into exchange
             */
            exchange.getAttributes().put(GatewayContext.CACHE_GATEWAY_CONTEXT, gatewayContext);
            // 处理参数
            MediaType contentType = gatewayContext.getHeaders().getContentType();
            long contentLength = gatewayContext.getHeaders().getContentLength();
            if (contentLength > 0) {
                //处理json请求
                if (MediaType.APPLICATION_JSON.equals(contentType) || MediaType.APPLICATION_JSON_UTF8.equals(contentType)) {
                    return HttpRequestUtils.readBody(exchange, chain, gatewayContext,serverCodecConfigurer);
                }
                //处理x-www-form-urlencoded请求
                if (MediaType.APPLICATION_FORM_URLENCODED.equals(contentType)) {
                    return HttpRequestUtils.readFormData(exchange, chain, gatewayContext);
                }
                //处理multipart/form-data请求
             /*   if(MediaType.MULTIPART_FORM_DATA.equals(contentType)){
                    return HttpRequestUtils.readFormData(exchange, chain, gatewayContext);
                }*/
            }
            log.debug("[GatewayContext]ContentType:{},Gateway context is set with {}", contentType, gatewayContext);
            // 重新设置请求头中channelCode属性
            ServerHttpRequest changeRequest = exchange.getRequest().mutate()
                    .header(HeaderConstant.CHANNEL_CODE, gatewayContext.getChannelNo())
                    .build();
            return chain.filter(exchange.mutate().request(changeRequest).build());
        } catch (BffServiceException ge) {
            return responseEnd(ge.getErrorCode(), ge.getErrorMsg(), exchange);
        }
    }

    /**
     * 统一的返回的结果
     *
     * @param errorCode
     * @param errorMsg
     * @param serverWebExchange
     * @return
     */
    private synchronized Mono<Void> responseEnd(BffErrorCode errorCode, String errorMsg, ServerWebExchange serverWebExchange) {
        JSONObject json = new JSONObject();
        errorMsg = StringUtils.isBlank(errorMsg) ? errorCode.getMessage() : errorMsg;
        //原有MOBILE移动端状态返回
        json.put("resultCode", WSEnum.GATEWAY_FAIL.getResultCode());
        json.put("resultInfo", errorMsg);
        //目前官网所使用的状态信息返回
        json.put("code", errorCode.name());
        json.put("status", errorCode.getStatus());
        json.put("message", errorMsg);

        ServerHttpResponse response = serverWebExchange.getResponse();
        byte[] bits = json.toJSONString().getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bits);
        response.setStatusCode(HttpStatus.OK);
        //指定编码，否则在浏览器中会中文乱码
        response.getHeaders().add("Content-Type", "application/json; charset=utf-8");
        return response.writeWith(Mono.just(buffer));
    }

}
