package com.juneyaoair.oneorder.mobile.enums;

/**
 * <AUTHOR>
 * @Description 渠道清单
 * @created 2024/3/28 9:03
 */
public enum ChannelEnum {

    /** 渠道名称
     *  token有效时长 单位：秒 （在老逻辑基于redis手动缓存token使用，新版本基于saToken实现有效期配置在apollo中channel.map）
     *  redis缓存时长 单位：秒 （在老逻辑基于redis手动缓存token使用，新版本基于saToken实现有效期配置在apollo中channel.map）
     */
    MOBILE("MOBILE渠道", "token", 1296000L, 86400L),
    G_MOBILE("国际手机APP", "token", null, null),
    B2C("B2C", "cookie", 3 * 60L, 86400L),
    G_B2C("G_B2C","saToken",3 * 60L, 86400L),
    WXAPP("微信小程序", "token", 3 * 60L, 86400L),
    CHECKIN("微信值机小程序渠道", "token", 3 * 60L, 86400L),
    MWEB("MWEB", "token", 3 * 60L, 86400L),
    WEIXIN("微信公众号", "token", 3 * 60L, 86400L),
    MP_ALIPAY("支付宝小程序", "token", 3 * 60L, 86400L),
    ;

    private final String channelName;
    private final String checkType;
    private final Long tokenExpireTime;
    private final Long redisExpireTime;

    ChannelEnum(String channelName, String checkType, Long tokenExpireTime, Long redisExpireTime) {
        this.channelName = channelName;
        this.checkType = checkType;
        this.tokenExpireTime = tokenExpireTime;
        this.redisExpireTime = redisExpireTime;
    }

    public String getChannelName() {
        return channelName;
    }

    public String getCheckType() {
        return checkType;
    }

    public Long getTokenExpireTime() {
        return tokenExpireTime;
    }

    public Long getRedisExpireTime() {
        return redisExpireTime;
    }
}
