package com.juneyaoair.oneorder.mobile.filter;

import cn.dev33.satoken.error.SaErrorCode;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.errorcode.BffErrorCode;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.login.PtCheckLoginResponse;
import com.juneyaoair.oneorder.mobile.config.AppConfig;
import com.juneyaoair.oneorder.mobile.config.GatewayConfig;
import com.juneyaoair.oneorder.mobile.constant.HeaderConstant;
import com.juneyaoair.oneorder.mobile.constant.WSEnum;
import com.juneyaoair.oneorder.mobile.context.GatewayContext;
import com.juneyaoair.oneorder.mobile.context.UserInToken;
import com.juneyaoair.oneorder.mobile.domain.AuthUpdateRedisVo;
import com.juneyaoair.oneorder.mobile.domain.AuthorityAggr;
import com.juneyaoair.oneorder.mobile.domain.RedisTokenDualVo;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.enums.ChannelEnum;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import com.juneyaoair.oneorder.tools.utils.VersionNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.*;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

import static cn.dev33.satoken.exception.NotLoginException.INVALID_TOKEN;
import static cn.dev33.satoken.exception.NotLoginException.INVALID_TOKEN_MESSAGE;


/**
 * 统一登录鉴权处理过滤
 */
@Slf4j
public class AuthorityFilter implements WebFilter {
    /**
     * 登录检查
     */
    private static final String CHECK_LOGIN = "/Member/CheckLogin";
    @Autowired
    private ServerCodecConfigurer serverCodecConfigurer;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private GatewayConfig gatewayConfig;
    @Autowired
    private AppConfig appConfig;

    /**
     * 最低支持SSO-TOKEN版本
     */
    @Value("${minSsoTokenVersion:}")
    private String minSsoTokenVersion;

    @Value("${crm.login.url:}")
    private String crmLoginUrl;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        //log.info("AuthorityFilter执行开始");
        GatewayContext gatewayContext = exchange.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
        if (gatewayContext == null) {
            return responseEnd(WSEnum.ERROR_REQUEST_PARAMS, exchange);
        }
        String path = Optional.of(gatewayContext).map(GatewayContext::getPath).orElse("");
        log.info("ClientIp:{},当前获取到的path:{}", gatewayContext.getIpAddress(),path);
        try {
            AntPathMatcher antPathMatcher = new AntPathMatcher();
            // 接口是否下线
            if (CollectionUtils.isNotEmpty(gatewayConfig.getOfflinePath())) {
                // 下线接口直接放行
                if (gatewayConfig.getOfflinePath().stream().anyMatch(url -> antPathMatcher.match(url, path))) {
                    return responseEnd(WSEnum.OFFLINE_PATH, exchange);
                }
            }
            //默认不拦截get请求
            final HttpMethod method = exchange.getRequest().getMethod();
            if (HttpMethod.GET.equals(method)) {
                return chain.filter(exchange);
            }
            //不做任何请求放行的Url
            if (CollectionUtils.isNotEmpty(gatewayConfig.getRedirectPath())) {
                if (gatewayConfig.getRedirectPath().stream().anyMatch(url -> antPathMatcher.match(url, path))) {
                    return chain.filter(exchange);
                }
            }
            ChannelEnum channelEnum = gatewayContext.getChannelEnum();
            if (null == channelEnum) {
                return responseEnd(WSEnum.CHANNEL_INFO_FAIL, exchange);
            }

            // 是否使用SSO方法生产及解析token
            boolean ssoTokenFlag = getSsoTokenFlag(channelEnum, gatewayContext.getClientVersion(), minSsoTokenVersion);
            // 渠道登录校验使用token方式 存在token解析操作人 先不校验有效期
            AuthorityAggr authorityAggr = new AuthorityAggr(gatewayContext);
            if ("token".equals(channelEnum.getCheckType())) {
                if (ssoTokenFlag) {
                    authorityAggr.parsesSsoToken();
                } else {
                    authorityAggr.parseToken();
                }
            } else if ("saToken".equals(channelEnum.getCheckType())) {
                authorityAggr.parseSaToken();
            }
            //登录鉴权
            // COOKIE 方式校验
            if ("cookie".equals(channelEnum.getCheckType())) {
                return b2c(exchange, chain, gatewayContext, path);
            }
            // token 方式校验
            else if ("token".equals(channelEnum.getCheckType())) {
                if (ssoTokenFlag) {
                    return ssoCheckToken(exchange, chain, authorityAggr, path);
                } else {
                    return base(exchange, chain, authorityAggr);
                }
            }
            // saToken 方式校验
            else if ("saToken".equals(channelEnum.getCheckType())) {
                return saCheckToken(exchange, chain, authorityAggr, path);
            }
            // 其他情况抛出异常
            else {
                return responseEnd(WSEnum.CHANNEL_INFO_FAIL, exchange);
            }
        } catch (Exception e) {
            log.error("path:{},当前token:{}, 渠道：{} 版本：{} 语言：{} Token校验异常",
                    path, gatewayContext.getAuthorization(), gatewayContext.getChannelNo(),
                    gatewayContext.getClientVersion(), gatewayContext.getLanguage(), e);
            return responseInvalidToken(exchange, gatewayContext);
        }
    }

    /**
     * 是否使用SSO方法生产及解析token
     * @param channelEnum
     * @param clientVersion
     * @return
     */
    private static boolean getSsoTokenFlag(ChannelEnum channelEnum, String clientVersion, String minSsoTokenVersion) {
        // 非token方式 返回不需要
        if (!"token".equals(channelEnum.getCheckType())) {
            return false;
        }
        // 非MOBILE 使用SSO-TOKEN
        if (!ChannelEnum.MOBILE.equals(channelEnum)) {
            return true;
        }
        // 不存在默认版本 不使用sso-token
        if (StringUtils.isBlank(minSsoTokenVersion)) {
            return false;
        }
        // 版本号>=最低使用SSO-TOKEN版本 使用SSO-TOKEN
        return VersionNoUtil.compareVersions(clientVersion, minSsoTokenVersion) >= 0;
    }

    /**
     * 检查是否是白名单，此处登录态已失效
     *
     * @param exchange
     * @param chain
     * @param authorityAggr
     * @return
     */
    private Mono<Void> pathWhite(ServerWebExchange exchange, WebFilterChain chain, AuthorityAggr authorityAggr) {
        GatewayContext gatewayContext = exchange.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
        if (gatewayContext == null) {
            return responseEnd(WSEnum.ERROR_REQUEST_PARAMS, exchange);
        }
        // 判断是否需要进行登录有效期检验
        if (CollectionUtils.isNotEmpty(gatewayConfig.getPathWhiteList())) {
            String path = Optional.of(gatewayContext).map(GatewayContext::getPath).orElse("");
            AntPathMatcher antPathMatcher = new AntPathMatcher();
            boolean whitePathFlag = gatewayConfig.getPathWhiteList().stream().anyMatch(url -> antPathMatcher.match(url, path));
            // 白名单内容直接放行
            if (whitePathFlag) {
                return whiteBase(gatewayContext, authorityAggr, exchange, chain);
            }
        }
        // 非白名单返回报错
        return responseInvalidToken(exchange, gatewayContext);
    }

    /**
     * 基础
     *
     * @param exchange
     * @param chain
     * @param authorityAggr
     * @return
     */
    private Mono<Void> base(ServerWebExchange exchange, WebFilterChain chain, AuthorityAggr authorityAggr) {
        WSEnum reCheckToken = authorityAggr.verifyToken();
        if (WSEnum.SUCCESS.equals(reCheckToken)) {
            return chainBase(authorityAggr, exchange, chain);
        }

        if (!WSEnum.SUCCESS_NEXT_REDIS.equals(reCheckToken)) {
            GatewayContext gatewayContext = authorityAggr.getGatewayContext();
            log.error("当前token:{}, 渠道：{} 版本：{} 语言：{}", gatewayContext.getToken(), gatewayContext.getChannelNo(), gatewayContext.getClientVersion(), gatewayContext.getLanguage());
            return pathWhite(exchange, chain, authorityAggr);
        }
        // 获取REDIS中token
        RedisTokenDualVo dual = getRedisTokenDualVo(authorityAggr);
        // 校验REDIS中token
        WSEnum reCheckRedisToken = authorityAggr.verifyRedisToken(dual);
        // token有效 直接处理数据
        if (WSEnum.SUCCESS.equals(reCheckRedisToken)) {
            return chainBase(authorityAggr, exchange, chain);
        }
        // token有效且需要刷新token则刷新token
        if (WSEnum.SUCCESS_WITH_REFRESH.equals(reCheckRedisToken)) {
            AuthUpdateRedisVo vo = authorityAggr.getAuthUpdateRedisVo();
            updateExpiredToken(vo);
            return chainBase(authorityAggr, exchange, chain);
        }
        return pathWhite(exchange, chain, authorityAggr);
    }

    /**
     * B2C渠道解析
     *
     * @param exchange
     * @param chain
     * @param gatewayContext
     * @return
     */
    private Mono<Void> b2c(ServerWebExchange exchange, WebFilterChain chain, GatewayContext gatewayContext, String path) {
        //因为前端获取不到对应的cookie，此处自行从cookie获取
        MultiValueMap<String, HttpCookie> cookieMap = exchange.getRequest().getCookies();
        //log.info("获取到的cookie值:{}", HoAirGsonUtil.objectToJson(cookieMap));
        String authorization = StringUtils.isNotBlank(gatewayContext.getAuthorization()) ?
                gatewayContext.getAuthorization() : Optional.of(cookieMap).map(i -> i.getFirst(".uscka")).map(HttpCookie::getValue).orElse(null);
        if (StringUtils.isBlank(authorization)) {
            return pathWhite(exchange, chain, null);
        }

        Map<String, ChannelInfo> channelInfoMap = appConfig.getChannelSetMap();
        if (ObjectUtils.isEmpty(channelInfoMap)) {
            return responseEnd(WSEnum.CHANNEL_INFO_FAIL, exchange);
        }

        ChannelInfo b2cChannelInfo = channelInfoMap.get(gatewayContext.getChannelNo());
        if (ObjectUtils.isEmpty(b2cChannelInfo)) {
            log.error("{}渠道配置缺失", gatewayContext.getChannelNo());
            return responseEnd(WSEnum.CHANNEL_INFO_FAIL, exchange);
        }
        String cacheBody = gatewayContext.getCacheBody();
        JSONObject jsonObject;
        if (StringUtils.isNotBlank(cacheBody)) {
            jsonObject = JSONObject.parseObject(cacheBody);
        } else {
            jsonObject = new JSONObject();
        }
        //将请求头渠道设置到请求体中
        jsonObject.put(GatewayContext.CHANNEL_NO, gatewayContext.getChannelNo());
        //将请求IP设置到请求体中
        jsonObject.put(GatewayContext.ORIGIN_IP, gatewayContext.getIpAddress());
        //将请求IP设置到请求体中
        jsonObject.put(HeaderConstant.LANGUAGE, gatewayContext.getLanguage());
        // 将客户端版本号设置到请求体中
        jsonObject.put(HeaderConstant.CLIENT_VERSION, gatewayContext.getClientVersion());
        // 将币种设置到请求体中
        jsonObject.put(HeaderConstant.CURRENCY, gatewayContext.getCurrency());


        Header header = Header.builder()
                .Token(authorization)
                .ClientIP(gatewayContext.getIpAddress())
                .ClientVersion(gatewayContext.getClientVersion())
                .Timestamp(System.currentTimeMillis())
                .build();
        PtApiCRMRequest ptApiCRMRequest = PtApiCRMRequest.builder()
                .Channel(b2cChannelInfo.getChannelCode())
                .ChannelPwd(b2cChannelInfo.getChannelPwd())
                .Header(header).build();
        String url = crmLoginUrl + CHECK_LOGIN;
        HttpResult result = HttpUtil.doPostClient(ptApiCRMRequest, url);
        Type type = new TypeToken<PtCRMResponse<PtCheckLoginResponse>>() {
        }.getType();
        PtCRMResponse<PtCheckLoginResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result.getResponse(), type);
        if (0 == ptCRMResponse.getCode()) {
            if (sendFileUploadRequest(path)) {
                return chain.filter(exchange);
            }
            PtCheckLoginResponse ptCheckLoginResponse = ptCRMResponse.getData();
            jsonObject.put(GatewayContext.FFP_ID, ptCheckLoginResponse.getMemberInfo().getMemberId().toString());
            jsonObject.put(GatewayContext.FFP_NO, ptCheckLoginResponse.getMemberInfo().getMemberCardNo());
            jsonObject.put(GatewayContext.LOGIN_FLAG, true);
            return operationExchange(exchange, chain, jsonObject, null,true);
        } else {
            return pathWhite(exchange, chain, null);
        }
    }

    /**
     * 集成saToken验证方式
     *
     * @param exchange
     * @param chain
     * @return
     */
    private Mono<Void> ssoCheckToken(ServerWebExchange exchange, WebFilterChain chain, AuthorityAggr authorityAggr, String path) {
        try {
            String token = authorityAggr.getGatewayContext().getToken();
            // 获取登录账号ID 存在ID代表token有效登录状态
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (null == loginId) {
                throw NotLoginException.newInstance(null, INVALID_TOKEN, INVALID_TOKEN_MESSAGE, token).setCode(SaErrorCode.CODE_11012);
            }
            return chainBase(authorityAggr, exchange, chain);
        } catch (NotLoginException notLoginException) {
            return pathWhite(exchange, chain, authorityAggr);
        }
    }

    /**
     * 集成saToken验证方式
     *
     * @param exchange
     * @param chain
     * @return
     */
    private Mono<Void> saCheckToken(ServerWebExchange exchange, WebFilterChain chain, AuthorityAggr authorityAggr, String path) {
        try {
            StpUtil.checkLogin();
            return chainBase(authorityAggr, exchange, chain);
        } catch (NotLoginException notLoginException) {
            return pathWhite(exchange, chain, authorityAggr);
        }
    }

    /**
     * 发送文件上传请求
     * 此方法用于检查请求的路径是否匹配配置中的白名单路径，如果匹配，则放行请求
     *
     * @param path 请求的路径
     * @return 如果请求路径匹配白名单中的路径，则返回true，否则返回false
     */
    private boolean sendFileUploadRequest(String path) {
        if (CollectionUtils.isNotEmpty(gatewayConfig.getIgnoreFileList())) {
            AntPathMatcher antPathMatcher = new AntPathMatcher();
            // 白名单内容直接放行
            return gatewayConfig.getIgnoreFileList().stream().anyMatch(url1 -> antPathMatcher.match(url1, path));
        }
        return false;
    }


    /**
     * 修改json请求体，增加公共的ffpId,ffpNo
     * @param exchange
     * @param chain
     * @param jsonObject
     * @param authorityAggr
     * @param loginFlag 是否登录状态检验
     * @return
     */
    private Mono<Void> operationExchange(ServerWebExchange exchange, WebFilterChain chain, JSONObject jsonObject, AuthorityAggr authorityAggr,boolean loginFlag) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return chain.filter(exchange);
        }
        MediaType mediaType = exchange.getRequest().getHeaders().getContentType();
        //处理multipart/form-data请求
        if (MediaType.MULTIPART_FORM_DATA.isCompatibleWith(mediaType)) {
            return operationMultipartExchange(exchange, chain, jsonObject);
        } else {
            // 设置响应head 目前已知此方法是针对M站之类的渠道才会有此设置
            if (loginFlag && authorityAggr != null && authorityAggr.getUserInToken() != null) {
                exchange.getResponse().getHeaders().add(GatewayContext.FFP_ID, authorityAggr.getUserInToken().getFfpId());
                exchange.getResponse().getHeaders().add(GatewayContext.FFP_NO, authorityAggr.getUserInToken().getFfpNo());
                if (StringUtils.isNotBlank(authorityAggr.getNewToken())) {
                    exchange.getResponse().getHeaders().add("token", authorityAggr.getNewToken());
                    exchange.getResponse().getHeaders().add("Access-Control-Expose-Headers", "token");
                }
            }
            return operationJsonExchange(exchange, chain, jsonObject);
        }
    }

    /**
     * 修改json请求体，增加公共的ffpId,ffpNo
     *
     * @param exchange
     * @param chain
     * @return
     */
    private Mono<Void> operationJsonExchange(ServerWebExchange exchange, WebFilterChain chain, JSONObject jsonObject) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return chain.filter(exchange);
        }
        // mediaType
        MediaType mediaType = exchange.getRequest().getHeaders().getContentType();
        // read & modify body
        ServerRequest serverRequest = ServerRequest.create(exchange, serverCodecConfigurer.getReaders());
        //exchange.getRequest().ServerHttpRequest
        Mono<String> modifiedBody = serverRequest.bodyToMono(String.class)
                .flatMap(body -> {
                    if (MediaType.APPLICATION_JSON.isCompatibleWith(mediaType)) {
                        // 对原先的body进行修改操作
                        return Mono.just(JSONObject.toJSONString(jsonObject));
                    }
                    return Mono.empty();
                });
        BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);
        HttpHeaders headers = new HttpHeaders();
        headers.putAll(exchange.getRequest().getHeaders());
        headers.remove(HttpHeaders.CONTENT_LENGTH);
        CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);
        return bodyInserter.insert(outputMessage, new BodyInserterContext())
                .then(Mono.defer(() -> {
                    ServerHttpRequestDecorator decorator = decorate(exchange, headers, outputMessage);
                    return chain.filter(exchange.mutate().request(decorator).build());
                }));
    }

    /**
     * 装饰请求对象以支持缓存的请求体
     * 此装饰器主要用于处理请求的重新读取问题，通过缓存请求体来优化性能和资源使用
     *
     * @param exchange      服务器Web交换对象，包含请求和响应的所有信息
     * @param headers       客户端请求的HTTP头信息，用于获取内容长度等信息
     * @param outputMessage 用于缓存请求体的消息对象，允许多次读取请求体
     * @return 返回一个装饰过的ServerHttpRequestDecorator对象，带有修改过的HTTP头和缓存的请求体
     */
    private ServerHttpRequestDecorator decorate(ServerWebExchange exchange, HttpHeaders headers,
                                        CachedBodyOutputMessage outputMessage) {
        return new ServerHttpRequestDecorator(exchange.getRequest()) {
            @Override
            public HttpHeaders getHeaders() {
                long contentLength = headers.getContentLength();
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.putAll(super.getHeaders());
                if (contentLength > 0) {
                    httpHeaders.setContentLength(contentLength);
                } else {
                    httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                }
                return httpHeaders;
            }

            @Override
            public Flux<DataBuffer> getBody() {
                return outputMessage.getBody();
            }
        };
    }

    /**
     * 处理多部分请求交换
     * 该方法用于处理带有multipart数据的请求，通过修改请求头，将从请求中提取的信息作为自定义属性传递
     *
     * @param exchange   服务器Web交换对象，包含请求和响应的所有信息
     * @param chain      Web过滤链对象，用于继续执行请求过滤链
     * @param jsonObject 包含要添加到请求中的自定义数据的JSON对象
     * @return 返回一个Mono<Void>，表示异步操作完成
     */
    private Mono<Void> operationMultipartExchange(ServerWebExchange exchange, WebFilterChain chain, JSONObject jsonObject) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return chain.filter(exchange);
        }
        // 通过mutate()方法构建新的ServerHttpRequest，并添加自定义属性
        ServerHttpRequest requestWithCustomData = exchange.getRequest().mutate()
                .header(GatewayContext.FFP_NO, jsonObject.getString(GatewayContext.FFP_NO)) // 添加自定义请求头
                .build();
        // 使用新的请求继续过滤链
        exchange = exchange.mutate().request(requestWithCustomData).build();
        return chain.filter(exchange);
    }

    /**
     * 获取REDIS中token
     *
     * @param authorityAggr
     * @return
     */
    private RedisTokenDualVo getRedisTokenDualVo(AuthorityAggr authorityAggr) {
        String redisToken = (String) redisUtil.get(authorityAggr.getTokenRedisKey());
        String oldRedisToken = (String) redisUtil.get(authorityAggr.getTokenRedisOldKey());
        return new RedisTokenDualVo(redisToken, oldRedisToken);
    }

    private void updateExpiredToken(AuthUpdateRedisVo vo) {
        if (vo.isSetUpdateToken()) {
            //原OldKey换成本次token
            redisUtil.set(vo.getUpdateKey(), vo.getUpdateToken(), vo.getUpdateKeyExpireTime());
        } else {
            redisUtil.expire(vo.getUpdateKey(), vo.getUpdateKeyExpireTime());
        }

        if (vo.isSetNewToken()) {
            //新Key重新生成token
            redisUtil.set(vo.getNewKey(), vo.getNewToken(), vo.getNewKeyExpireTime());
        }
    }

    /**
     * @param serverWebExchange
     * @return Mono<Void>
     * @description 无效token返回
     * <AUTHOR>
     * @date 2025/1/10 23:08
     **/
    private Mono<Void> responseInvalidToken(ServerWebExchange serverWebExchange, GatewayContext gatewayContext) {
        LanguageEnum language = gatewayContext.getLanguage();
        try {
            if (LanguageEnum.EN_US.equals(language)) {
                return responseEnd(WSEnum.INVALID_TOKEN, WSEnum.INVALID_TOKEN_EN_US.getResultInfo(), serverWebExchange);
            } else if (LanguageEnum.ZH_HK.equals(language)) {
                return responseEnd(WSEnum.INVALID_TOKEN, WSEnum.INVALID_TOKEN_ZH_HK.getResultInfo(), serverWebExchange);
            } else if (LanguageEnum.JA_JP.equals(language)) {
                return responseEnd(WSEnum.INVALID_TOKEN, WSEnum.INVALID_TOKEN_JA_JP.getResultInfo(), serverWebExchange);
            }
            return responseEnd(WSEnum.INVALID_TOKEN, serverWebExchange);
        } catch (Exception e) {
            log.error(e.toString());
        }
        log.info("token校验不通过，token:{} 渠道：{} 版本：{} 语言：{}",
                gatewayContext.getToken(), gatewayContext.getChannelNo(),
                gatewayContext.getClientVersion(), gatewayContext.getLanguage());
        return null;
    }


    /**
     * 统一的返回的结果
     *
     * @param status
     * @param serverWebExchange
     * @return
     */
    private Mono<Void> responseEnd(WSEnum status, ServerWebExchange serverWebExchange) {
        return responseEnd(status, status.getResultInfo(), serverWebExchange);
    }

    /**
     * 构建并发送响应结束信号的方法
     * <p>
     * 该方法主要用于向客户端发送响应，表示操作结束它同步了对它的访问，以防止并发问题
     *
     * @param status            枚举类型，表示操作的状态或结果
     * @param message           操作结果的描述信息
     * @param serverWebExchange 服务器与客户端之间交换的数据和控制信息
     * @return 返回一个Mono<Void>，表示异步操作
     */
    private synchronized Mono<Void> responseEnd(WSEnum status, String message, ServerWebExchange serverWebExchange) {
        JSONObject json = new JSONObject();
        //原有MOBILE移动端状态返回
        json.put("resultCode", status.getResultCode());
        json.put("resultInfo", message);
        json.put("loginFlag", true);
        json.put("traceId", MDC.get("X-B3-TraceId"));
        //目前官网所使用的状态信息返回
        json.put("code", status.name());
        json.put("status", BffErrorCode.SUCCESS.getStatus());
        json.put("message", message);

        ServerHttpResponse response = serverWebExchange.getResponse();
        byte[] bits = json.toJSONString().getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bits);
        response.setStatusCode(HttpStatus.OK);
        //指定编码，否则在浏览器中会中文乱码
        response.getHeaders().add("Content-Type", "application/json; charset=utf-8");
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 此方法用于验证登录后使用，标记登录状态信息有效
     * 它主要负责将登录后的用户信息和请求上下文信息整合，并传递给下一个过滤器或处理器
     *
     * @param authorityAggr 权限聚合对象，包含用户信息、渠道号等
     * @param serverWebExchange 服务器Web交换对象，用于处理HTTP请求和响应
     * @param gatewayFilterChain 网关过滤器链，用于执行下一个过滤器或处理器
     * @return 返回一个Mono<Void>，表示异步操作完成
     */
    private synchronized Mono<Void> chainBase(AuthorityAggr authorityAggr, ServerWebExchange serverWebExchange, WebFilterChain gatewayFilterChain) {
        // 调用接口通用参数从head以及token中获取
        String cacheBody = authorityAggr.getCacheBody();
        JSONObject jsonObject;
        if (StringUtils.isNotBlank(cacheBody)) {
            jsonObject = JSONObject.parseObject(cacheBody);
        } else {
            jsonObject = new JSONObject();
        }
        jsonObject.put(GatewayContext.CHANNEL_NO, authorityAggr.getChannelNo());
        jsonObject.put(GatewayContext.ORIGIN_IP, authorityAggr.getOriginIp());
        jsonObject.put(HeaderConstant.CLIENT_VERSION, authorityAggr.getGatewayContext().getClientVersion());
        jsonObject.put(GatewayContext.FFP_ID, authorityAggr.getUserInToken().getFfpId());
        jsonObject.put(GatewayContext.FFP_NO, authorityAggr.getUserInToken().getFfpNo());
        jsonObject.put(GatewayContext.LOGIN_FLAG, true);
        jsonObject.put(HeaderConstant.LANGUAGE, authorityAggr.getLanguage());
        jsonObject.put(HeaderConstant.CURRENCY, authorityAggr.getGatewayContext().getCurrency());
        return operationExchange(serverWebExchange, gatewayFilterChain, jsonObject, authorityAggr,true);
    }

    /**
     * 白名单接口调整
     *
     * @param gatewayContext
     * @param authorityAggr
     * @param serverWebExchange
     * @param gatewayFilterChain
     * @return
     */
    private synchronized Mono<Void> whiteBase(GatewayContext gatewayContext, AuthorityAggr authorityAggr, ServerWebExchange serverWebExchange, WebFilterChain gatewayFilterChain) {
        // 调用接口通用参数从head以及token中获取
        String cacheBody = gatewayContext.getCacheBody();
        JSONObject jsonObject;
        if (StringUtils.isNotBlank(cacheBody)) {
            jsonObject = JSONObject.parseObject(cacheBody);
        } else {
            jsonObject = new JSONObject();
        }
        jsonObject.put(GatewayContext.CHANNEL_NO, gatewayContext.getChannelNo());
        jsonObject.put(GatewayContext.ORIGIN_IP, gatewayContext.getIpAddress());
        jsonObject.put(HeaderConstant.CLIENT_VERSION, gatewayContext.getClientVersion());
        // 免登陆状态 卡号不记录原始入参信息
        jsonObject.put(GatewayContext.FFP_ID, null);
        jsonObject.put(GatewayContext.FFP_NO, null);
        // 存在token解析记录token解析到的会员信息
        UserInToken userInToken = null == authorityAggr ? null : authorityAggr.getUserInToken();
        if (null != userInToken) {
            jsonObject.put(GatewayContext.FFP_ID, userInToken.getFfpId());
            jsonObject.put(GatewayContext.FFP_NO, userInToken.getFfpNo());
        }
        jsonObject.put(GatewayContext.LOGIN_FLAG, false);
        jsonObject.put(HeaderConstant.LANGUAGE, gatewayContext.getLanguage());
        jsonObject.put(HeaderConstant.CURRENCY, gatewayContext.getCurrency());
        return operationExchange(serverWebExchange, gatewayFilterChain, jsonObject, authorityAggr,false);
    }
}