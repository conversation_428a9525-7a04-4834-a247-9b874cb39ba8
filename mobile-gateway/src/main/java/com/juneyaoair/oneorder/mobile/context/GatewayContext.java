package com.juneyaoair.oneorder.mobile.context;

import com.juneyaoair.oneorder.mobile.constant.HeaderConstant;
import com.juneyaoair.oneorder.mobile.enums.ChannelEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mobile.utils.HttpRequestUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;


/**
 * 网关上下文
 */
@Data
@Slf4j
public class GatewayContext {

    public static final String CACHE_GATEWAY_CONTEXT = "cacheGatewayContext";

    /**
     * 会员ID
     */
    public static final String FFP_ID = "ffpId";
    /**
     * 会员NO
     */
    public static final String FFP_NO = "ffpNo";
    /** 用户userId(如：支付宝userId) */
    public static final String USER_ID = "userId";
    /** 用户openId(如：微信openId) */
    public static final String OPEN_ID = "openId";
    /**
     * 是否通过token校验 true：通过 false:未通过
     */
    public static final String LOGIN_FLAG = "loginFlag";
    /**
     * 渠道号
     */
    public static final String CHANNEL_NO = "channelNo";
    /**
     * 请求来源IP
     */
    public static final String ORIGIN_IP = "originIp";
    /**
     * 移动端登录校验版本号，小于该版本号的不会进行校验
     */
    private static final Integer MOBILE_TOKEN_VERSION = 6040000;
    /**
     * 安卓和ios在为空时，默认值为该值
     */
    public static final String TOKEN_DEFAULT_CONTENT = "www.juneyaoair.com";
    /**
     * cache headers
     */
    private HttpHeaders headers;

    /**
     * cache json body
     */
    private String cacheBody;
    /**
     * cache formdata
     */
    private MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();

    /**
     * ipAddress
     */
    private String ipAddress;

    /**
     * path
     */
    private String path;

    /**
     * token
     */
    private String token;

    /**
     * 新的登录凭据
     */
    private String authorization;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号
     */
    private String currency;

    /**
     * 渠道号
     */
    private ChannelEnum channelEnum;

    /**
     * 语言
     */
    private LanguageEnum language;

    /**
     * 客户端版本
     */
    private String clientVersion;

    public GatewayContext() {

    }

    public GatewayContext(ServerHttpRequest request) {
        path = request.getPath().pathWithinApplication().value();
        formData.addAll(request.getQueryParams());
        ipAddress = HttpRequestUtils.getIpAddress(request);
        headers = request.getHeaders();
        String str = headers.getFirst(HeaderConstant.AUTHORIZATION);
        authorization = (StringUtils.isBlank(str) || "null".equals(str)) ? "" : str;
        token = headers.getFirst(HeaderConstant.TOKEN);
        String lang = headers.getFirst(HeaderConstant.LANGUAGE);
        language = LanguageEnum.getLanguage(lang);
        String channelCode = getChannelCode();
        try {
            channelEnum = ChannelEnum.valueOf(channelCode);
        } catch (Exception e) {

        }
        clientVersion = headers.getFirst(HeaderConstant.CLIENT_VERSION);
        currency = headers.getFirst(HeaderConstant.CURRENCY);
    }

    /**
     * 获取渠道号 channelCode > channelNo
     * @return
     */
    private String getChannelCode() {
        channelNo = headers.getFirst(HeaderConstant.CHANNEL_CODE);
        if (StringUtils.isBlank(channelNo)) {
            channelNo = headers.getFirst(HeaderConstant.CHANNEL_NO);
        }
        // 渠道为MOBILE 且 语言非简体中文 返回渠道为国际APP 并 重新设置请求头中channelCode属性
        if (ChannelEnum.MOBILE.name().equals(channelNo) && !LanguageEnum.ZH_CN.equals(language)) {
            channelNo = ChannelEnum.G_MOBILE.name();
        }
        return channelNo;
    }

    /**
     * 版本判断，如果小于指定版本，则通过校验
     */
    public boolean checkVersionLess() {
        if (ChannelEnum.MOBILE.name().equals(channelNo)) {
            return getVersionValue(clientVersion) < MOBILE_TOKEN_VERSION;
        }

        return false;
    }

    /**
     * 版本判断，如果小于指定版本，则通过校验
     */
    public boolean isMobileChannel() {
        return ChannelEnum.MOBILE.name().equals(channelNo);
    }

    public boolean isB2CChannel() {
        return ChannelEnum.B2C.name().equals(channelNo);
    }

    public boolean checkWXAndCheckin() {
        return ChannelEnum.WXAPP.name().equals(channelNo) || ChannelEnum.CHECKIN.name().equals(channelNo);
    }

    public boolean isChannelSameWithJWT(String channelNoJwt) {
        return channelNo.equals(channelNoJwt);
    }

    private int getVersionValue(String versionNo) {
        int versionValue = 0;
        if (versionNo == null || versionNo.isEmpty()) {
            return versionValue;
        }
        try {
            String[] vers = versionNo.split("\\.");
            for (int i = 0; i < vers.length; i++) {
                String ver = vers[i];
                if (i == 0) {//第一位
                    versionValue = Integer.parseInt(ver) * 1000000;
                } else if (i == 1) {//中间
                    versionValue = versionValue + Integer.parseInt(ver) * 10000;
                } else if (i == 2) {//末尾
                    versionValue = versionValue + Integer.parseInt(ver);
                }
            }
        } catch (Exception e) {
            return versionValue;
        }
        return versionValue;
    }
}