package com.juneyaoair.oneorder.mobile.context;

import com.juneyaoair.oneorder.mobile.constant.JWTConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * @Description; 保存token中用户信息
 * @Version: V1.0
 * created by <PERSON> on 2021/12/27 17:58
 */
@Data
@NoArgsConstructor
public class UserInToken {

    @ApiModelProperty(value = "会员ID")
    private String ffpId;

    @ApiModelProperty(value = "会员卡号")
    private String ffpNo;

    @ApiModelProperty(value = "渠道")
    private String channelNo;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "用户openId(如：微信openId)")
    private String openId;

    @ApiModelProperty(value = "用户userId(如：支付宝userId)")
    private String userId;

    private Map<String, String> map;

    public UserInToken(String ffpIdP, String ffpNoP, String channelNoP) {
        ffpId = ffpIdP;
        ffpNo = ffpNoP;
        channelNo = channelNoP;
    }

    public String getTokenRedisKey() {
        return JWTConstant.REDIS_REGULAR_TOKEN_KEY + ":" + channelNo + ":" + ffpNo;
    }

    public String getTokenRedisOldKey() {
        return JWTConstant.REDIS_OLD_TOKEN_KEY + ":" + channelNo + ":" + ffpNo;
    }

    public String getTokenRedisKey(String channelNoP) {
        return JWTConstant.REDIS_REGULAR_TOKEN_KEY + ":" + channelNoP + ":" + ffpNo;
    }

    public String getTokenRedisOldKey(String channelNoP) {
        return JWTConstant.REDIS_OLD_TOKEN_KEY + ":" + channelNoP + ":" + ffpNo;
    }

    public String getRedisMobileTokenOldKey() {
        return JWTConstant.REDIS_MOBILE_LOGIN_TOKEN + ffpNo;
    }

}



