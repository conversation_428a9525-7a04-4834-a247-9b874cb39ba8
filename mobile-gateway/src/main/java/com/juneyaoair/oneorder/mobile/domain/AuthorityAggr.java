package com.juneyaoair.oneorder.mobile.domain;

import cn.dev33.satoken.stp.StpUtil;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mobile.constant.JWTConstant;
import com.juneyaoair.oneorder.mobile.constant.VerifyTokenEnum;
import com.juneyaoair.oneorder.mobile.constant.WSEnum;
import com.juneyaoair.oneorder.mobile.context.GatewayContext;
import com.juneyaoair.oneorder.mobile.context.MobileTokenUserInfo;
import com.juneyaoair.oneorder.mobile.context.UserInToken;
import com.juneyaoair.oneorder.mobile.context.UserInTokenWithWX;
import com.juneyaoair.oneorder.mobile.enums.ChannelEnum;
import com.juneyaoair.oneorder.mobile.utils.JwtTokenUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AuthorityAggr {
    /**
     * 当jwt中token过期，会生成新的token，同时老的token在很短一段时间也可使用
     * 保障新老token都可使用，平滑过渡业务
     * 单位 秒
     */
    private static final long REDIS_OLD_TOKEN_EXPIRE_TIME = 5 * 60L;

    private GatewayContext gatewayContext;

    private VerifyTokenEnum verifyTokenStatus;

    private UserInToken userInToken;

    private String newToken;

    public AuthorityAggr() {

    }

    public AuthorityAggr(GatewayContext context) {
        gatewayContext = context;
    }

    public UserInToken getUserInToken() {
        return userInToken;
    }

    public String getNewToken() {
        if (newToken == null) {
            return gatewayContext.getToken();
        }

        return newToken;
    }

    public String getTokenRedisKey() {
        if (gatewayContext.isMobileChannel()) {
            return userInToken.getRedisMobileTokenOldKey();
        }

        return userInToken.getTokenRedisKey();
    }

    public String getTokenRedisOldKey() {
        if (gatewayContext.isMobileChannel()) {
            return "";
        }

        return userInToken.getTokenRedisOldKey();
    }

    /**
     * 解析token数据
     */
    public void parseToken() {
        // 获取参数
        Map<String, String> tokenParamMap = JwtTokenUtil.parserToken(gatewayContext.getToken());
        userInToken = null == tokenParamMap ? null : getUserInToken(tokenParamMap);
    }

    /**
     * @description 解析saToken
     * <AUTHOR>
     * @date 2024/11/25 13:56
     * @param
     * @return void
     **/
    public void parseSaToken(){
        if (StringUtils.isNotBlank(gatewayContext.getAuthorization())) {
            log.info("parseSaToken当前获取到的token:{} 渠道：{} 版本：{} 语言：{}",
                    gatewayContext.getAuthorization(), gatewayContext.getChannelNo(),
                    gatewayContext.getClientVersion(), gatewayContext.getLanguage());
            UserInToken user = new UserInToken();
            user.setFfpId((String) StpUtil.getExtra(gatewayContext.getAuthorization(), GatewayContext.FFP_ID));
            user.setFfpNo((String) StpUtil.getExtra(gatewayContext.getAuthorization(), GatewayContext.FFP_NO));
            user.setUserId((String) StpUtil.getExtra(gatewayContext.getAuthorization(), GatewayContext.USER_ID));
            user.setOpenId((String) StpUtil.getExtra(gatewayContext.getAuthorization(), GatewayContext.OPEN_ID));
            user.setChannelNo(gatewayContext.getChannelNo());
            userInToken = user;
        }
    }

    public void parsesSsoToken() {
        if (StringUtils.isBlank(gatewayContext.getToken()) ||
            GatewayContext.TOKEN_DEFAULT_CONTENT.equals(gatewayContext.getToken())) {
            return;
        }
        log.info("parsesSsoToken当前获取到的token:{} 渠道：{} 版本：{} 语言：{}",
                gatewayContext.getToken(), gatewayContext.getChannelNo(),
                gatewayContext.getClientVersion(), gatewayContext.getLanguage());
        UserInToken user = new UserInToken();
        user.setFfpId((String) StpUtil.getExtra(gatewayContext.getToken(), GatewayContext.FFP_ID));
        user.setFfpNo((String) StpUtil.getExtra(gatewayContext.getToken(), GatewayContext.FFP_NO));
        user.setUserId((String) StpUtil.getExtra(gatewayContext.getToken(), GatewayContext.USER_ID));
        user.setOpenId((String) StpUtil.getExtra(gatewayContext.getToken(), GatewayContext.OPEN_ID));
        user.setChannelNo(gatewayContext.getChannelNo());
        userInToken = user;
    }

    /**
     * 移动端根据token进行校验
     */
    public WSEnum verifyToken() {
        if (gatewayContext.checkVersionLess()) {
            return WSEnum.ERROR_VERSION_LESS;
        }
        verifyTokenStatus = JwtTokenUtil.verifyToken(gatewayContext.getToken());
        if (VerifyTokenEnum.TOKEN_EXCEPTION.equals(verifyTokenStatus) ||
            VerifyTokenEnum.TOKEN_VALID.equals(verifyTokenStatus)) {
            return WSEnum.INVALID_TOKEN;
        }
        if (null == userInToken) {
            return WSEnum.INVALID_TOKEN;
        }
        return WSEnum.SUCCESS_NEXT_REDIS;
    }

    /**
     * 移动端根据token进行校验
     */
    public WSEnum verifyRedisToken(RedisTokenDualVo dualVo) {
        String token = gatewayContext.getToken();

        //手机渠道，RedisToken校验
        if (gatewayContext.isMobileChannel()) {
            //2.1 token对应redis为空
            if (dualVo.isInvalid()) {
                return WSEnum.INVALID_TOKEN;
            }

            if (!dualVo.isSameToken(token)) {
                return WSEnum.ACCOUNT_LOGIN_ON_OTHER_MOBILE;
            }

            //2.1.1 token相同，校验成功，刷新redis缓存
            return WSEnum.SUCCESS_WITH_REFRESH;
        }

        // 非手机渠道 且 token与redis中新旧token均不一致 则认为是无效token
        if (dualVo.isInvalid(token)) {
            return WSEnum.INVALID_TOKEN;
        }

        // 过期token只验证redis中是否存在
        if (dualVo.isSameOldToken(token)) {
            // 传入的token是老token 不存在新token 新token设置为Redis中的新token
            if (null == newToken) {
                newToken = dualVo.getToken();
            }
            return WSEnum.SUCCESS;
        }

        //过期token只验证redis中是否存在
        if (dualVo.isSameToken(token)) {
            return WSEnum.SUCCESS_WITH_REFRESH;
        }

        return WSEnum.INVALID_TOKEN;
    }

    public AuthUpdateRedisVo getAuthUpdateRedisVo() {
        //MOBILE 渠道情况处理，
        if (gatewayContext.isMobileChannel()) {
            String key = userInToken.getRedisMobileTokenOldKey();
            return new AuthUpdateRedisVo(key, ChannelEnum.MOBILE.getTokenExpireTime());
        }

        //非MOBILE 渠道情况处理，
        //VerifyTokenEnum.TOKEN_SUCCESS 成功情况处理，
        String channelNo = gatewayContext.getChannelNo();
        ChannelEnum channelEnum = gatewayContext.getChannelEnum();
        String maintainKey = userInToken.getTokenRedisKey(channelNo);
        String token = gatewayContext.getToken();
        if (VerifyTokenEnum.TOKEN_SUCCESS.equals(verifyTokenStatus)) {
            return new AuthUpdateRedisVo(maintainKey, token, channelEnum.getRedisExpireTime());
        }

        //VerifyTokenEnum.TOKEN_SOON_EXPIRE TOKEN即将过期情况处理，
        // 处理老Redis缓存
        String maintainOldKey = userInToken.getTokenRedisOldKey(channelNo);
        // 生成新缓存
        newToken = createNewToken();
        return new AuthUpdateRedisVo(maintainOldKey, token, REDIS_OLD_TOKEN_EXPIRE_TIME, maintainKey, newToken, channelEnum.getRedisExpireTime());
    }

    private String createNewToken() {
        Map<String, String> newMap = new HashMap<>(userInToken.getMap());
        // 更新token中的渠道号
        newMap.put(JWTConstant.TOKEN_CHANNELCODE, gatewayContext.getChannelNo());
        // 微信小程序或者值机小程序夸系统时，将openId置空，因为原Token中的openId在新系统中不可用
        if (gatewayContext.checkWXAndCheckin() && !gatewayContext.isChannelSameWithJWT(userInToken.getChannelNo())) {
            newMap.remove(JWTConstant.WX_OPENID);
        }
        //redis中存在，生成新的token，缓存到redis中
        return JwtTokenUtil.sign(newMap, gatewayContext.getChannelEnum().getRedisExpireTime());
    }

    private UserInToken getUserInToken(Map<String, String> tokenParamMap) {
        try {
            if (gatewayContext.isMobileChannel()) {
                String str = tokenParamMap.get(JWTConstant.MOBILE_TOKEN_USER_INFO);
                MobileTokenUserInfo mobileTokenUserInfo = HoAirGsonUtil.fromJson(str, MobileTokenUserInfo.class);
                UserInToken userInToken = new UserInToken();
                userInToken.setFfpId(mobileTokenUserInfo.getUserId().toString());
                userInToken.setFfpNo(mobileTokenUserInfo.getMemberId());
                userInToken.setDeviceId(mobileTokenUserInfo.getDeviceId());
                userInToken.setMap(tokenParamMap);
                return userInToken;
            }
            String str = tokenParamMap.get(JWTConstant.TOKEN_MEMBERINFO);
            UserInTokenWithWX user = HoAirGsonUtil.fromJson(str, UserInTokenWithWX.class);
            String memberNo = tokenParamMap.get(JWTConstant.TOKEN_MEMBERCARDNO);
            // 上一次签名在token中的渠道号，此渠道号可能与这次header中的渠道号不同的原因是跨渠道访问
            String channelCode = tokenParamMap.get(JWTConstant.TOKEN_CHANNELCODE);
            UserInToken userWX = new UserInToken(user.getId().toString(), memberNo, channelCode);
            userWX.setMap(tokenParamMap);
            return userWX;
        } catch (Exception e) {
            log.info("getUserInToken Exception:", e);
            throw e;
        }
    }

    public String getChannelNo() {
        return gatewayContext.getChannelNo();
    }

    public String getOriginIp() {
        return gatewayContext.getIpAddress();
    }

    public LanguageEnum getLanguage() {
        return gatewayContext.getLanguage();
    }

    public String getCacheBody() {
        return gatewayContext.getCacheBody();
    }

    public GatewayContext getGatewayContext() {
        return gatewayContext;
    }

}