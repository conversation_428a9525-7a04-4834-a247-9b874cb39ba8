package com.juneyaoair.oneorder.cuss.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.cuss.enums.ENUM_FLIGHT_SEAT_STATUS;
import com.juneyaoair.cuss.exception.CommonException;
import com.juneyaoair.cuss.fegin.CussBookingClient;
import com.juneyaoair.cuss.param.trip.TravellerTripLimit;
import com.juneyaoair.cuss.param.trip.TravellerTripParam;
import com.juneyaoair.cuss.response.seat.SeatOrderInfo;
import com.juneyaoair.cuss.response.trip.TravellerTicketInfo;
import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import com.juneyaoair.cuss.util.CussClientUtil;
import com.juneyaoair.cuss.util.NameUtils;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.util.TraceUtil;
import com.juneyaoair.oneorder.cuss.bean.param.QueryTravellerTrip;
import com.juneyaoair.oneorder.cuss.bean.result.TravellerTripResult;
import com.juneyaoair.oneorder.cuss.mapstruct.TravellerTripInfoMapper;
import com.juneyaoair.oneorder.cuss.service.TravellerTripService;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description 值机选座行程服务
 * @created 2024/11/28 17:24
 */
@Slf4j
@Service
public class TravellerTripServiceImpl implements TravellerTripService {

    @Autowired
    private CommonService commonService;
    @Autowired
    private IMemberService memberService;

    @Autowired
    private CussBookingClient cussBookingClient;

    @Override
    public TravellerTripResult getTravellerTripResult(RequestDataDto requestData, QueryTravellerTrip queryTravellerTrip, TravellerTripParam travellerTripParam) {
        String traceId = TraceUtil.getTraceId();
        List<TravellerTripInfo> travellerTripInfoList = null;
        String uuid = UUID.randomUUID().toString();
        BaseRequestDTO<TravellerTripParam> travellerTripParamBase = BaseRequestUtil.createRequest(requestData, travellerTripParam);
        log.info("调用CUSS行程接口开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(travellerTripParamBase));
        BaseResultDTO<com.juneyaoair.cuss.response.trip.TravellerTripResult> baseResult = cussBookingClient.getTravellerTrip(travellerTripParamBase, uuid);
        log.info("调用CUSS行程接口结束，请求ID：{} 返回结果：{}", uuid, JSON.toJSONString(baseResult));
        com.juneyaoair.cuss.response.trip.TravellerTripResult travellerTripCussResult = null;
        try {
            travellerTripCussResult = CussClientUtil.getResult(baseResult);
        } catch (CommonException ce) {
            log.error("查询旅客行程信息失败，traceId:{} 请求ID：{} 请求参数：{} 异常信息：", traceId, uuid, JSON.toJSONString(travellerTripParam), ce);
        }
        if (null == travellerTripCussResult || CollectionUtils.isEmpty(travellerTripCussResult.getTravellerTripInfoList())) {
            log.info("未获取到旅客行程信息，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(travellerTripParam));
        } else {
            travellerTripInfoList = travellerTripCussResult.getTravellerTripInfoList();
        }
        if (CollectionUtils.isEmpty(travellerTripInfoList)) {
            // 基于会员查询无行程 且 证件号为空 不报异常
            if ("member".equalsIgnoreCase(queryTravellerTrip.getType()) && StringUtils.isBlank(queryTravellerTrip.getTravellerNumber())) {
                return null;
            }
            throw new MultiLangServiceException(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP, "无有效行程信息");
        }
        // 待支付选座
        Map<String, TravellerTripInfo> unPaySeatMap = Maps.newHashMap();
        // 可选座且未选座
        Map<String, TravellerTripInfo> canSeatMap = Maps.newHashMap();
        // 已选座
        Map<String, TravellerTripInfo> seatMap = Maps.newHashMap();
        // 不可选座
        Map<String, TravellerTripInfo> unSeatMap = Maps.newHashMap();
        // 用于首页行程调整值机选座页面 基于会员查询 且 证件号不为空 认为首页调整
        boolean firstTrip = "member".equalsIgnoreCase(queryTravellerTrip.getType()) && StringUtils.isNotBlank(queryTravellerTrip.getTravellerNumber());
        travellerTripInfoList.forEach(travellerTripInfo -> {
            // 入参存在航班号 且航班号不匹配
            boolean unMatch = StringUtils.isNotBlank(queryTravellerTrip.getFlightNo()) &&
                    !(queryTravellerTrip.getFlightNo().equalsIgnoreCase(travellerTripInfo.getMarketingFlightNo()) ||
                            queryTravellerTrip.getFlightNo().equalsIgnoreCase(travellerTripInfo.getFlightNo()) ||
                            queryTravellerTrip.getFlightNo().equalsIgnoreCase(travellerTripInfo.getOperationFlightNo()));
            if (unMatch) {
                log.info("航班号不配置，入参航班：{} 实际航班：{}", queryTravellerTrip.getFlightNo(), travellerTripInfo.getFlightNo());
                return;
            }
//            // 校验航班日期是否匹配
//            unMatch = StringUtils.isNotBlank(queryTravellerTrip.getFlightDate()) &&
//                    !queryTravellerTrip.getFlightDate().equalsIgnoreCase(travellerTripInfo.getFlightDate());
//            if (unMatch) {
//                log.info("航班日期不配置，入参航班：{} 实际航班：{}", queryTravellerTrip.getFlightDate(), travellerTripInfo.getFlightDate());
//                return;
//            }
            for (TravellerTicketInfo travellerTicketInfo : travellerTripInfo.getTravellerList()) {
                // 入参存在姓名校验姓名
                if (StringUtils.isNotBlank(queryTravellerTrip.getTravellerName())) {
                    if (!NameUtils.patternName(queryTravellerTrip.getTravellerName(), travellerTicketInfo.getTravellerName())) {
                        log.info("客票：{} 姓名不匹配，入参姓名：{} 客票姓名：{}", travellerTicketInfo.getTicketNo(), queryTravellerTrip.getTravellerName(), travellerTicketInfo.getTravellerName());
                        throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "姓名不匹配");
                    }
                }
                // 基于会员查询 且 证件号不为空 校验证件号是否与票号匹配 用于行程页值机选座跳转
                if (firstTrip) {
                    boolean unTicketMatch = !queryTravellerTrip.getTravellerNumber().equalsIgnoreCase(travellerTicketInfo.getTicketNo());
                    if (unTicketMatch) {
                        log.info("客票：{} 证件比匹配，入参票号：{} 实际票号：{}", travellerTicketInfo.getTicketNo(), queryTravellerTrip.getTravellerNumber(), travellerTicketInfo.getTicketNo());
                        continue;
                    }
                }
                // 存在提示信息 放入不可选座清单
                if (StringUtils.isNotBlank(travellerTicketInfo.getTip())) {
                    TravellerTripInfoMapper.setMapValue(unSeatMap, travellerTripInfo, travellerTicketInfo);
                    continue;
                }
                // 存在座位号
                if (StringUtils.isNotBlank(travellerTicketInfo.getSeatNo())) {
                    SeatOrderInfo seatOrderInfo = travellerTicketInfo.getSeatOrderInfo();
                    // 不存在订单 放入已选座清单
                    if (null == seatOrderInfo) {
                        TravellerTripInfoMapper.setMapValue(seatMap, travellerTripInfo, travellerTicketInfo);
                    }
                    // 待支付订单
                    else if ("UN_PAY".equals(seatOrderInfo.getSeatState())) {
                        TravellerTripInfoMapper.setMapValue(unPaySeatMap, travellerTripInfo, travellerTicketInfo);
                    } else {
                        TravellerTripInfoMapper.setMapValue(seatMap, travellerTripInfo, travellerTicketInfo);
                    }
                    continue;
                }
                // 航班不支持选座 放入不可选座清单
                if (ENUM_FLIGHT_SEAT_STATUS.NONE.equals(travellerTripInfo.getFlightSeatStatus())) {
                    TravellerTripInfoMapper.setMapValue(unSeatMap, travellerTripInfo, travellerTicketInfo);
                } else {
                    TravellerTripInfoMapper.setMapValue(canSeatMap, travellerTripInfo, travellerTicketInfo);
                }
            }
        });
        // 是否为空行程 首页行程调整 且 行程数据均为空 保存
        boolean noTripFlag = unPaySeatMap.isEmpty() && canSeatMap.isEmpty() && seatMap.isEmpty() && unSeatMap.isEmpty();
        // 存在证件号 且 无行程 报错
        if (StringUtils.isNotBlank(queryTravellerTrip.getTravellerNumber()) && noTripFlag) {
            throw new MultiLangServiceException(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP, "无有效行程信息");
        }
        // 针对待支付选座、可选座、已选座、不可选座数据进行排序整合
        List<TravellerTripInfo> unPaySeatList = TravellerTripInfoMapper.sortList(unPaySeatMap.values());
        List<TravellerTripInfo> canSeatList = TravellerTripInfoMapper.sortList(canSeatMap.values());
        List<TravellerTripInfo> seatList = TravellerTripInfoMapper.sortList(seatMap.values());
        List<TravellerTripInfo> unSeatList = TravellerTripInfoMapper.sortList(unSeatMap.values());
        for (TravellerTripInfo travellerTripInfo : unSeatList) {
            // 旅客特服不可选座时 移除航班维度文案
            String tip = CollectionUtils.isEmpty(travellerTripInfo.getTravellerList()) ? null : travellerTripInfo.getTravellerList().get(0).getTip();
            if (StringUtils.isNotBlank(tip)) {
                travellerTripInfo.setTourTip(null);
            }
        }
        TravellerTripResult travellerTripResult = new TravellerTripResult();
        List<TravellerTripInfo> travellerTripInfoResultList = Lists.newArrayList();
        travellerTripInfoResultList.addAll(unPaySeatList);
        travellerTripInfoResultList.addAll(canSeatList);
        travellerTripInfoResultList.addAll(seatList);
        travellerTripResult.setTravellerTripInfoList(travellerTripInfoResultList);
        travellerTripResult.setUnTravellerTripInfoList(unSeatList);
        return travellerTripResult;
    }

    @Override
    public List<TravellerTripInfo> getTravellerTrip(RequestDataDto requestData, TravellerTripLimit travellerTripLimit) {
        // 查询会员行程信息
        TravellerTripParam travellerTripParam = new TravellerTripParam();
        travellerTripParam.setFfpCardNo(requestData.getFfpNo());
        travellerTripParam.setTravellerTripLimit(travellerTripLimit);
        String uuid = UUID.randomUUID().toString();
        BaseRequestDTO<TravellerTripParam> travellerTripParamBase = BaseRequestUtil.createRequest(requestData, travellerTripParam);
        log.info("调用CUSS行程接口开始，请求ID：{} 请求参数：{}", uuid, JSON.toJSONString(travellerTripParamBase));
        BaseResultDTO<com.juneyaoair.cuss.response.trip.TravellerTripResult> baseResult = cussBookingClient.getTravellerTrip(travellerTripParamBase, uuid);
        log.info("调用CUSS行程接口结束，请求ID：{} 返回结果：{}", uuid, JSON.toJSONString(baseResult));
        try {
            com.juneyaoair.cuss.response.trip.TravellerTripResult travellerTripResult = CussClientUtil.getResult(baseResult);
            return travellerTripResult.getTravellerTripInfoList();
        } catch (CommonException ce) {
            return null;
        }
    }

}
