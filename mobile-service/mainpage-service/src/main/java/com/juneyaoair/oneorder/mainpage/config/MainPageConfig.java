package com.juneyaoair.oneorder.mainpage.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.oneorder.common.request.LimitCount;
import com.juneyaoair.oneorder.flight.dto.FlightReminder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/15 10:10
 */
@Data
@Configuration
public class MainPageConfig {
    @Value("${new_checkin_seat_version:30}")
    private String NEW_CHECKIN_SEAT_VERSION;

    /**
     * app 安卓下载地址
     */
    @Value("${appVersionUrl:https://dl.juneyaoair.com/ec/juneyaoair_70700.apk}")
    private String appVersionUrl;

    //接口访问token密钥
    @Value("${accesssecret:juneyaoair}")
    private String accessSecret;
    // 客票验真次数限制
    @Value("${TicketVerify:TicketVerify}")
    private String TICKET_VERIFY_SOURCE;

    //内部IP正则表达式
    @Value("${IpPattern:((^(172)\\\\.(19|20)\\\\.(\\\\d|[1-9]\\\\d|1\\\\d{2}|2[0-4]\\\\d||25[0-5])\\\\.(\\\\d|[1-9]\\\\d|1\\\\d{2}|2[0-4]\\\\d||25[0-5]))||127.0.0.1)}")
    private String IP_PATTERN;
    @Value("${pdf_path:/juneyaoair/pdfFiles}")
    private String PDF_PATH;

    @ApolloJsonValue("${tripTicketIpLimit:{\"limitDay\":{\"frequency\":86400,\"accessLimit\":30},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":180,\"accessLimit\":10}}}")
    private LimitCount tripTicketIpLimit;

    @Value("${tripCertChannelCodes:MOBILE,B2C,MWEB,WEIXIN,CallCenter,UMETRIP}")
    private String tripCertChannelCodes;

    /**
     * 意见建议走哪个系统
     * P：旅客服务网，O：统一订单
     */
    @Value("${suggestionSystem:P}")
    private String suggestionSystem;
    /**
     * 通用航线提示信息提醒
     */
    @ApolloJsonValue("${flightReminderList:[]}")
    private List<FlightReminder> flightReminderList;
    //获取微信二维码的version
    @Value("${mainPage.queryWechatQRCode.version:release}")
    private String queryWechatQRCodeVersion;

    //获取微信二维码的有效时间 unit：s
    @Value("${mainPage.queryWechatQRCode.expire:1800}")
    private String queryWechatQRCodeExpire;

    @Value("${mainPage.hoLogo:https://mediaws.juneyaoair.com/b2c/images/reserve/jixianglogo.png}")
    private String hoLogo;

    @Value("${mainPage.iataLogo:https://mediaws.juneyaoair.com/b2c/images/reserve/logo.png}")
    private String iataLogo;




}
