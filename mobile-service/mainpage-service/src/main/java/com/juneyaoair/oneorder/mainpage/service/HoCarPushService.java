package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.pushflight.FlightPushDto;
import com.juneyaoair.flightbasic.request.airLine.AttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.CancelAttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.QueryAttentionFlightParam;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.clientfeign.CrmBffClient;
import com.juneyaoair.oneorder.api.crm.dto.hocar.AccountRelationQueryReqDto;
import com.juneyaoair.oneorder.api.crm.dto.hocar.RelationAccount;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.flight.dto.FlightDynamicParam;
import com.juneyaoair.oneorder.restresult.enums.SuccessCode;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/12/29 14:04
 */
@Slf4j
@Service
public class HoCarPushService extends CommonService {
    @Autowired
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private CrmBffClient crmBffClient;
    @Autowired
    private IFlightService flightService;

    /**
     * @param originRequestData 原始requestData
     * @param operationType     操作类型:0:新增，1：修改，2：取消
     * @param type              类型：0：关注，1：行程计划
     */
    @Async("mobileThreadPoolExecutor")
    public void pushFlight(RequestDataDto<AttentionFlightParam> originRequestData, int operationType, int type) {
        AttentionFlightParam attentionFlightParam = originRequestData.getData();
        FlightInfoDynamicDto flightInfoDynamicDto = searchFlightDynamicsInfo(attentionFlightParam.getFlightNo(), attentionFlightParam.getFlightDate(), attentionFlightParam.getDepAirportCode(), attentionFlightParam.getArrAirportCode(), originRequestData.getOriginIp(), originRequestData.getChannelNo());
        if (flightInfoDynamicDto == null) return;
        //查询会员卡号绑定的云度账户信息
        String hoCarAccount = queryHoCarAccount(originRequestData.getFfpId(), originRequestData.getFfpNo(), originRequestData.getOriginIp(), originRequestData.getChannelNo());
        if (StringUtils.isBlank(hoCarAccount)) return;
        //查询到吉祥汽车ID情况下，进行航班动态相关推送
        FlightPushDto flightPushDto = new FlightPushDto();
        flightPushDto.setRequestId(HoAirUuidUtil.randomUUID32());
        flightPushDto.setFlightNo(flightInfoDynamicDto.getFlight_no());
        flightPushDto.setFlightDate(flightInfoDynamicDto.getFlight_date());
        flightPushDto.setDepartureCity(flightInfoDynamicDto.getDeparture_city());
        flightPushDto.setDepartureAirport(flightInfoDynamicDto.getDepAirPortName());
        flightPushDto.setDepartureTerminal(flightInfoDynamicDto.getFlightHTerminal());
        flightPushDto.setDepartureDateTime(flightInfoDynamicDto.getStd());
        flightPushDto.setArrivalCity(flightInfoDynamicDto.getArrival_city());
        flightPushDto.setArrivalAirport(flightInfoDynamicDto.getArrAirPortName());
        flightPushDto.setArrivalTerminal(flightInfoDynamicDto.getFlightTerminal());
        flightPushDto.setArrivalDateTime(flightInfoDynamicDto.getSta());
        flightPushDto.setDuration(flightInfoDynamicDto.getFlyTimeLength());
        flightPushDto.setPlaneModel(flightInfoDynamicDto.getPlaneModel());
        flightPushDto.setPlaneModelName(flightInfoDynamicDto.getPlaneModelName());
        flightPushDto.setType(type);
        flightPushDto.setOperationType(operationType);
        flightPushDto.setUid(hoCarAccount);
        RequestData<FlightPushDto> requestData2 = new RequestData();
        requestData2.setChannelNo(originRequestData.getChannelNo());
        requestData2.setOriginIp(originRequestData.getOriginIp());
        requestData2.setData(flightPushDto);
        ResponseData responseData2 = flightBasicConsumerClient.pushFlightInfo(requestData2);
        if (!SuccessCode.SUCCESS.getCode().equals(responseData2.getCode())) {
            log.error("flightBasicConsumerClient.pushFlightInfo调用基础服务推送失败:请求:{},返回:{}", HoAirGsonUtil.objectToJson(requestData2), HoAirGsonUtil.objectToJson(responseData2));
        }
    }


    /**
     * 取消关注推送
     * 取消关注推送  originRequestData 原始requestData
     *
     * @param operationType //操作类型:0:新增，1：修改，2：取消
     * @param type          类型：0：关注，1：行程计划
     */
    @Async("mobileThreadPoolExecutor")
    public void cancelPushFlight(RequestData<CancelAttentionFlightParam> originRequestData, int operationType, int type) {
        //根据ID查询关注信息
        QueryAttentionFlightParam queryAttentionFlightParam = new QueryAttentionFlightParam();
        queryAttentionFlightParam.setId(originRequestData.getData().getId());
        RequestData requestData = new RequestData<>();
        requestData.setOriginIp(originRequestData.getOriginIp());
        requestData.setChannelNo(originRequestData.getChannelNo());
        requestData.setFfpNo(originRequestData.getFfpNo());
        requestData.setFfpId(originRequestData.getFfpId());
        requestData.setData(originRequestData.getData());
        long cur = System.currentTimeMillis();
        printReqLog("", "queryAttentionDetail", requestData);
        ResponseData<FollowAirLineResDTO> responseData = flightBasicProviderClient.queryAttentionDetail(requestData);
        printResultLog("", "queryAttentionDetail", responseData, cur);
        if (SuccessCode.SUCCESS.getCode().equals(responseData.getCode()) && responseData.getData() != null) {
            FollowAirLineResDTO followAirLineResDTO = responseData.getData();
            FlightInfoDynamicDto flightInfoDynamicDto = searchFlightDynamicsInfo(followAirLineResDTO.getFlightNo(), followAirLineResDTO.getFlightDate(), followAirLineResDTO.getDepAirportCode(), followAirLineResDTO.getArrAirportCode(), originRequestData.getOriginIp(), originRequestData.getChannelNo());
            if (flightInfoDynamicDto == null) return;
            //查询会员卡号绑定的云度账户信息
            String hoCarAccount = queryHoCarAccount(originRequestData.getFfpId(), originRequestData.getFfpNo(), originRequestData.getOriginIp(), originRequestData.getChannelNo());
            if (StringUtils.isBlank(hoCarAccount)) return;
            //查询到吉祥汽车ID情况下，进行航班动态相关推送
            FlightPushDto flightPushDto = new FlightPushDto();
            flightPushDto.setRequestId(HoAirUuidUtil.randomUUID32());
            flightPushDto.setFlightNo(flightInfoDynamicDto.getFlight_no());
            flightPushDto.setFlightDate(flightInfoDynamicDto.getFlight_date());
            flightPushDto.setDepartureCity(flightInfoDynamicDto.getDeparture_city());
            flightPushDto.setDepartureAirport(flightInfoDynamicDto.getDepAirPortName());
            flightPushDto.setDepartureTerminal(flightInfoDynamicDto.getFlightHTerminal());
            flightPushDto.setDepartureDateTime(flightInfoDynamicDto.getStd());
            flightPushDto.setArrivalCity(flightInfoDynamicDto.getArrival_city());
            flightPushDto.setArrivalAirport(flightInfoDynamicDto.getArrAirPortName());
            flightPushDto.setArrivalTerminal(flightInfoDynamicDto.getFlightTerminal());
            flightPushDto.setArrivalDateTime(flightInfoDynamicDto.getSta());
            flightPushDto.setDuration(flightInfoDynamicDto.getFlyTimeLength());
            flightPushDto.setPlaneModel(flightInfoDynamicDto.getPlaneModel());
            flightPushDto.setPlaneModelName(flightInfoDynamicDto.getPlaneModelName());
            flightPushDto.setType(type);
            flightPushDto.setOperationType(operationType);
            flightPushDto.setUid(hoCarAccount);
            RequestData<FlightPushDto> requestData2 = new RequestData();
            requestData2.setChannelNo(originRequestData.getChannelNo());
            requestData2.setOriginIp(originRequestData.getOriginIp());
            requestData2.setData(flightPushDto);
            ResponseData responseData2 = flightBasicConsumerClient.pushFlightInfo(requestData2);
            if (!SuccessCode.SUCCESS.getCode().equals(responseData2.getCode())) {
                log.error("flightBasicConsumerClient.pushFlightInfo调用基础服务推送失败:请求:{},返回:{}", HoAirGsonUtil.objectToJson(requestData2), HoAirGsonUtil.objectToJson(responseData2));
            }
        }
    }

    /**
     * @param flightNo
     * @param flightDate
     * @param depAirportCode
     * @param arrAirportCode
     * @param ip
     * @param channelNo
     * @return
     */
    private FlightInfoDynamicDto searchFlightDynamicsInfo(String flightNo, String flightDate, String depAirportCode, String arrAirportCode, String ip, String channelNo) {
        FlightDynamicParam flightDynamicParam = new FlightDynamicParam();
        flightDynamicParam.setQueryType("innerService");
        flightDynamicParam.setFlightNo(flightNo);
        flightDynamicParam.setFlightDate(flightDate);
        flightDynamicParam.setDepAirport(depAirportCode);
        flightDynamicParam.setArrAirport(arrAirportCode);
        flightDynamicParam.setIp(ip);
        flightDynamicParam.setHeadChannelCode(channelNo);
        List<FlightInfoDynamicDto> flightInfoDynamicDtoList = flightService.searchFlightDynamicsInfo(null, null, flightDynamicParam);
        if (CollectionUtils.isNotEmpty(flightInfoDynamicDtoList)) {
            return flightInfoDynamicDtoList.get(0);
        }
        return null;
    }

    /**
     * @param ffpId
     * @param ffpCardNo
     * @param ip
     * @param channelNo
     * @return
     */
    private String queryHoCarAccount(String ffpId, String ffpCardNo, String ip, String channelNo) {
        //查询会员卡号绑定的云度账户信息
        com.juneyaoair.oneorder.restresult.response.RequestData<AccountRelationQueryReqDto> requestData = new com.juneyaoair.oneorder.restresult.response.RequestData<>();
        requestData.setChannelNo(channelNo);
        requestData.setFfpId(ffpId);
        requestData.setFfpNo(ffpCardNo);
        requestData.setOriginIp(ip);
        AccountRelationQueryReqDto accountRelationQueryReqDto = AccountRelationQueryReqDto.builder()
                .accountType("HO")
                .accountValue(requestData.getFfpNo()).build();
        requestData.setData(accountRelationQueryReqDto);
        log.info("调用accountRelationQuery请求参数:{}", HoAirGsonUtil.objectToJson(requestData));
        com.juneyaoair.oneorder.restresult.response.ResponseData<RelationAccount> responseData = crmBffClient.accountRelationQuery(requestData);
        log.info("调用accountRelationQuery返回结果:{}", HoAirGsonUtil.objectToJson(responseData));
        if (SuccessCode.SUCCESS.getCode().equals(responseData.getCode()) && responseData.getData() != null) {
            return responseData.getData().getRelationAccount();
        }
        return "";
    }
}
