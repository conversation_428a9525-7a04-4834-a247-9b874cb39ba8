package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.horder.dto.electronic.ElectronQueryDTO;
import com.juneyaoair.horder.dto.electronic.post.ElectronicCheckChangePostDTO;
import com.juneyaoair.horder.dto.electronic.result.CancelResult;
import com.juneyaoair.horder.dto.electronic.result.CheckChangeResult;
import com.juneyaoair.horder.dto.electronic.result.PrintResult;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicCancelPostParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicEmailParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicInfoResult;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicPrintPostParam;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;

/**
 * <AUTHOR>
 * @Description 新版电子行程及发票服务
 * @created 2024/5/8 16:07
 */
public interface ElectronicItineraryService {

    /**
     * 查询可开具电子行程单开具清单接口
     * @param ffpCardNo
     * @param electronQuery
     * @return
     */
    PageResult<ElectronicInfoResult> queryElectronic(String ffpCardNo, ElectronQueryDTO electronQuery);

    /**
     * 开具电子行程单
     * @param ffpCardNo
     * @param channelCode
     * @param ip
     * @param electronicPrintPostParam
     * @return
     */
    PrintResult print(String ffpCardNo, String channelCode, String ip, ElectronicPrintPostParam electronicPrintPostParam);

    /**
     * 电子行程单重推
     * @param ffpCardNo
     * @param channelCode
     * @param ip
     * @param electronicEmailParam
     */
    void rePush(String ffpCardNo, String channelCode, String ip, ElectronicEmailParam electronicEmailParam);

    /**
     * 电子发票冲红接口
     * @param ffpCardNo
     * @param channelCode
     * @param ip
     * @param electronicCancelPostParam
     * @return
     */
    CancelResult cancel(String ffpCardNo, String channelCode, String ip, ElectronicCancelPostParam electronicCancelPostParam);

    /**
     * 检查行程单是否拆分开具
     * @param requestData
     * @param checkChangePost
     * @return
     */
    CheckChangeResult checkChange(RequestData requestData, ElectronicCheckChangePostDTO checkChangePost);

}
