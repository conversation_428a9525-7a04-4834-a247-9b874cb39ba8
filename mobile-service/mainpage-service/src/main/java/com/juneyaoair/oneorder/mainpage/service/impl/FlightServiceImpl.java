package com.juneyaoair.oneorder.mainpage.service.impl;

import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfo;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.oneorder.api.basic.service.I18nDictService;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.api.dsop.dto.PtFlightStatusReq;
import com.juneyaoair.oneorder.api.dsop.service.IDsopService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.enums.FlightWeatherInfoEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.flight.dto.FlightDynamicParam;
import com.juneyaoair.oneorder.mainpage.service.IFlightService;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 19:29
 */
@Slf4j
@Service
public class FlightServiceImpl extends CommonService implements IFlightService {
    @Autowired
    private IDsopService dsopService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private IBasicService basicService;
    @Resource
    private I18nDictService i18nDict;

    @Override
    public List<FlightInfoDynamicDto> searchFlightDynamicsInfo(String ffpId, String ffpCardNo,
            FlightDynamicParam flightDynamicParam) {
        PtFlightStatusReq ptFlightStatusReq = checkParam(flightDynamicParam);
        List<FlightInfoDynamicDto> flightInfoDynamicDtoList = dsopService.searchFlightDynamicsInfo(ptFlightStatusReq,
                null);
        if (CollectionUtils.isNotEmpty(flightInfoDynamicDtoList)) {
            List<FollowAirLineResDTO> followAirLineResDTOList = null;
            if (StringUtils.isNotBlank(ffpCardNo)) {
                followAirLineResDTOList = basicService.queryAttentionFlightList(flightDynamicParam, ffpCardNo);
            }
            for (FlightInfoDynamicDto flightInfoDynamicDto : flightInfoDynamicDtoList) {
                // 完善动态信息
                supplyFlightInfoDynamicDto(flightDynamicParam, flightInfoDynamicDto);
                if (CollectionUtils.isNotEmpty(followAirLineResDTOList)) {
                    Optional<FollowAirLineResDTO> optional = followAirLineResDTOList.stream()
                            .filter(followAirLineResDTO -> followAirLineResDTO.getFlightNo()
                                    .equals(flightInfoDynamicDto.getFlight_no())
                                    && followAirLineResDTO.getFlightDate()
                                            .equals(flightInfoDynamicDto.getFlight_date_local())
                                    && followAirLineResDTO.getDepAirportCode()
                                            .equals(flightInfoDynamicDto.getDeparture_airport())
                                    && followAirLineResDTO.getArrAirportCode()
                                            .equals(flightInfoDynamicDto.getArrival_airport()))
                            .findFirst();
                    if (optional.isPresent()) {
                        FollowAirLineResDTO followAirLineResDTO = optional.get();
                        flightInfoDynamicDto.setConcert(true);
                        flightInfoDynamicDto.setConcertId(followAirLineResDTO.getId());
                    }
                }
                // 关注按钮展示判断
                flightInfoDynamicDto.checkShowAttendButton();
                // 前序航班关注按钮展示判断
                flightInfoDynamicDto.checkPreShowAttendButton();
            }

            // i18n handle
            List<CityInfoDTO> allCityList = basicService.toCatchAllCityList(HoAirIpUtil.getLocalIp(), "MOBILE", "");
            LanguageEnum language = SecurityContextHolder.getLanguage();
            
            // 处理多语言翻译
            handleI18n(flightInfoDynamicDtoList, allCityList, language);

            return flightInfoDynamicDtoList.stream()
                    .sorted(Comparator.comparing(FlightInfoDynamicDto::getStd))
                    .collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void handleI18n(List<FlightInfoDynamicDto> flightInfoDynamicList, LanguageEnum language) {
        if (CollectionUtils.isEmpty(flightInfoDynamicList) || LanguageEnum.ZH_CN.equals(language)) {
            return;
        }
        // i18n handle
        List<CityInfoDTO> allCityList = basicService.toCatchAllCityList(HoAirIpUtil.getLocalIp(), "MOBILE", "");
        // 处理多语言翻译
        handleI18n(flightInfoDynamicList, allCityList, language);
    }

    /**
     * 判断是否显示关注按钮
     *
     * @param flightInfoDynamicDto
     */
    private void checkShowAttendButton(FlightInfoDynamicDto flightInfoDynamicDto) {
        Date flightDate = DateUtil.toDate(flightInfoDynamicDto.getFlight_date(), DateUtil.YYYY_MM_DD_PATTERN);
        Date curDate = DateUtil.toDate(DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN);
        // 未来的航班且未关注过显示关注按钮，否则不显示
        boolean attentionButton;
        if (flightDate.getTime() >= curDate.getTime()) {
            attentionButton = true;
        } else {
            attentionButton = false;
        }
        flightInfoDynamicDto.setShowAttentionButton(attentionButton);
    }

    /**
     * 航班动态信息数据补充完善
     *
     * @param flightDynamicParam
     * @param flightInfoDynamicDto
     */
    @Override
    public void supplyFlightInfoDynamicDto(BizDto flightDynamicParam, FlightInfoDynamicDto flightInfoDynamicDto) {
        ApiAirPortInfoDto deptAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getDeparture_airport());
        ApiAirPortInfoDto arrAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getArrival_airport());
        // 前序航班机场城市信息处理
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_departure_airport())
                && null != cacheService.getLocalAirport(flightInfoDynamicDto.getPre_departure_airport())) {
            ApiAirPortInfoDto preDeptAirPoty = cacheService
                    .getLocalAirport(flightInfoDynamicDto.getPre_departure_airport());
            flightInfoDynamicDto.setPre_depAirPortName(preDeptAirPoty.getAirPortName());
            flightInfoDynamicDto.setPre_departure_city(preDeptAirPoty.getCityName());
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_arrival_airport())
                && null != cacheService.getLocalAirport(flightInfoDynamicDto.getPre_arrival_airport())) {
            ApiAirPortInfoDto preArrAirPort = cacheService
                    .getLocalAirport(flightInfoDynamicDto.getPre_arrival_airport());
            flightInfoDynamicDto.setPre_arrAirPortName(preArrAirPort.getAirPortName());
            flightInfoDynamicDto.setPre_arrival_city(preArrAirPort.getCityName());
        }
        // 航班状态
        if ("0".equals(flightInfoDynamicDto.getAdjust_type())) {
            flightInfoDynamicDto.setFlight_status("取消");
        }
        // 备降地信息处理
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getDivert_airport())
                && null != cacheService.getLocalAirport((flightInfoDynamicDto.getDivert_airport()))) {
            ApiAirPortInfoDto divertAirPort = cacheService.getLocalAirport(flightInfoDynamicDto.getDivert_airport());
            if (null != divertAirPort) {
                flightInfoDynamicDto
                        .setDivert_airPortName(divertAirPort.getCityName() + divertAirPort.getAirPortName());
            }
        }
        // 飞行时长
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getStd())
                && StringUtils.isNotBlank(flightInfoDynamicDto.getSta()) && null != deptAirport && null != arrAirport) {
            String flyTime = DateUtil.getFlyTime(flightInfoDynamicDto.getStd(), deptAirport.getCityTimeZone(),
                    flightInfoDynamicDto.getSta(), arrAirport.getCityTimeZone());
            flightInfoDynamicDto.setFlyTimeLength(flyTime);
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getSta()) && (flightInfoDynamicDto.getSta().indexOf('-') < 0)) {
            flightInfoDynamicDto.setSta(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getSta());
        }
        String std = flightInfoDynamicDto.getStd().indexOf('-') < 0
                ? flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getStd()
                : flightInfoDynamicDto.getStd();
        // 计算延误时间
        if ("延误".equals(flightInfoDynamicDto.getFlight_status())) {
            Date staDate = DateUtil.toDate(std, DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
            if (staDate != null) {
                Long delayTime = DateUtil.millisecondDiff(new Date(), staDate);
                if (delayTime > 0) {
                    flightInfoDynamicDto.setDelayTime(delayTime);
                }
            }
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getStd()) && (flightInfoDynamicDto.getStd().indexOf('-') < 0)) {
            flightInfoDynamicDto.setStd(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getStd());
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getAta()) && (flightInfoDynamicDto.getAta().indexOf('-') < 0)) {
            flightInfoDynamicDto.setAta(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getAta());
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getAtd()) && (flightInfoDynamicDto.getAtd().indexOf('-') < 0)) {
            flightInfoDynamicDto.setAtd(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getAtd());
        }

        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_sta())
                && (flightInfoDynamicDto.getPre_sta().indexOf('-') < 0)) {
            flightInfoDynamicDto
                    .setPre_sta(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getPre_sta());
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_std())
                && (flightInfoDynamicDto.getPre_std().indexOf('-') < 0)) {
            flightInfoDynamicDto
                    .setPre_std(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getPre_std());
        }

        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_ata())
                && (flightInfoDynamicDto.getPre_ata().indexOf('-') < 0)) {
            flightInfoDynamicDto
                    .setPre_ata(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getPre_ata());
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_atd())
                && (flightInfoDynamicDto.getPre_atd().indexOf('-') < 0)) {
            flightInfoDynamicDto
                    .setPre_atd(flightInfoDynamicDto.getFlight_date() + " " + flightInfoDynamicDto.getPre_atd());
        }
        flightInfoDynamicDto.setDepAirPortName(null != deptAirport ? deptAirport.getAirPortName() : "");
        flightInfoDynamicDto.setDeparture_city_code(null != deptAirport ? deptAirport.getCityCode() : "");
        flightInfoDynamicDto.setDeparture_city(null != deptAirport ? deptAirport.getCityName() : "");
        flightInfoDynamicDto.setDeparture_city_type(null != deptAirport ? deptAirport.getIsInternational() : "");
        flightInfoDynamicDto.setArrAirPortName(null != arrAirport ? arrAirport.getAirPortName() : "");
        flightInfoDynamicDto.setArrival_city_code(null != arrAirport ? arrAirport.getCityCode() : "");
        flightInfoDynamicDto.setArrival_city(null != arrAirport ? arrAirport.getCityName() : "");
        flightInfoDynamicDto.setArrival_city_type(null != arrAirport ? arrAirport.getIsInternational() : "");
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getDeptTemp())) {
            flightInfoDynamicDto.setDeptTemp(flightInfoDynamicDto.getDeptTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getDestTemp())) {
            flightInfoDynamicDto.setDestTemp(flightInfoDynamicDto.getDestTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        FlightInfoReqDTO flightInfoReqDTO = new FlightInfoReqDTO();
        flightInfoReqDTO.setArrAirport(flightInfoDynamicDto.getArrival_airport());
        flightInfoReqDTO.setDepAirport(flightInfoDynamicDto.getDeparture_airport());
        flightInfoReqDTO.setFlightDate(flightInfoDynamicDto.getFlight_date());
        flightInfoReqDTO.setFlightNo(flightInfoDynamicDto.getFlight_no());
        // 补充机型数据
        BaseRequestDTO<FlightInfoReqDTO> baseRequestDTO = createBaseRequestDTO(flightDynamicParam, flightInfoReqDTO);
        baseRequestDTO.setRequest(flightInfoReqDTO);
        List<FlightInfoDTO> flightInfoDTOList = basicService.searchFlightInfo(baseRequestDTO);
        FlightInfoDTO flightInfo;
        if (CollectionUtils.isNotEmpty(flightInfoDTOList)) {
            flightInfo = flightInfoDTOList.get(0);
            AircraftTypeInfo aircraftTypeInfo = basicService.getAircraftTypeInfo(flightInfo.getPlanType());
            flightInfoDynamicDto.setPlaneModel(flightInfo.getPlanType());
            flightInfoDynamicDto.setPlaneModelName("机型" + flightInfo.getPlanType());
            if (aircraftTypeInfo != null) {
                flightInfoDynamicDto.setPlaneModelName(aircraftTypeInfo.getAircraftTypeName());
                flightInfoDynamicDto.setPlanModelUrl(aircraftTypeInfo.getAircraftIcon());
            }
            String flightHTerminal = StringUtils.isNotBlank(flightInfoDynamicDto.getFlightHTerminal())
                    ? flightInfoDynamicDto.getFlightHTerminal()
                    : flightInfo.getDepAirportTerminal();
            String flightTerminal = StringUtils.isNotBlank(flightInfoDynamicDto.getFlightTerminal())
                    ? flightInfoDynamicDto.getFlightTerminal()
                    : flightInfo.getArrAirportTerminal();
            flightInfoDynamicDto.setFlightHTerminal(flightHTerminal);
            flightInfoDynamicDto.setFlightTerminal(flightTerminal);
        }
        FlightWeatherInfoEnum deptWeather = FlightWeatherInfoEnum
                .formatWeatherStatus(flightInfoDynamicDto.getDeptWeather());
        FlightWeatherInfoEnum destWeather = FlightWeatherInfoEnum
                .formatWeatherStatus(flightInfoDynamicDto.getDestWeather());
        flightInfoDynamicDto.setDeptWeatherPic(deptWeather.getWeatherPictureUrl());
        flightInfoDynamicDto.setDestWeatherPic(destWeather.getWeatherPictureUrl());
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_flight_no())) {
            // 存在前序航班，并且前序航班状态为空
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoDynamicDto.getPre_flight_no());
            ptFlightStatusReq.setDepartureAirport(flightInfoDynamicDto.getPre_departure_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoDynamicDto.getPre_arrival_airport());
            ptFlightStatusReq.setFlightDateLocal(flightInfoDynamicDto.getFlight_date());
            ptFlightStatusReq.setIp(flightDynamicParam.getIp());
            List<FlightInfoDynamicDto> preFlight = dsopService.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (CollectionUtils.isNotEmpty(preFlight)) {
                FlightInfoDynamicDto newFlightInfo = preFlight.get(0);
                if ("0".equals(newFlightInfo.getAdjust_type())) {
                    flightInfoDynamicDto.setPre_flight_status("取消");
                } else {
                    flightInfoDynamicDto.setPre_flight_status(newFlightInfo.getFlight_status());
                }
                flightInfoDynamicDto.setPre_etd(newFlightInfo.getEtd());
                flightInfoDynamicDto.setPre_eta(newFlightInfo.getEta());
            }
        }
        // 经停航班查询
        int cancelAll = 0;
        // 经停信息处理
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getStopover_station())) {
            ApiAirPortInfoDto stopoverAirPort = cacheService
                    .getLocalAirport(flightInfoDynamicDto.getStopover_station());
            // 机场信息
            if (null != stopoverAirPort) {
                flightInfoReqDTO = new FlightInfoReqDTO();
                flightInfoReqDTO.setArrAirport(flightInfoDynamicDto.getStopover_station());
                flightInfoReqDTO.setDepAirport(flightInfoDynamicDto.getDeparture_airport());
                flightInfoReqDTO.setFlightDate(flightInfoDynamicDto.getFlight_date_local());
                flightInfoReqDTO.setFlightNo(flightInfoDynamicDto.getFlight_no());
                BaseRequestDTO<FlightInfoReqDTO> baseRequest = createBaseRequestDTO(flightDynamicParam,
                        flightInfoReqDTO);
                baseRequestDTO.setRequest(flightInfoReqDTO);
                List<FlightInfoDTO> flightInfoList = basicService.searchFlightInfo(baseRequest);
                if (CollectionUtils.isNotEmpty(flightInfoList)) {
                    flightInfo = flightInfoList.get(0);
                    flightInfoDynamicDto
                            .setStopovert_airPortName(stopoverAirPort.getCityName() + stopoverAirPort.getAirPortName()
                                    + (StringUtils.isBlank(flightInfo.getArrAirportTerminal()) ? ""
                                            : flightInfo.getArrAirportTerminal()));
                    String flightHTerminal = StringUtils.isNotBlank(flightInfoDynamicDto.getFlightHTerminal())
                            ? flightInfoDynamicDto.getFlightHTerminal()
                            : flightInfo.getDepAirportTerminal();
                    String flightTerminal = StringUtils.isNotBlank(flightInfoDynamicDto.getFlightTerminal())
                            ? flightInfoDynamicDto.getFlightTerminal()
                            : flightInfo.getArrAirportTerminal();
                    flightInfoDynamicDto.setFlightHTerminal(flightHTerminal);
                    flightInfoDynamicDto.setFlightTerminal(flightTerminal);
                } else {
                    flightInfoDynamicDto
                            .setStopovert_airPortName(stopoverAirPort.getCityName() + stopoverAirPort.getAirPortName());
                }
            }
            // A-B段查询有没有备降信息
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoDynamicDto.getFlight_no());
            ptFlightStatusReq.setDepartureAirport(flightInfoDynamicDto.getDeparture_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoDynamicDto.getStopover_station());
            ptFlightStatusReq.setFlightDateLocal(flightInfoDynamicDto.getFlight_date());
            ptFlightStatusReq.setIp(flightDynamicParam.getIp());
            List<FlightInfoDynamicDto> newFlightInfos = dsopService.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                FlightInfoDynamicDto newFlightInfo = newFlightInfos.get(0);
                // A-B存在备降
                if (StringUtils.isNotBlank(newFlightInfo.getDivert_airport())) {
                    flightInfoDynamicDto.setDivert_airport1(newFlightInfo.getDivert_airport());
                    flightInfoDynamicDto.setDivert_weather1(newFlightInfo.getDivert_weather());
                    flightInfoDynamicDto.setDivert_temp1(newFlightInfo.getDivert_temp());
                    flightInfoDynamicDto.setDivert_pm1(newFlightInfo.getDivert_pm());
                    flightInfoDynamicDto.setDivert_airPortName1(
                            null == cacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? ""
                                    : cacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                            cacheService.getLocalAirport(newFlightInfo.getDivert_airport())
                                                    .getAirPortName());
                    flightInfoDynamicDto.setDivert_ata1(newFlightInfo.getDivert_ata());
                    flightInfoDynamicDto.setDivert_eta1(newFlightInfo.getDivert_eta());
                    flightInfoDynamicDto.setDivert_etd1(newFlightInfo.getDivert_etd());
                    flightInfoDynamicDto.setDivert_atd1(newFlightInfo.getDivert_atd());
                }
                // 行李转盘
                flightInfoDynamicDto.setStopovert_baggageID(newFlightInfo.getBaggageID());
                flightInfoDynamicDto
                        .setStopovert_Temp(StringUtils.isBlank(newFlightInfo.getDestTemp()) ? ""
                                : newFlightInfo.getDestTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                flightInfoDynamicDto.setStopovert_weather(newFlightInfo.getDestWeather());
                flightInfoDynamicDto.setStopovert_pm(newFlightInfo.getDestpm());
                FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum
                        .formatWeatherStatus(flightInfoDynamicDto.getStopovert_weather());
                flightInfoDynamicDto.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                // 经停站的到达时间补充
                // 计划到达
                if (StringUtils.isNotBlank(newFlightInfo.getEta())) {
                    // 存在预到
                    flightInfoDynamicDto.setStopover_eta(newFlightInfo.getEta());
                } else {
                    if (StringUtils.isNotBlank(flightInfoDynamicDto.getStopover_eta())) {
                        flightInfoDynamicDto.setStopover_eta(newFlightInfo.getSta());
                    }
                }
                // 实际到达
                if (StringUtils.isBlank(flightInfoDynamicDto.getStopover_ata())) {
                    flightInfoDynamicDto.setStopover_ata(newFlightInfo.getAta());
                }
                // 如果a-b段有延误原因
                if (StringUtils.isNotBlank(newFlightInfo.getDelay_reason())
                        && "延误".equals(flightInfoDynamicDto.getFlight_status())) {
                    flightInfoDynamicDto.setDelay_reason(newFlightInfo.getDelay_reason());
                }
                if ("取消".equals(newFlightInfo.getFlight_status())) {
                    cancelAll++;
                }
            }
            // B-C段信息不全
            ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoDynamicDto.getFlight_no());
            ptFlightStatusReq.setFlightDateLocal(flightInfoDynamicDto.getFlight_date());
            ptFlightStatusReq.setDepartureAirport(flightInfoDynamicDto.getStopover_station());
            ptFlightStatusReq.setArrivalAirport(flightInfoDynamicDto.getArrival_airport());
            ptFlightStatusReq.setIp(flightDynamicParam.getIp());
            List<FlightInfoDynamicDto> bToCList = dsopService.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (CollectionUtils.isNotEmpty(bToCList)) {
                FlightInfoDynamicDto newFlightInfo = bToCList.get(0);
                // B-C存在备降
                if (StringUtils.isNotBlank(newFlightInfo.getDivert_airport())) {
                    flightInfoDynamicDto.setDivert_airport2(newFlightInfo.getDivert_airport());
                    flightInfoDynamicDto.setDivert_weather2(newFlightInfo.getDivert_weather());
                    flightInfoDynamicDto.setDivert_temp2(newFlightInfo.getDivert_temp());
                    flightInfoDynamicDto.setDivert_pm2(newFlightInfo.getDivert_pm());
                    flightInfoDynamicDto.setDivert_airPortName2(
                            null == cacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? ""
                                    : cacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                            cacheService.getLocalAirport(newFlightInfo.getDivert_airport())
                                                    .getAirPortName());
                    flightInfoDynamicDto.setDivert_ata2(newFlightInfo.getDivert_ata());
                    flightInfoDynamicDto.setDivert_eta2(newFlightInfo.getDivert_eta());
                    flightInfoDynamicDto.setDivert_etd2(newFlightInfo.getDivert_etd());
                    flightInfoDynamicDto.setDivert_atd2(newFlightInfo.getDivert_atd());
                }
                // B-C缺少信息
                flightInfoDynamicDto.setStopovert_boardGate(newFlightInfo.getBoardGate());
                flightInfoDynamicDto.setStopovert_checkinTable(newFlightInfo.getCheckinTable());
                flightInfoDynamicDto.setStopovert_Temp(StringUtils.isBlank(newFlightInfo.getDeptTemp()) ? ""
                        : newFlightInfo.getDeptTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                flightInfoDynamicDto.setStopovert_weather(newFlightInfo.getDeptWeather());
                flightInfoDynamicDto.setStopovert_pm(newFlightInfo.getDeptpm());
                FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum
                        .formatWeatherStatus(flightInfoDynamicDto.getStopovert_weather());
                flightInfoDynamicDto.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                // 经停站的起飞时间补充
                if (StringUtils.isNotBlank(newFlightInfo.getEtd())) {
                    flightInfoDynamicDto.setStopover_etd(newFlightInfo.getEtd());
                } else {
                    if (StringUtils.isBlank(flightInfoDynamicDto.getStopover_etd())) {
                        flightInfoDynamicDto.setStopover_etd(newFlightInfo.getStd());
                    }
                }
                if (StringUtils.isBlank(flightInfoDynamicDto.getStopover_atd())) {
                    flightInfoDynamicDto.setStopover_atd(newFlightInfo.getAtd());
                }
                // 如果b-c段有延误原因，则a-c段也有延误原因
                if (StringUtils.isNotBlank(newFlightInfo.getDelay_reason())) {
                    flightInfoDynamicDto.setDelay_reason(newFlightInfo.getDelay_reason());
                }
                if ("取消".equals(newFlightInfo.getFlight_status())) {
                    cancelAll++;
                }
            }
            if (cancelAll == 2) {
                flightInfoDynamicDto.setIs_interline("N");
            }
            // 有经停，时间转换
            ApiAirPortInfoDto stopAirPort = cacheService.getLocalAirport(flightInfoDynamicDto.getStopover_station());
            if (null != stopAirPort && StringUtils.isNotBlank(stopAirPort.getCityTimeZone().trim())) {
                flightInfoDynamicDto.setStopTimeZone(Integer.parseInt(stopAirPort.getCityTimeZone().trim()));
            }
        }
        if (null != deptAirport && StringUtils.isNotBlank(deptAirport.getCityTimeZone().trim())) {
            int depTimeZone = Integer.parseInt(deptAirport.getCityTimeZone().trim());
            flightInfoDynamicDto.setDepTimeZone(depTimeZone);
        }
        if (null != arrAirport && StringUtils.isNotBlank(arrAirport.getCityTimeZone().trim())) {
            int arrTimeZone = Integer.parseInt(arrAirport.getCityTimeZone().trim());
            flightInfoDynamicDto.setArrTimeZone(arrTimeZone);
        }
    }

    /**
     * 参数检验
     *
     * @param flightDynamicParam
     */
    private PtFlightStatusReq checkParam(FlightDynamicParam flightDynamicParam) {
        PtFlightStatusReq.PtFlightStatusReqBuilder ptFlightStatusReqBuilder = PtFlightStatusReq.builder();
        if ("airline".equals(flightDynamicParam.getQueryType())) {
            if (StringUtils.isAnyBlank(flightDynamicParam.getDepCityCode(), flightDynamicParam.getArrCityCode())) {
                throw new ArgumentCheckFailException("航线信息不可为空");
            }
            ptFlightStatusReqBuilder.departureCityCode(flightDynamicParam.getDepCityCode())
                    .arrivalCityCode(flightDynamicParam.getArrCityCode());
        } else if ("flightNo".equals(flightDynamicParam.getQueryType())) {
            if (StringUtils.isBlank(flightDynamicParam.getFlightNo())) {
                throw new ArgumentCheckFailException("航班号不可为空");
            }
            String flightNo = flightDynamicParam.getFlightNo().toUpperCase();
            if (!flightNo.matches(PatternCommon.HO_FLIGHT)) {
                flightDynamicParam.setFlightNo("HO" + flightNo);
            }
            ptFlightStatusReqBuilder.flightNo(flightDynamicParam.getFlightNo())
                    .departureCityCode(flightDynamicParam.getDepCityCode())
                    .arrivalCityCode(flightDynamicParam.getArrCityCode());
            // 内部服务调用
        } else if ("innerService".equals(flightDynamicParam.getQueryType())) {
            ptFlightStatusReqBuilder.flightNo(flightDynamicParam.getFlightNo())
                    .departureAirport(flightDynamicParam.getDepAirport())
                    .arrivalAirport(flightDynamicParam.getArrAirport());
        } else {
            throw new ArgumentCheckFailException("非法查询类型");
        }
        return ptFlightStatusReqBuilder
                .flightDateLocal(flightDynamicParam.getFlightDate())
                .ip(flightDynamicParam.getIp()).build();
    }

    // 修改handleI18n方法
    private void handleI18n(List<FlightInfoDynamicDto> flightInfoDynamicDtoList, List<CityInfoDTO> allCityList, LanguageEnum language) {
        if (CollectionUtils.isEmpty(flightInfoDynamicDtoList)) {
            return;
        }

        // 预先获取所有需要的字典数据
        Map<String, Map<String, Map<String, String>>> dictionaries = i18nDict.fetchMultipleDictData(
                I18nDictionaryTypeEnum.FLIGHT_STATUS_FLIGHT_STATUS_ZH,
                I18nDictionaryTypeEnum.PLAN_TYPE_NAME,
                I18nDictionaryTypeEnum.FLIGHT_STATUS_BRIDGE_ZH
        );

        flightInfoDynamicDtoList.forEach(flightInfoDynamicDto -> {
            // 保存原始航班状态到flight_status_enum
            flightInfoDynamicDto.setFlight_status_enum(flightInfoDynamicDto.getFlight_status());

            // 使用预先获取的字典数据进行翻译
            String flightStatus = i18nDict.getTranslationFromDict(dictionaries, I18nDictionaryTypeEnum.FLIGHT_STATUS_FLIGHT_STATUS_ZH, flightInfoDynamicDto.getFlight_status(), language);
            flightInfoDynamicDto.setFlight_status(flightStatus);

            String preFlightStatus = i18nDict.getTranslationFromDict(dictionaries, I18nDictionaryTypeEnum.FLIGHT_STATUS_FLIGHT_STATUS_ZH, flightInfoDynamicDto.getPre_flight_status(), language);
            flightInfoDynamicDto.setPre_flight_status(preFlightStatus);

            if (StringUtils.isBlank(flightInfoDynamicDto.getPlaneModel())) {
                flightInfoDynamicDto.setPlaneModelName(null);
            } else {
                String planeModel = "A" + flightInfoDynamicDto.getPlaneModel();
                if ("789".equals(flightInfoDynamicDto.getPlaneModel())) {
                    planeModel = "B787-9";
                }
                String planeModelName = i18nDict.getTranslationFromDict(dictionaries, I18nDictionaryTypeEnum.PLAN_TYPE_NAME, planeModel, language);
                flightInfoDynamicDto.setPlaneModelName(planeModelName);
            }

            String bridge = i18nDict.getTranslationFromDict(dictionaries, I18nDictionaryTypeEnum.FLIGHT_STATUS_BRIDGE_ZH, flightInfoDynamicDto.getBridge(), language);
            flightInfoDynamicDto.setBridge(bridge);

            String arrBridge = i18nDict.getTranslationFromDict(dictionaries, I18nDictionaryTypeEnum.FLIGHT_STATUS_BRIDGE_ZH, flightInfoDynamicDto.getArr_bridge(), language);
            flightInfoDynamicDto.setArr_bridge(arrBridge);

            // 飞行时长翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getFlyTimeLength())) {
                String flyTime = flightInfoDynamicDto.getFlyTimeLength();
                if (language != LanguageEnum.ZH_CN) {
                    // 将"3小时20分钟"转换为"3h 20m"格式
                    flyTime = flyTime.replaceAll("小时", "h ")
                                    .replaceAll("分钟", "m");
                }
                flightInfoDynamicDto.setFlyTimeLength(flyTime);
            }

            // 出发机场翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getDeparture_airport())) {
                ApiAirPortInfoDto depAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getDeparture_airport());
                if (depAirport != null) {
                    switch (language) {
                        case EN_US:
                            flightInfoDynamicDto.setDepAirPortName(depAirport.getAirPortEName());
                            break;
                        case JA_JP:
                            flightInfoDynamicDto.setDepAirPortName(depAirport.getAirportJpName());
                            break;
                        case ZH_HK:
                            flightInfoDynamicDto.setDepAirPortName(depAirport.getAirportTcName());
                            break;
                        default:
                            flightInfoDynamicDto.setDepAirPortName(depAirport.getAirPortName());
                    }
                }
            }

            // 到达机场翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getArrival_airport())) {
                ApiAirPortInfoDto arrAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getArrival_airport());
                if (arrAirport != null) {
                    switch (language) {
                        case EN_US:
                            flightInfoDynamicDto.setArrAirPortName(arrAirport.getAirPortEName());
                            break;
                        case JA_JP:
                            flightInfoDynamicDto.setArrAirPortName(arrAirport.getAirportJpName());
                            break;
                        case ZH_HK:
                            flightInfoDynamicDto.setArrAirPortName(arrAirport.getAirportTcName());
                            break;
                        default:
                            flightInfoDynamicDto.setArrAirPortName(arrAirport.getAirPortName());
                    }
                }
            }

            // 经停机场翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getStopover_station())) {
                ApiAirPortInfoDto stopoverAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getStopover_station());
                if (stopoverAirport != null) {
                    String airportName = "";
                    switch (language) {
                        case EN_US:
                            airportName = stopoverAirport.getAirPortEName();
                            break;
                        case JA_JP:
                            airportName = stopoverAirport.getAirportJpName();
                            break;
                        case ZH_HK:
                            airportName = stopoverAirport.getAirportTcName();
                            break;
                        default:
                            airportName = stopoverAirport.getAirPortName();
                    }
                    flightInfoDynamicDto.setStopovert_airPortName(airportName);
                }
            }
            // 前端航班出发机场翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_departure_airport())) {
                ApiAirPortInfoDto preDepAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getPre_departure_airport());
                if (preDepAirport != null) {
                    switch (language) {
                        case EN_US:
                            flightInfoDynamicDto.setPre_depAirPortName(preDepAirport.getAirPortEName());
                            break;
                        case JA_JP:
                            flightInfoDynamicDto.setPre_depAirPortName(preDepAirport.getAirportJpName());
                            break;
                        case ZH_HK:
                            flightInfoDynamicDto.setPre_depAirPortName(preDepAirport.getAirportTcName());
                            break;
                        default:
                            flightInfoDynamicDto.setPre_depAirPortName(preDepAirport.getAirPortName());
                    }
                    ApiCityInfoDto preDepCity = cacheService.getLocalCity(preDepAirport.getCityCode());
                    if (preDepCity != null) {
                        switch (language) {
                            case EN_US:
                                flightInfoDynamicDto.setPre_departure_city(preDepCity.getCityEName());
                                break;
                            case JA_JP:
                                flightInfoDynamicDto.setPre_departure_city(preDepCity.getCityJpName());
                                break;
                            case ZH_HK:
                                flightInfoDynamicDto.setPre_departure_city(preDepCity.getCityTcName());
                                break;
                            default:
                                flightInfoDynamicDto.setPre_departure_city(preDepCity.getCityName());
                        }
                    }
                }
            }
            // 前端航班到达机场翻译
            if (StringUtils.isNotBlank(flightInfoDynamicDto.getPre_arrival_airport())) {
                ApiAirPortInfoDto preArrAirport = cacheService.getLocalAirport(flightInfoDynamicDto.getPre_arrival_airport());
                if (preArrAirport != null) {
                    switch (language) {
                        case EN_US:
                            flightInfoDynamicDto.setPre_arrAirPortName(preArrAirport.getAirPortEName());
                            break;
                        case JA_JP:
                            flightInfoDynamicDto.setPre_arrAirPortName(preArrAirport.getAirportJpName());
                            break;
                        case ZH_HK:
                            flightInfoDynamicDto.setPre_arrAirPortName(preArrAirport.getAirportTcName());
                            break;
                        default:
                            flightInfoDynamicDto.setPre_arrAirPortName(preArrAirport.getAirPortName());
                    }
                    ApiCityInfoDto preArrCity = cacheService.getLocalCity(preArrAirport.getCityCode());
                    if (preArrCity != null) {
                        switch (language) {
                            case EN_US:
                                flightInfoDynamicDto.setPre_arrival_city(preArrCity.getCityEName());
                                break;
                            case JA_JP:
                                flightInfoDynamicDto.setPre_arrival_city(preArrCity.getCityJpName());
                                break;
                            case ZH_HK:
                                flightInfoDynamicDto.setPre_arrival_city(preArrCity.getCityTcName());
                                break;
                            default:
                                flightInfoDynamicDto.setPre_arrival_city(preArrCity.getCityName());
                        }
                    }
                }
            }

            // 城市名称翻译
            handleCityNameTranslation(flightInfoDynamicDto, allCityList, language);
        });
    }

    /**
     * 处理城市名称翻译
     */
    private void handleCityNameTranslation(FlightInfoDynamicDto flightInfoDynamicDto, List<CityInfoDTO> allCityList, LanguageEnum language) {
        // 出发城市翻译
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getDeparture_city())) {
            Optional<CityInfoDTO> depCity = allCityList.stream()
                    .filter(city -> city.getCityCode().equals(flightInfoDynamicDto.getDeparture_city_code()))
                    .findFirst();

            if (depCity.isPresent()) {
                switch (language) {
                    case EN_US:
                        flightInfoDynamicDto.setDeparture_city(depCity.get().getCityEName());
                        break;
                    case JA_JP:
                        flightInfoDynamicDto.setDeparture_city(depCity.get().getCityJpName());
                        break;
                    case ZH_HK:
                        flightInfoDynamicDto.setDeparture_city(depCity.get().getCityTcName());
                        break;
                    default:
                        flightInfoDynamicDto.setDeparture_city(depCity.get().getCityName());
                }
            }
        }

        // 到达城市翻译
        if (StringUtils.isNotBlank(flightInfoDynamicDto.getArrival_city())) {
            Optional<CityInfoDTO> arrCity = allCityList.stream()
                    .filter(city -> city.getCityCode().equals(flightInfoDynamicDto.getArrival_city_code()))
                    .findFirst();

            if (arrCity.isPresent()) {
                switch (language) {
                    case EN_US:
                        flightInfoDynamicDto.setArrival_city(arrCity.get().getCityEName());
                        break;
                    case JA_JP:
                        flightInfoDynamicDto.setArrival_city(arrCity.get().getCityJpName());
                        break;
                    case ZH_HK:
                        flightInfoDynamicDto.setArrival_city(arrCity.get().getCityTcName());
                        break;
                    default:
                        flightInfoDynamicDto.setArrival_city(arrCity.get().getCityName());
                }
            }
        }
    }
}
