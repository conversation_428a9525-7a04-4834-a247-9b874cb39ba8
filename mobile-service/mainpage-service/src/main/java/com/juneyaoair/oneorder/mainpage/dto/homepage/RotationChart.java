package com.juneyaoair.oneorder.mainpage.dto.homepage;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.flightbasic.request.wechat.PicInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RotationChart {
    @ApiModelProperty("图片ID")
    private String picId;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("开始时间戳")
    private Long startTimeStamp;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("结束时间戳")
    private Long endTimeStamp;
    @ApiModelProperty("图片位置")
    private String picLocation;

    @ApiModelProperty(value = "图片地址（老版本使用 新版本使用picInfoList替代）", hidden = true)
    private String picUrl;

    @ApiModelProperty(value = "图片信息清单")
    private List<PicInfo> picInfoList;

    @ApiModelProperty("名称")
    private String title;
    @ApiModelProperty("小名称")
    private String littleTitle;
    @ApiModelProperty("窗口名称")
    private String winName;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("纯文本描述")
    private String descriptionPlainTxt;
    @ApiModelProperty("Html地址")
    private String url;
    @ApiModelProperty("渠道号")
    private String channelCode;
    @ApiModelProperty("判断是否需要登录")
    private String isLogin;
    @ApiModelProperty("图片发布模式")
    private String modeType;
    @ApiModelProperty("是否可分享")
    private String isShared;
    @ApiModelProperty("缩略图")
    private String shareIconUrl;
    @ApiModelProperty("视频地址")
    private String videoUrl;
    @ApiModelProperty("分享描述")
    private String shareDesc;
    @ApiModelProperty("是否赠送积分")
    private String isGiftPoints;
    @ApiModelProperty("是否送优惠券")
    private String isGiftCoupons;
    @ApiModelProperty("统计事件名称")
    private String statisticalEvents;
    @ApiModelProperty("自定义图片类型  pay_result支付成功广告图片checkin_visa值机选座广告图片")
    private String type;
    @ApiModelProperty("信息展示标签  需观看X秒广告")
    private String showLabelInfo;
    @ApiModelProperty("英文逗号分隔的舱位")
    private String cabins;
    @ApiModelProperty("图片展示时长 单位：秒")
    private String picShowTimes;
    @ApiModelProperty("广告弹出频次 “1”代表每次；“2代表每天一次”")
    private String videoOrimgShowTimes;
    @ApiModelProperty("广告文案展示")
    private String advShow;
    @ApiModelProperty("英文逗号分隔的航线")
    private String airlines;
    @ApiModelProperty("事件类型")
    private String eventType;
    @ApiModelProperty(value = "图片渲染模式",notes = "W-轮播，S-静置")
    private String picType;
    @ApiModelProperty(
            value = "生效方式，登录(on)、未登录(out)、总是(all)、登录已实名认证(auth)、登录未实名认证(unauth)",
            example = "on",
            allowableValues = "on,out,all,auth,unauth"
    )
    private String enabledMode;
}
