package com.juneyaoair.oneorder.mainpage.service.impl;


import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityCheckRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityResponse;
import com.juneyaoair.oneorder.api.b2chand.service.IB2CHandService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.enums.CrmCommonEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofApplyReq;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofResponse;
import com.juneyaoair.oneorder.api.order.dto.flightproof.IBETicketInfo;
import com.juneyaoair.oneorder.api.order.dto.flightproof.SegmentInfo;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.api.tinyurl.ITinyUrlService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.util.BeanUtils;
import com.juneyaoair.oneorder.config.SmsConfig;
import com.juneyaoair.oneorder.mainpage.config.EmailConfig;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.constant.MainPageConstant;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofApply;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofMsgInfo;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofResp;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofSendInfo;
import com.juneyaoair.oneorder.mainpage.service.IFlightChangeProofService;
import com.juneyaoair.oneorder.mainpage.util.FavFTPUtil;
import com.juneyaoair.oneorder.util.FlightUtil;
import com.juneyaoair.oneorder.mainpage.util.PdfUtil;
import com.juneyaoair.oneorder.mobile.config.GeetestPropertiesConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.util.JsonUtil;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.AirJsonUtil;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.juneyaoair.oneorder.mainpage.constant.MainPageConstant.COMMON_LOG_INFO;

@Service
@Slf4j
public class FlightChangeProofServiceImpl implements IFlightChangeProofService {

    @Resource
    IB2CHandService flightAbnormalityService;
    @Resource
    CommonService commonService;
    @Autowired
    private GeetestPropertiesConfig geetestPropertiesConfig;
    @Resource
    IOrderService orderService;
    @Resource
    CacheService cacheService;
    @Resource
    IB2CHandService ib2CHandService;
    @Resource
    MainPageConfig mainPageConfig;
    @Resource
    SmsConfig smsConfig;
    @Resource
    RedisUtil redisUtil;
    @Resource
    protected TemplateEngine templateEngine;
    @Resource
    private EmailConfig emailConfig;
    @Autowired
    private IGeetestService geetestService;
    private static final String COUPON_PRO_CHANNELCODE = "B2C";
    public static final String BUSINESS_TYPE_DELAYPROVE = "DELAYPROVE";

    @Autowired
    private IB2CHandService b2CHandService;

    @Override
    public ResponseData<List<FlightChangeProofResp>> applyFlightChangeProof(RequestData<FlightChangeProofApply> req,
                                                                            HttpServletRequest request) {
        ResponseData<List<FlightChangeProofResp>> baseResp = ResponseData.success();
        String ip = ServiceContext.getHead().clientIp;
        try {
            ChannelInfo channelInfo = commonService.findChannelInfo(req.getChannelNo());
            HashMap<String, String> param = new HashMap<>();
            DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
            param.put("digestmod", digestmodEnum.getName());
            param.put("user_id", req.getFfpId()); //网站用户id
            param.put("client_type", req.getData().getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
            param.put("ip_address", ip); //传输用户请求验证时所携带的IP
            geetestService.validate(req.getData().getScene(), req.getData(), param);
            FlightChangeProofApply proofApply = req.getData();
            //请求参数判错
            checkApplyReq(req, baseResp);
            if (!WSEnum.SUCCESS.getResultCode().equals(baseResp.getCode())) {
                String logStr2 = AirJsonUtil.toJSONString(baseResp);
                log.info(MainPageConstant.COMMON_LOG_WITH_RESP_INFO, ip, logStr2);
                return baseResp;
            }
            //构建请求类，发送给后台
            FlightChangeProofApplyReq applyReq = new FlightChangeProofApplyReq("10", COUPON_PRO_CHANNELCODE, channelInfo.getUserNo());
            log.debug(MainPageConstant.COMMON_LOG_WITH_RESP_INFO, ip, "applyReq请求参数：" + AirJsonUtil.toJSONString(applyReq));
            applyReq = createApplyReq(applyReq, req);
            FlightChangeProofResponse proofResponse = orderService.getTicketFlightCheck(applyReq);
            if (proofResponse == null || WSEnum.SUCCESS.getResultCode().equals(proofResponse.getResultCode())) {
                throw new ServiceException("获取航变信息异常");
            }
            getFlightChangeResult(req, baseResp, proofApply, proofResponse, ip);
            return baseResp;
        } catch (Exception e) {
            log.error(COMMON_LOG_INFO, ip, req, e.getMessage());
            throw new ServiceException("获取航变信息异常");
        }
    }

    @Override
    public ResponseData<List<String>> sendFlightChangeProof(RequestData<FlightChangeProofSendInfo> req,
                                                            HttpServletRequest request, HttpServletResponse response) {
        String ip = ServiceContext.getHead().clientIp;
        ResponseData<List<String>> baseResp = ResponseData.success();
        try {
            FlightChangeProofSendInfo sendInfo = req.getData();
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<RequestData<FlightChangeProofSendInfo>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                baseResp.setCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                baseResp.setMessage(violations.iterator().next().getMessage());
                return baseResp;
            }
            List<String> sftpPathList = new ArrayList<>();
            List<FlightChangeProofResp> proofResps = sendInfo.getProofResps();
            for (int i = 0; i < proofResps.size(); i++) {
                //取消
                String cancel = ((proofResps.get(i).getSegmentStatus()).equals("C")) ? "√" : "";
                //延误
                String depDelay = ((proofResps.get(i).getSegmentStatus()).equals("D")
                        && (proofResps.get(i).getDepDelay() == null || proofResps.get(i).getDepDelay())) ? "√" : ""; //离港延误标记
                String arrDelay = ((proofResps.get(i).getSegmentStatus()).equals("D")
                        && (proofResps.get(i).getArrDelay() == null || proofResps.get(i).getArrDelay())) ? "√" : ""; //到港延误标记
                String altLanding = ((proofResps.get(i).getSegmentStatus()).equals("V")) ? "√" : "";
                boolean gongsi = proofResps.get(i).getStatusReason().contains("公司原因") || proofResps.get(i).getStatusReason().contains("机械故障");
                boolean feigongsi = proofResps.get(i).getStatusReason().contains("非公司原因");
                boolean tianqi = proofResps.get(i).getStatusReason().contains("天气");
                boolean jichang = proofResps.get(i).getStatusReason().contains("机场");
                boolean junshihuodong = proofResps.get(i).getStatusReason().contains("军事活动");
                boolean liuliang = proofResps.get(i).getStatusReason().contains("流量控制");
                boolean gonggonganquan = proofResps.get(i).getStatusReason().contains("公共安全");
                boolean daiding = proofResps.get(i).getStatusReason().contains("待定");
                // 消息站位对象
                FlightChangeProofMsgInfo info = new FlightChangeProofMsgInfo();
                info.setPassengerName(proofResps.get(i).getPassengerName());
                info.setIdNo(proofResps.get(i).getIdNo());
                Date flightDate = DateUtil.toDate(proofResps.get(i).getFlightDate(), DateUtil.YYYY_MM_DD_PATTERN);
                String flightDateFormat = DateUtil.convertDateToString(flightDate, DateUtil.YYYY_MM_DD_CHINA);
                info.setFlightDate(flightDateFormat);
                info.setFlightNo(proofResps.get(i).getFlightNo());
                info.setOrgCity(proofResps.get(i).getOrgCity());
                info.setDstCity(proofResps.get(i).getDstCity());
                info.setOrgEnCity(proofResps.get(i).getOrgEnCity());
                info.setDstEnCity(proofResps.get(i).getDstEnCity());
                info.setDelayNum(proofResps.get(i).getDelayNum() == null ? "" : proofResps.get(i).getDelayNum());
                info.setDepTime(getDateString(proofResps.get(i).getDepTime() == null ? "" : proofResps.get(i).getDepTime()));
                info.setEnDepTime(getDateTimeString(proofResps.get(i).getDepTime() == null ? "" : proofResps.get(i).getDepTime()));
                info.setArrTime(getDateString(proofResps.get(i).getArrTime() == null ? "" : proofResps.get(i).getArrTime()));
                info.setEnArrTime(getDateTimeString(proofResps.get(i).getArrTime() == null ? "" : proofResps.get(i).getArrTime()));
                info.setActDepTime(getDateString(proofResps.get(i).getActDepTime() == null ? "" : proofResps.get(i).getActDepTime()));
                info.setEnActDepTime(getDateTimeString(proofResps.get(i).getActDepTime() == null ? "" : proofResps.get(i).getActDepTime()));
                info.setActArrTime(getDateString(proofResps.get(i).getActArrTime() == null ? "" : proofResps.get(i).getActArrTime()));
                info.setEnActArrTime(getDateTimeString(proofResps.get(i).getActArrTime() == null ? "" : proofResps.get(i).getActArrTime()));

                info.setFlightInnDate(getDateString(proofResps.get(i).getFlightInn() == null ? "" : proofResps.get(i).getFlightInn()));
                info.setFlightInnTime(getDateTimeString(proofResps.get(i).getFlightInn() == null ? "" : proofResps.get(i).getFlightInn()));
                info.setFlightOutDate(getDateString(proofResps.get(i).getFlightOut() == null ? "" : proofResps.get(i).getFlightOut()));
                info.setFlightOutTime(getDateTimeString(proofResps.get(i).getFlightOut() == null ? "" : proofResps.get(i).getFlightOut()));

                info.setCancel(cancel);
                info.setGongsi(gongsi);
                info.setFeigongsi(feigongsi);
                info.setTianqi(tianqi);
                info.setJichang(jichang);
                info.setJunshihuodong(junshihuodong);
                info.setLiuliang(liuliang);
                info.setGonggonganquan(gonggonganquan);
                info.setDaiding(daiding);
                info.setCurrentDateStr(getCurrentDateStr());
                info.setDelay(depDelay);
                info.setArrDelay(arrDelay);
                info.setAltLanding(altLanding);
                info.setDepCityZone(proofResps.get(i).getDepCityZone());
                info.setArrCityZone(proofResps.get(i).getArrCityZone());
                String tempHtml = this.generateCertificateHTML(info);
                ByteArrayInputStream byteArrayInputStream = PdfUtil.exportPdf(tempHtml);
                String fileName = proofResps.get(i).getFlightNo() + "_" + proofResps.get(i).getFlightDate() + "_" + proofResps.get(i).getDepAirportCode() + proofResps.get(i).getArrAirportCode() + "_" + proofResps.get(i).getIdNo() + ".pdf";
                String directory = emailConfig.SFTP_PATHNAME_01;
                Boolean isFile = FavFTPUtil.sftpUploadStream(byteArrayInputStream, directory, fileName, emailConfig);
                if (isFile) {
                    String sftpPath = emailConfig.SFTP_TOMCAT_PORT_01 + emailConfig.SFTP_IMG_PATH_01 + fileName;
                    sftpPathList.add(sftpPath);
                }
            }

            baseResp.setCode(CrmCommonEnum.SUC0.eName);
            baseResp.setMessage(WSEnum.SUCCESS.getResultInfo());
            baseResp.setData(sftpPathList);
            return baseResp;
        } catch (Exception e) {
            log.error(MainPageConstant.COMMON_LOG_INFO, ip, req, e.getMessage());
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public ResponseData<Boolean> verifyFlightDelayCode(RequestData<VerifyFlightDelayCodeReq> req, HttpServletRequest request, BindingResult bindingResult) {
        ResponseData<Boolean> resp = ResponseData.success();
        if (bindingResult.hasErrors()) {
            throw new ServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }

        if (req.getData() == null || StringUtils.isBlank(req.getData().getCode())) {
            throw new ServiceException("请输入编号");
        }
        FlightAbnormalityCheckRequest flightAbnormalityCheckRequest = new FlightAbnormalityCheckRequest("10",
                ChannelCodeEnum.MOBILE.getChannelCode(),
                ServiceContext.getHead().channelInfo.getUserNo(),
                req.getData().getCode());

        FlightAbnormalityResponse flightAbnormalityResponse = b2CHandService.checkFlightAbnormality(flightAbnormalityCheckRequest);
        if (flightAbnormalityResponse == null) {
            throw new ServiceException("系统繁忙");
        }
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(flightAbnormalityResponse.getResultCode())) {
            resp.setData(true);
            resp.setMessage("航班不正常证明真实有效");
            return resp;
        } else {
            resp.setData(false);
            resp.setMessage("请输入正确的编号");
            resp.setCode(WSEnum.ERROR.getResultCode());
            return resp;
        }
    }


    private String generateCertificateHTML(FlightChangeProofMsgInfo info) {
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);

        TemplateEngine templateEngine = new TemplateEngine();
        templateEngine.setTemplateResolver(templateResolver);

        Context context = new Context();
        context.setVariable("passengerName", info.getPassengerName());
        context.setVariable("idNo", info.getIdNo());
        context.setVariable("flightDate", info.getFlightDate());
        context.setVariable("flightNo", info.getFlightNo());
        context.setVariable("orgCity", info.getOrgCity());
        context.setVariable("dstCity", info.getDstCity());
        context.setVariable("orgEnCity", info.getOrgEnCity());
        context.setVariable("dstEnCity", info.getDstEnCity());
        context.setVariable("depTime", info.getDepTime());
        context.setVariable("enDepTime", StringUtils.isNotBlank(info.getEnDepTime()) ? info.getEnDepTime() : "");
        context.setVariable("arrTime", info.getArrTime());
        context.setVariable("enArrTime", StringUtils.isNotBlank(info.getEnArrTime()) ? info.getEnArrTime() : "");
        context.setVariable("actDepTime", info.getActDepTime());
        context.setVariable("enActDepTime", StringUtils.isNotBlank(info.getEnActDepTime()) ? info.getEnActDepTime() : "");
        context.setVariable("actArrTime", info.getActArrTime());
        context.setVariable("enActArrTime", StringUtils.isNotBlank(info.getEnActArrTime()) ? info.getEnActArrTime() : "");
        context.setVariable("depDelay", info.getDelay());
        context.setVariable("arrDelay", info.getArrDelay());
        context.setVariable("altLanding", info.getAltLanding());
        context.setVariable("depCityZone", StringUtils.isNotBlank(info.getDepTime()) && StringUtils.isNotBlank(info.getEnDepTime()) && info.getDepCityZone() != null ? "GMT+" + info.getDepCityZone().toString() : "");
        context.setVariable("arrCityZone", StringUtils.isNotBlank(info.getArrTime()) && StringUtils.isNotBlank(info.getEnArrTime()) && info.getArrCityZone() != null ? "GMT+" + info.getArrCityZone().toString() : "");
        context.setVariable("actDepCityZone", StringUtils.isNotBlank(info.getActDepTime()) && StringUtils.isNotBlank(info.getEnActDepTime()) && info.getDepCityZone() != null ? "GMT+" + info.getDepCityZone().toString() : "");
        context.setVariable("actArrCityZone", StringUtils.isNotBlank(info.getActArrTime()) && StringUtils.isNotBlank(info.getEnActArrTime()) && info.getArrCityZone() != null ? "GMT+" + info.getArrCityZone().toString() : "");
        context.setVariable("cancel", info.getCancel());
        context.setVariable("currentDateStr", getCurrentDateStr());
        context.setVariable("delayNum", info.getDelayNum());
        context.setVariable("gongsi", info.isGongsi());
        context.setVariable("feigongsi", info.isFeigongsi());
        context.setVariable("tianqi", info.isTianqi());
        context.setVariable("jichang", info.isJichang());
        context.setVariable("junshihuodong", info.isJunshihuodong());
        context.setVariable("gonggonganquan", info.isGonggonganquan());
        context.setVariable("liuliangkongzhi", info.isLiuliang());
        context.setVariable("daiding", info.isDaiding());

        context.setVariable("flightInnDate", info.getFlightInnDate());
        context.setVariable("flightInnTime", info.getFlightInnTime());
        context.setVariable("flightOutDate", info.getFlightOutDate());
        context.setVariable("flightOutTime", info.getFlightOutTime());
        String filledTemplate = templateEngine.process("templates/CertificateOfIrregularFlight", context);
        return filledTemplate;
    }

    private String hideCheckBox(String html, String factor, boolean isChecked) {
        char checked = 'T';
        char check = 'F';
        //隐藏属性
        String hide = " style=\"display:none;\" ";
        if (isChecked) {
            html = html.replace('{' + factor + check + '}', hide);
            html = html.replace('{' + factor + checked + '}', "");
        } else {
            html = html.replace('{' + factor + check + '}', "");
            html = html.replace('{' + factor + checked + '}', hide);
        }
        return html;
    }

    @Resource
    ITinyUrlService tinyUrlService;

    /**
     * 获取当前时间，格式yyyy年MM月dd日 HH:mm:ss
     */
    public static String getCurrentDateStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        return formatter.format(currentTime);
    }

    /**
     * 日期转换方法，yy年MM月dd日
     */
    public static String getDateString(String dateString) {
        String result = "";
        if (!StringUtil.isNullOrEmpty(dateString)) {
            Date date = DateUtil.toDate(dateString, "yyyy-MM-dd HH:mm");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            result = simpleDateFormat.format(date);
        }

        return result;
    }

    /**
     * 根据夏令时进行区时转换
     *
     * @param dateTimeZone 已知时区
     * @param dateStr      已知区时 YYYY_MM_DD_HH_MM
     * @param airPortInfo  目标区时
     * @return dateTime
     */
    public static String convertDateWithSummer(String dateTimeZone, String dateStr, ApiAirPortInfoDto airPortInfo) {
        Date dateTime = DateUtil.toTargetDate(dateStr, dateTimeZone, airPortInfo.getCityTimeZone());
        //调整以后的时区
        String finalZone = convertSummerOrWinterTime(airPortInfo.getCityTimeZone(), DateUtil.getDateStringAll(dateTime), airPortInfo);
        //时区没有变化，直接返回dateTime
        if (finalZone.equals(airPortInfo.getCityTimeZone())) {
            return DateUtil.getDateStringAll(dateTime);
        }
        //时区有变化，需要更正时间
        dateTime = DateUtil.toTargetDate(dateStr, dateTimeZone, finalZone);
        return DateUtil.getDateStringAll(dateTime);
    }

    /**
     * 夏令时时间调整
     *
     * @param zone        当前默认时区
     * @param date        当前日期 YYYY_MM_DD_HH_MM
     * @param airPortInfo 当前所属夏令时信息
     * @return
     */
    public static String convertSummerOrWinterTime(String zone, String date, ApiAirPortInfoDto airPortInfo) {
        if (airPortInfo != null) {
            //对于信息不全的不做任何计算
            if (StringUtils.isBlank(airPortInfo.getDstMsg()) || StringUtils.isBlank(airPortInfo.getDstOffset())) {
                return zone;
            } else {
                try {
                    if (StringUtils.isNotBlank(airPortInfo.getDstMsg())) {
                        Date depDate = DateUtil.toDate(date, DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                        List<Map<String, String>> list = (List<Map<String, String>>) JsonUtil.jsonToList(airPortInfo.getDstMsg(), new TypeToken<List<Map<String, String>>>() {
                        }.getType());
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (Map<String, String> map : list) {
                                Date startDate = DateUtil.toDate(map.get("dstStart") + " 00:00", DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                                Date endDate = DateUtil.toDate(map.get("dstEnd") + " 23:59", DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                                if (startDate != null && endDate != null && depDate.getTime() >= startDate.getTime() && depDate.getTime() <= endDate.getTime()) {
                                    return String.valueOf(Double.parseDouble(zone) + Double.parseDouble(airPortInfo.getDstOffset()));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    return zone;
                }
            }
        }
        return zone;
    }

    private void getFlightChangeResult(RequestData<FlightChangeProofApply> req, com.juneyaoair.oneorder.restresult.response.ResponseData<List<FlightChangeProofResp>> baseResp, FlightChangeProofApply proofApply, FlightChangeProofResponse infoResponse, String ip) {
        List<IBETicketInfo> ibeTicketInfos = new ArrayList<>();
        List<IBETicketInfo> ibeTicketInfoList = infoResponse.getIBETicketInfoList();
        List<FlightChangeProofResp> proofResps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ibeTicketInfoList)) {
            ibeTicketInfoList.forEach(ibeTicketInfo -> {
                List<SegmentInfo> segmentInfoList = new ArrayList<>();
                //匹配对应的航班日期
                ibeTicketInfo.getSegmentInfoList().forEach(segmentInfo -> {
                    if (segmentInfo.getBeforeChangeSegment() != null) {
                        SegmentInfo s = new SegmentInfo();
                        BeanUtils.copyNotNullProperties(segmentInfo.getBeforeChangeSegment(), s);
                        if (("D".equals(segmentInfo.getSegmentStatus()) || "C".equals(segmentInfo.getSegmentStatus()))) {
                            segmentInfoList.add(s);
                        }
                    } else {
                        if (("D".equals(segmentInfo.getSegmentStatus()) || "C".equals(segmentInfo.getSegmentStatus()))
                                && segmentInfo.getDepTime().substring(0, 10).equals(req.getData().getFlightDate())) {
                            segmentInfoList.add(segmentInfo);
                        }
                    }
                });
                if (CollectionUtils.isNotEmpty(segmentInfoList)) {
                    ibeTicketInfo.setSegmentInfoList(segmentInfoList);
                    ibeTicketInfos.add(ibeTicketInfo);
                }
            });
            if (CollectionUtils.isNotEmpty(ibeTicketInfos)) {
                //再次查询官网接口生成航延编号
                for (int i = 0; i < ibeTicketInfos.size(); i++) {
                    IBETicketInfo ibeTicketInfo = ibeTicketInfos.get(i);
                    List<SegmentInfo> segmentInfoList = ibeTicketInfo.getSegmentInfoList();
                    proofResps = segmentInfoList.stream().map(segmentInfo -> convertToproofResps(ibeTicketInfo, segmentInfo, proofApply)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(proofResps)) {
                        proofResps = proofResps.stream().filter(proofResp -> StringUtils.isNotBlank(proofResp.getDelayNum())).collect(Collectors.toList());
                    }
                }
                if (CollectionUtils.isEmpty(proofResps)) {
                    baseResp.setCode(WSEnum.NO_DATA.getResultCode());
                    baseResp.setMessage(WSEnum.NO_DATA.getResultInfo());
                    baseResp.setMessage("未查询到相关航班不正常证明");
                } else {
                    baseResp.setCode(CrmCommonEnum.SUC0.eName);
                    baseResp.setMessage(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setData(proofResps);
                }
            } else {
                baseResp.setCode(WSEnum.NO_DATA.getResultCode());
                baseResp.setMessage("未查询到相关航班不正常证明");
            }
        } else {
            baseResp.setCode(WSEnum.NO_DATA.getResultCode());
            baseResp.setMessage("未查询到相关客票信息");
        }
    }

    private FlightChangeProofResp convertToproofResps(IBETicketInfo ibeTicketInfo, SegmentInfo segmentInfo, FlightChangeProofApply proofApply) {
        FlightChangeProofResp proofResp = new FlightChangeProofResp();
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        FlightAbnormalityRequest flightAbnormalityRequest = new FlightAbnormalityRequest("10", channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
        flightAbnormalityRequest.setFlightDate(proofApply.getFlightDate());
        flightAbnormalityRequest.setFlightNo(segmentInfo.FlightNo);
        flightAbnormalityRequest.setTicketNo(ibeTicketInfo.getTicketNo());
        flightAbnormalityRequest.setPassengerName(proofApply.getPassengerName());
        FlightAbnormalityResponse flightAbnormalityResponse = ib2CHandService.queryFlightAbnormality(flightAbnormalityRequest);
        if (flightAbnormalityResponse != null && UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(flightAbnormalityResponse.getResultCode())) {
            proofResp.setFlightNo(flightAbnormalityResponse.getFlightNo());

            proofResp.setPassengerName(flightAbnormalityResponse.getName());
            //出发机场中文名，城市中文名，英文名
            ApiAirPortInfoDto airPortInfoDep = cacheService.getLocalAirport(segmentInfo.getDepAirportCode());
            proofResp.setDepAirportCode(segmentInfo.getDepAirportCode());
            proofResp.setDepAirportName(airPortInfoDep.getAirPortName());
            proofResp.setOrgCity(airPortInfoDep.getCityName());
            proofResp.setOrgEnCity(airPortInfoDep.getCityEName());
            String depAirportTerminal = segmentInfo.getDepAirportTerminal();
            proofResp.setDepAirportTerminal(FlightUtil.formatTerminal(depAirportTerminal));
            //到达机场中文名，城市中文名，英文名
            ApiAirPortInfoDto airPortInfoArr = cacheService.getLocalAirport(segmentInfo.getArrAirportCode());//时间不需要 ibeTicketInfo.getFlightDate()
            proofResp.setArrAirportCode(segmentInfo.getArrAirportCode());
            proofResp.setArrAirportName(airPortInfoArr.getAirPortName());
            proofResp.setDstCity(airPortInfoArr.getCityName());
            proofResp.setDstEnCity(airPortInfoArr.getCityEName());
            String arrAirportTerminal = segmentInfo.getArrAirportTerminal();
            proofResp.setArrAirportTerminal(FlightUtil.formatTerminal(arrAirportTerminal));
            proofResp.setDepSpecificTime(getDateTimeString(segmentInfo.getDepTime()));
            proofResp.setArrSpecificTime(getDateTimeString(segmentInfo.getArrTime()));
            if (StringUtils.isNotBlank(segmentInfo.getFlightOut())) {
                proofResp.setFlightOut(convertDateWithSummer("8", segmentInfo.getFlightOut(), airPortInfoDep));
            }
            if (StringUtils.isNotBlank(segmentInfo.getFlightInn())) {
                proofResp.setFlightInn(convertDateWithSummer("8", segmentInfo.getFlightInn(), airPortInfoArr));
            }
            //计算飞行时间
            //出发城市 todo gdqi
            String depCityTimeZone = null;//airPortInfoDep.getCityTimeZone();
            //到达城市 todo gdqi
            String arrCityTimeZone = null;//airPortInfoArr.getCityTimeZone();
            //获取出发时间
            String depTime = segmentInfo.getDepTime();
            String depActTime = segmentInfo.getActDepTime();
            proofResp.setDepTime(depTime);
            proofResp.setActDepTime(depActTime);
            //获取到达时间,实际时间，航班号，
            String arrTime = segmentInfo.getArrTime();
            String arrActTime = segmentInfo.getActArrTime();
            proofResp.setActArrTime(arrActTime);
            proofResp.setArrTime(arrTime);
            Long time = DateUtil.calDuration(depTime, depCityTimeZone, arrTime, arrCityTimeZone);
            int day = DateUtil.diffDays(depTime, depCityTimeZone, arrTime, arrCityTimeZone);
            proofResp.setFlightTime(time);
            proofResp.setDay(day);
            Date date = DateUtil.toDate(depTime, "yyyy-MM-dd");
            String weekStr = DateUtil.getWeekStr(date);
            proofResp.setWeekStr(weekStr);
            String dateString = DateUtil.dateToString(date, "yyyy-MM-dd");
            proofResp.setFlightDate(dateString);
            proofResp.setFlightDateTime(dateString);
            proofResp.setDepCityZone(segmentInfo.getDepCityZone());
            proofResp.setArrCityZone(segmentInfo.getArrCityZone());
            //封装延误、取消理由
            if (segmentInfo.getSegmentStatus().equals("D") || segmentInfo.getSegmentStatus().equals("C")) {
                proofResp.setSegmentStatus(segmentInfo.getSegmentStatus());
                proofResp.setStatusReason(segmentInfo.getStatusReason());
                proofResp.setDepDelay(segmentInfo.getDepDelay());
                proofResp.setArrDelay(segmentInfo.getArrDelay());
            }
            //航延编号
            proofResp.setDelayNum(flightAbnormalityResponse.getCodeNumber());
            //存储身份信息
            ibeTicketInfo.getIdentityInfoList().forEach(identityInfo -> {
                if (identityInfo.getIdType().equals("PP")) {
                    proofResp.setIdType("PP");
                    proofResp.setIdNo(identityInfo.getIdNo());
                } else if (identityInfo.getIdType().equals("NI")) {
                    proofResp.setIdType("NI");
                    proofResp.setIdNo(identityInfo.getIdNo());
                } else {
                    proofResp.setIdType(identityInfo.getIdType());
                    proofResp.setIdNo(identityInfo.getIdNo());
                }
            });
        }
        return proofResp;
    }

    /**
     * 日期转换方法，HH:mi
     */
    public static String getDateTimeString(String dateString) {
        String result = "";
        if (!StringUtil.isNullOrEmpty(dateString)) {
            Date date = DateUtil.toDate(dateString, "yyyy-MM-dd HH:mm");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
            result = simpleDateFormat.format(date);
        }

        return result;
    }

    /**
     * 构建航变申请请求类
     *
     * @param applyReq
     * @param req
     * @return
     */
    private FlightChangeProofApplyReq createApplyReq(FlightChangeProofApplyReq applyReq, RequestData<FlightChangeProofApply> req) {
        FlightChangeProofApply proofApply = req.getData();
        applyReq.setFlightDate(proofApply.getFlightDate());
        applyReq.setFlightNo(proofApply.getFlightNo());
        //护照校验
        Pattern patternOne = Pattern.compile(PatternCommon.PASSPORT_NO);
        Matcher matcherOne = patternOne.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        //身份证校验
        Pattern patternTwo = Pattern.compile(PatternCommon.ID_NUMBER);
        Matcher matcherTwo = patternTwo.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        //票号校验
        Pattern patternThree = Pattern.compile(PatternCommon.DIGIT_13_TICKET_REGEXP);
        Matcher matcherThree = patternThree.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        if (matcherOne.matches()) {
            //证件号为护照
            applyReq.setCertNo(proofApply.getPassengerCard());
            applyReq.setCertType("PP");
            throw ServiceException.fail(CommonErrorCode.NOT_SUPPORT);
        } else if (matcherTwo.matches()) {
            //证件号为身份证
            applyReq.setCertNo(proofApply.getPassengerCard());
            applyReq.setCertType("NI");
            throw ServiceException.fail(CommonErrorCode.NOT_SUPPORT);
        } else if (matcherThree.matches()) {
            //票号
            String passengerCard = proofApply.getPassengerCard();
            if (passengerCard.indexOf('-') < 0) {
                String card = passengerCard.substring(0, 3) + "-" + passengerCard.substring(3);
                applyReq.setTicketNo(card);
            } else {
                applyReq.setTicketNo(passengerCard);
            }
        }
        applyReq.setBusinessType(BUSINESS_TYPE_DELAYPROVE);
        applyReq.setFlightDate(proofApply.getFlightDate());
        applyReq.setPassengerName(proofApply.getPassengerName());
        return applyReq;

    }

    /**
     * 参数判错
     */
    private ResponseData<List<FlightChangeProofResp>> checkApplyReq(RequestData<FlightChangeProofApply> req, ResponseData<List<FlightChangeProofResp>> baseResp) {
        FlightChangeProofApply proofApply = req.getData();
        //判断航班日期
        Pattern pattern = Pattern.compile(PatternCommon.DATE_NORMAL);
        Matcher matcher = pattern.matcher(proofApply.getFlightDate() == null ? "" : proofApply.getFlightDate());
        if (!matcher.matches()) {
            baseResp.setCode(WSEnum.ERROR.getResultCode());
            baseResp.setMessage("日期格式错误！");
            return baseResp;
        }
        //护照校验
        Pattern patternOne = Pattern.compile(PatternCommon.PASSPORT_NO);
        Matcher matcherOne = patternOne.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        //身份证校验
        Pattern patternTwo = Pattern.compile(PatternCommon.ID_NUMBER);
        Matcher matcherTwo = patternTwo.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        //票号校验
        Pattern patternThree = Pattern.compile(PatternCommon.DIGIT_13_TICKET_REGEXP);
        Matcher matcherThree = patternThree.matcher(proofApply.getPassengerCard() == null ? "" : proofApply.getPassengerCard());
        if ((!matcherOne.matches()) && (!matcherTwo.matches()) && (!matcherThree.matches())) {
            baseResp.setCode(WSEnum.ERROR.getResultCode());
            baseResp.setMessage("未查询到相关信息");
            return baseResp;
        }

        baseResp.setCode(WSEnum.SUCCESS.getResultCode());
        baseResp.setMessage(WSEnum.SUCCESS.getResultInfo());
        return baseResp;
    }
}
