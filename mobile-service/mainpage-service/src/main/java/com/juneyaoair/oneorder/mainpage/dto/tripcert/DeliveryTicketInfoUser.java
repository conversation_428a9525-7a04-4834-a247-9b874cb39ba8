package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@ApiModel
public class DeliveryTicketInfoUser extends GeetestDto {

    @ApiModelProperty("票号")
    @NotBlank(message = "客票信息不能为空")
    @Pattern(regexp = PatternCommon.TICKET_NO,message = "请输入正确的票号")
    private String ticketNo;

    @ApiModelProperty("乘机人姓名")
    @NotBlank(message = "乘机人姓名不能为空")
    private String passName;

    @ApiModelProperty("航班号")
    @NotBlank(message = "航班号不能为空")
    public String flightNo;

    @ApiModelProperty(value = "航班时间", example = "2024-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式需为yyyy-MM-dd")
    public String flightDate;

    @ApiModelProperty("出发城市code")
    public String depCityCode;

    @ApiModelProperty("到达城市code")
    public String arrCityCode;

    @ApiModelProperty("blackBox")
    private String blackBox;

}
