package com.juneyaoair.oneorder.mainpage.controller;

import cn.hutool.json.JSONUtil;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.mainpage.config.TripCertConfig;
import com.juneyaoair.oneorder.mainpage.dto.tripcert.*;
import com.juneyaoair.oneorder.mainpage.service.ITripCertService;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.IpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;


@RequestMapping("trip")
@RestController
@Api(value = "邮寄行程单服务", tags = "邮寄行程单服务")
@Slf4j
public class TripCertController {
    @Resource
    ITripCertService tripCertService;
    @Resource
    TripCertConfig tripCertConfig;
    @Resource
    private IGeetestService geetestService;

    @ApiOperation(value = "行程单方式查询", notes = "行程单方式查询")
    @RequestMapping(value = "queryTripMethod", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<TripCert> queryTripMethod(HttpServletRequest request, @RequestBody RequestData req) {
        ResponseData<TripCert> resp = ResponseData.success();
        TripCert tripCert = JSONUtil.toBean(tripCertConfig.tripCert, new TypeToken<TripCert>() {
        }.getType(), true);
        resp.setData(tripCert);
        return resp;
    }

    @ApiOperation(value = "邮寄行程单提交", notes = "邮寄行程单提交")
    @PostMapping(value = "/submitTripOrder", produces = {"application/json;charset=UTF-8"})
    public ResponseData<SubmitTripOrderResp> submitTripOrder(@RequestBody RequestData<DeliveryBooking> baseReq, HttpServletRequest request, BindingResult bindingResult) {
        return tripCertService.submitTripOrder(baseReq, request, bindingResult);
    }


    @ApiOperation(value = "行程单客票验证", notes = "行程单客票验证")
    @RequestMapping(value = "/checkTicket", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<List<DeliveryTicketInfo>> checkTicket(@RequestBody @Validated RequestData<DeliveryTicketInfoUser> baseReq,
                                                              BindingResult bindingResult, HttpServletRequest request) {

        if (SceneEnum.getEnumByCode(baseReq.getData().getScene()) != SceneEnum.TICKET_CHECK) {
            throw MultiLangServiceException.fail("验证失败");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        HashMap<String, String> param = new HashMap<>();
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", IpUtils.getIpAddr(request)); //网站用户id
        param.put("client_type", baseReq.getData().getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ServiceContext.getHead().clientIp); //传输用户请求验证时所携带的IP
        geetestService.validate(baseReq.getData().getScene(), baseReq.getData(), param);
        return tripCertService.checkTicket(baseReq, bindingResult, request);
    }


}
