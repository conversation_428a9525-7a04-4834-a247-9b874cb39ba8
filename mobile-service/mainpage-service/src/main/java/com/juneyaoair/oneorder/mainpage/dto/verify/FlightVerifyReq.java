package com.juneyaoair.oneorder.mainpage.dto.verify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
@Data
@ApiModel
@NoArgsConstructor
public class FlightVerifyReq {
    //极验scene: common
    @ApiModelProperty()
    @NotNull(message = "票号不能为空")
    @Pattern(regexp = "^\\d{3}-?\\d{10}$", message = "输入票号有误")
    private String certNo;
    @ApiModelProperty(value = "旅客姓名", required = true)
    @NotNull(message = "旅客姓名不能为空")
    private String passengerNm;
    //设备指纹 2021-04-15
    private String blackBox;

    @ApiModelProperty("航班号")
    @NotNull(message = "航班号不能为空")
    private String flightNo;
    /**
     * @see com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum
     */
    @ApiModelProperty(value = "极验场景参数")
    private String scene;
}
