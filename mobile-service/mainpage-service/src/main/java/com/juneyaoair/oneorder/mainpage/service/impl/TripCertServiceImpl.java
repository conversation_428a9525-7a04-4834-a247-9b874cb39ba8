package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.constant.TripTypeEnum;
import com.juneyaoair.oneorder.api.crm.constant.VoucherTypesEnum;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CrmUtil;
import com.juneyaoair.oneorder.api.order.dto.ticket.*;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.request.LimitCount;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.localcache.Segment;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.config.TripCertConfig;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.tripcert.*;
import com.juneyaoair.oneorder.mainpage.service.ITripCertService;
import com.juneyaoair.oneorder.mainpage.util.IdentityInfoUtil;
import com.juneyaoair.oneorder.order.constant.AirCompanyEnum;
import com.juneyaoair.oneorder.order.dto.IdentityInfo;
import com.juneyaoair.oneorder.util.FlightUtil;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.CouponProductBuyRequestDto;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.CouponProductBuyResponseDto;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.MailTravelProduct;
import com.juneyaoair.oneorder.order.feign.BenefitOrderClient;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.constant.TicketStateEnum;
import com.juneyaoair.oneorder.order.dto.TicketInfoRequest;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.*;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class TripCertServiceImpl implements ITripCertService {

    @Resource
    private IMemberService memberService;
    @Resource
    private IOrderService orderService;
    @Resource
    private MainPageConfig mainPageConfig;
    @Resource
    private CommonService commonService;
    @Resource
    private CacheService cacheService;
    @Resource
    private TripCertConfig tripCertConfig;
    @Resource
    private BenefitOrderClient benefitOrderClient;


    @Override
    public ResponseData<SubmitTripOrderResp> submitTripOrder(RequestData<DeliveryBooking> baseReq, HttpServletRequest request, BindingResult bindingResult) {
        ResponseData<SubmitTripOrderResp> resp = ResponseData.success();
        log.info("【邮寄行程单提交】IP地址:{}，客户端提交参数：{}", ServiceContext.getHead().clientIp, JSONUtil.toJsonStr(baseReq));
        DeliveryBooking deliveryBooking = baseReq.getData();
        if (deliveryBooking == null) {
            throw new ServiceException("请求参数不完整");
        }
        //验证必要的请求参数
        if (bindingResult.hasErrors()) {
            throw new ServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        //验证票号和姓名
        for (DeliveryTicketInfo deliveryTicketInfo : deliveryBooking.getDeliveryTicketInfoList()) {
            if (!deliveryTicketInfo.getCheckFlag().equals(EncoderHandler.encodeByMD5(deliveryTicketInfo.getTicketNo()
                    + deliveryTicketInfo.getPassName()))) {
                throw new ServiceException("客票与姓名不匹配！");
            }
        }
        TripCert tripCert = JSONUtil.toBean(tripCertConfig.tripCert, new TypeToken<TripCert>() {
        }.getType(), true);
        if (deliveryBooking.getDeliveryFee() == null || tripCert.getShipPrice() != deliveryBooking.getDeliveryFee().doubleValue()) {
            throw new ServiceException("价格金额有误！");
        }
        //提交订单
        RequestData<CouponProductBuyRequestDto> requestData = getRequestData(baseReq, deliveryBooking);
        ResponseData<CouponProductBuyResponseDto> responseData = benefitOrderClient.buyCouponProduct(requestData);

        if (responseData == null || responseData.getData() == null
                || !UnifiedOrderResultEnum.SUCCESS.name().equalsIgnoreCase(responseData.getCode())) {
            throw ServiceException.fail(Optional.ofNullable(responseData).map(ResponseData::getMessage).orElse("系统繁忙"));
        }
        CouponProductBuyResponseDto couponProductBuyResponse = responseData.getData();
        resp.setMessage(WSEnum.SUCCESS.getResultInfo());
        SubmitTripOrderResp orderInfo = new SubmitTripOrderResp();
        orderInfo.orderNo = couponProductBuyResponse.getOrderNo();
        orderInfo.channelOrderNo = couponProductBuyResponse.getChannelOrderNo();
        resp.setData(orderInfo);
        return resp;
    }

    @NotNull
    private RequestData<CouponProductBuyRequestDto> getRequestData(RequestData<DeliveryBooking> baseReq, DeliveryBooking deliveryBooking) {
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        //转换请求参数
        RequestData<CouponProductBuyRequestDto> requestData = new RequestData<>();
        CouponProductBuyRequestDto couponProductBuyRequest = createCouponProBuyReq(deliveryBooking, channelInfo.getChannelCode(), channelInfo.getUserNo());
        couponProductBuyRequest.setOrderRequestIp(ServiceContext.getHead().clientIp);
        requestData.setData(couponProductBuyRequest);
        requestData.setChannelNo(baseReq.getChannelNo());
        requestData.setFfpNo(baseReq.getFfpNo());
        requestData.setFfpId(baseReq.getFfpId());
        requestData.setOriginIp(baseReq.getOriginIp());
        return requestData;
    }


    @Override
    public ResponseData<List<DeliveryTicketInfo>> checkTicket(RequestData<DeliveryTicketInfoUser> baseReq, BindingResult bindingResult, HttpServletRequest request) {
        DeliveryTicketInfoUser deliveryTicketInfoUser = baseReq.getData();
        if (deliveryTicketInfoUser == null) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        ResponseData<List<DeliveryTicketInfo>> resp = new ResponseData<>();
        // 验证必要的请求参数
        if (bindingResult.hasErrors()) {
            resp.setCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setMessage(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        String clientIp = ServiceContext.getHead().clientIp;
        String channelCode = channelInfo.getChannelCode();
        String userNo = channelInfo.getUserNo();
        String ffpCardNo = ServiceContext.getHead().ffpNo;
        //记录手机IP对应关系
        Map<String, Object> info = new HashMap<>();
        info.put("ffpcardNo", ffpCardNo);
        info.put("ip", clientIp);
        commonService.saveDayInfo("TRIP_TICKET", clientIp, ffpCardNo, info, DateUtil.addOrLessDay(new Date(), 1));
        //客票操作次数检验
        LimitCount limitCount = mainPageConfig.getTripTicketIpLimit();
        boolean chkFlag = commonService.chkCountOperation(clientIp, "TRIP_TICKET", "", limitCount, resp);
        if (!chkFlag) {
            commonService.countOperation(clientIp, "TRIP_TICKET", "", limitCount);
            return resp;
        }
        //提取客票信息
        String passName = deliveryTicketInfoUser.getPassName().toUpperCase();
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest("10", channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
        ticketInfoRequest.setTicketNo(deliveryTicketInfoUser.getTicketNo());
        ticketInfoRequest.setCertType("TN");
        ticketInfoRequest.setPassengerName(passName);
        //提取客票信息
        try {
            TicketListInfoResponse ticketListInfoResponse = orderService.listTicketInfo(ticketInfoRequest, headMap);
            if (ticketListInfoResponse == null) {
                throw MultiLangServiceException.fail("未查询到匹配的行程数据");
            }
            // 接口返回失败
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                resp.setCode(WSEnum.ERROR.getResultCode());
                resp.setMessage(ticketListInfoResponse.getErrorInfo());
                return resp;
            }
            if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
                throw MultiLangServiceException.fail("无符合条件的客票信息！");
            }
            // 查询客票本地信息
            PtOwnSaleRequestDto ptOwnSaleRequestDto = new PtOwnSaleRequestDto("10", channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), "");
            String ticketNo = deliveryTicketInfoUser.getTicketNo();
            if (!ticketNo.contains("-")) {
                ticketNo = deliveryTicketInfoUser.getTicketNo().substring(0, 3) + "-" + deliveryTicketInfoUser.getTicketNo().substring(3);
            }
            ptOwnSaleRequestDto.setTicketNo(ticketNo);
            String curDateStr = DateUtil.getCurrentDateTimeStr();
            Date curDate = DateUtil.toDate(curDateStr, "yyyy-MM-dd HH:mm");
            // 自营渠道划分
            String allowChannelCodes = mainPageConfig.getTripCertChannelCodes();
            String[] channelCodes = allowChannelCodes.split(",");
            List<String> channelCodeList = new ArrayList<>(Arrays.asList(channelCodes));
            ptOwnSaleRequestDto.setChannelCodes(channelCodeList);
            PtOwnSaleResponseDto ptOwnSaleResponseDto = orderService.queryOwnSaleTicketInfo(ptOwnSaleRequestDto, headMap);
            // 调用接口查询出票渠道信息
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOwnSaleResponseDto.getResultCode())) {
                resp.setCode(WSEnum.ERROR.getResultCode());
                resp.setMessage(ptOwnSaleResponseDto.getErrorInfo());
                return resp;
            }
            // 是否可开具
            if (!ptOwnSaleResponseDto.isOwnSale()) {
                throw MultiLangServiceException.fail("该机票不在本平台受理范围，请至购票地申领报销凭证！");
            }
            // 是否本人订单
            boolean ownOrder = baseReq.getFfpNo().equals(ptOwnSaleResponseDto.getFfpCardNo()) || (AirCompanyEnum.HO.getAirCompanyCode() + baseReq.getFfpNo()).equals(ptOwnSaleResponseDto.getFfpCardNo());
            // 操作人会员信息
            Set<String> operatorName = Sets.newHashSet();
            Set<String> operatorCert = Sets.newHashSet();
            String operatorBirthDay = null;
            boolean operatorRealName = false;
            boolean operatorTicket = false;
            // 非本人订单查询本人会员信息
            if (!ownOrder) {
                // 查询会员信息
                String[] items = new String[]{MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
                PtMemberDetail ptMemberDetail = memberService.memberDetail(baseReq.getChannelNo(), baseReq.getFfpNo(), items);
                MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
                // 检查姓名是否匹配
                boolean nameMatch = false;
                // 中文姓名匹配
                if (StringUtils.isNoneBlank(basicInfo.getCLastName(), basicInfo.getCFirstName())) {
                    String cnName = basicInfo.getCLastName() + basicInfo.getCFirstName();
                    operatorName.add(cnName);
                }
                // 英文姓名匹配
                if (!nameMatch && StringUtils.isNoneBlank(basicInfo.getCLastName(), basicInfo.getCFirstName())) {
                    String enName = basicInfo.getELastName() + "/" + basicInfo.getEFirstName();
                    operatorName.add(enName);
                }
                operatorBirthDay = DateUtil.timeStampToDateStr(basicInfo.getBirthDay(), DateUtil.YYYY_MM_DD_PATTERN);
                if (CollectionUtils.isNotEmpty(ptMemberDetail.getCertificateInfo())) {
                    ptMemberDetail.getCertificateInfo().forEach(certInfo -> operatorCert.add(certInfo.getCertificateNumber()));
                }
                operatorRealName = CrmUtil.toCheckRealNameStatus(ptMemberDetail.getRealVerifyInfos());
            }
            // 是否国际行程 默认国内行程
            String segmentType = TripTypeEnum.TRIP_TYPE_D.getCode();
            // 是否9月30日（不含）之后航班
            boolean isAfter20250930 = false;
            // 行程信息是否匹配
            boolean segmentMatchFlag = false;
            for (PtIBETicketInfo ticketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                // 非本人订单 判断是否是本人行程
                if (!ownOrder) {
                    // 检查姓名是否匹配
                    boolean nameMatch = false;
                    for (String name : operatorName) {
                        if (nameMatch) {
                            break;
                        }
                        nameMatch = NameUtils.patternName(name, ticketInfo.getPassengerName());
                    }
                    // 获取乘机证件信息
                    IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfo(ticketInfo.getIdentityInfoList());
                    // 检查证件号是否匹配
                    boolean certMatch = false;
                    for (String certNo : operatorCert) {
                        if (certMatch) {
                            break;
                        }
                        certMatch = identityInfo.getIdNo().equals(certNo);
                    }
                    // NI类型无需校验出生日期 出生日期用证件号匹配即可
                    boolean birthDayMatch = "NI".equals(identityInfo.getIdType()) ||
                            (null != identityInfo.getBirthdate() && DateUtil.dateToString(identityInfo.getBirthdate(), DateUtil.YYYY_MM_DD_PATTERN).equals(operatorBirthDay));
                    // 是否是操作人本人客票 姓名匹配、证件匹配、出生日期匹配
                    operatorTicket = nameMatch && certMatch && birthDayMatch;
                }
                for (PtSegmentInfo segmentDetail : ticketInfo.getSegmentInfoList()) {
                    // 航班号不匹配
                    if (!deliveryTicketInfoUser.getFlightNo().equalsIgnoreCase(segmentDetail.getFlightNo())) {
                        continue;
                    }
                    ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(segmentDetail.getDepAirportCode());
                    // 出发机场信息不存在
                    if (depAirportInfo == null) {
                        log.error("出发机场信息不存在，机场编码：{}", segmentDetail.getDepAirportCode());
                        continue;
                    }
                    ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(segmentDetail.getArrAirportCode());
                    // 到达机场信息不存在
                    if (arrAirportInfo == null) {
                        log.error("到达机场信息不存在，机场编码：{}", segmentDetail.getDepAirportCode());
                        continue;
                    }
                    Segment segment = cacheService.getSegment(deliveryTicketInfoUser.getDepCityCode(), deliveryTicketInfoUser.getArrCityCode());
                    // 航段类型国际 设置航段类型为当前航段
                    if (TripTypeEnum.TRIP_TYPE_I.getCode().equals(segment.getSegmentType())) {
                        segmentType = segment.getSegmentType();
                    }
                    String flightDate = segmentDetail.getDepTime().substring(0, 10);
                    Date flightDateDate = DateUtil.toDate(flightDate, DateUtil.YYYY_MM_DD_PATTERN);
                    Date thresholdDate = DateUtil.toDate("2025-09-30", DateUtil.YYYY_MM_DD_PATTERN);
                    // 航班日期在2025-09-30之后
                    if (flightDateDate.after(thresholdDate)) {
                        isAfter20250930 = true;
                    }
                    // 入参航班日期 且 航班日期不匹配
                    if (StringUtils.isNotBlank(deliveryTicketInfoUser.getFlightDate()) &&
                            !deliveryTicketInfoUser.getFlightDate().equalsIgnoreCase(flightDate)) {
                        continue;
                    }
                    // 入参出发机场
                    if (StringUtils.isNotBlank(deliveryTicketInfoUser.getDepCityCode())) {
                        // 入参出发城市编码 与 实际成行出发城市编码不匹配
                        if (!deliveryTicketInfoUser.getDepCityCode().equalsIgnoreCase(depAirportInfo.getCityCode())) {
                            continue;
                        }
                    }
                    // 入参到达机场
                    if (StringUtils.isNotBlank(deliveryTicketInfoUser.getDepCityCode())) {
                        // 入参到达城市编码 与 实际成行到达城市编码不匹配
                        if (!deliveryTicketInfoUser.getArrCityCode().equalsIgnoreCase(arrAirportInfo.getCityCode())) {
                            continue;
                        }
                    }
                    // 标记行程匹配
                    segmentMatchFlag = true;
                }
            }
            if (!segmentMatchFlag) {
                throw MultiLangServiceException.fail("未查询到匹配的行程数据");
            }
            // 国内行程 且 航班日期在9月30日之后 提示不再提供纸质行程单
            if (TripTypeEnum.TRIP_TYPE_D.getCode().equals(segmentType) && isAfter20250930) {
                throw MultiLangServiceException.fail(CommonErrorCode.PAPERLESS_ITINERARY);
            }
            // 非操作人本人订单
            if (!ownOrder) {
                // 非操作人本人客票
                if (!operatorTicket) {
                    throw MultiLangServiceException.fail("乘机人与会员信息不一致，请本人操作。");
                }
                // 未实名
                if (!operatorRealName) {
                    throw MultiLangServiceException.fail(CommonErrorCode.NO_REAL_NAME);
                }
            }
            List<DeliveryTicketInfo> deliveryTicketInfoList = Lists.newArrayList();
            //客票查询计数操作
            commonService.countOperation(clientIp, "TRIP_TICKET", "", limitCount);
            for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                //团队票检验  签注项中有GV标志，认为是团体票
                if (!StringUtil.isNullOrEmpty(ptIBETicketInfo.getSigningInfo()) && ptIBETicketInfo.getSigningInfo().contains("GV")) {
                    throw MultiLangServiceException.fail("团队票不支持邮寄行程单！");
                }
                if ("Y".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted()) || "true".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted())) {
                    throw MultiLangServiceException.fail("您已打印过行程单，请勿重复提交");
                }
                //行程结束检验
                int usedCount = 0, openCount = 0;
                //最后的使用日期
                String lastFlightArrTime = curDateStr;
                ApiAirPortInfoDto airPortInfo = null;
                for (PtSegmentInfo ptSegmentInfo : ptIBETicketInfo.getSegmentInfoList()) {
                    if (TicketStateEnum.USED_FLOWN.equals(ptSegmentInfo.getTicketStatus())) {
                        usedCount++;
                        lastFlightArrTime = ptSegmentInfo.getArrTime();
                        airPortInfo = cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                    }
                    if (TicketStateEnum.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()) || TicketStateEnum.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus())) {
                        openCount++;
                    }
                }
                if (usedCount == 0 && openCount == 0) {
                    throw MultiLangServiceException.fail("当前客票不符合邮寄条件！");
                }
                if (openCount > 0) {
                    throw MultiLangServiceException.fail("您尚未出行无法打印行程单！");
                }
                if (usedCount == 0) {
                    throw MultiLangServiceException.fail("无有效的出行行程！");
                }
                Date curArrDateTime = DateUtil.toTargetDate(lastFlightArrTime,
                        Optional.ofNullable(airPortInfo).map(ApiAirPortInfoDto::getCityCode)
                                .map(i -> cacheService.getLocalCity(i))
                                .map(ApiCityInfoDto::getCityTimeZone)
                                .orElse("8"),
                        "8");
                Date endDate = DateUtil.addOrLessDay(curArrDateTime, 28);
                if (curDate.before(curArrDateTime)) {
                    throw MultiLangServiceException.fail("请于航班结束次日提交行程单申请");
                }
                if (curDate.after(endDate)) {
                    throw MultiLangServiceException.fail("客票已超过有效期，请联系客服办理");
                }

                DeliveryTicketInfo deliveryTicketInfo = new DeliveryTicketInfo();
                ticketNo = deliveryTicketInfoUser.getTicketNo().contains("-") ? deliveryTicketInfoUser.getTicketNo().replace("-", "") : deliveryTicketInfoUser.getTicketNo();
                deliveryTicketInfo.setTicketNo(ticketNo);
                deliveryTicketInfo.setPassName(deliveryTicketInfoUser.getPassName());
                deliveryTicketInfo.setCheckFlag(EncoderHandler.encodeByMD5(ticketNo + deliveryTicketInfoUser.getPassName()));
                //航段信息处理
                List<PassageInfo> passageInfoList = new ArrayList<>();
                ptIBETicketInfo.getSegmentInfoList().forEach(ptSegmentInfo -> {
                    PassageInfo passageInfo = new PassageInfo();
                    passageInfo.setFlightNo(ptSegmentInfo.getFlightNo());
                    passageInfo.setDepTime(ptSegmentInfo.getDepTime());
                    passageInfo.setArrTime(ptSegmentInfo.getArrTime());
                    passageInfo.setDepAirPortCode(ptSegmentInfo.getDepAirportCode());

                    passageInfo.setDepAirPortName(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(ApiAirPortInfoDto::getAirPortName).orElse(null));
                    passageInfo.setArrAirPortCode(ptSegmentInfo.getArrAirportCode());
                    passageInfo.setArrAirPortName(
                            Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(ApiAirPortInfoDto::getAirPortName).orElse(null)
                    );
                    //城市名称
                    passageInfo.setDepCityName(
                            Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(ApiAirPortInfoDto::getCityName).orElse(null));
                    passageInfo.setArrCityName(
                            Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(ApiAirPortInfoDto::getCityName).orElse(null));
                    passageInfo.setDepTerminal(FlightUtil.formatTerminal(Optional.ofNullable(ptSegmentInfo.getDepAirportTerminal()).orElse(null))
                    );
                    passageInfo.setArrTerminal(
                            FlightUtil.formatTerminal(Optional.ofNullable(ptSegmentInfo.getArrAirportTerminal()).orElse(null)));
                    passageInfo.setDepCityCode(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(ApiAirPortInfoDto::getCityCode).orElse(null));
                    passageInfo.setArrCityCode(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(ApiAirPortInfoDto::getCityCode).orElse(null));
                    passageInfoList.add(passageInfo);
                });
                deliveryTicketInfo.setPassageInfoList(passageInfoList);
                deliveryTicketInfoList.add(deliveryTicketInfo);

                //处理改期客票（判断客票为已改期且符合行程单打印标准的客票，若原票未打印行程单，则前端取改期后客票和改期前原票展示；若原票已打印行程单，则仅展示改期后客票）
                if (StringUtils.isNotBlank(ptIBETicketInfo.getExchangeInfo())) {
                    Pattern tickectPattern = Pattern.compile(PatternCommon.TICKET_NO);
                    Matcher tickectMatcher = tickectPattern.matcher(ptIBETicketInfo.getExchangeInfo());
                    if (tickectMatcher.matches()) {
                        ticketInfoRequest.setTicketNo(ptIBETicketInfo.getExchangeInfo());
                        ticketInfoRequest.setPassengerName(passName);
                        processExchangeInfo(ticketInfoRequest, headMap, deliveryTicketInfoUser, deliveryTicketInfoList, channelCode, userNo);
                    }
                }
            }
            // 不存在符合开具的客票信息
            if (CollectionUtils.isEmpty(deliveryTicketInfoList)) {
                throw MultiLangServiceException.fail("无符合条件的客票信息！");
            }
            resp.setData(deliveryTicketInfoList);
            return resp;
        } catch (MultiLangServiceException me) {
            throw me;
        } catch (ServiceException se) {
            String exMsg = se.getError().getMessage();
            log.error("邮寄行程单客票验证异常:{}, 异常信息：", exMsg, se);
            throw MultiLangServiceException.fail(exMsg);
        } catch (Exception e) {
            String exMsg = WSEnum.ERROR.getResultInfo();
            log.error("邮寄行程单客票验证异常:{} 异常信息：", exMsg, e);
            throw MultiLangServiceException.fail(exMsg);
        }
    }

    /**
     * 下单请求参数
     *
     * @param deliveryBooking
     * @param channelCode
     * @param userNo
     * @return
     */
    private CouponProductBuyRequestDto createCouponProBuyReq(DeliveryBooking deliveryBooking, String channelCode, String userNo) {
        String ffpId = ServiceContext.getHead().ffpId;
        String ffpCardNo = ServiceContext.getHead().ffpNo;

        CouponProductBuyRequestDto couponProductBuyRequest = new CouponProductBuyRequestDto();
        couponProductBuyRequest.version = "10";
        couponProductBuyRequest.channelCode = "MOBILE";
        couponProductBuyRequest.setUserNo(userNo);
        couponProductBuyRequest.setChannelOrderNo(deliveryBooking.getChannelOrderNo());
        couponProductBuyRequest.setFfpId(ffpId);
        couponProductBuyRequest.setFfpCardNo(ffpCardNo);
        //邮寄行程单固定为MailTravel
        couponProductBuyRequest.setBuyType(VoucherTypesEnum.MAILTRAVEL.getCode());
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.MailTravelProduct mailTravelProduct = new MailTravelProduct();
        DeliveryAddress deliveryAddress = deliveryBooking.getDeliveryAddress();
        mailTravelProduct.setRecipientsAddress(deliveryAddress.getDeliverToProvinceName() + deliveryAddress.getDeliverToCityName() + deliveryAddress.getDetailAddress());
        mailTravelProduct.setRecipientsName(deliveryAddress.getLinker());
        mailTravelProduct.setRecipientsPhone(deliveryAddress.getHandphoneNumber());
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary> passengerItineraryList = new ArrayList<>();
        deliveryBooking.getDeliveryTicketInfoList().stream().forEach(deliveryTicketInfo -> {
            deliveryTicketInfo.getPassageInfoList().stream().forEach(passageInfo -> {
                com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary passengerItinerary = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary();
                passengerItinerary.setTicketNo(deliveryTicketInfo.getTicketNo());
                passengerItinerary.setPassengerName(deliveryTicketInfo.getPassName());
                passengerItinerary.setFlightNo(passageInfo.getFlightNo());
                passengerItinerary.setDeptAirport(passageInfo.getDepAirPortCode());
                passengerItinerary.setArrAirport(passageInfo.getArrAirPortCode());
                passengerItinerary.setDepDateTime(passageInfo.getDepTime());
                passengerItinerary.setArrDateTime(passageInfo.getArrTime());
                passengerItineraryList.add(passengerItinerary);
            });
        });
        mailTravelProduct.setPassengerItineraryList(passengerItineraryList);
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo> bookProductInfoList = new ArrayList<>();
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo productInfo = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo();
        productInfo.setProductName("邮寄行程单");
        productInfo.setProductType(VoucherTypesEnum.MAILTRAVEL.getCode());
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo> bookResourceInfos = CollUtil.newArrayList();
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo bookResourceInfo = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo();
        bookResourceInfo.setResourceType(VoucherTypesEnum.MAILTRAVEL.getCode());
        bookResourceInfo.setMailTravelProduct(mailTravelProduct);
        bookResourceInfos.add(bookResourceInfo);

        productInfo.setResources(bookResourceInfos);
        bookProductInfoList.add(productInfo);
        couponProductBuyRequest.setProducts(bookProductInfoList);
        couponProductBuyRequest.setTotalPrice(new BigDecimal(deliveryBooking.getDeliveryFee().toString()));
        couponProductBuyRequest.setPayAmount(new BigDecimal(deliveryBooking.getDeliveryFee().toString()));
        couponProductBuyRequest.setUseScore(0);
        couponProductBuyRequest.setLinkerMobile(deliveryBooking.getDeliveryAddress().getHandphoneNumber());
        couponProductBuyRequest.setLinker(deliveryBooking.getDeliveryAddress().getLinker());
        return couponProductBuyRequest;
    }

    //处理改期客票信息
    private void processExchangeInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap,
                                     DeliveryTicketInfoUser deliveryTicketInfoUser, List<DeliveryTicketInfo> deliveryTicketInfoList,
                                     String channelCode, String userNo) {
        //提取客票信息
        try {
            TicketListInfoResponse ticketListInfoResponse = orderService.listTicketInfo(ticketInfoRequest, headMap);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                    checkExchangeTicketInfo(deliveryTicketInfoUser, ptIBETicketInfo, headMap, deliveryTicketInfoList, channelCode, userNo);
                }
            }
        } catch (Exception e) {
            log.error("改期原票获取客票信息异常", e);
        }
    }

    private void checkExchangeTicketInfo(DeliveryTicketInfoUser deliveryTicketInfoUser, PtIBETicketInfo ptIBETicketInfo, Map<String, String> headMap,
                                         List<DeliveryTicketInfo> deliveryTicketInfoList, String channelCode, String userNo) {
        //yyyy-MM-dd HH:mm
        String curDateStr = DateUtil.getCurrentDateTimeStr();
        Date curDate = DateUtil.toDate(curDateStr, "yyyy-MM-dd HH:mm");
        //姓名匹配
        String passName = deliveryTicketInfoUser.getPassName().toUpperCase();
        String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*)";//正则表达式
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
        if (matcher.matches()) {
            //自营客票判断
            PtOwnSaleRequestDto ptOwnSaleRequestDto = new PtOwnSaleRequestDto("10", channelCode, userNo, "");
            String ticketNo = ptIBETicketInfo.getTicketNo();
            if (ticketNo.indexOf("-") == -1) {
                ticketNo = ptIBETicketInfo.getTicketNo().substring(0, 3) + "-" + ptIBETicketInfo.getTicketNo().substring(3);
            }
            ptOwnSaleRequestDto.setTicketNo(ticketNo);
            //自营渠道划分
            String allowChannelCodes = mainPageConfig.getTripCertChannelCodes();
            String[] channelCodes = allowChannelCodes.split(",");
            List<String> channelCodeList = new ArrayList<>(Arrays.asList(channelCodes));
            ptOwnSaleRequestDto.setChannelCodes(channelCodeList);
            try {
                PtOwnSaleResponseDto ptOwnSaleResponseDto = orderService.queryOwnSaleTicketInfo(ptOwnSaleRequestDto, headMap);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOwnSaleResponseDto.getResultCode())) {
                    if (!ptOwnSaleResponseDto.isOwnSale()) {
                        return;
                    }
                } else {
                    return;
                }
            } catch (Exception e) {
                log.error("改期原票自营客票判断异常", e);
                return;
            }
            //团队票检验  签注项中有GV标志，认为是团体票
            if (!StringUtil.isNullOrEmpty(ptIBETicketInfo.getSigningInfo()) && ptIBETicketInfo.getSigningInfo().indexOf("GV") > -1) {
                return;
            }
            if ("Y".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted()) || "true".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted())) {
                return;
            }
            //行程结束检验
            int exchangedCount = 0;
            //最后的使用日期
            String lastFlightArrTime = curDateStr;
            ApiAirPortInfoDto airPortInfo = null;
            for (PtSegmentInfo ptSegmentInfo : ptIBETicketInfo.getSegmentInfoList()) {
                if (TicketStateEnum.EXCHANGED.equals(ptSegmentInfo.getTicketStatus())) {
                    exchangedCount++;
                    lastFlightArrTime = ptSegmentInfo.getArrTime();
                    airPortInfo =
                            Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).orElse(null);
                }
            }
            //为测试方便，此处测试时暂时注释
            if (exchangedCount == 0) {
                return;
            }
            Date curArrDateTime = DateUtil.toTargetDate(lastFlightArrTime,
                    Optional.ofNullable(airPortInfo).map(i -> cacheService.getLocalCity(i.getCityCode())).map(ApiCityInfoDto::getCityTimeZone).orElse("8"), "8");
            Date endDate = DateUtil.addOrLessDay(curArrDateTime, 28);
            if (curDate.before(curArrDateTime) || curDate.after(endDate)) {
                return;
            }
            DeliveryTicketInfo deliveryTicketInfo = new DeliveryTicketInfo();
            ticketNo = ticketNo.indexOf("-") > -1 ? ticketNo.replace("-", "") : ticketNo;
            deliveryTicketInfo.setTicketNo(ticketNo);
            deliveryTicketInfo.setPassName(deliveryTicketInfoUser.getPassName());
            deliveryTicketInfo.setCheckFlag(EncoderHandler.encodeByMD5(ticketNo + deliveryTicketInfoUser.getPassName()));
            //航段信息处理
            List<PassageInfo> passageInfoList = new ArrayList<>();
            ptIBETicketInfo.getSegmentInfoList().stream().forEach(ptSegmentInfo -> {
                PassageInfo passageInfo = new PassageInfo();
                passageInfo.setFlightNo(ptSegmentInfo.getFlightNo());
                passageInfo.setDepTime(ptSegmentInfo.getDepTime());
                passageInfo.setArrTime(ptSegmentInfo.getArrTime());
                passageInfo.setDepAirPortCode(ptSegmentInfo.getDepAirportCode());

                passageInfo.setDepAirPortName(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrAirPortCode(ptSegmentInfo.getArrAirportCode());
                passageInfo.setArrAirPortName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getAirPortName()).orElse(null)
                );
                //城市名称
                passageInfo.setDepCityName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrCityName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setDepTerminal(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportTerminal())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrTerminal(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportTerminal())).map(i -> i.getAirPortName()).orElse(null));
                passageInfoList.add(passageInfo);
                passageInfoList.add(passageInfo);
            });
            deliveryTicketInfo.setPassageInfoList(passageInfoList);
            deliveryTicketInfoList.add(deliveryTicketInfo);
        }
    }

}
