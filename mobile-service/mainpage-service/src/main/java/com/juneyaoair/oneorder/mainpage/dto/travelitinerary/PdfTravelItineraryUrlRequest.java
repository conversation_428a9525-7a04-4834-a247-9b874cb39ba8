package com.juneyaoair.oneorder.mainpage.dto.travelitinerary;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class PdfTravelItineraryUrlRequest extends GeetestDto {
    @ApiModelProperty("票号")
    @NotNull(message="票号不能为空")
    private String ticketNo;
    @ApiModelProperty("旅客姓名")
    @NotNull(message="旅客姓名不能为空")
    private String passengerName;

    @ApiModelProperty("航班号")
    @NotNull(message = "航班号不能为空")
    public String flightNo;
}
