package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.horder.dto.electronic.ElectronQueryDTO;
import com.juneyaoair.horder.dto.electronic.base.ElectronicBaseDTO;
import com.juneyaoair.horder.dto.electronic.base.ElectronicBaseResult;
import com.juneyaoair.horder.dto.electronic.post.ElectronicCancelPostDTO;
import com.juneyaoair.horder.dto.electronic.post.ElectronicCheckChangePostDTO;
import com.juneyaoair.horder.dto.electronic.post.ElectronicEmailDto;
import com.juneyaoair.horder.dto.electronic.post.ElectronicPrintPostDTO;
import com.juneyaoair.horder.dto.electronic.result.CancelResult;
import com.juneyaoair.horder.dto.electronic.result.CheckChangeResult;
import com.juneyaoair.horder.dto.electronic.result.ElectronSegment;
import com.juneyaoair.horder.dto.electronic.result.ElectronicInfo;
import com.juneyaoair.horder.dto.electronic.result.EmailResult;
import com.juneyaoair.horder.dto.electronic.result.PrintResult;
import com.juneyaoair.horder.feign.ElectronicFeignClient;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicCancelPostParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicEmailParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicInfoResult;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicPrintPostParam;
import com.juneyaoair.oneorder.mainpage.mapstruct.ElectronicItineraryMapstruct;
import com.juneyaoair.oneorder.mainpage.service.ElectronicItineraryService;
import com.juneyaoair.oneorder.mainpage.util.ElectronicBaseUtil;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 新版电子行程及发票服务
 * @created 2024/5/8 16:07
 */
@Slf4j
@Service
public class ElectronicItineraryServiceImpl implements ElectronicItineraryService {

    @Resource
    private CacheService cacheService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private CommonService commonService;

    @Autowired
    private ElectronicFeignClient electronicFeignClient;

    @Override
    public PageResult<ElectronicInfoResult> queryElectronic(String ffpCardNo, ElectronQueryDTO electronQuery) {
        log.info("查询可开具电子行程单开具清单开始,请求参数：{}", JSON.toJSONString(electronQuery));
        ElectronicBaseResult<PageInfo<? extends ElectronicInfo>> pageInfoResponse = electronicFeignClient.queryElectronic(electronQuery);
        log.info("查询可开具电子行程单开具清单结束,返回结果：{}", JSON.toJSONString(pageInfoResponse));
        PageInfo<? extends ElectronicInfo> pageInfo = ElectronicBaseUtil.getResult(pageInfoResponse, null);
        PageResult<ElectronicInfoResult> pageResult = new PageResult<>();
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotal(pageInfo.getTotal());
        List<ElectronicInfoResult> electronicInfoResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            for (ElectronicInfo orderElectronicInfo : pageInfo.getList()) {
                ElectronicInfoResult electronicInfoResult = ElectronicItineraryMapstruct.MAPPER.getElectronicInfoResult(orderElectronicInfo);
                String signature = SecureUtil.md5(ffpCardNo + electronicInfoResult.getTicketNo().replaceAll("-", ""));
                electronicInfoResult.setSignature(signature.toUpperCase());
                electronicInfoResultList.add(electronicInfoResult);
                List<ElectronSegment> segmentInfoList = orderElectronicInfo.getSegmentInfoList();
                if (CollectionUtils.isEmpty(segmentInfoList)) {
                    continue;
                }
                // 对数据按照航段顺序进行排序
                segmentInfoList = segmentInfoList.stream().sorted(Comparator.comparing(ElectronSegment::getTripSegmentSeq)).collect(Collectors.toList());
                //航班日期
                StringBuilder flightDate = new StringBuilder();
                //航线
                StringBuilder flightRute = new StringBuilder();
                for (int i = 0; i < segmentInfoList.size(); i++) {
                    ElectronSegment electronSegment = segmentInfoList.get(i);
                    ApiCityInfoDto arrCity = cacheService.getLocalCity(electronSegment.getArrCity());
                    if (i > 0) {
                        flightDate.append("/").append(DateUtil.getDateString(electronSegment.getFlightDate()));
                        flightRute.append("-").append(null == arrCity ? "" : arrCity.getCityName());
                    } else {
                        flightDate.append(DateUtil.getDateString(electronSegment.getFlightDate()));
                        ApiCityInfoDto depCity = cacheService.getLocalCity(electronSegment.getDepCity());
                        flightRute.append(null == depCity ? "" : depCity.getCityName()).append("-")
                                .append(null == arrCity ? "" : arrCity.getCityName());
                    }
                }
                electronicInfoResult.setFlightDate(flightDate.toString());
                electronicInfoResult.setFlightRute(flightRute.toString());
            }
        }
        pageResult.setRows(electronicInfoResultList);
        return pageResult;
    }

    @Override
    public PrintResult print(String ffpCardNo, String channelCode, String ip, ElectronicPrintPostParam electronicPrintPostParam) {
        // 校验签名
        checkSign(ffpCardNo, electronicPrintPostParam.getTicketNumber(), electronicPrintPostParam.getSignature());
        String memberName = getMemberName(channelCode, ffpCardNo);
        ElectronicBaseDTO<ElectronicPrintPostDTO> electronicPrintPostBase = ElectronicBaseUtil.createRequest(channelCode, ip, ffpCardNo, memberName, electronicPrintPostParam);
        log.info("电子行程单开具开始,请求参数：{}", JSON.toJSONString(electronicPrintPostBase));
        ElectronicBaseResult<? extends PrintResult> printBaseResult = electronicFeignClient.print(electronicPrintPostBase);
        log.info("电子行程单开具结束,返回结果：{}", JSON.toJSONString(printBaseResult));
        return ElectronicBaseUtil.getResult(printBaseResult, null);
    }

    /**
     * 校验签名
     * @param ffpCardNo
     * @param ticketNumber
     * @param signature
     */
    private void checkSign(String ffpCardNo, String ticketNumber, String signature) {
        String sign = SecureUtil.md5(ffpCardNo + ticketNumber.replaceAll("-", "")).toUpperCase();
        if (!sign.equals(signature)) {
            log.info("电子行程单开具参数校验不通过,会员卡号：{} 票号：{} 入参签名信息：{} 后台签名信息：{}", ffpCardNo, ticketNumber, signature, sign);
            throw ServiceException.fail("签名校验不通过");
        }
    }

    /**
     * 基于会员卡号获取会员姓名
     * @param channelCode
     * @param ffpCardNo
     * @return
     */
    private String getMemberName(String channelCode, String ffpCardNo) {
        PtMemberDetail ptMemberDetail = memberService.memberDetail(channelCode, ffpCardNo, new String[]{MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName});
        if (!CollectionUtils.isEmpty(ptMemberDetail.getCertificateInfo())) {
            Optional<MemberCertificateSoaModelV2> certInfo = ptMemberDetail.getCertificateInfo().stream().filter(MemberCertificateSoaModelV2::isVerify).findFirst();
            if (certInfo.isPresent()) {
                return CRMReqUtil.getMemberName(ptMemberDetail.getBasicInfo());
            }
        }
        return null;
    }

    @Override
    public void rePush(String ffpCardNo, String channelCode, String ip, ElectronicEmailParam electronicEmailParam) {
        // 校验签名
        checkSign(ffpCardNo, electronicEmailParam.getTicketNumber(), electronicEmailParam.getSignature());
        // 不存在文件格式 默认.ofd
        if (CollectionUtils.isEmpty(electronicEmailParam.getTypeList())) {
            electronicEmailParam.setTypeList(Lists.newArrayList(".ofd"));
        }
        ElectronicBaseDTO<ElectronicEmailDto> electronicRePushPostBase = ElectronicBaseUtil.createRequest(channelCode, ip, ffpCardNo, null, electronicEmailParam);
        log.info("电子行程单手动发送邮件开始,请求参数：{}", JSON.toJSONString(electronicRePushPostBase));
        ElectronicBaseResult<EmailResult> rePushBaseResult = electronicFeignClient.send(electronicRePushPostBase);
        log.info("电子行程单手动发送邮件结束,返回结果：{}", JSON.toJSONString(rePushBaseResult));
        EmailResult emailResult = ElectronicBaseUtil.getResult(rePushBaseResult, null);
        if (emailResult.isIfSend()) {
            return;
        }
        throw ServiceException.fail("电子行程单重推失败！");
    }

    @Override
    public CancelResult cancel(String ffpCardNo, String channelCode, String ip, ElectronicCancelPostParam electronicCancelPostParam) {
        // 校验签名
        checkSign(ffpCardNo, electronicCancelPostParam.getTicketNumber(), electronicCancelPostParam.getSignature());
        String memberName = getMemberName(channelCode, ffpCardNo);
        // 电子行程单冲红
        ElectronicCancelPostDTO electronicCancelPost = new ElectronicCancelPostDTO();
        electronicCancelPost.setTicketNumber(electronicCancelPostParam.getTicketNumber());
        ElectronicBaseDTO<ElectronicCancelPostDTO> electronicCancelBase = ElectronicBaseUtil.createRequest(channelCode, ip, ffpCardNo, memberName, electronicCancelPost);
        log.info("电子行程单冲红接口开始,请求参数：{}", JSON.toJSONString(electronicCancelBase));
        ElectronicBaseResult<? extends CancelResult> electronicBaseResult = electronicFeignClient.cancel(electronicCancelBase);
        log.info("电子行程单冲红接口结束,返回结果：{}", JSON.toJSONString(electronicBaseResult));
        return ElectronicBaseUtil.getResult(electronicBaseResult, null);
    }

    @Override
    public CheckChangeResult checkChange(RequestData requestData, ElectronicCheckChangePostDTO checkChangePost) {
        ElectronicBaseDTO<ElectronicCheckChangePostDTO> electronicCheckChangePostBase = ElectronicBaseUtil.createRequest(requestData.getChannelNo(), requestData.getOriginIp(), requestData.getFfpNo(), null, checkChangePost);
        log.info("检查行程单是否拆分开具接口开始,请求参数：{}", JSON.toJSONString(electronicCheckChangePostBase));
        ElectronicBaseResult<CheckChangeResult> electronicBaseResult = electronicFeignClient.checkChange(electronicCheckChangePostBase);
        log.info("检查行程单是否拆分开具接口结束,返回结果：{}", JSON.toJSONString(electronicBaseResult));
        return ElectronicBaseUtil.getResult(electronicBaseResult, null);
    }

}
