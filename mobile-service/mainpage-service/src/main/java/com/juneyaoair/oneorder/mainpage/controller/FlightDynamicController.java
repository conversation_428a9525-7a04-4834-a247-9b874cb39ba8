package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.airLine.AttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.CancelAttentionFlightParam;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.flight.dto.FlightDynamicParam;
import com.juneyaoair.oneorder.mainpage.service.IAttentionFlightService;
import com.juneyaoair.oneorder.mainpage.service.IFlightService;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/13 9:30
 */
@Api(value = "FlightDynamicController", tags = "航班动态服务")
@RequestMapping
@RestController
@Slf4j
public class FlightDynamicController extends BaseController {
    @Autowired
    private IFlightService flightService;
    @Autowired
    private IAttentionFlightService addAttentionFlight;
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;

    @ApiLog
    @ApiOperation(value = "查询航班动态", notes = "查询航班动态")
    @PostMapping("/queryFlightDynamicInfo")
    public ResponseData<List<FlightInfoDynamicDto>> queryFlightDynamicInfo(@RequestBody @Validated RequestDataDto<FlightDynamicParam> requestData, BindingResult bindingResult, HttpServletRequest request){
        FlightDynamicParam flightDynamicParam = requestData.getData();
        checkParam(requestData,bindingResult);
        initBizDto(flightDynamicParam,request);
        return ResponseData.suc(flightService.searchFlightDynamicsInfo(requestData.getFfpId(),requestData.getFfpNo(),requestData.getData()));
    }

    @ApiOperation(value = "查询关注航班列表", notes = "查询关注航班列表")
    @PostMapping("/queryAttentionFlightList")
    public ResponseData<List<FlightInfoDynamicDto>> queryAttentionFlightList(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult,HttpServletRequest request){
        checkBaseParam(bindingResult);
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(addAttentionFlight.queryAttentionFlightList(requestData, bizDto));
    }

    @ApiOperation(value = "新增航班关注", notes = "新增航班关注")
    @PostMapping("/addAttentionFlight")
    public com.juneyaoair.flightbasic.commondto.ResponseData addAttentionFlight(@RequestBody @Validated com.juneyaoair.flightbasic.commondto.RequestData<AttentionFlightParam> requestData, BindingResult bindingResult) {
        checkBaseParam(bindingResult);
        return addAttentionFlight.addAttentionFlight(requestData);
    }

    @ApiOperation(value = "取消航班关注", notes = "取消航班关注")
    @PostMapping("/cancelAttentionFlight")
    public com.juneyaoair.flightbasic.commondto.ResponseData cancelAttentionFlight(@RequestBody @Validated com.juneyaoair.flightbasic.commondto.RequestData<CancelAttentionFlightParam> requestData, BindingResult bindingResult) {
        checkBaseParam(bindingResult);
        return addAttentionFlight.cancelAttentionFlight(requestData);
    }

}
