package com.juneyaoair.oneorder.mainpage.service;

import com.github.pagehelper.PageInfo;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.api.index.cobrandcreditcard.CoBrandCreditCardAdBO;
import com.juneyaoair.flightbasic.version.response.VersionDto;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.flight.dto.FlightNotice;
import com.juneyaoair.oneorder.flight.dto.FlightReminder;
import com.juneyaoair.oneorder.mainpage.dto.AirportInfoDto;
import com.juneyaoair.oneorder.mainpage.dto.homepage.GetWechatQRCodeBffReq;
import com.juneyaoair.oneorder.mainpage.dto.homepage.GetWechatQRCodeBffResp;
import com.juneyaoair.oneorder.mainpage.dto.homepage.MallProductRequest;
import com.juneyaoair.oneorder.mainpage.dto.homepage.MallProductResponse;
import com.juneyaoair.oneorder.mainpage.dto.homepage.RotationChart;
import com.juneyaoair.oneorder.mainpage.dto.homepage.RotationChartReq;

import java.util.List;

/**
 * <AUTHOR>
 * @description 首页服务处理
 * @date 2023/6/19 11:21
 */
public interface IHomeService {
    /**
     * 查询数据版本信息
     * @param requestData
     * @return
     */
    List<VersionDto> queryDataVersion(RequestDataDto requestData);

    /**
     * 查询航班公告提醒
     * @param requestData
     * @return
     */
    List<FlightReminder> queryFlightNotice(RequestDataDto<FlightNotice> requestData);

    /**
     * 获取旅游度假产品
     * @return
     */
    String getPackageAd();

    /**
     * 获取轮播图配置
     * @param data
     * @return
     */
    List<RotationChart> queryRotationChart(RequestDataDto<RotationChartReq> data);

    /**
     * 获取公司公告披露细信息
     * @param bizDto
     * @param advertisementParam
     * @return
     */
    PageInfo<AdvertisementDto> queryAdvertisementListByPage(BizDto bizDto, AdvertisementParam advertisementParam);

    /**
     * 服务帮助-机场信息
     * @return
     */
    List<AirportInfoDto> queryAllAirportInfo(BizDto bizDto);

    GetWechatQRCodeBffResp getSuccessResponseData(RequestDataDto<GetWechatQRCodeBffReq> req);

    List<CoBrandCreditCardAdBO> getCreditCardAd(BizDto bizDto);

    /**
     * 查询积分商城商品
     *
     * @param request 请求参数
     * @return 商品响应
     */
    MallProductResponse queryMallProducts(MallProductRequest request,BizDto bizDto);
}
