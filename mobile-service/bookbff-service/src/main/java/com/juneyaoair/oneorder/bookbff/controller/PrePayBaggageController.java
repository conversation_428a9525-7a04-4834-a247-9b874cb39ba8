package com.juneyaoair.oneorder.bookbff.controller;


import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.bookbff.dto.QueryFlightReq;
import com.juneyaoair.oneorder.bookbff.service.DesensitizationService;
import com.juneyaoair.oneorder.bookbff.service.ScoreUseRuleService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.enums.PlatFormEnum;
import com.juneyaoair.oneorder.common.util.FraudApiInvoker;
import com.juneyaoair.oneorder.dto.prepaybaggage.*;
import com.juneyaoair.oneorder.dto.salecoupon.product.PrePayProductInfo;
import com.juneyaoair.oneorder.fegin.PrePayBaggageClient;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.prepaybaggage.PrePayBaggageCreateOrderResp;
import com.juneyaoair.oneorder.order.feign.BenefitOrderClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.mobile.exception.util.AESTool;
import com.juneyaoair.oneorder.tools.utils.IpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@RequestMapping("/prePayBaggage")
@RestController
@Api(value = "PrePayBaggageController", tags = "行前-额外行李（预付费行李）")
public class PrePayBaggageController {

    @Value("${prePayBaggageQuerySegmentApi.enabled:false}")
    private boolean enableQueryPrepaySegment;
    @Resource
    public PrePayBaggageClient prePayBaggageClient;
    @Resource
    public BenefitOrderClient benefitOrderClient;
    @Resource
    private IGeetestService geetestService;
    @Resource
    private ScoreUseRuleService scoreUseRuleService;
    @Resource
    private FraudApiInvoker fraudApiInvoker;
    @Resource
    private DesensitizationService desensitizationService;

    @ApiOperation(value = "预付费行李-查询航段信息", notes = "根据票号,或证件号查询航段")
    @PostMapping(value = "/queryFlight", produces = {"application/json;charset=UTF-8"})
    public ResponseData<QuerySegmentResponse> querySegment(@RequestBody @Validated RequestData<QueryFlightReq> req, BindingResult bindingResult, HttpServletRequest request) {
        if (SceneEnum.getEnumByCode(
                req.getData().getScene()) != SceneEnum.TICKET_CHECK) {
            ServiceException.fail("验证失败");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        HashMap<String, String> param = new HashMap<>();
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", IpUtils.getIpAddr(request)); //网站用户id
        param.put("client_type", req.getData().getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ServiceContext.getHead().clientIp); //传输用户请求验证时所携带的IP
        geetestService.validate(req.getData().getScene(), req.getData(), param);

        RequestData<QueryPrepaymentBaggageSegmentReq> requestDtoData = new RequestData<>();
        requestDtoData.setData(req.getData().toQueryPrepaymentBaggageSegmentReq());
        if (enableQueryPrepaySegment) {
            ResponseData<QuerySegmentResponse> resp = prePayBaggageClient.querySegment(requestDtoData);
            // 脱敏
            return desensitizationService.prepaymentBaggageCertNoDesensitization(resp);
        }
        return ResponseData.success();
    }

    @ApiOperation(value = "预付费行李-查询我的行程", notes = "根据用户信息查询航段")
    @PostMapping(value = "/queryMyItinerary", produces = {"application/json;charset=UTF-8"})
    public ResponseData<QuerySegmentResponse> myItinerary(@RequestBody @Validated RequestData req) {
        ResponseData<QuerySegmentResponse> resp = prePayBaggageClient.myItinerary(req);
        // 脱敏
        return desensitizationService.prepaymentBaggageCertNoDesensitization(resp);
    }

    @ApiOperation(value = "预付费行李-查询行李价格", notes = "根据出发地点、到达地、起飞时间查询")
    @PostMapping(value = "/queryPrice", produces = {"application/json;charset=UTF-8"})
    public ResponseData<PrePayProductInfo> queryPrice(@RequestBody RequestData<QueryPrepaymentBaggagePriceReq> reqParam, HttpServletRequest request) {
        return prePayBaggageClient.queryPrice(reqParam);
    }

    @ApiOperation(value = "预付费行李-查询行李定价", notes = "根据出发地点、到达地、起飞时间查询")
    @PostMapping(value = "/queryPricing", produces = {"application/json;charset=UTF-8"})
    public ResponseData<QueryPrePaymentBaggagePriceResp> queryPricing(@RequestBody RequestData<QueryPrepaymentBaggagePriceReq> reqParam, HttpServletRequest request) {
        return prePayBaggageClient.queryPricing(reqParam);
    }

    @ApiOperation(value = "预付费行李-详情页退款", notes = "预付费行李-退款")
    @PostMapping(value = "/refundPrepaymentBaggage", produces = {"application/json;charset=UTF-8"})
    public ResponseData refundPrepaymentBaggage(@RequestBody @Validated RequestData<PrePaymentBaggageRefundReq> req) {
        return prePayBaggageClient.refundPrepaymentBaggage(req);
    }

    @ApiOperation(value = "预付费行李-下单", notes = "预付费行李-下单")
    @PostMapping(value = "/prePayBaggageCreateOrder", produces = {"application/json;charset=UTF-8"})
    public ResponseData<PrePayBaggageCreateOrderResp> prePayBaggageCreateOrder(
            @RequestBody @Validated RequestData<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.prepaybaggage.PrePayBaggageCreateOrderReq> req,
            HttpServletRequest httpServletRequest) {
        try {
            req.getData().setSalePwd(AESTool.decrypt(req.getData().getSalePwd(), CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16)));
        } catch (Exception e) {
            throw new ServiceException("解密出错");
        }

        String blackBox = httpServletRequest.getHeader("BlackBox");
        fraudApiInvoker.b2cWebOrderEvent(ServiceContext.getHead().channelNo,
                PlatFormEnum.B2C.getSystemCode(),
                null,
                null,
                req.getOriginIp(),
                blackBox,
                req.getFfpNo(),
                req.getFfpId());
        scoreUseRuleService.checkScoreRule(req.getFfpId(), req.getFfpNo(), req.getOriginIp());
        // 脱敏信息还原
        req = desensitizationService.RestoreDesensitization(req);
        return benefitOrderClient.createOrderPrePayBaggage(req);
    }

    @Deprecated
    @ApiOperation(value = "预付费行李-购买说明须知文案", notes = "前端未使用数据，考虑删除")
    @PostMapping(value = "/queryPrepaymentBaggageDocument", produces = {"application/json;charset=UTF-8"})
    public ResponseData<PrePaymentBaggageDocumentResp> queryPrepaymentBaggageDocument(@RequestBody @Validated RequestData req) {
        return prePayBaggageClient.queryPrepaymentBaggageDocument(req);
    }


}
