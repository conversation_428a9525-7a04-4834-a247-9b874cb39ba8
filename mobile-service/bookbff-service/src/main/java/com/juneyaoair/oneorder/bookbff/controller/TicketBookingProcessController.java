package com.juneyaoair.oneorder.bookbff.controller;


import com.juneyaoair.oneorder.dto.premiumservice.PremiumInfoRequest;
import com.juneyaoair.oneorder.dto.premiumservice.PremiumRespV2;
import com.juneyaoair.oneorder.fegin.PrePayBaggageClient;
import com.juneyaoair.oneorder.fegin.PremiumServiceClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/ticketBookingProcess")
@RestController
@Api(value = "TicketBookingProcessController", tags = "机票预定流程-优选服务（增值服务）")
public class TicketBookingProcessController {

    @Resource
    public PremiumServiceClient premiumServiceClient;

    @ApiOperation(value = "机票预定-查询优选服务(优享服务)", notes = "机票预定后,创建订单前,查询优选服务(优享服务)")
    @RequestMapping(value = "/queryPremiumProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<PremiumRespV2> queryPremiumProduct(@RequestBody RequestData<PremiumInfoRequest> req) {
        return premiumServiceClient.queryPremiumProduct(req);
    }

}
