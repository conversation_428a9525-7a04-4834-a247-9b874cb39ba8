package com.juneyaoair.oneorder.bookbff.service.impl;

import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.bookbff.service.DesensitizationService;
import com.juneyaoair.oneorder.bookbff.util.RedisKeyUtil;
import com.juneyaoair.oneorder.dto.prepaybaggage.QuerySegmentResponse;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.prepaybaggage.PrePayBaggageCreateOrderReq;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DesensitizationServiceImpl implements DesensitizationService {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 额外行李证件号脱敏
     * modify by {@link com.juneyaoair.mobile.handler.controller.NewCouponController#prepaymentBaggageCertNoDesensitization}
     *
     * @param resp
     */
    @Override
    public ResponseData<QuerySegmentResponse> prepaymentBaggageCertNoDesensitization(ResponseData<QuerySegmentResponse> resp) {
        // 如果返回错误码，直接返回原始响应
        if (resp.getData() == null) {
            return resp;
        }
        resp.getData().getData().forEach(queryPrepaymentBaggageSegmentResp -> {
            // 页面证件信息
            String certNo = queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().getIdNo();
            String desensitizedCertNo = SensitiveInfoHider.hideMiddleSensitiveInfo(certNo);
            queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().setIdNo(desensitizedCertNo);
            String tktNo = queryPrepaymentBaggageSegmentResp.getTicketNumber();
            redisUtil.set(RedisKeyUtil.createUpIdInfo(tktNo), certNo, 60 * 10L);

            // BaggageSegmentInfoList仅在由数据接口返回时，包含证件信息，也隐藏
            queryPrepaymentBaggageSegmentResp.getBaggageSegmentInfoList().forEach(v -> {
                if (certNo.equals(v.getTravellerNumber())) {
                    v.setTravellerNumber(desensitizedCertNo);
                }
            });
        });
        return resp;
    }


    /**
     * 额外行李脱敏信息还原
     *
     * @param reqParam
     * @return
     */
    @Override
    public RequestData<PrePayBaggageCreateOrderReq> RestoreDesensitization(RequestData<PrePayBaggageCreateOrderReq> reqParam) {
        String certRequest = reqParam.getData().getSubCouponOrderList().get(0).getBaggagePassengerInfo().getIdNo();
        if (certRequest.contains("*")) {
            String info =(String) redisUtil.get(RedisKeyUtil.createUpIdInfo(reqParam.getData().getSubCouponOrderList().get(0).getTicketNumber()));
            if (StringUtils.isBlank(info)) {
                throw new ServiceException(CommonErrorCode.OPERATIONAL_TIMEOUT, "操作超时，请返回重新查询下单");
            }
            reqParam.getData().getSubCouponOrderList().get(0).getBaggagePassengerInfo().setIdNo(info);
        }
        return reqParam;
    }

}
