package com.juneyaoair.oneorder.bookbff.service.impl;

import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.appenum.VerifyStatusEnum;
import com.juneyaoair.flightbasic.response.crm.MemberRealNameSummarySoaModel;
import com.juneyaoair.flightbasic.response.crm.PtMemberDetail;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.bookbff.service.ScoreUseRuleService;
import com.juneyaoair.oneorder.crm.enums.MileageAccountRequestItemsEnum;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ScoreUseRuleServiceImpl implements ScoreUseRuleService {

    @Value("${ownRealChannel:Face,Photo,Tencent,Unionpay,ZhiFuBao,AliFace,AlipayApp}")
    private String ownRealChannel;

    @Resource
    private BookMemberServiceImpl bookMemberService;

    /**
     * 1.实名判断
     * 2.判断实名时间
     * 3.当前账户内的积分是否存在非航空累的会员积分
     */
    @Override
    public void checkScoreRule(String ffpId, String ffpNo, String originIp) {

        if (StringUtils.isAnyBlank(ffpId, ffpNo)) {
            throw new ArgumentCheckFailException("会员信息不可为空");
        }
        //1.实名判断
        String[] requestItems = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtMemberDetail ptMemberDetail = bookMemberService.getMemberDetail(ffpNo, requestItems);
        List<MemberRealNameSummarySoaModel> memberRealNameSummarySoaModelList = ptMemberDetail.getRealVerifyInfos();
        if (CollectionUtils.isEmpty(memberRealNameSummarySoaModelList)) {
            throw new ServiceException(CommonErrorCode.NO_REAL_NAME, "您还未实名，请先完成实名认证！");
        }
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = this.getNewRealNamePassSummary(memberRealNameSummarySoaModelList);
        if (Objects.isNull(memberRealNameSummarySoaModel)) {
            throw new ServiceException(CommonErrorCode.NO_REAL_NAME, "您还未实名，请先完成实名认证！");
        }
        LocalDateTime verifyDate = DateUtil.toLocalDateTime(memberRealNameSummarySoaModel.getVerifyDate(), "+8");
        long useTime = DateUtil.differOfYear(verifyDate, LocalDateTime.now());
        //2.为空的情况下不进行自有渠道检验
        if (StringUtils.isNotBlank(ownRealChannel)) {
            if (!(ownRealChannel.contains(memberRealNameSummarySoaModel.getVerifyChannel()) && useTime <= 2)) {
                throw new ServiceException(CommonErrorCode.REAL_NAME_EXPIRED, "实名认证超过期限");
            }
        }
        //3.当前账户内的积分是否存在非航空累的会员积分,存在需要实名信息在6个月
        List<String> items = new ArrayList<>();
        items.add(MileageAccountRequestItemsEnum.TOTALBILL.eName);
        if (!bookMemberService.existMemberClubMiles(originIp, ffpNo, items)) {
            String verifyDate2 = DateUtil.timeStampToDateStr(memberRealNameSummarySoaModel.getVerifyDate(), DateUtil.YYYY_MM_DD_PATTERN);
            //若账户内存在非航空累积的积分，则判断其最近一次实名时间，是否在6个月以内，若超过6个月，则在使用积分时，需要重新进行实名认证
            int diffDays = DateUtil.dateDiff(verifyDate2, DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN);
            if (diffDays > 180) {
                log.info("卡号:{},不存在航空累积积分且认证有效期超过180天", ffpNo);
                throw new ServiceException(CommonErrorCode.REAL_NAME_EXPIRED, "实名认证超过期限");
            }
        }
    }


    /**
     * 返回最新认证通过的记录
     *
     * @param realVerifyInfos
     * @return
     */
    public static MemberRealNameSummarySoaModel getNewRealNamePassSummary(List<MemberRealNameSummarySoaModel> realVerifyInfos) {
        if (CollectionUtils.isEmpty(realVerifyInfos)) {
            return null;
        }
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = realVerifyInfos.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
        if (memberRealNameSummarySoaModel != null && VerifyStatusEnum.PASS.code.equals(memberRealNameSummarySoaModel.getStatus())) {
            return memberRealNameSummarySoaModel;
        }
        return null;
    }

}
