package com.juneyaoair.oneorder.bookbff.enums;


/**
 * @ClassName MileageAccountRequestItemsEnum
 * @Description 积分账户请求项
 * <AUTHOR>
 * @Date 2020/3/6 14:56
 **/
public enum MileageAccountRequestItemsEnum {
    UNKNOW(0,"UNKNOW","未知"),
    TOTALBILL(1,"TotalBill","总账信息"),
    PERIODBILL(2,"PeriodBill","时间段账单信息"),
    EXPIREBILL(3,"ExpireBill","积分失效账单"),
    LEVELCHANGE(4,"LevelChange","升降级信息")
    ;
    public final int code;
    public final String eName;
    public final String desc;

    MileageAccountRequestItemsEnum(int code, String eName, String desc) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
    }

    public static MileageAccountRequestItemsEnum checkType(String v)
    {
        for (MileageAccountRequestItemsEnum c: MileageAccountRequestItemsEnum.values()) {
            if (c.eName.equals(v)) {
                return c;
            }
        }
        return null;
    }
}
