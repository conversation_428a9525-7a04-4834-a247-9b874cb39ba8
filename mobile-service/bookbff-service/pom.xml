<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mobile-service</artifactId>
        <groupId>com.juneyaoair.oneorder</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bookbff-service</artifactId>
    <description>预定服务模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <!--actuator 完善监控信息-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-logback</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-aspect</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-restresult</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>woodstox-core</artifactId>
                    <groupId>com.fasterxml.woodstox</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-tools</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-config-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>product-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>order-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-sink-api</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact-manager</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-model</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-plugin-api</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-repository-metadata</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-settings</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-component-annotations</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-container-default</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-interpolation</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-utils</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>service-support</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>order-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-config-redis</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>