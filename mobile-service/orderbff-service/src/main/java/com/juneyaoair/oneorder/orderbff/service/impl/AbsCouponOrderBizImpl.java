package com.juneyaoair.oneorder.orderbff.service.impl;

import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.exception.OrderServiceException;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.util.IPUtil;
import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleReq;
import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleRes;
import com.juneyaoair.oneorder.fare.feign.FareClient;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderBaseInfoDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderCommonExtInfoQueryResDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderContactInfoDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderDetailQueryReqDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderDetailQueryResDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderExtInfoQueryReqDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderPassengerInfoDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderRefundDetailReqDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderRefundDetailResDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponOrderRefundInfoDTO;
import com.juneyaoair.oneorder.order.dto.couponorder.CouponSubOrderInfoDTO;
import com.juneyaoair.oneorder.order.dto.score.ApplyScoreRespDto;
import com.juneyaoair.oneorder.order.feign.OrderClient;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderPointPostParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.OrderPointPostResult;
import com.juneyaoair.oneorder.orderbff.service.ICouponOrderBiz;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.AirJsonUtil;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @version v1.0
 * @date 2024/9/24 14:52
 * @description
 **/
public abstract class AbsCouponOrderBizImpl implements ICouponOrderBiz {

    @Autowired
    @Qualifier("com.juneyaoair.oneorder.order.feign.OrderClient")
    protected OrderClient orderClient;
    @Resource
    private FareClient fareClient;

    @Autowired
    private CommonService commonService;

    private static final Logger log = LoggerFactory.getLogger(AbsCouponOrderBizImpl.class);

    @Override
    public abstract ResponseData<CouponOrderDetailQueryResDTO> queryMyCouponOrderDetail(RequestData<CouponOrderDetailQueryReqDTO> requestData);

    @Override
    public CouponOrderDetailQueryResDTO queryCouponOrderBaseInfo(RequestData<CouponOrderDetailQueryReqDTO> requestData, boolean security) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        requestData.setChannelNo(channelInfo.getOrderChannelCode());
        log.info("Request: {} ===> 查询权益订单基本信息请求: {}", "queryCouponOrderBasicInfo", AirJsonUtil.toJSONString(requestData));
        ResponseData<CouponOrderDetailQueryResDTO> resData = orderClient.queryCouponOrderBasicInfo(requestData);
        log.info("Response: {} ===> 查询权益订单基本信息响应: {}", "queryCouponOrderBasicInfo", AirJsonUtil.toJSONString(resData));

        if ((Objects.isNull(resData)) || (!WSEnum.SUCCESS.name().equals(resData.getCode()))) {
            log.error("{} ===> 查询权益订单基本信息发生异常: {}", "queryCouponOrderBasicInfo", AirJsonUtil.toJSONString(resData));
            throw OrderServiceException.fail("查询权益订单基本信息发生错误");
        }
        CouponOrderDetailQueryResDTO couponOrderDetailQueryRes = resData.getData();
        // 无需脱敏 直接返回原始数据
        if (!security) {
            return couponOrderDetailQueryRes;
        }
        CouponOrderBaseInfoDTO couponOrderDetail = couponOrderDetailQueryRes.getCouponOrderDetail();
        // 无权益订单详情 直接返回
        if (null == couponOrderDetail) {
            return couponOrderDetailQueryRes;
        }
        // 对数据进行脱敏
        CouponOrderContactInfoDTO contactInfo = couponOrderDetail.getContactInfo();
        // 对联系人信息进行脱敏
        if (null != contactInfo) {
            contactInfo.setLinkerHandphone(SensitiveInfoHider.hidePhone(contactInfo.getLinkerHandphone()));
            contactInfo.setLinkerEmail(SensitiveInfoHider.hideMail(contactInfo.getLinkerEmail()));
            contactInfo.setLinkerName(SensitiveInfoHider.hideName(contactInfo.getLinkerName()));
        }
        List<CouponSubOrderInfoDTO> couponSubOrderInfoList = couponOrderDetail.getCouponSubOrderInfo();
        // 不存在子订单信息 直接返回
        if (CollectionUtils.isEmpty(couponSubOrderInfoList)) {
            return couponOrderDetailQueryRes;
        }
        // 对关联乘客信息进行脱敏
        for (CouponSubOrderInfoDTO couponSubOrderInfo : couponSubOrderInfoList) {
            CouponOrderPassengerInfoDTO passengerInfo = couponSubOrderInfo.getPassengerInfo();
            if (null != passengerInfo) {
                passengerInfo.setPassengerName(SensitiveInfoHider.hideName(passengerInfo.getPassengerName()));
                passengerInfo.setCertNo(SensitiveInfoHider.hideAllCertNo(passengerInfo.getCertNo()));
            }
        }
        return couponOrderDetailQueryRes;
    }


    @Override
    public CouponOrderCommonExtInfoQueryResDTO queryCouponOrderExtInfo(RequestData<CouponOrderExtInfoQueryReqDTO> requestData) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        requestData.setChannelNo(channelInfo.getOrderChannelCode());
        log.info("Request: {} ===> 查询权益订单扩展信息请求: {}", "queryCouponOrderExtInfo", AirJsonUtil.toJSONString(requestData));
        ResponseData<CouponOrderCommonExtInfoQueryResDTO> resData = orderClient.queryCouponOrderExtInfo(requestData);
        log.info("Response: {} ===> 查询权益订单扩展信息响应: {}", "queryCouponOrderExtInfo", AirJsonUtil.toJSONString(resData));

        if ((Objects.isNull(resData)) || (!WSEnum.SUCCESS.name().equals(resData.getCode()))) {
            log.error("{} ===> 查询权益订单扩展信息发生异常: {}", "queryCouponOrderExtInfo", AirJsonUtil.toJSONString(resData));
            throw OrderServiceException.fail("查询权益订单扩展信息发生错误");
        }
        return resData.getData();
    }

    @Override
    public abstract ResponseData<CouponOrderRefundDetailResDTO> queryCouponOrderRefundInfo(RequestData<CouponOrderRefundDetailReqDTO> requestData);

    @Override
    public CouponOrderRefundDetailResDTO queryCouponOrderRefundInfoBase(RequestData<CouponOrderRefundDetailReqDTO> requestData, boolean security) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        requestData.setChannelNo(channelInfo.getOrderChannelCode());
        log.info("Request: {} ===> 查询权益订单退单详情基本信息请求: {}", "queryCouponOrderRefundInfo", AirJsonUtil.toJSONString(requestData));
        ResponseData<CouponOrderRefundDetailResDTO> resData = orderClient.queryCouponOrderRefundInfo(requestData);
        log.info("Response: {} ===> 查询权益订单退单详情基本信息响应: {}", "queryCouponOrderRefundInfo", AirJsonUtil.toJSONString(resData));

        if ((Objects.isNull(resData)) || (!WSEnum.SUCCESS.name().equals(resData.getCode()))) {
            log.error("{} ===> 查询权益订单退单详情基本信息发生异常: {}", "queryCouponOrderRefundInfo", AirJsonUtil.toJSONString(resData));
            throw OrderServiceException.fail("查询权益订单退单详情基本信息发生错误");
        }
        CouponOrderRefundDetailResDTO couponOrderRefundDetailRes = resData.getData();
        // 无需脱敏 直接返回原始数据
        if (!security) {
            return couponOrderRefundDetailRes;
        }
        CouponOrderRefundInfoDTO refundInfoDetail = couponOrderRefundDetailRes.getRefundInfoDetail();
        // 无权益订单详情 直接返回
        if (null == refundInfoDetail) {
            return couponOrderRefundDetailRes;
        }
        // 对数据进行脱敏
        // 对关联乘客信息进行脱敏
        CouponOrderPassengerInfoDTO passengerInfo = refundInfoDetail.getPassengerInfo();
        if (null != passengerInfo) {
            passengerInfo.setPsgMobileNumber(SensitiveInfoHider.hidePhone(passengerInfo.getPsgMobileNumber()));
            passengerInfo.setPassengerName(SensitiveInfoHider.hideName(passengerInfo.getPassengerName()));
            passengerInfo.setCertNo(SensitiveInfoHider.hideAllCertNo(passengerInfo.getCertNo()));
        }
        return couponOrderRefundDetailRes;
    }

    @Override
    public QueryScoreRuleRes getSoreRateRule(QueryScoreRuleReq queryScoreRuleReq) {
        RequestData<QueryScoreRuleReq> requestData = new RequestData<>();
        requestData.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        requestData.setOriginIp(IPUtil.getLocalIp());
        requestData.setData(queryScoreRuleReq);
        log.info("Request: getSoreRateRule ===> 查询汇率信息请求: {}", AirJsonUtil.toJSONString(requestData));
        ResponseData<QueryScoreRuleRes> responseData = fareClient.getSoreRateRule(requestData);
        log.info("Response: getSoreRateRule ===> 查询汇率信息响应: {}", AirJsonUtil.toJSONString(responseData));

        if (null == responseData || !WSEnum.SUCCESS.name().equals(responseData.getCode())) {
            log.error("getSoreRateRule ===> 查询汇率信息发生异常: {}", AirJsonUtil.toJSONString(responseData));
            throw MultiLangServiceException.fail("汇率信息查询失败");
        }
        return responseData.getData();
    }

    @Override
    public OrderPointPostResult getOrderPointPost(RequestData<? extends CouponOrderParam> requestData) {
        throw MultiLangServiceException.fail("暂不支持积分后置");
    }

    @Override
    public ApplyScoreRespDto orderPointPost(RequestData<CouponOrderPointPostParam> requestData) {
        throw MultiLangServiceException.fail("暂不支持积分后置");
    }

}
