package com.juneyaoair.oneorder;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.juneyaoair.horder.api.CrmApiHystric;
import com.juneyaoair.oneorder.restresult.GlobalExceptionHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(
        basePackages = {"com.juneyaoair.oneorder"
                , "com.juneyaoair.mobile"
                , "com.juneyaoair.cuss.service"
                , "com.juneyaoair.i18n"},
        excludeFilters =
        @ComponentScan.Filter(
                type = FilterType.ASSIGNABLE_TYPE,
                classes = {
                        //CrmApiHystric.class,       //排除 JAR 包中的熔断器回退类
                        GlobalExceptionHandler.class // 排除 JAR 包中的全局异常处理类
                }
        )
)
@EnableAsync // 启用异步支持
@EnableDiscoveryClient // 启用服务发现客户端
@ServletComponentScan // 扫描 Servlet 组件（如 Filter、Servlet、Listener）
@EnableApolloConfig(value = {"orderbff", "application"})
@EnableFeignClients(basePackages = {"com.juneyaoair"}) //启用 Feign 客户端，指定扫描的基础包
public class OrderbffServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderbffServiceApplication.class, args);
    }

}
