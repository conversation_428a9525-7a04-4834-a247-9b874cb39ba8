package com.juneyaoair.oneorder.orderbff.controller;

import com.juneyaoair.oneorder.order.dto.b2crefund.*;
import com.juneyaoair.oneorder.order.dto.refund.RefundType;
import com.juneyaoair.oneorder.order.feign.RefundClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/8 10:02
 */
@Slf4j
@RestController
public class RefundController {

    @Resource
    private RefundClient refundClient;


    /**
     * 退票确认
     *
     * @param requestData
     * @return
     */
    @PostMapping(value = "/RefundConfirm")
    @ApiOperation(value = "退票确认", notes = "提交退票请求")
    public ResponseData<OrderRefundResp> refundConfirm(@RequestBody @Validated RequestData<OrderRefundRequest> requestData) {
        ResponseData<OrderRefundResp> responseData = refundClient.refundConfirm(requestData);
        return responseData;

    }


    @PostMapping(value = "/RefundApply")
    @ApiOperation(value = "退票费用申请", notes = "退票相关费用查询")
    public ResponseData<RefundApplyResp> refundApply(@RequestBody @Validated RequestData<RefundApplyRequest> requestData) {
        ResponseData<RefundApplyResp> responseData = refundClient.refundApply(requestData);
        return responseData;

    }

    /**
     * 查询退票类型
     *
     * @return
     */
    @GetMapping(value = "QueryRefundTypes")
    public ResponseData<List<RefundType>> queryRefundTypes() {
        ResponseData<List<RefundType>> responseData = refundClient.queryRefundTypes();
        return responseData;
    }


    /**
     * 查询退票类型
     *
     * @return
     */
    @PostMapping(value = "QueryRefundTypesI18n")
    @ApiOperation(value = "查询退票类型(国际化)", notes = "查询退票类型(国际化)")
    ResponseData<List<RefundType>> queryRefundTypesI18n(@RequestBody RequestData requestData) {

        return refundClient.queryRefundTypesI18n(requestData);


    }


    /**
     * 退票费用申请
     *
     * @param requestData
     * @return
     */
    @PostMapping(value = "/RefundApplyI18n")
    @ApiOperation(value = "退票费用申请(国际化)", notes = "退票费用申请(国际化)")
    ResponseData<RefundApplyResp> refundApplyI18n(@RequestBody @Validated RequestData<RefundApplyRequest> requestData) {
        return refundClient.refundApplyI18n(requestData);

    }

    /**
     * 退票确认
     *
     * @param requestData
     * @return
     */
    @PostMapping(value = "/RefundConfirmI18n")
    @ApiOperation(value = "退票确认(国际化)", notes = "退票确认(国际化)")
    ResponseData<OrderRefundResp> refundConfirmI18n(@RequestBody @Validated RequestData<OrderRefundRequest> requestData) {
        return refundClient.refundConfirmI18n(requestData);
    }


    /**
     * 查询退单详情(新)
     * 查询退单详情
     *
     * @return 退票详情
     */
    @ApiOperation(value = "查询退单详情（国际化）", notes = "查询退单详情（国际化）")
    @PostMapping(value = "QueryRefundDetailI18n")
    ResponseData<RefundOrderDetailResponse> queryRefundDetailI18n(@RequestBody @Validated RequestData<RefundDetailInfoRequest> requestData) {
        return refundClient.queryRefundDetailI18n(requestData);
    }

}
