package com.juneyaoair.oneorder.orderbff.controller;

import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.api.pay.config.PayConfig;
import com.juneyaoair.oneorder.api.pay.dto.*;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.order.dto.SubOrderResDTO;
import com.juneyaoair.oneorder.orderbff.service.PayAggrService;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.util.LoginCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @description 支付相关服务
 * @date 2023/6/27 18:32
 */
@Api(value = "PayController", tags = "支付相关服务")
@RestController
@Slf4j
public class PayController extends BaseController {
    @Autowired
    private PayAggrService payAggrService;
    @Resource
    private PayConfig payConfig;

    @ApiOperation(value = "渲染支付方式列表", notes = "渲染支付方式列表")
    @PostMapping("/initPayMethod")
    public ResponseData<List<Bank>> initPayMethod(@RequestBody @Validated RequestDataDto<InitPayMethodParam> requestData, BindingResult bindingResult, HttpServletRequest request) {
        InitPayMethodParam initPayMethodParam = requestData.getData();
        if (ObjectUtils.isEmpty(initPayMethodParam)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        // 存在订单币种 币种使用订单币种
        if (StringUtils.isNotBlank(initPayMethodParam.getOrderCurrency())) {
            initPayMethodParam.setCurrency(initPayMethodParam.getOrderCurrency());
        }
        initBizDto(initPayMethodParam, request);
        List<Bank> bankList = payAggrService.queryAllPayMethod(initPayMethodParam.getHeadChannelCode(), initPayMethodParam);
        return ResponseData.suc(bankList);
    }

    @ApiOperation(value = "发起支付请求", nickname = "发起支付请求", notes = "发起支付请求")
    @PostMapping("/orderPay")
    public ResponseData<PayResult> orderPay(@RequestBody @Validated RequestDataDto<OrderPayParam> requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response) {
        OrderPayParam orderPayParam = requestData.getData();
        // 存在订单币种 币种使用订单币种
        if (StringUtils.isNotBlank(orderPayParam.getOrderCurrency())) {
            orderPayParam.setCurrency(orderPayParam.getOrderCurrency());
        }
        checkParam(requestData, bindingResult);
        initBizDto(orderPayParam, request);
        //此处的渠道号来源于请求头
        return ResponseData.suc(payAggrService.doPay(orderPayParam.getHeadChannelCode(), requestData.getLanguage(), orderPayParam, requestData.getFfpNo(), response));
    }

    @ApiOperation(value = "toSendVerifyCode", nickname = "（交行信用卡支付）发送验证码", notes = "（交行信用卡支付）发送验证码")
    @PostMapping("/toSendVerifyCode")
    @SuppressWarnings("all")
    public ResponseData<VerifyCodeSendResult> toSendVerifyCode(@RequestBody @Validated RequestDataDto<VerifyCodeSendParam> requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response) {
        VerifyCodeSendParam verifyCodeSendParam = requestData.getData();
        checkParam(requestData, bindingResult);
        initBizDto(verifyCodeSendParam, request);
        //此处的渠道号来源于请求头
        return ResponseData.suc(payAggrService.toSendVerifyCode(verifyCodeSendParam.getHeadChannelCode(), verifyCodeSendParam, requestData.getFfpNo(), response));
    }

    @ApiOperation(value = "toCatchDFTSignInfo", nickname = "（德付通）查询签约情况", notes = "（德付通）查询签约情况")
    @PostMapping("/toCatchDFTSignInfo")
    @SuppressWarnings("all")
    public ResponseData<Boolean> toCatchDFTSignInfo(@RequestBody @Validated RequestData requestData) {
        return ResponseData.suc(payAggrService.toCatchDFTSignInfo(requestData.getFfpNo()));
    }

    @ApiOperation(value = "toIdentify", nickname = "（德付通）身份验证", notes = "（德付通）身份验证")
    @PostMapping("/toIdentify")
    @SuppressWarnings("all")
    public ResponseData toIdentify(@RequestBody @Validated RequestDataDto<DFTSignRequest> requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response) {
        DFTSignRequest dftSignRequest = requestData.getData();
        checkParam(requestData, bindingResult);
        initBizDto(dftSignRequest, request);
        payAggrService.toIdentify(dftSignRequest.getHeadChannelCode(), dftSignRequest, requestData.getFfpNo());
        return ResponseData.suc();
    }

    @ApiOperation(value = "toSign", nickname = "（德付通）签约", notes = "（德付通）签约")
    @PostMapping("/toSign")
    @SuppressWarnings("all")
    public ResponseData toSign(@RequestBody @Validated RequestDataDto<DFTSignRequest> requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response) {
        DFTSignRequest dftSignRequest = requestData.getData();
        checkParam(requestData, bindingResult);
        initBizDto(dftSignRequest, request);
        payAggrService.toSign(dftSignRequest.getHeadChannelCode(), dftSignRequest, requestData.getFfpNo());
        return ResponseData.suc();
    }

    @ApiOperation(value = "toReSign", nickname = "（德付通）解约", notes = "（德付通）解约")
    @PostMapping("/toReSign")
    @SuppressWarnings("all")
    public ResponseData toReSign(@RequestBody @Validated RequestData requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response) {
        payAggrService.toReSign(requestData.getFfpNo());
        return ResponseData.suc();
    }


    @ApiOperation(value = "查询支付状态", notes = "查询支付状态")
    @PostMapping("/queryPayState")
    public ResponseData<OrderStatusDto> queryPayState(@RequestBody @Validated RequestDataDto<OrderParam> requestData, BindingResult bindingResult, HttpServletRequest request) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        OrderParam orderParam = requestData.getData();
        checkParam(requestData, bindingResult);
        initBizDto(orderParam, request);
        return ResponseData.suc(payAggrService.queryPayState(requestData.getChannelNo(), requestData.getFfpId(), requestData.getFfpNo(), orderParam));
    }

    @ApiOperation(value = "支付成功后重定向", notes = "支付成功后重定向")
    @PostMapping("/redirectHomePage")
    public ResponseData redirectHomePage(
            @RequestParam(name = "ChannelNo", required = false) String channelNo,
            @RequestParam(name = "ChannelOrderNo", required = false) String channelOrderNo,
            @RequestParam(name = "OrderNo", required = false) String orderNo,
            @RequestParam(name = "Currency", required = false) String currency,
            @RequestParam(name = "Amount", required = false) String amount,
            @RequestParam(name = "ChannelPriInfo", required = false) String channelPriInfo,
            @RequestParam(name = "PaymentSeq", required = false) String paymentSeq,
            @RequestParam(name = "PaymentTransId", required = false) String paymentTransId,
            @RequestParam(name = "ConfirmTime", required = false) String confirmTime,
            @RequestParam(name = "GatewayNo", required = false) String gatewayNo,
            @RequestParam(name = "MerchantNo", required = false) String merchantNo,
            @RequestParam(name = "RespCode", required = false) String respCode,
            @RequestParam(name = "ErrorMsg", required = false) String errorMsg,
            @RequestParam(name = "PaymentChannelNo", required = false) String paymentChannelNo,
            @RequestParam(name = "ChkValue", required = false) String chkValue,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) {
        try {
            httpServletResponse.sendRedirect(payConfig.getHomePage());
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return ResponseData.suc();
    }

    @ApiOperation(value = "国际支付成功后重定向", notes = "支付成功后重定向，目前适应于国际网站")
    @PostMapping("/redirectHomePageGlobal")
    public ResponseData redirectHomePageGlobal(
            @RequestParam(name = "ChannelNo", required = false) String channelNo,
            @RequestParam(name = "ChannelOrderNo", required = false) String channelOrderNo,
            @RequestParam(name = "OrderNo", required = false) String orderNo,
            @RequestParam(name = "Currency", required = false) String currency,
            @RequestParam(name = "Amount", required = false) String amount,
            @RequestParam(name = "ChannelPriInfo", required = false) String channelPriInfo,
            @RequestParam(name = "PaymentSeq", required = false) String paymentSeq,
            @RequestParam(name = "PaymentTransId", required = false) String paymentTransId,
            @RequestParam(name = "ConfirmTime", required = false) String confirmTime,
            @RequestParam(name = "GatewayNo", required = false) String gatewayNo,
            @RequestParam(name = "MerchantNo", required = false) String merchantNo,
            @RequestParam(name = "RespCode", required = false) String respCode,
            @RequestParam(name = "ErrorMsg", required = false) String errorMsg,
            @RequestParam(name = "PaymentChannelNo", required = false) String paymentChannelNo,
            @RequestParam(name = "ChkValue", required = false) String chkValue,
            @RequestParam(name = "page", required = false) String page,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) {
        try {
            //重定向至前端指定页面
            StringBuilder stringBuilder = new StringBuilder(payConfig.getGB2cUrl());
            if (StringUtils.isNotBlank(page)) {
                String newPage = URLDecoder.decode(page, "UTF-8");
                stringBuilder.append(newPage);
                stringBuilder.append("?OrderNo=" + orderNo);
                stringBuilder.append("&ChannelOrderNo=" + channelOrderNo);
            }
            log.info("重定向跳转地址:", stringBuilder);
            httpServletResponse.sendRedirect(stringBuilder.toString());
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return ResponseData.suc();
    }
}
