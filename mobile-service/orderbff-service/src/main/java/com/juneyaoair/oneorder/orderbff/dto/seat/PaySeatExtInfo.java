package com.juneyaoair.oneorder.orderbff.dto.seat;

import com.juneyaoair.cuss.enums.PayTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 *  优选座位额外信息
 */
@Data
public class PaySeatExtInfo {

    @ApiModelProperty("座位号")
    private String paySeatNo;

    @ApiModelProperty("emd票号")
    private String emdTktNo;

    @ApiModelProperty("选座会员等级")
    private String memberCode;

    @ApiModelProperty(value = "是否本人 Y:本人 N:非本人")
    private String self;

    @Deprecated
    @ApiModelProperty(value = "支付方式(PayTypeEnum) 1:积分; 2:现金; 3:积分现金组合支付(现金转积分); 4:积分现金组合支付")
    private Integer payType;

    @ApiModelProperty(value = "支付方式(PayTypeEnum)")
    private Set<PayTypeEnum> payTypeList;

}
