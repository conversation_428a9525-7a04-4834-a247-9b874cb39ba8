package com.juneyaoair.oneorder.orderbff.controller;

import com.juneyaoair.mobile.exception.AppException;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.impl.GeetestService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.mapstruct.ConvertRequestDataDto;
import com.juneyaoair.oneorder.order.dto.b2cinvoice.InvoiceTicketInfo;
import com.juneyaoair.oneorder.order.dto.b2cinvoice.InvoiceTicketInfoResponse;
import com.juneyaoair.oneorder.order.dto.invoice.*;
import com.juneyaoair.oneorder.order.dto.invoiceII.req.CommonInvoiceInfoReq;
import com.juneyaoair.oneorder.order.dto.invoiceII.req.InvoiceInfoNoticeReq;
import com.juneyaoair.oneorder.order.dto.invoiceII.req.InvoiceTitleReq;
import com.juneyaoair.oneorder.order.dto.invoiceII.res.InvoiceTitleRes;
import com.juneyaoair.oneorder.order.dto.invoiceII.trd.TrdInvoiceResponseData;
import com.juneyaoair.oneorder.order.feign.InvoiceClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/21 11:16
 */
@RestController
public class InvoiceController {

    @Resource
    private InvoiceClient invoiceClient;
    @Autowired
    private GeetestService geetestService;


    /**
     * 获取订单行程列表
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    @PostMapping(value = "QueryOrderInvoice")
    @ApiOperation(value = "查询订单发票信息", notes = "查询订单发票信息")
    public ResponseData<InvoiceTicketInfoResponse> queryOrderInvoice(@RequestBody @Validated RequestData<OrderInvoiceRequestDto> requestData) throws Exception {
        ResponseData<InvoiceTicketInfoResponse> responseData = invoiceClient.queryOrderInvoice(requestData);
        return responseData;
    }

    /**
     * 自动填充常用报销凭证
     *
     * @param requestData
     * @return
     */
    @PostMapping(value = "CommonAutoInvoiceQuery")
    @ApiOperation(value = "自动填充常用报销凭证", notes = "自动填充常用报销凭证")
    public ResponseData<QueryGeneralInvoiceResponseDto> commonAutoInvoiceQuery(@RequestBody @Validated RequestData<QueryGeneralInvoiceRequestDto> requestData) {

        ResponseData<QueryGeneralInvoiceResponseDto> responseData = invoiceClient.commonAutoInvoiceQuery(requestData);
        return responseData;
    }

    /**
     * 开票申请接口
     *
     * @param
     * @return
     */
    @PostMapping(value = "ElectricOutputInvoice")
    @ApiOperation(value = "开票申请接口", notes = "开票申请接口")
    public ResponseData electricOutputInvoice(@RequestBody @Validated RequestData<ElectricOutputInvoiceRequest> requestData) throws AppException {
        ResponseData responseData = invoiceClient.electricOutputInvoice(requestData);
        return responseData;
    }


    /**
     * 重发电子发票邮件
     *
     * @param
     * @return
     */
    @PostMapping(value = "ResendInvoiceByEmail")
    @ApiOperation(value = "resendInvoiceByEmail", notes = "重发电子发票邮件")
    public ResponseData resendInvoiceByEmail(@RequestBody RequestData<InvoiceTicketInfo> requestData) {
        ResponseData responseData = invoiceClient.resendInvoiceByEmail(requestData);
        return responseData;
    }

    @PostMapping(value = "CheckTickStatus")
    @ApiOperation(value = "CheckTickStatus", notes = "检验客票状态")
    public ResponseData checkTickStatus(@RequestBody RequestData<TicketStatusRequest> requestData) {
        ResponseData responseData = invoiceClient.checkTickStatus(requestData);
        return responseData;
    }

    @PostMapping(value = "ExcessBaggageInvoice")
    @ApiOperation(value = "ExcessBaggageInvoice", notes = "检验客票状态")
    public ResponseData excessBaggageInvoice(@RequestBody RequestData<ExcessBaggageInvoiceReq> requestData) {
        ResponseData responseData = invoiceClient.excessBaggageInvoice(requestData);
        return responseData;
    }

    @PostMapping(value = "ResendExcessEmail")
    @ApiOperation(value = "ResendExcessEmail", notes = "逾重行李重发电子邮件")
    public ResponseData resendExcessEmail(@RequestBody RequestData<ResendExcessEmailReq> requestData) {
        ResponseData responseData = invoiceClient.resendExcessEmail(requestData);
        return responseData;
    }


    /**
     * 接收发票信息推送
     *
     * @param invoiceInfoNoticeReq
     * @return
     */
    @ApiOperation(value = "InvoiceInfoNotice", notes = "接收发票信息推送")
    @PostMapping("/InvoiceInfoNotice")
    TrdInvoiceResponseData invoiceInfoNotice(@RequestBody InvoiceInfoNoticeReq invoiceInfoNoticeReq) {
        return invoiceClient.invoiceInfoNotice(invoiceInfoNoticeReq);
    }


    /**
     * 查询其他客票数据
     *
     * @param requestData
     * @return
     */
    @ApiOperation(value = "FindOtherTicket", notes = "查询其他客票数据")
    @PostMapping("/FindOtherTicket")
    ResponseData<Integer> findOtherTicket(@RequestBody RequestDataDto<OrderInvoiceRequestDto> requestData) {
        geetestService.validate(SceneEnum.TICKET_QUERY, requestData);
        RequestData requestData1 = ConvertRequestDataDto.MAPPER.toRequestData(requestData);
        ResponseData responseData = invoiceClient.findOtherTicket(requestData1);
        return responseData;
    }

    /**
     * 抬头查询
     *
     * @param requestData
     * @return
     */
    @ApiOperation(value = "抬头查询", notes = "直接开红票接口")
    @PostMapping("/InvoiceTitleQuery")
    ResponseData<List<InvoiceTitleRes>> invoiceTitleQuery(@RequestBody RequestData<InvoiceTitleReq> requestData) throws Exception {

        return invoiceClient.invoiceTitleQuery(requestData);

    }

    /***
     * 添加常用报销凭证
     * @param requestData
     * @return
     */
    @ApiOperation(value = "添加常用报销凭证", notes = "添加常用报销凭证")
    @PostMapping("/AddCommonInvoice")
    ResponseData addCommonInvoice(@RequestBody RequestData<CommonInvoiceInfoReq> requestData) throws Exception {

        return invoiceClient.addCommonInvoice(requestData);
    }

    /***
     * 添加常用报销凭证
     * @param requestData
     * @return
     */
    @ApiOperation(value = "修改常用报销凭证", notes = "修改常用报销凭证")
    @PostMapping("/ModifyCommonInvoice")
    ResponseData<GeneralInvoiceInfoDto> modifyCommonInvoice(@RequestBody RequestData<CommonInvoiceInfoReq> requestData) throws Exception {

        return invoiceClient.modifyCommonInvoice(requestData);
    }

    /***
     * 查询常用报销凭证
     * @param requestData
     * @return
     */
    @ApiOperation(value = "查询常用报销凭证", notes = "查询常用报销凭证")
    @PostMapping("/CommonInvoiceQuerys")
    ResponseData<List<GeneralInvoiceInfoDto>> commonInvoiceQuerys(@RequestBody RequestData requestData) throws Exception {

        return invoiceClient.commonInvoiceQuerys(requestData);
    }


    /**
     * 批量删除常用报销凭证
     *
     * @param requestData
     * @return
     */
    @ApiOperation(value = "批量删除常用报销凭证", notes = "批量删除常用报销凭证")
    @PostMapping(value = "/DelGeneralInvoiceBatch")
    ResponseData<List<GeneralInvoiceInfoDto>> delGeneralInvoiceBatch(@RequestBody RequestData<List<GeneralInvoiceInfoDto>> requestData) {
        return invoiceClient.delGeneralInvoiceBatch(requestData);
    }


    /**
     * 电子发票冲红接口
     * 实现电子发票冲红业务逻辑，包含数据校验、原票状态验证、冲红记录创建、第三方系统调用等操作流程
     *
     * @param requestData 请求参数对象，包含渠道信息及发票冲红所需的票据信息（InvoiceTicketInfo）
     * @return ResponseData 统一响应对象，包含第三方冲红接口调用结果
     * @throws Exception 处理过程中出现异常时抛出
     */
    @PostMapping(value = "OrderInvoiceRed")
    @ApiOperation(value = "orderInvoiceRed", notes = "电子发票冲红接口")
    ResponseData orderInvoiceRed(@RequestBody RequestData<InvoiceTicketInfo> requestData) throws Exception {
        return invoiceClient.orderInvoiceRed(requestData);
    }
}
