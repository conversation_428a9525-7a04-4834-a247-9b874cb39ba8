package com.juneyaoair.oneorder.orderbff.controller;


import cn.hutool.core.util.StrUtil;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.util.BeanUtils;
import com.juneyaoair.oneorder.disney.ProductOrderBookReq;
import com.juneyaoair.oneorder.dto.disneyCalendar.DisneyCalendarQueryReq;
import com.juneyaoair.oneorder.dto.disneyCalendar.DisneyChangeCalendarQueryReq;
import com.juneyaoair.oneorder.dto.disneyChangeDate.DisneyChangeRequest;
import com.juneyaoair.oneorder.dto.orderBook.ProductOrderBookReqDto;
import com.juneyaoair.oneorder.dto.orderBook.ProductOrderBookResDto;
import com.juneyaoair.oneorder.fegin.ProductOrderClient;

import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(value = "DisneyOrderController", tags = "迪士尼单独购买系统")
@RestController
public class DisneyOrderController {


    @Resource
    private ProductOrderClient  productOrderClient;


    @PostMapping(value = "disneyPriceBuyCalendar")
    @ApiOperation(value = "迪士单独购票价格日历", notes = "迪士单独购票价格日历")
    ResponseData disneyPriceBuyCalendar(@RequestBody @Validated RequestData<DisneyCalendarQueryReq> req) {
        DisneyCalendarQueryReq disneyCalendarQueryReq = req.getData();
        if (ObjectUtils.isEmpty(disneyCalendarQueryReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return  productOrderClient.priceBuyCalendar(req);
    }


    @PostMapping(value = "disneyPriceChangeCalendar")
    @ApiOperation(value = "迪士单独购票价格日历", notes = "迪士单独购票价格日历")
    ResponseData disneyPriceChangeCalendar(@RequestBody @Validated RequestData<DisneyChangeCalendarQueryReq> req) {
        DisneyChangeCalendarQueryReq disneyChangeCalendarQueryReq = req.getData();
        if (ObjectUtils.isEmpty(disneyChangeCalendarQueryReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return productOrderClient.priceChangeCalendar(req);
    }


    @PostMapping(value = "createDisneyCouponOrder")
    @ApiOperation(value = "迪士单独购票下单", notes = "迪士单独购票下单")
    ResponseData<ProductOrderBookResDto> createDisneyCouponOrder(@RequestBody @Validated RequestData<ProductOrderBookReq> req){
        ProductOrderBookReq productOrderBookReq = req.getData();
        if (ObjectUtils.isEmpty(productOrderBookReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        RequestData<ProductOrderBookReqDto>  productOrderBookReqDtoRequestData = new RequestData<>();
        BeanUtils.copyProperties(req,productOrderBookReqDtoRequestData);
        productOrderBookReqDtoRequestData.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        //productOrderBookReqDtoRequestData.setOriginIp(req.getOriginIp());
        ResponseData responseData = productOrderClient.productBook(productOrderBookReqDtoRequestData);
        return responseData;
    }
    @PostMapping("disneyChangeDate")
    @ApiOperation(value = "迪士尼单独购买改期", notes = "迪士尼单独购买改期")
    public ResponseData<Boolean> disneyChangeDate(@RequestBody RequestData<DisneyChangeRequest> req) {
        if (StrUtil.isBlank(req.getFfpId())) {
            ServiceException.fail("用户Id不能为空");
        }
        if (req.getData() == null) {
            ServiceException.fail("参数不能为空");
        }
        if (StrUtil.isBlank(req.getData().targetDate)) {
            ServiceException.fail("改期时间不能为空");
        }
        if (req.getData().couponOrderId == null) {
            ServiceException.fail("权益订单id不能为空");
        }
        return productOrderClient.disneyChangeDate(req);
    }

}
