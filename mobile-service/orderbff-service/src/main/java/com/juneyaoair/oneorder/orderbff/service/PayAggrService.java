package com.juneyaoair.oneorder.orderbff.service;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.request.payMethod.ParamPayMethodDTO;
import com.juneyaoair.flightbasic.response.paynote.PayMethodActivityDTO;
import com.juneyaoair.flightbasic.response.paynote.PayMethodDTO;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.exception.OrderServiceException;
import com.juneyaoair.oneorder.api.pay.dto.*;
import com.juneyaoair.oneorder.api.pay.service.IPayService;
import com.juneyaoair.oneorder.common.dto.enums.RouteTypeEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mobile.dto.PayChannelDetail;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.dto.SubOrderReqDTO;
import com.juneyaoair.oneorder.order.dto.SubOrderResDTO;
import com.juneyaoair.oneorder.order.feign.OrderClient;
import com.juneyaoair.oneorder.restresult.enums.CommonErrorCode;
import com.juneyaoair.oneorder.restresult.enums.SuccessCode;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/29 10:02
 */
@Slf4j
@Service
public class PayAggrService extends CommonService {
    @Autowired
    private IPayService payService;
    @Autowired
    private OrderClient orderClient;

    private static final String SIGN = "SIGN:";

    private static final String IDENTIFY = "IDENTIFY:";

    @Autowired
    private RedisUtil redisUtils;

    /**
     * @param channelCode        客户端真实渠道
     * @param initPayMethodParam
     * @return
     */
    public List<Bank> queryAllPayMethod(String channelCode, InitPayMethodParam initPayMethodParam) {
        String currency = initPayMethodParam.getCurrency();
        ParamPayMethodDTO paramPayMethodDTO = new ParamPayMethodDTO();
        paramPayMethodDTO.setChannel(channelCode);
        paramPayMethodDTO.setPayProductType(initPayMethodParam.getOrderType());
        paramPayMethodDTO.setMerchantPayment(StringUtils.isBlank(initPayMethodParam.getMerchantPayment()) ? "JX" : initPayMethodParam.getMerchantPayment());
        BaseRequestDTO<ParamPayMethodDTO> baseRequestDTO = createBaseRequestDTO(initPayMethodParam, paramPayMethodDTO);
        BaseResultDTO<List<PayMethodDTO>> baseResultDTO = payService.queryAllPayMethod(baseRequestDTO);
        List<Bank> bankList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(baseResultDTO.getResult())) {
            long curTime = System.currentTimeMillis();
            //支付方式遍历
            for (PayMethodDTO payMethodDTO : baseResultDTO.getResult()) {
                Bank bank = new Bank();
                bank.setMethod(payMethodDTO.getPayMethodCode());
                bank.setBankName(payMethodDTO.getPayMethodName());
                bank.setBankLogo(payMethodDTO.getPayLogoStream());
                bank.setSubBankLogo(payMethodDTO.getUnionpayLogoStream());
                bank.setOrderNo(StringUtils.isBlank(payMethodDTO.getOrderNo()) ? 0 : Integer.parseInt(payMethodDTO.getOrderNo()));
                bank.setIsDefault("Y".equals(payMethodDTO.getIsDefault()));
                try{
                    //根据支付方式获取对应支付配置,
                    PayChannelDetail payChannelDetail = findPayChannelInfo(initPayMethodParam.getMerchantPayment(), channelCode, payMethodDTO.getPayMethodCode(), initPayMethodParam.getOrderType(),currency);
                    if (payChannelDetail != null) {
                        bank.setMode(payChannelDetail.getMode());
                    }
                    //银行活动信息
                    bank.setBankActivityInfo(initBankActivityInfo(curTime, initPayMethodParam, payMethodDTO));
                    bankList.add(bank);
                }catch (Exception e){
                    log.error("支付配置获取存在异常:",e);
                }
            }
        }
        return bankList;
    }

    /**
     * 发起支付
     *
     * @return
     */
    public PayResult doPay(String headChannelCode, LanguageEnum languageEnum, OrderPayParam orderPayParam, String ffpCardNo, HttpServletResponse response) {
        String currency = orderPayParam.getCurrency();
        //查询支付配置信息
        PayChannelDetail payChannelDetail = findPayChannelInfo(orderPayParam.getMerchantPayment(), headChannelCode, orderPayParam.getMethod(), orderPayParam.getOrderType(),currency);
        //查询活动信息
        List<Bank> bankList = queryAllPayMethod(headChannelCode, orderPayParam);
        if (CollectionUtils.isNotEmpty(bankList)) {
            Optional<Bank> optional = bankList.stream().filter(bank -> bank.getMethod().equals(orderPayParam.getMethod())).findFirst();
            if (optional.isPresent()) {
                BankActivityInfo bankActivityInfo = optional.get().getBankActivityInfo();
                if (ObjectUtils.isNotEmpty(bankActivityInfo) && StringUtils.isNotBlank(bankActivityInfo.getPromoParam())) {
                    payChannelDetail.setPromoParam(bankActivityInfo.getPromoParam());
                }
            }
        }
        return payService.doPay(payChannelDetail,languageEnum,currency, orderPayParam, ffpCardNo,response);
    }

    /**
     * @param channelCode
     * @param dftSignRequest
     * @param ffpCardNo
     * @return void
     * <AUTHOR>
     * @Description 身份验证
     * @Date 12:26 2024/5/27
     **/
    public void toIdentify(String channelCode, DFTSignRequest dftSignRequest, String ffpCardNo) {
        if (toCheckSignCircumstance(dftSignRequest, ffpCardNo)) {
            throw OrderServiceException.fail("已签约，请勿重复进行身份验证");
        }
        //查询支付配置信息
        PayChannelDetail payChannelDetail = findPayChannelInfo(dftSignRequest.getMerchantPayment(), channelCode, dftSignRequest.getMethod(), dftSignRequest.getOrderType());
        payService.toIdentify(payChannelDetail, dftSignRequest, ffpCardNo);
        String redisKey = IDENTIFY + ffpCardNo + ":" + dftSignRequest.getIdNo() + ":" + dftSignRequest.getCardNo() + ":" + dftSignRequest.getPhone();
        redisUtils.set(redisKey, "IDENTIFIED", 3 * 60L);
    }

    /**
     * @param channelCode
     * @param dftSignRequest
     * @param ffpCardNo
     * @return void
     * <AUTHOR>
     * @Description 签约操作
     * @Date 12:31 2024/5/27
     **/
    public void toSign(String channelCode, DFTSignRequest dftSignRequest, String ffpCardNo) {
        if (toCheckSignCircumstance(dftSignRequest, ffpCardNo)) {
            throw OrderServiceException.fail("已签约，请勿重复进行签约操作");
        }
        if (!toCheckIdentifyCircumstance(dftSignRequest, ffpCardNo)) {
            throw OrderServiceException.fail("未验证身份或身份验证信息已失效，请重新进行身份验证");
        }
        //查询支付配置信息
        PayChannelDetail payChannelDetail = findPayChannelInfo(dftSignRequest.getMerchantPayment(), channelCode, dftSignRequest.getMethod(), dftSignRequest.getOrderType());
        payService.toSign(payChannelDetail, dftSignRequest, ffpCardNo);
    }

    /**
     * @param ffpCardNo
     * @return void
     * <AUTHOR>
     * @Description 签约操作
     * @Date 12:31 2024/5/27
     **/
    public void toReSign(String ffpCardNo) {
        //查询支付配置信息
        PayChannelDetail payChannelDetail = findPayChannelInfo("JX", "B2C", "DEFUTONG", "D");
        payService.toReSign(payChannelDetail, ffpCardNo);
    }


    /**
     * @param ffpCardNo
     * @return boolean
     * <AUTHOR>
     * @Description 查询德付通签约情况
     * @Date 9:57 2024/5/27
     **/
    public boolean toCatchDFTSignInfo(String ffpCardNo) {
        return payService.toCatchDFTSignInfo(ffpCardNo);
    }


    /**
     * @param dftSignRequest
     * @return boolean
     * <AUTHOR>
     * @Description 检查签约情况 缓存Key 会员卡号:身份证号:银行卡号:银行预留手机号 true-已签约
     * @Date 12:43 2024/5/27
     **/
    private boolean toCheckSignCircumstance(DFTSignRequest dftSignRequest, String ffpCardNo) {
        String redisKey = SIGN + ffpCardNo + ":" + dftSignRequest.getIdNo() + ":" + dftSignRequest.getCardNo() + ":" + dftSignRequest.getPhone();
        if (StringUtils.isNotEmpty((String) redisUtils.get(redisKey))) {
            return true;
        }
        if (payService.toCatchDFTSignInfo(ffpCardNo)) {
            redisUtils.set(redisKey, "SIGNED", 3 * 60L);
            return true;
        }
        return false;
    }

    /**
     * @param dftSignRequest
     * @param ffpCardNo
     * @return boolean
     * <AUTHOR>
     * @Description 检查身份校验情况 缓存Key 会员卡号:身份证号:银行卡号:银行预留手机号 true-已校验身份信息
     * @Date 12:54 2024/5/27
     **/
    private boolean toCheckIdentifyCircumstance(DFTSignRequest dftSignRequest, String ffpCardNo) {
        String redisKey = IDENTIFY + ffpCardNo + ":" + dftSignRequest.getIdNo() + ":" + dftSignRequest.getCardNo() + ":" + dftSignRequest.getPhone();
        return StringUtils.isNotEmpty((String) redisUtils.get(redisKey));
    }

    /**
     * @param channelCode
     * @param verifyCodeSendParam
     * @param ffpCardNo
     * @param response
     * @return com.juneyaoair.oneorder.api.pay.dto.HoMotoVerifyResponse
     * <AUTHOR>
     * @Description 交行信用卡支付发送验证码
     * @Date 14:38 2024/4/18
     **/
    public VerifyCodeSendResult toSendVerifyCode(String channelCode, VerifyCodeSendParam verifyCodeSendParam, String ffpCardNo, HttpServletResponse response) {
        //查询支付配置信息
        PayChannelDetail payChannelDetail = findPayChannelInfo(verifyCodeSendParam.getMerchantPayment(), channelCode, verifyCodeSendParam.getMethod(), verifyCodeSendParam.getOrderType());
        return payService.toSendVerifyCode(payChannelDetail, verifyCodeSendParam, ffpCardNo, response);
    }

    /**
     * 查询订单支付方式
     *
     * @param channelNo
     * @param ffpId
     * @param ffpId
     * @param orderParam
     */
    public OrderStatusDto queryPayState(String channelNo, String ffpId, String ffpNo, OrderParam orderParam) {
        SubOrderReqDTO subOrderReqDTO = new SubOrderReqDTO();
        //无卡号认为是匿名访问
        if(StringUtils.isBlank(ffpId)){
            ffpId = "-1";
        }
        subOrderReqDTO.setCustomerNo(ffpId);
        subOrderReqDTO.setChannelOrderNo(orderParam.getChannelOrderNo());
        subOrderReqDTO.setOrderNo(orderParam.getOrderNo());
        RequestData<SubOrderReqDTO> requestData = new RequestData();
        requestData.setFfpId(ffpId);
        requestData.setFfpNo(ffpNo);
        requestData.setChannelNo(channelNo);
        requestData.setData(subOrderReqDTO);
        long start = System.currentTimeMillis();
        printReqLog("", "orderClient.querySubOrder", requestData);
        ResponseData<SubOrderResDTO> responseData = orderClient.querySubOrder(requestData);
        printResultLog("", "orderClient.querySubOrder", responseData, start);
        if (!SuccessCode.SUCCESS.getCode().equals(responseData.getCode())) {
            throw OrderServiceException.fail(CommonErrorCode.BUSINESS_ERROR, responseData.getMessage());
        }
        SubOrderResDTO subOrderResDTO = responseData.getData();
        return OrderStatusDto.builder()
                .orderNo(subOrderResDTO.getOrderNo())
                .channelOrderNo(subOrderResDTO.getChannelOrderNo())
                .state(subOrderResDTO.getState())
                .payState(subOrderResDTO.getPayState())
                .orderState(subOrderResDTO.getOrderState())
                .amount(subOrderResDTO.getAmount())
                .createDatetime(subOrderResDTO.getCreateDatetime())
                .linker(subOrderResDTO.getLinker())
                .linkerHandPhone(subOrderResDTO.getLinkerHandphone())
                .originChannelCode(subOrderResDTO.getOriginChannelCode())
                .paidDatetime(subOrderResDTO.getPaidDatetime())
                .payMethod(subOrderResDTO.getPayMethod())
                .build();
    }

    /**
     * 渲染活动信息
     *
     * @param curTime
     * @param initPayMethodParam
     * @param payMethodDTO
     * @return
     */
    private BankActivityInfo initBankActivityInfo(long curTime, InitPayMethodParam initPayMethodParam, PayMethodDTO payMethodDTO) {
        if (CollectionUtils.isNotEmpty(payMethodDTO.getActivityList())) {
            List<PayMethodActivityDTO> payMethodActivityDTOList = new ArrayList<>();
            for (PayMethodActivityDTO payMethodActivityDTO : payMethodDTO.getActivityList()) {
                //存在开始结束时间时，需要进行时间检验，否则不做相关相关控制
                if (payMethodActivityDTO.getStartTime() != null && payMethodActivityDTO.getEndTime() != null) {
                    long start = payMethodActivityDTO.getStartTime().getTime();
                    long end = payMethodActivityDTO.getEndTime().getTime();
                    Boolean isShowPayMethodActivity = true;
                    //立减活动目前支持支付宝
                    if ("ALIPAY".equals(payMethodDTO.getPayMethodCode())) {
                        //支付宝立减活动 是否区分航线  Y  N
                        Boolean isDistinguishRoute = "Y".equals(payMethodActivityDTO.getDistinguishRoute());
                        if (isDistinguishRoute) {
                            isShowPayMethodActivity = false;
                            String routeData = initPayMethodParam.getDepAirport() + "-" + initPayMethodParam.getArrAirport() + "-" + initPayMethodParam.getInterFlag();
                            if (StringUtils.isNotBlank(payMethodActivityDTO.getRouteData())) {
                                isShowPayMethodActivity = payMethodActivityDTO.getRouteData().contains(routeData);
                            }
                            //往返航班取去程的的活动参数
                            if (RouteTypeEnum.RT.getCode().equals(initPayMethodParam.getRouteType()) && !isShowPayMethodActivity && CollectionUtils.isEmpty(payMethodActivityDTOList)) {
                                String rtouteData = initPayMethodParam.getArrAirport() + "-" + initPayMethodParam.getDepAirport() + "-" + initPayMethodParam.getInterFlag();
                                isShowPayMethodActivity = payMethodActivityDTO.getRouteData().contains(rtouteData);
                            }
                        }
                    }
                    if (curTime >= start && curTime <= end && isShowPayMethodActivity) {
                        payMethodActivityDTOList.add(payMethodActivityDTO);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(payMethodActivityDTOList)) {
                //活动时间排序 开始时间升序 结束时间升序
                payMethodActivityDTOList.sort(Comparator.comparing(PayMethodActivityDTO::getStartTime).thenComparing(PayMethodActivityDTO::getEndTime));
                BankActivityInfo bankActivityInfo = BankActivityInfo.builder()
                        .bankActivity(payMethodActivityDTOList.get(0).getPromotionDescription())
                        .bankActivityUrl(payMethodActivityDTOList.get(0).getUrl())
                        .bankActivityRemark(payMethodActivityDTOList.get(0).getActivityDescription())
                        .promoParam(payMethodActivityDTOList.get(0).getPromoParam()).build();
                return bankActivityInfo;
            }
        }
        return null;
    }
}
