package com.juneyaoair.oneorder.orderbff.context;


import com.juneyaoair.oneorder.orderbff.dto.ThreadParamDTO;

/**
 * 用于线程间传递值
 */
public class OrderContext {


    private static final ThreadLocal<ThreadParamDTO> CONTEXT_HOLDER = new ThreadLocal<>();


    public static ThreadParamDTO getContext() {
        if (CONTEXT_HOLDER.get() == null) {
            CONTEXT_HOLDER.set(new ThreadParamDTO());
        }
        return CONTEXT_HOLDER.get();
    }

    public static void clear() {
        CONTEXT_HOLDER.remove();
    }
}
