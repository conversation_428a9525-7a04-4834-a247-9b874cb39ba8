package com.juneyaoair.oneorder.orderbff.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.cuss.dto.booking.request.flight.FlightInfoBase;
import com.juneyaoair.cuss.dto.booking.request.member.MemberRequest;
import com.juneyaoair.cuss.dto.booking.response.member.MemberResponse;
import com.juneyaoair.cuss.dto.booking.response.seat.PayInfo;
import com.juneyaoair.cuss.enums.PayTypeEnum;
import com.juneyaoair.cuss.fegin.CussCustomerClient;
import com.juneyaoair.cuss.request.seat.SeatOrderParam;
import com.juneyaoair.cuss.response.seat.SeatOrderRemark;
import com.juneyaoair.cuss.response.seat.SeatOrderResult;
import com.juneyaoair.cuss.response.seat.SeatOrderSingle;
import com.juneyaoair.cuss.service.CussBookingService;
import com.juneyaoair.cuss.util.CussClientUtil;
import com.juneyaoair.cuss.util.CussRequestUtil;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.crm.dto.common.MileageTotalBillSoaModel;
import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleReq;
import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleRes;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.dto.couponorder.*;
import com.juneyaoair.oneorder.order.dto.score.ApplyScoreReqDto;
import com.juneyaoair.oneorder.order.dto.score.ApplyScoreRespDto;
import com.juneyaoair.oneorder.order.dto.score.CouponOrderPayInfo;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderPointPostParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponSubOrderPayInfo;
import com.juneyaoair.oneorder.orderbff.dto.coupon.OrderPointPostResult;
import com.juneyaoair.oneorder.orderbff.dto.seat.PaySeatExtInfo;
import com.juneyaoair.oneorder.orderbff.enums.EnumOrderDetailState;
import com.juneyaoair.oneorder.orderbff.enums.EnumSubOrderDetailState;
import com.juneyaoair.oneorder.orderbff.utils.RequestDataUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.AirJsonUtil;
import com.juneyaoair.oneorder.tools.utils.AirStringUtil;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @version v1.0
 * @date 2024/9/24 14:55
 * @description
 **/
@Slf4j
@Component("PaySeatCouponOrderBizImpl")
public class PaySeatCouponOrderBizImpl extends AbsCouponOrderBizImpl {

    private static final TypeReference<Set<PayTypeEnum>> SET_PAY_TYPE_REFERENCE = new TypeReference<Set<PayTypeEnum>>(){};

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CacheService cacheService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IMemberService memberService;

    @Autowired
    private CussCustomerClient cussCustomerClient;
    @Autowired
    private CussBookingService cussBookingService;

    /**
     * 接口文档查询权益订单详情：https://kdocs.cn/l/crfI4U0bdCBY
     *
     * @param requestData
     * @return
     */
    @Override
    public ResponseData<CouponOrderDetailQueryResDTO> queryMyCouponOrderDetail(RequestData<CouponOrderDetailQueryReqDTO> requestData) {
        // 查询订单信息
        CouponOrderDetailQueryResDTO couponOrderDetailQueryRes = queryCouponOrderBaseInfo(requestData, true);
        CouponOrderBaseInfoDTO couponOrderDetail = couponOrderDetailQueryRes.getCouponOrderDetail();
        if (null == couponOrderDetail) {
            throw MultiLangServiceException.fail("查询权益订单基本信息发生错误");
        }
        // 不存在子订单信息直接返回
        if (CollectionUtils.isEmpty(couponOrderDetail.getCouponSubOrderInfo())) {
            return ResponseData.success(couponOrderDetailQueryRes);
        }
        // 获取首个权益订单信息 航段信息不为空 设置机型及飞行时长
        CouponSubOrderInfoDTO couponSubOrderInfoFirst = couponOrderDetail.getCouponSubOrderInfo().get(0);
        CouponOrderSegmentInfoDTO segmentInfo = couponSubOrderInfoFirst.getSegmentInfo();
        if (null != segmentInfo) {
            // 出发机场信息
            ApiAirPortInfoDto depAirPortInfo = cacheService.getLocalAirport(segmentInfo.getDepAirportCode());
            if (null != depAirPortInfo) {
                segmentInfo.setDepCityName(BffI18nUtils.getCityName(depAirPortInfo, requestData.getLanguage()));
                segmentInfo.setDepCityCode(depAirPortInfo.getCityCode());
                segmentInfo.setDepAirportName(BffI18nUtils.getAirportName(depAirPortInfo, requestData.getLanguage()));
            }
            // 到达机场信息
            ApiAirPortInfoDto arrAirPortInfo = cacheService.getLocalAirport(segmentInfo.getArrAirportCode());
            if (null != arrAirPortInfo) {
                segmentInfo.setArrCityName(BffI18nUtils.getCityName(arrAirPortInfo, requestData.getLanguage()));
                segmentInfo.setArrCityCode(arrAirPortInfo.getCityCode());
                segmentInfo.setArrAirportName(BffI18nUtils.getAirportName(arrAirPortInfo, requestData.getLanguage()));
            }
            // 航班信息
            FlightInfoDTO flightInfo = basicService.searchSingleFlightInfo(segmentInfo.getFlightNo(), segmentInfo.getDepFlightDate(), segmentInfo.getDepAirportCode(), segmentInfo.getArrAirportCode());
            if (null != flightInfo) {
                segmentInfo.setDepTime(DateUtil.timeFormat(flightInfo.getDepDateTime()));
                segmentInfo.setArrTime(DateUtil.timeFormat(flightInfo.getArrDateTime()));
                segmentInfo.setPlaneType(flightInfo.getPlanType());
                segmentInfo.setPlaneTypeName(basicService.getPlaneTypeName(flightInfo.getPlanType(), requestData.getLanguage()));
                segmentInfo.setDepAirportTerminal(flightInfo.getDepAirportTerminal());
                segmentInfo.setArrAirportTerminal(flightInfo.getArrAirportTerminal());
                // 基于中国时间计算飞行时长
                segmentInfo.setDuration(DateUtil.calDuration(flightInfo.getDepDateChinaTime(), flightInfo.getArrDateChinaTime()));
            }
        }

        // 查询选座信息
        SeatOrderParam seatOrderParam = new SeatOrderParam();
        seatOrderParam.setChannelOrderNo(requestData.getData().getChannelOrderNo());
        Map<String, SeatOrderSingle> seatOrderSingleMap = getSeatOrderInfo(requestData, seatOrderParam);
        for (CouponSubOrderInfoDTO couponSubOrderInfo : couponOrderDetail.getCouponSubOrderInfo()) {
            CouponOrderPayInfoDTO payInfo = couponOrderDetail.getPayInfo();
            if (Objects.nonNull(payInfo) && AirStringUtil.isNotEmpty(payInfo.getPayState())) {
                CouponOrderRebateInfoDTO rebateInfo = couponSubOrderInfo.getRebateInfo();
                EnumSubOrderDetailState orderDetailState = EnumSubOrderDetailState.convertSubOrderState(
                        payInfo.getPayState(),
                        Objects.nonNull(rebateInfo) ? rebateInfo.getIsVoluntaryRefund() : "",
                        couponSubOrderInfo.getCouponState(),
                        Objects.nonNull(rebateInfo) ? rebateInfo.getRebateState() : "");
                couponSubOrderInfo.setSubOrderState(orderDetailState.getStateCode());
                couponSubOrderInfo.setSubOrderStateName(orderDetailState.getTranslatedStr(SecurityContextHolder.getLanguage()));
            }
            SeatOrderSingle seatOrderSingle = seatOrderSingleMap.get(couponSubOrderInfo.getCouponCode());
            if (null != seatOrderSingle) {
                PaySeatExtInfo paySeatExtInfo = new PaySeatExtInfo();
                paySeatExtInfo.setPaySeatNo(seatOrderSingle.getSeatOrderRemark().getSeatNo());
                paySeatExtInfo.setEmdTktNo(seatOrderSingle.getEmdNo());
                SeatOrderRemark seatOrderRemark = seatOrderSingle.getSeatOrderRemark();
                if (null != seatOrderRemark) {
                    paySeatExtInfo.setMemberCode(seatOrderRemark.getMemberCode());
                    paySeatExtInfo.setPayType(seatOrderRemark.getPayType());
                    paySeatExtInfo.setPayTypeList(null == seatOrderRemark.getPayTypeList() ? null : JSON.parseObject(seatOrderRemark.getPayTypeList(), SET_PAY_TYPE_REFERENCE));
                    paySeatExtInfo.setSelf(seatOrderRemark.getSelf());
                }
                couponSubOrderInfo.setExtInfo(paySeatExtInfo);
            }
        }
        return ResponseData.success(couponOrderDetailQueryRes);
    }

    /**
     * 调用值机选座系统获取订单的额外信息
     *
     * @param requestData
     * @param seatOrderParam
     * @return
     */
    private Map<String, SeatOrderSingle> getSeatOrderInfo(RequestData requestData, SeatOrderParam seatOrderParam) {
        BaseRequestDTO<SeatOrderParam> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(requestData.getOriginIp());
        baseRequest.setVersion("1.0");
        baseRequest.setChannelCode(requestData.getChannelNo());
        baseRequest.setRequest(seatOrderParam);
        String uuid = UUID.randomUUID().toString();
        log.info("调用CUSS获取优选座位订单信息开始，请求ID：{}请求参数：{}", uuid, JSON.toJSONString(baseRequest));
        BaseResultDTO<SeatOrderResult> baseResult = cussCustomerClient.getSeatOrder(baseRequest, uuid);
        log.info("调用CUSS获取优选座位订单信息结束，请求ID：{} 返回结果：{}", uuid, JSON.toJSONString(baseResult));
        SeatOrderResult seatOrderResult = CussClientUtil.getResult(baseResult);
        List<SeatOrderSingle> seatOrderSingleList = null == seatOrderResult || CollectionUtils.isEmpty(seatOrderResult.getSeatOrderList()) ? null : seatOrderResult.getSeatOrderList();
        if (CollectionUtils.isEmpty(seatOrderSingleList)) {
            log.error("值机选座系统获取权益订单信息失败，请求ID：{} 请求参数：{}", uuid, JSON.toJSONString(seatOrderParam));
            return Maps.newHashMap();
        }
        return seatOrderSingleList.stream().collect(Collectors.toMap(SeatOrderSingle::getCouponCode, Function.identity()));
    }

    @Override
    public ResponseData<CouponOrderRefundDetailResDTO> queryCouponOrderRefundInfo(RequestData<CouponOrderRefundDetailReqDTO> requestData) {
        CouponOrderRefundDetailResDTO couponOrderRefundDetailRes = queryCouponOrderRefundInfoBase(requestData, true);
        if (null == couponOrderRefundDetailRes || null == couponOrderRefundDetailRes.getRefundInfoDetail()) {
            throw MultiLangServiceException.fail("查询权益订单退单基本信息发生错误");
        }
        CouponOrderRefundInfoDTO refundInfoDetail = couponOrderRefundDetailRes.getRefundInfoDetail();

        CouponOrderPayInfoDTO payInfo = refundInfoDetail.getPayInfo();
        CouponOrderRebateInfoDTO rebateInfo = refundInfoDetail.getRebateInfo();
        EnumSubOrderDetailState orderDetailState = EnumSubOrderDetailState.convertSubOrderState(
                Objects.nonNull(payInfo) ? payInfo.getPayState() : EnumOrderDetailState.UnPay.getStateCode(),
                Objects.nonNull(rebateInfo) ? rebateInfo.getIsVoluntaryRefund() : "",
                refundInfoDetail.getCouponState(),
                Objects.nonNull(rebateInfo) ? rebateInfo.getRebateState() : ""
        );
        refundInfoDetail.setCouponState(orderDetailState.getStateCode());
        // 暂时未使用REMARK项
        refundInfoDetail.setRemark(null);

        CouponOrderRefundDetailReqDTO couponOrderRefundDetailReq = requestData.getData();
        // 查询选座信息
        SeatOrderParam seatOrderParam = new SeatOrderParam();
        seatOrderParam.setChannelOrderNo(couponOrderRefundDetailReq.getChannelOrderNo());
        seatOrderParam.setCouponCode(couponOrderRefundDetailReq.getCouponCode());
        Map<String, SeatOrderSingle> seatOrderSingleMap = getSeatOrderInfo(requestData, seatOrderParam);
        SeatOrderSingle seatOrderSingle = seatOrderSingleMap.get(couponOrderRefundDetailReq.getCouponCode());
        if (null != seatOrderSingle) {
            PaySeatExtInfo paySeatExtInfo = new PaySeatExtInfo();
            paySeatExtInfo.setPaySeatNo(seatOrderSingle.getSeatOrderRemark().getSeatNo());
            paySeatExtInfo.setEmdTktNo(seatOrderSingle.getEmdNo());
            refundInfoDetail.setExtInfo(paySeatExtInfo);
        }
        return ResponseData.success(couponOrderRefundDetailRes);
    }

    @Override
    public OrderPointPostResult getOrderPointPost(RequestData<? extends CouponOrderParam> requestData) {
        OrderPointPostResult orderPointPostResult = new OrderPointPostResult();
        CouponOrderParam couponOrderParam = requestData.getData();
        // 默认不支持积分后置
        orderPointPostResult.setUseScore(false);
        // 查询订单信息
        CouponOrderDetailQueryReqDTO couponOrderDetailQueryReq = new CouponOrderDetailQueryReqDTO();
        couponOrderDetailQueryReq.setChannelOrderNo(couponOrderParam.getChannelOrderNo());
        couponOrderDetailQueryReq.setOrderNo(couponOrderParam.getOrderNo());
        couponOrderDetailQueryReq.setCouponSource(couponOrderParam.getCouponSource());
        RequestData<CouponOrderDetailQueryReqDTO> couponOrderRequestData = RequestDataUtil.getRequestData(requestData, couponOrderDetailQueryReq);
        CouponOrderDetailQueryResDTO couponOrderDetailQueryRes = queryCouponOrderBaseInfo(couponOrderRequestData, false);
        CouponOrderBaseInfoDTO couponOrderDetail = couponOrderDetailQueryRes.getCouponOrderDetail();
        if (null == couponOrderDetail) {
            log.error("订单未获取到订单信息，订单号：{}", couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        if (CollectionUtils.isEmpty(couponOrderDetail.getCouponSubOrderInfo())) {
            log.error("订单未获取到子订单信息，订单号：{}", couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        if (!EnumOrderDetailState.UnPay.getStateCode().equals(couponOrderDetail.getOrderState())) {
            log.error("订单状态非待支付状态，订单号：{}", couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        CouponOrderPayInfoDTO couponOrderPayInfo = couponOrderDetail.getPayInfo();
        // 订单存在使用积分数 直接返回
        if (null != couponOrderPayInfo.getFfpUseTotalScore() && couponOrderPayInfo.getFfpUseTotalScore() != 0) {
            return orderPointPostResult;
        }
        // 查询选座业务渠道、航班支付配置信息
        CouponSubOrderInfoDTO couponSubOrderInfoFirst = couponOrderDetail.getCouponSubOrderInfo().get(0);
        CouponOrderSegmentInfoDTO segmentInfo = couponSubOrderInfoFirst.getSegmentInfo();
        FlightInfoBase flightInfoBase = new FlightInfoBase();
        flightInfoBase.setFlightDate(segmentInfo.getDepFlightDate());
        flightInfoBase.setFlightNo(segmentInfo.getFlightNo());
        flightInfoBase.setDepAirportCode(segmentInfo.getDepAirportCode());
        flightInfoBase.setArrAirportCode(segmentInfo.getArrAirportCode());
        BaseRequestDTO<FlightInfoBase> baseRequest = CussRequestUtil.createRequest(flightInfoBase, requestData.getChannelNo());
        PayInfo payInfo = cussBookingService.getPayInfo(baseRequest);
        // 渠道不支持积分后置 直接返回
        if (!"Y".equals(payInfo.getPointPost())) {
            log.error("渠道暂不支持积分后置，渠道：{} 订单号：{}", requestData.getChannelNo(), couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        // 选座支付配置信息不支持积分支付 直接返回
        if (!payInfo.getPayTypeList().contains(PayTypeEnum.POINT)) {
            log.error("渠道不支持积分支付，渠道：{} 订单号：{}", requestData.getChannelNo(), couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        boolean useScore = false;
        // 可使用积分总金额
        BigDecimal orderMaxScorePrize = BigDecimal.ZERO;
        List<CouponSubOrderPayInfo> couponSubOrderList = Lists.newArrayList();
        for (CouponSubOrderInfoDTO couponSubOrderInfo : couponOrderDetail.getCouponSubOrderInfo()) {
            CouponSubOrderPayInfo couponSubOrderPayInfo = new CouponSubOrderPayInfo();
            // 积分分配方式
            switch (payInfo.getPointUseType()) {
                case "AMOUNT_AVG":
                    useScore = true;
                    orderMaxScorePrize = orderMaxScorePrize.add(new BigDecimal(couponSubOrderInfo.getActPaidAmount()));
                    couponSubOrderPayInfo.setCouponOrderId(couponSubOrderInfo.getCouponOrderId());
                    couponSubOrderList.add(couponSubOrderPayInfo);
                    break;
                case "SELF_AMOUNT_AVG":
                    // 格式为 SeatOrderRemark 数据源是cuss下单时保存
                    String remark = couponSubOrderInfo.getRemark();
                    if (StringUtils.isBlank(remark)) {
                        break;
                    }
                    // 查询客票信息
                    MemberRequest memberRequest = new MemberRequest();
                    memberRequest.setTicketNumber(couponSubOrderInfo.getTicketNo());
                    BaseRequestDTO<MemberRequest> memberRequestBase = CussRequestUtil.createRequest(memberRequest, requestData.getChannelNo());
                    MemberResponse memberInfo = cussBookingService.getMemberInfo(memberRequestBase);
                    // 本人订单
                    if (requestData.getFfpNo().equals(memberInfo.getMemberCardNo())) {
                        useScore = true;
                        orderMaxScorePrize = orderMaxScorePrize.add(new BigDecimal(couponSubOrderInfo.getActPaidAmount()));
                        couponSubOrderPayInfo.setCouponOrderId(couponSubOrderInfo.getCouponOrderId());
                        couponSubOrderList.add(couponSubOrderPayInfo);
                    }
                    break;
                // 非以上积分分配方式默认不支持积分后置
                default:
                    return orderPointPostResult;
            }
        }
        // 订单支持积分后置 返回
        if (!useScore) {
            log.error("无符合积分分配规则的订单，渠道：{} 订单号：{}", requestData.getChannelNo(), couponOrderParam.getOrderNo());
            return orderPointPostResult;
        }
        // 查询用户可使用积分信息
        MileageTotalBillSoaModel mileageTotalBillSoaModel = memberService.mileageTotalBillSoaModel(requestData.getChannelNo(), requestData.getOriginIp(), requestData.getFfpNo());
        Integer availableMiles = mileageTotalBillSoaModel.getAvailableMiles();
        if (null == availableMiles || availableMiles < 1) {
            log.error("用户无可用积分，订单号：{} 会员卡号：{}", couponOrderParam.getOrderNo(), requestData.getFfpNo());
            return orderPointPostResult;
        }
        orderPointPostResult.setAvailableScore(availableMiles);
        orderPointPostResult.setUseScore(true);
        // 查询汇率信息
        QueryScoreRuleReq queryScoreRuleReq = new QueryScoreRuleReq();
        queryScoreRuleReq.setCurrency(couponOrderPayInfo.getCurrency());
        queryScoreRuleReq.setScoreNum(BigDecimal.ONE);
        QueryScoreRuleRes soreRateRule = getSoreRateRule(queryScoreRuleReq);
        orderPointPostResult.setRate(soreRateRule.getRate());
        // 根据订单金额、汇率计算可使用的最大积分数
        int orderMaxScore = orderMaxScorePrize.divide(soreRateRule.getRate(), 0, RoundingMode.UP).intValue();
        // 用户可用积分 < 计算后的最大订单可使用积分数 最多可使用积分变更为用户可用积分并计算积分抵扣金额
        if (availableMiles < orderMaxScore) {
            // 最多可使用积分变更为用户可用积分
            orderMaxScore = availableMiles;
            // 计算积分抵扣金额
            orderMaxScorePrize = soreRateRule.getRate().multiply(BigDecimal.valueOf(orderMaxScore)).setScale(2, RoundingMode.DOWN);
        }
        orderPointPostResult.setOrderMaxScorePrize(orderMaxScorePrize);
        orderPointPostResult.setOrderMaxScore(orderMaxScore);
        orderPointPostResult.setCouponSubOrderList(couponSubOrderList);
        return orderPointPostResult;
    }

    @Override
    public ApplyScoreRespDto orderPointPost(RequestData<CouponOrderPointPostParam> requestData) {
        CouponOrderPointPostParam couponOrderPointPostParam = requestData.getData();
        // 订单后置配置信息获取 优先使用缓存数据
        final String redisKey = RedisConstantConfig.ORDER_POINT_POST + couponOrderPointPostParam.getOrderNo() + ":" + couponOrderPointPostParam.getChannelOrderNo();
        OrderPointPostResult orderPointPost = redisUtil.getObject(redisKey, OrderPointPostResult.class);
        if (null == orderPointPost) {
            orderPointPost = getOrderPointPost(requestData);
        }
        if (!orderPointPost.getUseScore()) {
            throw MultiLangServiceException.fail("订单不支持积分后置");
        }
        // 执行积分后置操作
        ApplyScoreReqDto applyScoreReq = new ApplyScoreReqDto();
        applyScoreReq.setOrderNo(couponOrderPointPostParam.getOrderNo());
        applyScoreReq.setChannelOrderNo(couponOrderPointPostParam.getChannelOrderNo());
        applyScoreReq.setUseScore(couponOrderPointPostParam.getUseScore());
        applyScoreReq.setCurrencyByScore(couponOrderPointPostParam.getCurrencyByScore());
        List<CouponOrderPayInfo> couponOrderPayInfoList = Lists.newArrayList();
        for (CouponSubOrderPayInfo couponSubOrderPayInfo : orderPointPost.getCouponSubOrderList()){
            CouponOrderPayInfo couponOrderPayInfo = new CouponOrderPayInfo();
            // 默认子订单抵扣积分和积分抵扣金额不传   由订单系统基于子订单金额占比平均分配
            couponOrderPayInfo.setCouponOrderId(couponSubOrderPayInfo.getCouponOrderId().toString());
            couponOrderPayInfoList.add(couponOrderPayInfo);
        }
        applyScoreReq.setCouponOrderPayInfoList(couponOrderPayInfoList);
        RequestData<ApplyScoreReqDto> applyScoreRequest = RequestDataUtil.getRequestData(requestData, applyScoreReq);
        log.info("Request: applyScore ===> 权益订单积分后置请求: {}", AirJsonUtil.toJSONString(requestData));
        ResponseData<ApplyScoreRespDto> responseData = orderClient.applyScore(applyScoreRequest);
        log.info("Response: applyScore ===> 权益订单积分后置响应: {}", AirJsonUtil.toJSONString(responseData));
        if (null == responseData) {
            log.error("applyScore ===> 权益订单积分后置返回空");
            throw MultiLangServiceException.fail("权益订单积分后置发生错误");
        }
        if (!WSEnum.SUCCESS.name().equals(responseData.getCode())) {
            throw MultiLangServiceException.fail(responseData.getMessage());
        }
        return responseData.getData();
    }

}
