package com.juneyaoair.oneorder.orderbff.utils;

import com.juneyaoair.oneorder.restresult.response.RequestData;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
public class RequestDataUtil {

    /***
     * @param requestData
     * @param t
     * @return
     * @param <T>
     */
    public static <T> RequestData<T> getRequestData(RequestData requestData, T t) {
        RequestData<T> request = new RequestData<>();
        BeanUtils.copyProperties(requestData, request, "data");
        request.setData(t);
        return request;
    }

}
