package com.juneyaoair.oneorder.orderbff.service.impl;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.order.dto.couponorder.*;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.restresult.response.SuccessResponseData;
import com.juneyaoair.oneorder.tools.utils.AirCollectionUtil;
import com.juneyaoair.oneorder.tools.utils.AirJsonUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @version v1.0
 * @date 2024/9/29 14:41
 * @description
 **/
@Component("MailTravelCouponOrderBizImpl")
public class MailTravelCouponOrderBizImpl extends AbsCouponOrderBizImpl {

    @Override
    public ResponseData<CouponOrderDetailQueryResDTO> queryMyCouponOrderDetail(RequestData<CouponOrderDetailQueryReqDTO> requestData) {
        CouponOrderDetailQueryResDTO detailInfoData = super.queryCouponOrderBaseInfo(requestData, false);

        CouponOrderDetailQueryReqDTO detailInfoReqData = requestData.getData();
        CouponOrderExtInfoQueryReqDTO couponOrderExtInfoQueryReqDTO = new CouponOrderExtInfoQueryReqDTO();
        couponOrderExtInfoQueryReqDTO.setOrderNo(detailInfoReqData.getOrderNo());
        couponOrderExtInfoQueryReqDTO.setChannelOrderNo(detailInfoReqData.getChannelOrderNo());
        couponOrderExtInfoQueryReqDTO.setCouponSource(detailInfoReqData.getCouponSource());
        RequestData<CouponOrderExtInfoQueryReqDTO> extInfoQueryReqDTO = new RequestData<>(
                requestData.getChannelNo(),
                requestData.getFfpId(),
                requestData.getFfpNo(),
                requestData.getOriginIp(),
                couponOrderExtInfoQueryReqDTO);

        CouponOrderCommonExtInfoQueryResDTO extInfoData = super.queryCouponOrderExtInfo(extInfoQueryReqDTO);

        List<Object> extInfoList = extInfoData.getExtInfoList();

        if (AirCollectionUtil.isNotEmpty(extInfoList)) {
            List<LoungeCouponExtInfoDTO> extInfoDTOList = new ArrayList<>();
            for (Object obj : extInfoList) {
                extInfoDTOList.add(AirJsonUtil.parseObject(AirJsonUtil.toJSONString(obj), LoungeCouponExtInfoDTO.class));
            }

            Map<Integer, LoungeCouponExtInfoDTO> extInfoMap = extInfoDTOList.stream()
                    .collect(Collectors.toMap(LoungeCouponExtInfoDTO::getCouponOrderId, Function.identity()));

            if (Objects.nonNull(detailInfoData) && Objects.nonNull(detailInfoData.getCouponOrderDetail())) {
                CouponOrderBaseInfoDTO couponOrderDetail = detailInfoData.getCouponOrderDetail();
                List<? extends CouponSubOrderInfoDTO> couponSubOrderInfo = couponOrderDetail.getCouponSubOrderInfo();
                if (AirCollectionUtil.isNotEmpty(couponSubOrderInfo)) {
                    couponSubOrderInfo.forEach(subOrderInfo -> {
                        subOrderInfo.setExtInfo(extInfoMap.get(subOrderInfo.getCouponOrderId()));
                    });
                }
            }
        }
        return SuccessResponseData.success(detailInfoData);
    }

    @Override
    public ResponseData<CouponOrderRefundDetailResDTO> queryCouponOrderRefundInfo(RequestData<CouponOrderRefundDetailReqDTO> requestData) {
        throw MultiLangServiceException.fail("暂不支持退单详情查询");
    }

}
