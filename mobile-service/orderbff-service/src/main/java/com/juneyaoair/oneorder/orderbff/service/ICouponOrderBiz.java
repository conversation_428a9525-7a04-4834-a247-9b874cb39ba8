package com.juneyaoair.oneorder.orderbff.service;

import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleReq;
import com.juneyaoair.oneorder.fare.dto.display.QueryScoreRuleRes;
import com.juneyaoair.oneorder.order.dto.couponorder.*;
import com.juneyaoair.oneorder.order.dto.score.ApplyScoreRespDto;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.CouponOrderPointPostParam;
import com.juneyaoair.oneorder.orderbff.dto.coupon.OrderPointPostResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;

/**
 * @<NAME_EMAIL>
 * @version v1.0
 * @date 2024/9/23 13:30
 * @description
 **/
public interface ICouponOrderBiz {

    /**
     * 查询权益订单详情
      * @param requestData
     * @return
     */
    ResponseData<CouponOrderDetailQueryResDTO> queryMyCouponOrderDetail(RequestData<CouponOrderDetailQueryReqDTO> requestData);

    /**
     * 查询权益订单详情
     * @param requestData
     * @param security
     * @return
     */
    CouponOrderDetailQueryResDTO queryCouponOrderBaseInfo(RequestData<CouponOrderDetailQueryReqDTO> requestData, boolean security);

    /**
     * 查询权益订单额外信息
     * @param requestData
     * @return
     */
    CouponOrderCommonExtInfoQueryResDTO queryCouponOrderExtInfo(RequestData<CouponOrderExtInfoQueryReqDTO> requestData);

    /**
     * 查询权益订单退单详情
     * @param requestData
     * @return
     */
    ResponseData<CouponOrderRefundDetailResDTO> queryCouponOrderRefundInfo(RequestData<CouponOrderRefundDetailReqDTO> requestData);

    /**
     * 查询权益订单退单详情基础信息
     * @param requestData
     * @return
     */
    CouponOrderRefundDetailResDTO queryCouponOrderRefundInfoBase(RequestData<CouponOrderRefundDetailReqDTO> requestData, boolean security);

    /**
     * 查询订单积分后置信息
     * @param requestData
     */
    OrderPointPostResult getOrderPointPost(RequestData<? extends CouponOrderParam> requestData);

    /**
     * 执行订单积分后置操作
     * @param requestData
     */
    ApplyScoreRespDto orderPointPost(RequestData<CouponOrderPointPostParam> requestData);

    /**
     * 汇率查询
     * @param queryScoreRuleReq
     * @return
     */
    QueryScoreRuleRes getSoreRateRule(QueryScoreRuleReq queryScoreRuleReq);

}
