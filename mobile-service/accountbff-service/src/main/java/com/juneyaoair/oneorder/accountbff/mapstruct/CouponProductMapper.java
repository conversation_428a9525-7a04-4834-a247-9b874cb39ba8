package com.juneyaoair.oneorder.accountbff.mapstruct;

import com.juneyaoair.oneorder.crm.dto.request.CouponProductCondition;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.order.dto.SingleBookCondition;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface CouponProductMapper {

    CouponProductMapper MAPPER = Mappers.getMapper(CouponProductMapper.class);

    /**
     * VoucherInfo to CouponProductResult
     * @param voucherInfo
     * @return
     */
    default CouponProductResult getCouponProductResult(VoucherInfo voucherInfo) {
        if (null == voucherInfo) {
            return null;
        }
        CouponProductResult couponProductResult = new CouponProductResult();
        ResourceInfo voucherDetail = voucherInfo.getVoucherDetail();
        couponProductResult.setProductName(null == voucherDetail ? null : voucherDetail.getProductName());
        couponProductResult.setVoucherNo(voucherInfo.getVoucherNo());
        couponProductResult.setVoucherState(voucherInfo.getVoucherState());
        couponProductResult.setVoucherType(voucherInfo.getVoucherType());
        couponProductResult.setActivateTime(voucherInfo.getActivateTime());
        couponProductResult.setExpireTime(voucherInfo.getExpireTime());
        couponProductResult.setSelfUse(voucherInfo.getSelfUse());
        couponProductResult.setUnAvailableMsg(voucherInfo.getUnAvailableMsg());
        couponProductResult.setAvailableStatus(voucherInfo.isAvailableStatus());
        CouponProductBookingLimit bookingLimit = CouponProductMapper.MAPPER.getCouponProductBookingLimit(voucherInfo.getBookingLimit());
        couponProductResult.setBookingLimit(bookingLimit);

        if (null != voucherInfo.getVoucherDetail()) {
            couponProductResult.setUseMode(null == voucherDetail ? null : voucherDetail.getUseMode());
            if (null != voucherInfo.getVoucherDetail().getBaggageExt()) {
                CouponProductVoucherDetailBaggageExt baggageExt = CouponProductMapper.MAPPER.getCouponProductVoucherDetailBaggageExt(voucherInfo.getVoucherDetail().getBaggageExt());
                couponProductResult.setBaggageExt(baggageExt);
            }
        }
        return couponProductResult;
    }

    default CouponProductVoucherDetailBaggageExt getCouponProductVoucherDetailBaggageExt(BaggageExtInfo baggageExt){
        if (null == baggageExt) {
            return null;
        }
        CouponProductVoucherDetailBaggageExt couponProductVoucherDetailBaggageExt = new CouponProductVoucherDetailBaggageExt();
        couponProductVoucherDetailBaggageExt.setBaggageUnit(baggageExt.getBaggageUnit());
        couponProductVoucherDetailBaggageExt.setBaggageValue(baggageExt.getBaggageValue());
        return couponProductVoucherDetailBaggageExt;
    }

    /**
     * BookingLimit to CouponProductBookingLimit
     * @param bookingLimit
     * @return
     */
    default CouponProductBookingLimit getCouponProductBookingLimit(BookingLimit bookingLimit) {
        if (null == bookingLimit) {
            return null;
        }
        CouponProductBookingLimit couponProductBookingLimit = new CouponProductBookingLimit();
        couponProductBookingLimit.setCabins(bookingLimit.getCabins());
        return couponProductBookingLimit;
    }

    /**
     * SingleBookCondition to SingleBookCondition
     * @param couponProductCondition
     * @return
     */
    default SingleBookCondition getSingleBookCondition(CouponProductCondition couponProductCondition) {
        if (null == couponProductCondition) {
            return null;
        }
        SingleBookCondition singleBookCondition = new SingleBookCondition();
        singleBookCondition.setFlightType(couponProductCondition.getFlightType());
        singleBookCondition.setFlightNo(couponProductCondition.getFlightNo());
        String flightDate = couponProductCondition.getFlightDate();
        // 日期格式为yyyy-MM-dd 补上时间00:00:00
        if (StringUtils.isNotBlank(flightDate) && flightDate.length() == 10) {
            singleBookCondition.setFlightDate(flightDate + " 00:00:00");
        } else {
            singleBookCondition.setFlightDate(flightDate);
        }
        singleBookCondition.setDepAirportCode(couponProductCondition.getDepAirportCode());
        singleBookCondition.setArrAirportCode(couponProductCondition.getArrAirportCode());
        return singleBookCondition;
    }
}
