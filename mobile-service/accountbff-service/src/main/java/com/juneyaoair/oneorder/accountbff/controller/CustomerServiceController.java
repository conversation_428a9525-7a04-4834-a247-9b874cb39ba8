package com.juneyaoair.oneorder.accountbff.controller;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.accountbff.config.AccountBffConfig;
import com.juneyaoair.oneorder.accountbff.dto.request.CustomerServiceParam;
import com.juneyaoair.oneorder.accountbff.dto.request.CustomerServiceProperties;
import com.juneyaoair.oneorder.accountbff.dto.response.CustomerServiceEntranceInfo;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.tools.utils.SM4Util;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;

/**
 * 智能客服
 * <AUTHOR>
 */
@Slf4j
@Api(value = "智能客服", tags = "智能客服")
@RestController
public class CustomerServiceController {

    @Autowired
    private AccountBffConfig accountBffConfig;

    @ApiOperation(value = "智能客服入口开关", notes = "智能客服入口开关")
    @PostMapping(value = "/customerService/artificialSwitch")
    public ResponseData<String> artificialSwitch(@RequestBody @Validated RequestDataDto<Object> requestData) {
        return ResponseData.suc(accountBffConfig.getCustomerServiceSwitch());
    }

    @ApiOperation(value = "智能客服入口信息", notes = "智能客服入口信息")
    @PostMapping(value = "/customerService/artificialCustomerService")
    public ResponseData<CustomerServiceEntranceInfo> artificialCustomerService(@RequestBody @Validated RequestDataDto<CustomerServiceParam> requestData) {
        return ResponseData.suc(getCustomerServiceEntranceInfo(requestData, requestData.getData()));
    }

    /**
     * 获取智能客服入口信息
     * @param requestData
     * @param customerServiceParam
     * @return
     */
    private CustomerServiceEntranceInfo getCustomerServiceEntranceInfo(RequestDataDto requestData, CustomerServiceParam customerServiceParam) {
        CustomerServiceProperties customerServiceProperties = accountBffConfig.getCustomerServiceChannelMap().get(requestData.getChannelNo());
        if (null == customerServiceProperties) {
            throw MultiLangServiceException.fail("当前渠道暂不支持在线客服");
        }
        if (StringUtils.isAllBlank(requestData.getFfpNo(), customerServiceParam.getOpenId())) {
            throw MultiLangServiceException.fail("会员卡号和OpenId不能同时为空");
        }
        CustomerServiceEntranceInfo customerServiceEntranceInfo = new CustomerServiceEntranceInfo();
        String customerServiceUrl = accountBffConfig.getCustomerServiceUrl();
        StringBuilder builder = new StringBuilder(customerServiceUrl);
        // cardId,channelCode,openid,timestamp 需加密
        builder.append("channelKey=").append(customerServiceProperties.getChannelKey());
        builder.append("&init=").append("1");
        builder.append("&channelCode=").append(getEncrypt(requestData.getChannelNo()));
        if (StringUtils.isNotBlank(requestData.getFfpNo())) {
            builder.append("&cardId=").append(getEncrypt(requestData.getFfpNo()));
        }
        if (StringUtils.isNotBlank(customerServiceParam.getOpenId())) {
            builder.append("&openid=").append(getEncrypt(customerServiceParam.getOpenId()));
        }
        builder.append("&timestamp=").append(getEncrypt(String.valueOf(System.currentTimeMillis())));
        customerServiceEntranceInfo.setUrl(builder.toString());
        return customerServiceEntranceInfo;
    }

    /**
     * 对数据进行加密并进行URLEncoder
     * @param data
     * @return
     */
    private String getEncrypt(String data) {
        try {
            if (StringUtils.isBlank(data)) {
                return null;
            }
            String encrypt = SM4Util.encryptCbc(data, accountBffConfig.getCustomerServiceSmKey(), accountBffConfig.getCustomerServiceSmIv());
            return URLEncoder.encode(encrypt, "UTF-8");
        } catch (Exception e) {
            log.error("生成智能客服加密参数");
            throw MultiLangServiceException.fail("参数加密出错");
        }
    }

}
