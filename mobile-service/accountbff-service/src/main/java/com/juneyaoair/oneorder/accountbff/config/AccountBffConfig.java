package com.juneyaoair.oneorder.accountbff.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.oneorder.accountbff.dto.common.UnlimitedCard2Config;
import com.juneyaoair.oneorder.accountbff.dto.request.CustomerServiceProperties;
import com.juneyaoair.oneorder.crm.dto.common.CertType;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/15 10:10
 */
@Data
@Configuration
public class AccountBffConfig {
    /**
     * 支付时限设置
     */
    @Value("${timeLimit:20}")
    private int timeLimit;

    /**
     * 会员星级查询是否开启:开启Y,关闭N 默认关闭
     */
    @Value("${memberStarQueryOpen:N}")
    private String memberStarQueryOpen;

    /**
     * 主题卡类型
     */
    @Value("${themeCouponList:[ThemeHotPotCoupon,ThemeOutHikeCoupon,ThemeVermicelliCoupon]}")
    private List<String> themeCouponList;

    /**
     * 升舱卡配置
     */
    @ApolloJsonValue("${upgradeCardV2Config:{\"cardSaleTimeBegin\":\"2020-12-24 10:00:00\",\"yearCardFlightTimeBegin\":\"2021-01-01 00:00:00\",\"yearCardFlightTimeEnd\":\"2021-12-31 23:59:59\",\"unusableTimeBegin\":\"2021-02-01 00:00:00\",\"unusableTimeEnd\":\"2021-02-01 00:00:00\"}}")
    private UpgradeCardV2Config upgradeCardV2Config;

    @Value("${url.coupon.api:}")
    private String urlCouponApi;

    /**
     * 积分免密支付额度 默认20
     */
    @Value("${scoreFreeLimit:20}")
    private int scoreFreeLimit;

    @Value("${HKMCTWRegions:[HKG, MFM, TPE, KHH]}")
    private List<String> HKMCTWRegions;

    /**
     * 可使用证件类型
     */
    @ApolloJsonValue("${useCertTypeMap:{\"CN\":[{\"CertDesc\":\"身份证\",\"CertType\":\"NI\"},{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳居民居住证\",\"CertType\":\"HMT\"},{\"CertDesc\":\"台胞证\",\"CertType\":\"MTP\"},{\"CertDesc\":\"回乡证\",\"CertType\":\"ORI\"},{\"CertDesc\":\"外国人永久居留身份证\",\"CertType\":\"NIPP\"},{\"CertDesc\":\"其它证件\",\"CertType\":\"CC\"}],\"DEFAULT\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"}],\"HK\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"回乡证\",\"CertType\":\"ORI\"}],\"HK_MO\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"回乡证\",\"CertType\":\"ORI\"}],\"MO\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"回乡证\",\"CertType\":\"ORI\"}],\"TW\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"台胞证\",\"CertType\":\"MTP\"},{\"CertDesc\":\"台湾通行证\",\"CertType\":\"TPP\"}]}}")
    private Map<String, List<CertType>> useCertTypeMap;

    /**
     * 是否开启受益人制度
     * Y/N
     */
    @Value("${limitScoreUseDate:2020-08-18}")
    private String limitScoreUseDate;

    /**
     * 获取指定的可使用证件类型
     *
     * @param segmentType
     * @return
     */
    public List<CertType> getUseCertTypeList(String segmentType) {
        if (StringUtils.isBlank(segmentType)) {
            segmentType = "CN";
        }
        List<CertType> certTypes = useCertTypeMap.get(segmentType);
        if (CollectionUtils.isNotEmpty(certTypes)) {
            return certTypes;
        }
        certTypes = useCertTypeMap.get("DEFAULT");
        return CollectionUtils.isEmpty(certTypes) ? new ArrayList<>() : certTypes;
    }


    /**
     * 常用乘机人 航线提醒消息
     */
    @ApolloJsonValue("${commonPersonWarmRemindMap:{\"I\":\"国际客票仅限实名注册会员本人携带最多2位儿童旅客预订\"}}")
    private Map<String, String> commonPersonWarmRemindMap;

    @Value("${applyRange:官网、APP等直营渠道}")
    private String usingChannels; //券使用规则中的使用渠道
    @Value("${applyRange:吉祥航空实际承运的国内航线使用}")
    private String applyRange; //券使用规则中的适用范围

    /**
     * 获取常用乘机人 航线提醒消息
     *
     * @param segmentType
     * @return
     */
    public String getCommonPersonWarmRemind(String segmentType) {
        if (null == commonPersonWarmRemindMap || commonPersonWarmRemindMap.isEmpty()) {
            return null;
        }
        return commonPersonWarmRemindMap.get(segmentType);
    }

    /**
     * 畅飞卡2.0限制
     */
    @ApolloJsonValue("${unlimitedCard2Config:{\"showCabinTimeBegin\":\"2020-12-07 00:00:00\",\"flightTimeBegin\":\"2021-01-21 00:00:00\",\"flightTimeEnd\":\"2021-06-30 23:59:59\",\"bCardUnusableFlightTimeBegin\":\"2021-02-01 00:00:00\",\"bCardUnusableFlightTimeEnd\":\"2021-02-28 23:59:59\",\"freeTicket3DayLimit\":3,\"payConfigUseCache\":true,\"availDateFly\":\"2021-01-21至2021-06-30?(不含2021-02-01至2021-02-28)\",\"availDateFlySF\":\"2021-01-21至2021-06-30\",\"sfTimeShowDoctorFreeTicket\":false,\"useCacheQueryProduct\":true,\"cardSaleTimeBegin\":\"2020-12-01 10:00:00\",\"noShowDay\":3}}")
    private UnlimitedCard2Config unlimitedCard2Config;

    /**
     * 会员零星  星级规则id
     */
    @Value("${memberStarRuleId:303}")
    private String memberStarRuleId;

    /**
     * 智能客服开关
     */
    @Value("${customerService.switch:Y}")
    private String customerServiceSwitch;

    /**
     * 智能客服请求地址
     */
    @Value("${customerService.url:https://ocs1.juneyaoair.com/visitor/?}")
    private String customerServiceUrl;
    /**
     * 智能客服加密key
     */
    @Value("${customerService.sm.Key:vvn1ovgE1rzc1OKm}")
    private String customerServiceSmKey;
    /**
     * 智能客服加密IV
     */
    @Value("${customerService.sm.iv:4hIhcawvPiWae1Ln}")
    private String customerServiceSmIv;
    /**
     * 智能客服渠道配置
     */
    @ApolloJsonValue("${customerService.channelMap:{}}")
    private Map<String, CustomerServiceProperties> customerServiceChannelMap;

    public static final String QUERY_THEME_COUPON = "/ThemeCoupon/queryEffectiveOfThemeCoupon";//查询产品信息

    public static final String PASSENGER_TYPE_CHD = "CHD";

    public static final String PASSENGER_TYPE_ADT = "ADT";

    public static final String  COUNT_UNPAY_ORDER= "/Order/CountUnpayOrder"; //统计未支付订单数

    /**
     * 会员中心会员信息缓存 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_INFO= "memberCenter:memberInfo:";

    public static final String COUPON_MY_PRODUCT_V2 = "/Order/GetMyCouponProduct";//我的权益券查看

    /**
     * 缓存实名信息
     */
    public static final String MEMBER_REAL = "memberReal:";

    /**
     * 会员中心权益券数量 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_RIGHTCOUPONTOTAL= "memberCenter:memberRightCouponTotal:";

    //接口访问token密钥
    public static final String ACCESSSECRET = "juneyaoair";

}
