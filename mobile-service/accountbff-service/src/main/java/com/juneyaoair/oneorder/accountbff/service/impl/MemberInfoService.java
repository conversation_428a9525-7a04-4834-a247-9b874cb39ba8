package com.juneyaoair.oneorder.accountbff.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.antifraud.FinalDecisionEnum;
import com.juneyaoair.flightbasic.antifraud.request.Antifraud;
import com.juneyaoair.flightbasic.antifraud.response.AntiResult;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.crm.ModifyCustomerInfoRequest;
import com.juneyaoair.flightbasic.request.crm.PtResetLoginPasswordRequest;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.AESTool;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.accountbff.config.AccountBffConfig;
import com.juneyaoair.oneorder.accountbff.constant.*;
import com.juneyaoair.oneorder.accountbff.dto.common.UnlimitedCard2Config;
import com.juneyaoair.oneorder.accountbff.dto.common.*;
import com.juneyaoair.oneorder.accountbff.dto.request.*;
import com.juneyaoair.oneorder.accountbff.dto.response.*;
import com.juneyaoair.oneorder.accountbff.mapstruct.CouponProductMapper;
import com.juneyaoair.oneorder.accountbff.pinyin.ChineseSpellUTils;
import com.juneyaoair.oneorder.accountbff.service.IMemberInfoService;
import com.juneyaoair.oneorder.accountbff.utils.CouponUtil;
import com.juneyaoair.oneorder.accountbff.utils.RightCouponConvert;
import com.juneyaoair.oneorder.api.basic.service.I18nDictService;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.VoucherTypesEnum;
import com.juneyaoair.oneorder.api.crm.constant.*;
import com.juneyaoair.oneorder.api.crm.dto.*;
import com.juneyaoair.oneorder.api.crm.service.IAccountService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.crm.utils.CertUtil;
import com.juneyaoair.oneorder.api.crm.utils.CrmUtil;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.api.order.service.IUnlimitedFlyService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.common.UnifiedOrderResultEnum;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.constant.TongDunEventType;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.*;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.service.SmsCodeSendService;
import com.juneyaoair.oneorder.common.util.TongDunUtil;
import com.juneyaoair.oneorder.config.TongDunConfig;
import com.juneyaoair.oneorder.constant.*;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.common.*;
import com.juneyaoair.oneorder.crm.dto.request.*;
import com.juneyaoair.oneorder.crm.dto.response.MemberRemainScoreResp;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.crm.enums.MileageAccountRequestItemsEnum;
import com.juneyaoair.oneorder.mobile.config.AppConfig;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.common.PtResponse;
import com.juneyaoair.oneorder.order.common.ScheduledDetails;
import com.juneyaoair.oneorder.order.constant.AirCompanyEnum;
import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import com.juneyaoair.oneorder.order.constant.CommonPersonMessageType;
import com.juneyaoair.oneorder.order.dto.*;
import com.juneyaoair.oneorder.order.util.AirStringUtil;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.*;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.juneyaoair.oneorder.api.crm.utils.CertNoUtil.toPassType;

/**
 * @ClassName MemberInfoService
 * @Description
 * <AUTHOR>
 * @Date 2023/6/19 16:28
 * @Version 1.0
 */
@Service
@Slf4j
public class MemberInfoService implements IMemberInfoService {

    @Autowired
    private AccountBffConfig accountBffConfig;

    @Autowired
    private RedisConstantConfig redisConstantConfig;

    @Resource
    TongDunConfig tongDunConfig;


    @Autowired
    private CrmConfig crmConfig;
    @Autowired
    private AppConfig appConfig;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IOrderService orderService;


    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    private IBasicService basicService;

    @Autowired
    private IUnlimitedFlyService unlimitedFlyService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private CommonService commonService;

    @Resource
    private SmsCodeSendService smsCodeSendService;

    @Resource
    private I18nDictService i18nDictService;

    @Resource
    private IAccountService iAccountService;

    private static final String PLATINUMCARD = "6,7,8,9";

    private static final String LEVEL = "Level";

    private static final String STAR = "Star";

    private static final String OTHER = "Other";

    private static final String COMPANY = "Company";

    private static final String ACTIVE = "A";

    private static final String MILEAGE_ADD_CALCULATOR_CLASS_LIST = "MileageAddCalculator_classList";

    private static final String MILEAGE_ADD_CALCULATOR_MILEAGE_LIST = "MileageAddCalculator_mileageList";

    private static final String MILEAGE_ADD_CALCULATOR_RATIO_LIST = "MileageAddCalculator_patioList";

    private static final String MILEAGE_ADD_CALCULATOR_MIN_MILEAGE_LIST = "MileageAddCalculator_minMileageList";

    private static final String MILEAGE_EXCHANGE_CALCULATE_RULES_HASH = "MemberMileage_MileageExchangeCalculateRules_hash";

    private static final String MILEAGE_AIRPORT_SELECTIONS = "MemberMileage_MileageAirPortSelections";

    private final String B2C_CHANNEL = "B2CWEB";
    private final String B2C_CHANNEL_PWD = "B2CCRM2013";

    @Resource
    FlightBasicConsumerClient flightBasicConsumerClient;

    @Override
    public MenberBasicInfo toCatchBasicInfo(String ffpId, String ffpCardNo, BizDto bizDto) {
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(bizDto.getIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName,
                MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest, false);
        MenberBasicInfo menberBasicInfo = new MenberBasicInfo();
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            throw ServiceException.fail("会员基本信息查询出错");
        }
        MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
        List<MemberRealNameSummarySoaModel> realVerifyInfos = ptCRMResponse.getData().getRealVerifyInfos();
        List<MemberContactSoaModel> contactInfo = ptCRMResponse.getData().getContactInfo();
        MemberStateInfoSoaModel stateInfo = ptCRMResponse.getData().getStateInfo();
        if (null == basicInfo || null == stateInfo) {
            throw ServiceException.fail("会员基本信息查询出错");
        }
        menberBasicInfo.setCFirstName(basicInfo.getCFirstName());
        menberBasicInfo.setCLastName(basicInfo.getCLastName());
        MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest();
        memberStarQueryRequest.setId(Integer.valueOf(ffpId));
        ptApiRequest.setData(memberStarQueryRequest);
        PtCRMResponse<MemberStarQueryResp> ptCRMResponseMember = memberService.queryMemberStar(ptApiRequest, false);
        MemberStarQueryResp memberStarQueryResp = ptCRMResponseMember.getData();
        menberBasicInfo.setCFirstName(basicInfo.getCFirstName());
        menberBasicInfo.setCLastName(basicInfo.getCLastName());
        menberBasicInfo.setEFirstName(basicInfo.getEFirstName());
        menberBasicInfo.setELastName(basicInfo.getELastName());
        menberBasicInfo.setRealName(CrmUtil.toCheckRealNameStatus(realVerifyInfos));
        menberBasicInfo.setFfpId(ffpId);
        menberBasicInfo.setFfpCardNo(ffpCardNo);
        //获取手机号及区号
        CommonPersonInfo commonPersonInfo = toCatchMobilePhone(contactInfo);
        menberBasicInfo.setPhoneNo(commonPersonInfo.getHandPhone());
        menberBasicInfo.setCountryTelCode(commonPersonInfo.getCountryTelCode());
        MemberContactSoaModel emailContact = CRMReqUtil.toMemberContactSoaModel(contactInfo, ContactTypeEnum.EMAIL.getCode());
        if (null != emailContact) {
            menberBasicInfo.setEmail(encrypt(emailContact.getContactNumber()));
        }
        menberBasicInfo.setBirthDay(DateUtil.timeStampToDateStr(basicInfo.getBirthDay(), DateUtil.YYYY_MM_DD_PATTERN));
        menberBasicInfo.setSex(basicInfo.getSex());
        menberBasicInfo.setHeadImage(basicInfo.getHeadImageUrl());
        menberBasicInfo.setFfpLevel(stateInfo.getMemberLevelCode());
        menberBasicInfo.setFfpLevelDesc(stateInfo.getMemberLevel());
        /*menberBasicInfo.setFfpStarLevel(stateInfo.getMemberStar());*/
        /*desensitizationContactList(ptCRMResponse);*/
        menberBasicInfo.setFfpStarLevel(memberStarQueryResp.getMemberStarCode());

        CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertRequest = CRMReqUtil.buildCommCrmMemberReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        QueryMemberCertificateReqDto queryCertDto = new QueryMemberCertificateReqDto();
        queryCertDto.setMemberId(Integer.parseInt(ffpId));
        queryCertRequest.setData(queryCertDto);
        CrmMemberBaseApiResponse<QueryMemberCertificateResDto> queryCertResponse = memberService.toCatchMemberCertList(queryCertRequest, false);

        menberBasicInfo.setCertificateInfo(toEncryptRecordId(ptCRMResponse.getData().getCertificateInfo(), queryCertResponse));
        menberBasicInfo.setAddressInfos(ptCRMResponse.getData().getAddressInfos());
        ChannelDTO catchRealNameChannelName = toCatchRealNameChannelName(ptCRMResponse);
        if (null != catchRealNameChannelName) {
            menberBasicInfo.setCredentialChannel(catchRealNameChannelName.isCredentialChannel());
            menberBasicInfo.setVerifyChannel(catchRealNameChannelName.getChannelCode());
            menberBasicInfo.setVerifyChannelName(catchRealNameChannelName.getChannelName());
        }
        if (CollectionUtils.isNotEmpty(contactInfo)) {
            List<RecordInfo> recordInfos = new ArrayList<>();
            contactInfo.forEach(
                    contactIn -> {
                        RecordInfo recordInfo = new RecordInfo();
                        BeanUtils.copyProperties(contactIn, recordInfo);
                        try {
                            recordInfo.setRecord(encrypt(String.valueOf(contactIn.getRecordId())));
                        } catch (Exception e) {
                            throw ServiceException.fail("record加密出错");
                        }
                        recordInfos.add(recordInfo);
                    }
            );
            menberBasicInfo.setRecordInfoList(recordInfos);
        }
        return menberBasicInfo;
    }

    /**
     * @param contactInfo
     * @return com.juneyaoair.oneorder.accountbff.dto.common.CommonPersonInfo
     * <AUTHOR>
     * @Description 获取手机号和区号
     * @Date 9:58 2024/12/5
     **/
    private CommonPersonInfo toCatchMobilePhone(List<MemberContactSoaModel> contactInfo) {
        CommonPersonInfo response = new CommonPersonInfo();
        MemberContactSoaModel mobileContact = CRMReqUtil.toMemberContactSoaModel(contactInfo, ContactTypeEnum.MOBILE.getCode());
        if (null == mobileContact || StringUtils.isEmpty(mobileContact.getContactNumber())) {
            return response;
        }
        if (!mobileContact.getContactNumber().contains("-")) {
            response.setHandPhone(encrypt(mobileContact.getContactNumber()));
            response.setCountryTelCode("86");
            return response;
        } else {
            String[] split = mobileContact.getContactNumber().split("-");
            response.setCountryTelCode(split[0]);
            response.setHandPhone(encrypt(split[1]));
            return response;
        }
    }

    /**
     * @param originStr
     * @return java.lang.String
     * <AUTHOR>
     * @Description 加密某数据
     * @Date 20:45 2023/9/19
     **/
    private String encrypt(String originStr) {
        if (StringUtils.isEmpty(originStr)) {
            return originStr;
        }
        try {
            return AESTool.encrypt(originStr, CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16));
        } catch (Exception e) {
            throw ServiceException.fail("加密出错");
        }
    }

    /**
     * @param encryptedStr
     * @return java.lang.String
     * <AUTHOR>
     * @Description 解密某数据
     * @Date 21:23 2023/9/19
     **/

    private String decrypt(String encryptedStr) {
        if (StringUtils.isEmpty(encryptedStr)) {
            return encryptedStr;
        }
        try {
            return AESTool.decrypt(encryptedStr, CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16));
        } catch (Exception e) {
            log.error("解密出错，", e);
            throw ServiceException.fail("解密出错");
        }
    }

    /**
     * @param certificateInfo
     * @return java.util.List<com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2>
     * <AUTHOR>
     * @Description 加密证件ID
     * @Date 13:48 2023/9/11
     **/
    private List<MemberCertificateSoaModelV2> toEncryptRecordId(List<MemberCertificateSoaModelV2> certificateInfo, CrmMemberBaseApiResponse<QueryMemberCertificateResDto> memberCertificateResDto) {
        if (CollectionUtils.isEmpty(certificateInfo)) {
            return null;
        }
        for (MemberCertificateSoaModelV2 memberCertificateSoaModelV2 : certificateInfo
        ) {
            if (0 != memberCertificateSoaModelV2.getRecordId()) {
                try {
                    if (null != memberCertificateResDto && null != memberCertificateResDto.getData() && CollectionUtils.isNotEmpty(memberCertificateResDto.getData().getCertificateList())) {
                        Optional<MemberCertificateResModel> memberCertificateResModel = memberCertificateResDto.getData().getCertificateList().stream().filter(e -> !StringUtils.isAnyEmpty(e.getCertificateType(), e.getCertificateNumber()) && e.getCertificateNumber().equals(memberCertificateSoaModelV2.getCertificateNumber())).findFirst();
                        if (memberCertificateResModel.isPresent() && StringUtils.isNotEmpty(memberCertificateResModel.get().getCertificateType())) {
                            memberCertificateSoaModelV2.setCertificateType(CertificateTypeEnum.getCodeByENAME(memberCertificateResModel.get().getCertificateType()));
                        }
                    }
                    memberCertificateSoaModelV2.setCertificateNumber(encrypt(memberCertificateSoaModelV2.getCertificateNumber()));
                    memberCertificateSoaModelV2.setRecord(encrypt(String.valueOf(memberCertificateSoaModelV2.getRecordId())));
                    memberCertificateSoaModelV2.setRecordId(0);
                } catch (Exception e) {
                    throw ServiceException.fail("加密会员证件信息出错");
                }
            }
        }
        return certificateInfo;
    }

    /**
     * @param ptCRMResponse
     * @return void
     * <AUTHOR>
     * @Description 对联系方式及地址信息进行脱敏
     * @Date 15:50 2023/8/25
     **/
    private void desensitizationContactList(PtCRMResponse<PtMemberDetail> ptCRMResponse) {
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            return;
        }
        PtMemberDetail ptCRMResponseData = ptCRMResponse.getData();
        List<MemberCertificateSoaModelV2> certificateInfoList = ptCRMResponseData.getCertificateInfo();
        List<MemberAddressSoaModel> addressInfoList = ptCRMResponseData.getAddressInfos();
        if (CollectionUtils.isNotEmpty(certificateInfoList)) {
            certificateInfoList.forEach(
                    certificateInfo -> {
                        if (1 == certificateInfo.getCertificateType() && StringUtils.isNotEmpty(certificateInfo.getCertificateNumber())) {
                            certificateInfo.setCertificateNumber(desensitizationIdCard(certificateInfo.getCertificateNumber()));
                        } else if (StringUtils.isNotEmpty(certificateInfo.getCertificateNumber())) {
                            certificateInfo.setCertificateNumber(desensitizationOtherCard(certificateInfo.getCertificateNumber()));
                        }
                    }
            );
        }
        if (CollectionUtils.isNotEmpty(addressInfoList)) {
            addressInfoList.forEach(
                    addressInfo -> {
                        addressInfo.setAddress(desensitizationAddress(addressInfo.getAddress()));
                        addressInfo.setReceiver(desensitizationName(addressInfo.getReceiver()));
                        addressInfo.setReceiverMobile(desensitizationPhone(addressInfo.getReceiverMobile()));
                    }
            );
        }
    }

    /**
     * @param address
     * @return java.lang.String
     * <AUTHOR>
     * @Description 脱敏地址信息
     * @Date 16:02 2023/8/25
     **/
    private String desensitizationAddress(String address) {
        if (StringUtils.isBlank(address) || address.length() <= 6) {
            return address;
        }
        String addressResult = "";
        for (int i = 0; i < address.length() - 6; i++) {
            addressResult = addressResult.concat("*");
        }
        return address.substring(0, 6) + addressResult;
    }

    /**
     * 对手机号脱敏
     *
     * @param mobile
     * @return
     */
    private String desensitizationPhone(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        int length = mobile.length();
        if (length <= 5) {
            return mobile;
        }
        return mobile.substring(0, 3) + "******" + mobile.substring(length - 2);
    }

    /**
     * @param email
     * @return java.lang.String
     * <AUTHOR>
     * @Description 邮箱加密
     * @Date 13:08 2023/8/25
     **/
    private String desensitizationEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }
        String[] splitEmail = email.split("@");
        int preLength = splitEmail[0].length();
        if (preLength <= 3) {
            return email;
        } else {
            String preEmail = "";
            for (int i = 0; i < preLength - 3; i++) {
                preEmail = preEmail.concat("*");
            }
            return splitEmail[0].substring(0, 3).concat(preEmail).concat("@").concat(splitEmail[1]);
        }
    }

    @Override
    public AssetResponse toCatchAssetInfo(RequestData requestData) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        //获取总积分数
        Callable<MemberRemainScoreResp> totalScoreCall = () -> {
            try {
                MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
                String[] items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
                mileageAccountQueryRequest.setMemberCardNo(requestData.getFfpNo());
                mileageAccountQueryRequest.setRequestItems(items);
                ptApiRequest.setData(mileageAccountQueryRequest);
                return memberService.queryMemberRemainScore(ptApiRequest, false);
            } catch (Exception e) {
                log.error("获取会员积分异常:", e);
                Thread.currentThread().interrupt();
                throw MultiLangServiceException.fail("获取会员积分异常");
            }
        };
        //查询会员航段
        Callable<PtCrmMileageResponse<MemberSegmentResponse>> currentSegmentCall = () -> {
            try {
                PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MemberSegmentRequest memberSegmentRequest = new MemberSegmentRequest();
                memberSegmentRequest.setMemberId(Integer.parseInt(requestData.getFfpId()));
                ptCrmMileageRequest.setData(memberSegmentRequest);
                return memberService.queryMemberSegment(requestData.getFfpId(), requestData.getOriginIp(), requestData.getChannelNo(), false);
            } catch (Exception e) {
                log.error("获取会员航段异常:", e);
                Thread.currentThread().interrupt();
                throw MultiLangServiceException.fail("获取会员航段异常");
            }
        };
        //优惠券数量
        Callable<AvailCouponsResponse> couponCall = () -> {
            try {
                RequestData<QueryCouponReq> queryCouponReqRequestData = new RequestData<>();
                BeanUtils.copyProperties(requestData, queryCouponReqRequestData);
                QueryCouponReq queryCouponReq = new QueryCouponReq();
                queryCouponReq.setCouponState(CouponStateEnum.R.getState());
                queryCouponReqRequestData.setData(queryCouponReq);
                CouponQueryRequest couponQueryRequest = CreateQueryCouponRequest(queryCouponReqRequestData, channelInfo);
                couponQueryRequest.setSale(1);  //非可售
                return memberService.toCatchCoupons(couponQueryRequest);
            } catch (Exception e) {
                log.error("获取优惠券资产异常:", e);
                Thread.currentThread().interrupt();
                throw MultiLangServiceException.fail("获取优惠券资产异常");
            }
        };
        //权益券数量
        Callable<PtCouponProductGetResponseDto> rightCouponCall = () -> {
            try {
                RequestData<MyCoupon> myCouponRequestData = new RequestData<>();
                BeanUtils.copyProperties(requestData, myCouponRequestData);
                MyCoupon myCoupon = new MyCoupon();
                myCoupon.setCouponState(CouponStateEnum.R.getState());
                myCouponRequestData.setData(myCoupon);
                PtCouponProductGetRequestDto ptCouponProductGetRequestDto = createCouponProductGetRequestDto(myCouponRequestData, channelInfo, false);
                return memberService.toCatchRightCoupons(ptCouponProductGetRequestDto);
            } catch (Exception e) {
                log.error("获取权益资产异常:", e);
                Thread.currentThread().interrupt();
                throw MultiLangServiceException.fail("获取权益资产异常");
            }
        };

        Future<MemberRemainScoreResp> remainScoreFuture = taskExecutor.submit(totalScoreCall);
        Future<PtCrmMileageResponse<MemberSegmentResponse>> segmentFuture = taskExecutor.submit(currentSegmentCall);
        Future<AvailCouponsResponse> couponFuture = taskExecutor.submit(couponCall);
        Future<PtCouponProductGetResponseDto> rightCouponFuture = taskExecutor.submit(rightCouponCall);
        AssetResponse assetResponse = new AssetResponse();
        try {
            MemberRemainScoreResp memberRemainScoreResp = remainScoreFuture.get();
            assetResponse.setScore(Integer.parseInt(memberRemainScoreResp.getPoint()));
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取总积分数出错,异常原因：", e);
            assetResponse.setScore(0);
        }
        try {
            PtCrmMileageResponse<MemberSegmentResponse> segmentResponse = segmentFuture.get();
            int totalNumbers = 0;
            if (null != segmentResponse.getData() && (CollectionUtils.isNotEmpty(segmentResponse.getData().getSegments()))) {
                for (MemberSegmentResponse.Segments segments : segmentResponse.getData().getSegments()) {
                    totalNumbers += segments.getNumber();
                }
            }
            assetResponse.setSegment(totalNumbers);
        } catch (InterruptedException | ExecutionException e) {
            log.error("查询会员航段出错,异常原因：", e);
            assetResponse.setSegment(0);
        }
        try {
            AvailCouponsResponse availCouponsResponse = couponFuture.get();
            if (null != availCouponsResponse && null != availCouponsResponse.getAvailCouponList()) {
                assetResponse.setCoupon(availCouponsResponse.getAvailCouponList().size());
            } else {
                assetResponse.setCoupon(0);
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取优惠券数量出错,异常原因：", e);
            assetResponse.setCoupon(0);
        }
        try {
            PtCouponProductGetResponseDto avaiRightCouponResponse = rightCouponFuture.get();
            if (null != avaiRightCouponResponse && null != avaiRightCouponResponse.getVouchers()) {
                assetResponse.setRightCoupon(avaiRightCouponResponse.getVouchers().size());
            } else {
                assetResponse.setRightCoupon(0);
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取权益券数量出错,异常原因：", e);
            assetResponse.setRightCoupon(0);
        }
        return assetResponse;
    }

    @Override
    public AccountLevelDetailResp toCatchLevelDetail(RequestData requestData) {
        AccountLevelDetailResp accountLevelDetailResp = new AccountLevelDetailResp();
        Future<MemberStarInfo> memberStarInfoFuture = null;
        Future<LevelTransferDTO> memberLevelInfoFuture = null;
        if (!StringUtils.isAnyEmpty(requestData.getFfpId(), requestData.getFfpNo())) {
            //1. 会员星级信息
            Callable<MemberStarInfo> memberStarCall = () -> this.queryMemberStar(requestData).getObjData();

            //2. 获取升级保级信息
            Callable<LevelTransferDTO> memberLevelCall = () -> this.toCatchUpAndProInfo(requestData);

            memberStarInfoFuture = taskExecutor.submit(memberStarCall);
            memberLevelInfoFuture = taskExecutor.submit(memberLevelCall);
        }

        MemberStarInfo memberStarInfo = null;
        LevelTransferDTO levelTransferDTO = null;
        try {
            if (null != memberStarInfoFuture) {
                memberStarInfo = memberStarInfoFuture.get();
            }
            if (null != memberLevelInfoFuture) {
                levelTransferDTO = memberLevelInfoFuture.get();
            }

        } catch (InterruptedException | ExecutionException e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw ServiceException.fail("查询会员等级详情出错");
        }
        accountLevelDetailResp.setMemberStarInfo(memberStarInfo);
        if (null != levelTransferDTO) {
            accountLevelDetailResp.setUpGradeInfo(levelTransferDTO.getMemberUpGradeResp());
            accountLevelDetailResp.setProGradeInfo(levelTransferDTO.getMemberProGradeResp());
        }
        return accountLevelDetailResp;
    }

    @Override
    public SeachMemberRightsResponse toCatchCommonRights(RequestDataDto requestData, BizDto bizDto) {
        return this.toCatchMemberRights(requestData, bizDto);
    }

    @Override
    public MemberRightsResponse toCatchCurrentRights(RequestDataDto requestData, BizDto bizDto) {
        try {
            // key 为会员权益规则ID value 为权益等级
            Map<String, MemberLevelDTOView> memberRightMap = new HashMap<>();
            // 全部权益列表,按照权益类型分类（等级权益、星级权益、敬医权益、企业专属权益） key是类型，value是权益等级集合
            Map<String, List<MemberLevelDTOView>> memberRightList = new HashMap<>();
            // 获取基础服务会员配置信息
            getMemberRights(memberRightList, memberRightMap, bizDto);
            //否则只返回会员当前等级的权益
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            PtCRMResponse<MemberRightsQueryResponse> ptCRMResponse = this.memberService.queryMemberRights(ptApiCRMRequest);
            List<MemberRightsRuleSoaModel> rightsInfos = Lists.newArrayList();
            if (ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 0 && null != ptCRMResponse.getData()) {
                rightsInfos = ptCRMResponse.getData().getRightsInfos();
            }
            return toCatchCurrentRight(memberRightList.get(LEVEL), rightsInfos, memberRightMap, requestData.getLanguage());
        } catch (Exception e) {
            throw ServiceException.fail("查询用户等级权益出错");
        }
    }

    /**
     * @param memberLevelDTOS
     * @param rightsInfos     会员返回的
     * @param memberRightMap
     * @return com.juneyaoair.oneorder.crm.dto.common.MemberRightsResponse
     * <AUTHOR>
     * @Description 获取用户当前等级的会员权益
     * @Date 14:37 2023/8/10
     **/
    private MemberRightsResponse toCatchCurrentRight(List<MemberLevelDTOView> memberLevelDTOS
            , List<MemberRightsRuleSoaModel> rightsInfos
            , Map<String, MemberLevelDTOView> memberRightMap
            , LanguageEnum language) {
        if (CollectionUtils.isEmpty(memberLevelDTOS) || CollectionUtils.isEmpty(rightsInfos) || memberRightMap.isEmpty()) {
            return null;
        }
        //获取用户当前会员等级
        int maxSerialNumber = rightsInfos.stream()
                .filter(rightsInfo -> rightsInfo.getRuleType().equals(MemberRightsTypeEnum.LEVEL.getRuleType()))
                .map(rightsInfo -> memberRightMap.get(rightsInfo.getRuleId()))
                .filter(Objects::nonNull)
                .map(MemberLevelDTOView::getSerialNumber)
                .mapToInt(memberLevelDTO -> memberLevelDTO)
                .filter(memberLevelDTO -> memberLevelDTO >= 0)
                .max()
                .orElse(0);
        MemberLevelDTOView memberLevelDTO = memberLevelDTOS.stream().filter(el -> el.getSerialNumber() == maxSerialNumber).findFirst().orElse(null);
        //只展示当前等级会员权益
        if (null == memberLevelDTO) {
            return null;
        }
        MemberRightsResponse memberRightsResponse = new MemberRightsResponse();
        if (CollectionUtils.isNotEmpty(memberLevelDTO.getMemberRights())) {
            memberLevelDTO.getMemberRights().sort((e1, e2) -> {
                if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                    return e1.getOrderNo() - e2.getOrderNo();
                } else {
                    return 0;
                }
            });
            memberLevelDTO.getMemberRights().forEach(i -> i.setAvailable(true));
            Map<String, Map<String, String>> rightNameMap = i18nDictService.fetchDictData(I18nDictionaryTypeEnum.MEMBER_RIGHT);
            if (rightNameMap != null) {
                for (MemberRightsDTOView memberRightsDTOView : memberLevelDTO.getMemberRights()) {
                    String key = memberRightsDTOView.getRightsRecordId() + ".NAME";
                    String translation = BffI18nUtils.getTranslation(rightNameMap, key, language);
                    if (StringUtils.isNotBlank(translation)) {
                        memberRightsDTOView.setName(translation);
                    }
                    String rightDescKey = memberRightsDTOView.getRightsRecordId() + ".DESCRIBE";
                    String rightDescTranslation = BffI18nUtils.getTranslation(rightNameMap, rightDescKey, language);
                    if (StringUtils.isNotBlank(rightDescTranslation)) {
                        memberRightsDTOView.setDescribe(rightDescTranslation);
                    }
                }
            }
        }
        memberRightsResponse.setMemberLevelName(memberLevelDTO.getRuleName());
        memberRightsResponse.setMemberLevelCode(memberLevelDTO.getRuleId());
        memberRightsResponse.setWatermark(memberLevelDTO.getWaterMark());
        memberRightsResponse.setSerialNumber(memberLevelDTO.getSerialNumber());
        memberRightsResponse.setMemberRights(memberLevelDTO.getMemberRights());
        return memberRightsResponse;
    }

    private SeachMemberRightsResponse toCatchMemberRights(RequestDataDto requestData, BizDto bizDto) {
        boolean available = false;
        SeachMemberRightsResponse seachMemberRightsResponse = new SeachMemberRightsResponse();
        try {
            // key 为会员权益规则ID value 为权益等级
            Map<String, MemberLevelDTOView> memberRightMap = new HashMap<>();
            // 全部权益列表,按照权益类型分类（等级权益、星级权益、敬医权益、企业专属权益） key是类型，value是权益等级集合
            Map<String, List<MemberLevelDTOView>> memberRightList = new HashMap<>();
            // 获取权益信息
            getMemberRights(memberRightList, memberRightMap, bizDto);
            //用户ID为空，没有登录获取所有权益
            if (StringUtils.isEmpty(requestData.getFfpId())) {
                if (memberRightList.get(LEVEL) == null) {
                    throw ServiceException.fail("查询等级权益出错");
                }
                //前端响应 会员等级权益集合对象
                List<MemberRightsResponse> responseList = Lists.newArrayList();
                MemberLevelDTOView memberLevelDTO = new MemberLevelDTOView();
                for (MemberLevelDTOView level : memberRightList.get(LEVEL)) {
                    BeanUtils.copyProperties(level, memberLevelDTO);
                    List<MemberRightsDTOView> value = memberLevelDTO.getMemberRights();
                    for (MemberRightsDTOView memberRightsDTO : value) {
                        memberRightsDTO.setAvailable(available);
                    }
                    MemberRightsResponse memberRightsResponse = new MemberRightsResponse();
                    memberRightsResponse.setMemberLevelName(memberLevelDTO.getRuleName());
                    memberRightsResponse.setMemberLevelCode(memberLevelDTO.getRuleId());
                    memberRightsResponse.setWatermark(memberLevelDTO.getWaterMark());
                    memberRightsResponse.setSerialNumber(memberLevelDTO.getSerialNumber());
                    memberRightsResponse.setMemberRights(value);
                    responseList.add(memberRightsResponse);
                }
                responseList.sort(Comparator.comparing(MemberRightsResponse::getSerialNumber));
                seachMemberRightsResponse.setMemberRightsResponse(responseList);
                return seachMemberRightsResponse;
            }
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            PtCRMResponse<MemberRightsQueryResponse> ptCRMResponse = this.memberService.queryMemberRights(ptApiCRMRequest);
            List<MemberRightsRuleSoaModel> rightsInfos = Lists.newArrayList();
            if (ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 0 && null != ptCRMResponse.getData()) {
                rightsInfos = ptCRMResponse.getData().getRightsInfos();
            }
            //会员当前普通权益
            MemberLevelDTOView memberLevel = new MemberLevelDTOView();
            for (Map.Entry<String, List<MemberLevelDTOView>> entry : memberRightList.entrySet()) {
                switch (entry.getKey()) {
                    case (LEVEL):
                        processCommonMemberRight(entry.getValue(), memberLevel, rightsInfos, memberRightMap, seachMemberRightsResponse); //处理等级权益
                        break;
                    case (STAR):
                        processStarMemberRight(entry.getValue(), rightsInfos, requestData, memberRightMap, seachMemberRightsResponse); //处理星级权益
                        break;
                    case (OTHER):
                        processDoctorMemberRight(rightsInfos, memberRightMap, seachMemberRightsResponse); //处理敬医权益
                        break;
                    case (COMPANY):
                        processCompanyMemberRight(rightsInfos, memberRightMap, requestData, seachMemberRightsResponse); //处理企业专属权益
                        break;

                }
            }
            //权益数据译文处理
            if (CollectionUtils.isNotEmpty(seachMemberRightsResponse.getMemberRightsResponse())) {
                Map<String, Map<String, String>> rightNameMap = i18nDictService.fetchDictData(I18nDictionaryTypeEnum.MEMBER_RIGHT);
                for (MemberRightsResponse memberRightsResponse : seachMemberRightsResponse.getMemberRightsResponse()) {
                    if (CollectionUtils.isNotEmpty(memberRightsResponse.getMemberRights())) {
                        if (rightNameMap != null) {
                            for (MemberRightsDTOView memberRightsDTOView : memberRightsResponse.getMemberRights()) {
                                String nameKey = memberRightsDTOView.getRightsRecordId() + ".NAME";
                                String nameTranslation = BffI18nUtils.getTranslation(rightNameMap, nameKey, requestData.getLanguage());
                                if (StringUtils.isNotBlank(nameTranslation)) {
                                    memberRightsDTOView.setName(nameTranslation);
                                }
                                String rightDescKey = memberRightsDTOView.getRightsRecordId() + ".DESCRIBE";
                                String rightDescTranslation = BffI18nUtils.getTranslation(rightNameMap, rightDescKey, requestData.getLanguage());
                                if (StringUtils.isNotBlank(rightDescTranslation)) {
                                    memberRightsDTOView.setDescribe(rightDescTranslation);
                                }
                            }
                        }
                    }
                }
            }
            if (StringUtils.isBlank(seachMemberRightsResponse.getMemberStarRightsResponse().getMemberLevelDesc()) && StringUtils.isNotEmpty(memberLevel.getRuleName())) {
                seachMemberRightsResponse.getMemberStarRightsResponse().setMemberLevelDesc(memberLevel.getRuleName().replace("权益", ""));
            }
            seachMemberRightsResponse.getMemberStarRightsResponse().setImgUrl(memberLevel.getImgUrl());
            if (seachMemberRightsResponse.getMemberCompanyRightsResponse() != null &&
                    CollectionUtils.isNotEmpty(seachMemberRightsResponse.getMemberCompanyRightsResponse().getMemberRights())) {
                seachMemberRightsResponse.getMemberStarRightsResponse().setHeadDesc("我的专享权益");
            }
        } catch (Exception e) {
            log.error("获取用户权益信息出错:{}", e.getMessage());
        }
        return seachMemberRightsResponse;
    }

    /**
     * 处理会员企业专属权益
     *
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param req
     * @param seachMemberRightsResponse 前端响应
     */
    private void processCompanyMemberRight(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTOView> memberRightMap,
                                           RequestDataDto req, SeachMemberRightsResponse seachMemberRightsResponse) {

        try {
            //创建调用 企业会员信息查询 请求对象
            ChannelInfo channelInfo = commonService.findChannelInfo(req.getChannelNo());
            PtCrmMileageRequest<CompanyMemberQueryInfoReqDto> ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(req.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            CompanyMemberQueryInfoReqDto companyMemberQueryInfoReqDto = new CompanyMemberQueryInfoReqDto();
            companyMemberQueryInfoReqDto.setMemberId(Integer.parseInt(req.getFfpId()));
            ptCrmMileageRequest.setData(companyMemberQueryInfoReqDto);
            PtCrmMileageResponse<CompanyMemberQueryInfoResDto> catchCompanyInfo = memberService.toCatchCompanyInfo(ptCrmMileageRequest);

            CompanyMemberInfoResponse companyMemberInfoResponse = new CompanyMemberInfoResponse();

            // 根据状态显示信息，如果是已绑定，传给前端企业信息
            CompanyMemberQueryInfoResDto companyMemberQueryInfoResDto = catchCompanyInfo.getData();
            if (companyMemberQueryInfoResDto != null && companyMemberQueryInfoResDto.getStatus() == CompanyVerifyStatusEnum.BIND.code) {
                if (companyMemberQueryInfoResDto.getCompanyInfo() != null) {
                    BeanUtils.copyProperties(companyMemberQueryInfoResDto.getCompanyInfo(), companyMemberInfoResponse);
                }
                //将敬医金权益，放到前端响应对象中
                MemberCompanyRightsResponse memberCompanyRightsResponse = new MemberCompanyRightsResponse();
                //判断失效日期是否已到期
                int dateDiff = DateUtil.dateDiff(DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN), companyMemberInfoResponse.getExpireDate(), DateUtil.YYYY_MM_DD_PATTERN);
                if (dateDiff <= 0) {
                    log.info("渠道:{},卡号:{},企业会员状态:{}", req.getChannelNo(), req.getFfpId(), "企业会员已到期，不支持查看企业会员专属权益");
                } else {
                    List<MemberRightsDTOView> lists = processUsingChannelsApplyRange(rightsInfos, memberRightMap, MemberRightsTypeEnum.COMPANY.getRuleType());

                    if (CollectionUtils.isNotEmpty(lists)) {
                        lists.forEach(i -> {
                            i.setAvailable(true);

                        });
                    }
                    memberCompanyRightsResponse.setMemberRights(lists);
                }
                memberCompanyRightsResponse.setMemberCompanyRightsDesc("企业会员专享权益");
                seachMemberRightsResponse.setMemberCompanyRightsResponse(memberCompanyRightsResponse);
            }

        } catch (Exception e) {
            log.error("企业会员信息查询异常", e);
        }
    }

    /**
     * 处理会员敬医权益
     *
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse 前端响应
     */
    private void processDoctorMemberRight(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTOView> memberRightMap,
                                          SeachMemberRightsResponse seachMemberRightsResponse) {

        List<MemberRightsDTOView> lists = processUsingChannelsApplyRange(rightsInfos, memberRightMap, MemberRightsTypeEnum.OTHER.getRuleType());
        if (CollectionUtils.isNotEmpty(lists)) {
            //将敬医金权益，放到前端响应对象中
            MemberDoctorRightsResponse memberDoctorRightsResponse = new MemberDoctorRightsResponse();
            lists.forEach(i -> {
                i.setAvailable(true);

            });
            memberDoctorRightsResponse.setMemberRights(lists);
            seachMemberRightsResponse.setMemberDoctorRightsResponse(memberDoctorRightsResponse);
        }
    }

    /**
     * 处理使用渠道和适用范围
     *
     * @param rightsInfos    会员所属权益（crm返回）
     * @param memberRightMap 所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param ruleType       权益类型
     * @return
     */
    public List<MemberRightsDTOView> processUsingChannelsApplyRange(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTOView> memberRightMap, String ruleType) {
        List<MemberRightsDTOView> lists = null;
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (rightsInfo.getRuleType().equals(ruleType)) {
                //将当前剩余可领取数量赋值给星级对象（做这一步的原因是因为，有的账号的星级权益领取过，与原数量不符）
                List<MemberRightsRuleDetailSoaModel> details = rightsInfo.getDetails();
                //获取会员敬医卡权益
                MemberLevelDTOView memberLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberLevelDTO != null) {
                    lists = memberLevelDTO.getMemberRights();
                    if (CollectionUtils.isNotEmpty(details) && CollectionUtils.isNotEmpty(lists)) {
                        for (MemberRightsRuleDetailSoaModel model : details) {
                            for (MemberRightsDTOView dto : lists) {
                                if (model.getRightsRecordId().equals(dto.getRightsRecordId())) {
                                    dto.setReceiveNumber(Integer.parseInt(model.getNumber()));
                                    //券的类型
                                    RightsProductTypeEnum type = RightsProductTypeEnum.checkEnum(model.getProductType());
                                    if (type != null) {
                                        dto.setRightsType(type.getCode());
                                    }

                                    //如果是优惠券
                                    if (model.getProductType().equalsIgnoreCase(RightsProductTypeEnum.COUPON.getName())) {
                                        processCouponActivity(model.getProductId(), dto);
                                    } else {
                                        //权益券
                                        processCoupon(model.getProductId(), dto);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }


        return lists;
    }


    /**
     * 处理会员等级权益
     *
     * @param levelDTOS                 等级权益集合
     * @param memberLevel               当前会员等级
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse 前端响应对象
     */
    private void processCommonMemberRight(List<MemberLevelDTOView> levelDTOS, MemberLevelDTOView memberLevel,
                                          List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTOView> memberRightMap,
                                          SeachMemberRightsResponse seachMemberRightsResponse) {
        int maxSerialNumber = 0;//最大的排序序号，此序号之前的权益全部点亮
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (rightsInfo.getRuleType().equals(MemberRightsTypeEnum.LEVEL.getRuleType())) {
                MemberLevelDTOView memberLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberLevelDTO != null) {
                    if (memberLevelDTO.getSerialNumber() > maxSerialNumber) {
                        maxSerialNumber = memberLevelDTO.getSerialNumber();
                    }
                }
            }
        }

        //处理普通权益展示及点亮会员所属权益图标
        if (CollectionUtils.isNotEmpty(levelDTOS)) {
            levelDTOS.sort(Comparator.comparing(MemberLevelDTOView::getSerialNumber));
            //前端响应 会员等级权益集合对象
            List<MemberRightsResponse> responseList = Lists.newArrayList();
            for (MemberLevelDTOView memberLevelDTO : levelDTOS) {
                List<MemberRightsDTOView> value = memberLevelDTO.getMemberRights();
                MemberRightsResponse memberRightsResponse = new MemberRightsResponse();
                boolean available = memberLevelDTO.getSerialNumber() != null && memberLevelDTO.getSerialNumber() <= maxSerialNumber;//最大序号之前的都亮
                if (available) {
                    BeanUtils.copyProperties(memberLevelDTO, memberLevel);
                }
                if (CollectionUtils.isNotEmpty(value)) {
                    value.sort((e1, e2) -> {
                        if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                            return e1.getOrderNo() - e2.getOrderNo();
                        } else {
                            return 0;
                        }
                    });
                    value.forEach(i -> i.setAvailable(available));
                }
                memberRightsResponse.setMemberLevelName(memberLevelDTO.getRuleName());
                memberRightsResponse.setMemberLevelCode(memberLevelDTO.getRuleId());
                memberRightsResponse.setWatermark(memberLevelDTO.getWaterMark());
                memberRightsResponse.setSerialNumber(memberLevelDTO.getSerialNumber());
                memberRightsResponse.setMemberRights(value);
                responseList.add(memberRightsResponse);
            }
            responseList.sort(Comparator.comparing(MemberRightsResponse::getSerialNumber));
            responseList.forEach(i -> {
                List<MemberRightsDTOView> memberRights = i.getMemberRights();
                memberRights.forEach(rightsDTO -> {
                    rightsDTO.setReceiveNumber(0);
                });
            });
            seachMemberRightsResponse.setMemberRightsResponse(responseList);
        }
    }

    /**
     * 处理会员星级权益
     *
     * @param levelDTOS                 星级权益集合
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse
     */
    private void processStarMemberRight(List<MemberLevelDTOView> levelDTOS, List<MemberRightsRuleSoaModel> rightsInfos, RequestDataDto requestData,
                                        Map<String, MemberLevelDTOView> memberRightMap, SeachMemberRightsResponse seachMemberRightsResponse) {
        //星级权益
        MemberLevelDTOView memberStarLevelDTO = null;
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (rightsInfo.getRuleType().equals(MemberRightsTypeEnum.STAR.getRuleType())) {
                //将当前剩余可领取数量赋值给星级对象（做这一步的原因是因为，有的账号的星级权益领取过，与原数量不符）
                List<MemberRightsRuleDetailSoaModel> details = rightsInfo.getDetails();
                memberStarLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberStarLevelDTO != null) {
                    List<MemberRightsDTOView> mrs = memberStarLevelDTO.getMemberRights();
                    if (CollectionUtils.isNotEmpty(details) && CollectionUtils.isNotEmpty(mrs)) {
                        for (MemberRightsRuleDetailSoaModel model : details) {
                            for (MemberRightsDTOView dto : mrs) {
                                if (model.getRightsRecordId().equals(dto.getRightsRecordId())) {
                                    dto.setReceiveNumber(Integer.parseInt(model.getNumber()));
                                    //券的类型
                                    RightsProductTypeEnum type = RightsProductTypeEnum.checkEnum(model.getProductType());
                                    if (type != null) {
                                        dto.setRightsType(type.getCode());
                                    }

                                    //如果是优惠券
                                    if (model.getProductType().equalsIgnoreCase(RightsProductTypeEnum.COUPON.getName())) {
                                        processCouponActivity(model.getProductId(), dto);
                                    } else {
                                        //权益券
                                        processCoupon(model.getProductId(), dto);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
        //处理星级权益及下一级星级权益展示
        processStarMemberRightsResponse(levelDTOS, memberStarLevelDTO, requestData, memberRightMap, seachMemberRightsResponse);
    }

    /**
     * 处理星级响应
     *
     * @param memberStarRightList 会员所有星级权益集合
     * @param memberStarLevelDTO  会员权益
     * @param req
     */
    public void processStarMemberRightsResponse(List<MemberLevelDTOView> memberStarRightList, MemberLevelDTOView memberStarLevelDTO,
                                                RequestDataDto req, Map<String, MemberLevelDTOView> memberRightMap, SeachMemberRightsResponse seachMemberRightsResponse) {
        memberStarRightList.sort((e1, e2) -> {
            if (null != e1.getSerialNumber() && null != e2.getSerialNumber()) {
                return e1.getSerialNumber() - e2.getSerialNumber();
            } else {
                return 0;
            }
        });
        //处理星级权益展示
        MemberStarRightsResponse memberStarRightsResponse = new MemberStarRightsResponse();
        memberStarRightsResponse.setHeadDesc("我的星级专享权益");
        memberStarRightsResponse.setMemberRightsDesc("会员权益");

        //调用crm 查询会员信息

        ChannelInfo channelInfo = commonService.findChannelInfo(req.getChannelNo());
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.STATEINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(req.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), req.getFfpId(), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(req.getFfpNo());
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRM = memberService.memberDetail(detaiReq, false);

        PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequestMemberStar = CRMReqUtil.buildCommReqNoToken(req.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest();
        memberStarQueryRequest.setId(Integer.valueOf(req.getFfpId()));
        ptApiCRMRequestMemberStar.setData(memberStarQueryRequest);
        PtCRMResponse<MemberStarQueryResp> ptCRMResponse = memberService.queryMemberStar(ptApiCRMRequestMemberStar, false);
        if (ptCRM.getCode() == 0) {
            if (req.getFfpId().equals(String.valueOf(ptCRM.getData().getBasicInfo().getMemberId()))) {
                memberStarRightsResponse.setCurrentLevel(ptCRM.getData().getStateInfo().getMemberLevelCode());
            } else {
                throw ServiceException.fail("会员信息不匹配");
            }
        } else {
            throw ServiceException.fail("会员信息不存在");
        }

        if (memberStarLevelDTO == null &&
                MemberLevelEnum.Fu_Card.getLevelCode().equals(ptCRM.getData().getStateInfo().getMemberLevelCode())
                && ptCRMResponse.getData().getMemberStarCode() == 0) {
            memberStarLevelDTO = memberRightMap.get(accountBffConfig.getMemberStarRuleId());
        }

        //星级响应赋值
        if (memberStarLevelDTO != null && StringUtils.isNotEmpty(memberStarLevelDTO.getRuleId())) {
            memberStarRightsResponse.setMemberLevelName(memberStarLevelDTO.getRuleName());
            memberStarRightsResponse.setMemberLevelCode(memberStarLevelDTO.getRuleId());
            memberStarRightsResponse.setWatermark(memberStarLevelDTO.getWaterMark());
            memberStarRightsResponse.setSerialNumber(memberStarLevelDTO.getSerialNumber());
            String currentRuleName = memberStarLevelDTO.getRuleName().replace("权益", "").replace("-新版", "");
            memberStarRightsResponse.setMemberLevelDesc(ptCRM.getData().getStateInfo().isIsDoctorCard() ? "敬医" + currentRuleName : currentRuleName);
            memberStarLevelDTO.getMemberRights().sort((e1, e2) -> {
                if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                    return e1.getOrderNo() - e2.getOrderNo();
                } else {
                    return 0;
                }
            });
            memberStarLevelDTO.getMemberRights().forEach(i -> {
                i.setAvailable(true);

            });
            memberStarRightsResponse.setMemberRights(memberStarLevelDTO.getMemberRights());
            if (CollectionUtils.isEmpty(memberStarRightsResponse.getMemberRights())) {
                memberStarRightsResponse.setDescription("暂无星级权益，提高星级获取专享权益");
            }
        } else {
            if (CollectionUtils.isEmpty(memberStarRightsResponse.getMemberRights())) {
                memberStarRightsResponse.setMemberRights(Lists.newArrayList());
                memberStarRightsResponse.setDescription("暂无星级权益，提高星级获取专享权益");
            }
        }

        if (ptCRM.getData().getStateInfo().isIsDoctorCard()) {
            if (ptCRM.getData().getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Silver.getLevelCode()) ||
                    ptCRM.getData().getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Golden.getLevelCode())) {
                if (ptCRM.getData().getStateInfo().getMemberStar() == 3) {
                    seachMemberRightsResponse.setMemberStarRightsResponse(memberStarRightsResponse);
                    return;
                }
            }
        }

        //赋值下一级星级权益
        MemberLevelDTOView nextMemberStarLevelDTO = null;
        for (int i = 0; i < memberStarRightList.size(); i++) {
            MemberLevelDTOView msr = memberStarRightList.get(i);
            //判断会员是否有星级权益
            if (memberStarLevelDTO != null) {
                if (StringUtils.isNotEmpty(memberStarLevelDTO.getRuleId())) {
                    if (msr.getSerialNumber() > memberStarLevelDTO.getSerialNumber()) {
                        if (CollectionUtils.isNotEmpty(msr.getMemberRights())) {
                            nextMemberStarLevelDTO = msr;
                            break;
                        }
                    }
                } else {
                    //如果会员没有星级权益，下一星级权益赋值
                    if (CollectionUtils.isNotEmpty(msr.getMemberRights())) {
                        nextMemberStarLevelDTO = msr;
                        break;
                    }
                }

            }

        }
        if (nextMemberStarLevelDTO != null) {
            memberStarRightsResponse.setNextMemberLevelName(ptCRM.getData().getStateInfo().isIsDoctorCard() ? "敬医" + nextMemberStarLevelDTO.getRuleName() : nextMemberStarLevelDTO.getRuleName());
            memberStarRightsResponse.setNextMemberLevelCode(nextMemberStarLevelDTO.getRuleId());
            nextMemberStarLevelDTO.getMemberRights().sort((e1, e2) -> {
                if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                    return e1.getOrderNo() - e2.getOrderNo();
                } else {
                    return 0;
                }
            });
            nextMemberStarLevelDTO.getMemberRights().forEach(i -> i.setAvailable(false));
            memberStarRightsResponse.setNextMemberStarRights(nextMemberStarLevelDTO.getMemberRights());
            String nextRuleName = memberStarRightsResponse.getNextMemberLevelName().replace("权益", "").replace("-新版", "");
            if ((Integer.valueOf(ptCRM.getData().getStateInfo().getMemberLevelCode()) <= 1 && ptCRMResponse.getData().getMemberStarCode() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                memberStarRightsResponse.setNextDescription("升级至" + nextRuleName + "获得更多权益");
            }
        } else {
            if (memberStarLevelDTO != null && StringUtils.isNotEmpty(memberStarLevelDTO.getRuleId())) {
                //判断当前星级权益级别是不是最高级别
                Boolean flag = memberStarRightList.get(memberStarRightList.size() - 1).getRuleId().equals(memberStarLevelDTO.getRuleId());
                String ruleName = "";
                if (flag) {
                    if ((Integer.valueOf(ptCRM.getData().getStateInfo().getMemberLevelCode()) <= 1 && ptCRMResponse.getData().getMemberStarCode() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                        ruleName = memberStarRightList.get(memberStarRightList.size() - 1).getRuleName().replace("权益", "会员").replace("-新版", "");
                        memberStarRightsResponse.setNextDescription("您已成为" + ruleName + "，续级成功后将保持享有以上权益");
                    }
                } else {
                    if ((Integer.valueOf(ptCRM.getData().getStateInfo().getMemberLevelCode()) <= 1 && ptCRM.getData().getStateInfo().getMemberStar() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                        ruleName = memberStarLevelDTO.getRuleName().replace("权益", "").replace("-新版", "");
                        memberStarRightsResponse.setNextDescription("您当前星级为" + ruleName);
                    }
                }
            } else {
                memberStarRightsResponse.setNextDescription(null);
            }
        }

        if (StringUtils.isEmpty(memberStarRightsResponse.getMemberLevelName())) {
            memberStarRightsResponse.setMemberLevelName(MemberLevelEnum.findLevelNameByLevelCode(ptCRM.getData().getStateInfo().getMemberLevelCode()));
        }
        if (StringUtils.isEmpty(memberStarRightsResponse.getMemberLevelCode())) {
            memberStarRightsResponse.setMemberLevelCode(ptCRM.getData().getStateInfo().getMemberLevelCode());
        }
        if (StringUtils.isEmpty(memberStarRightsResponse.getMemberLevelDesc())) {
            memberStarRightsResponse.setMemberLevelDesc(MemberLevelEnum.findLevelNameByLevelCode(ptCRM.getData().getStateInfo().getMemberLevelCode()) + ptCRM.getData().getStateInfo().getMemberStar() + "星");
        }
        seachMemberRightsResponse.setMemberStarRightsResponse(memberStarRightsResponse);
    }


    /**
     * 处理权益券的使用渠道、适用范围
     *
     * @param dto
     * @param activityNo
     */
    public void processCoupon(String activityNo, MemberRightsDTOView dto) {
        dto.setActivityNo(activityNo);
        CouponActivityReq couponActivityReq = new CouponActivityReq();
        couponActivityReq.setActivityNo(activityNo);
        couponActivityReq.setChannelCode("MOBILE");
        couponActivityReq.setVersion("10");
        CouponResp catchRightCoupons = memberService.toCatchRightCoupons(couponActivityReq);
        if (StringUtils.isEmpty(catchRightCoupons.getResultCode())
                || !"10001".equals(catchRightCoupons.getResultCode())) {
            dto.setUsingChannels(accountBffConfig.getUsingChannels());
            dto.setApplyRange(accountBffConfig.getApplyRange());
        } else {
            //适用范围
            dto.setApplyRange(StringUtils.isEmpty(catchRightCoupons.getApplyRange()) ? accountBffConfig.getApplyRange() : catchRightCoupons.getApplyRange().replaceAll("<p>", "").replaceAll("</p>", ""));
            //使用渠道
            dto.setUsingChannels(StringUtils.isEmpty(catchRightCoupons.getUsingChannels()) ? accountBffConfig.getUsingChannels() : catchRightCoupons.getUsingChannels().replaceAll("<p>", "").replaceAll("</p>", ""));
        }
    }

    /**
     * 处理优惠券的使用渠道、适用范围
     *
     * @param dto
     * @param activityNo
     */
    public void processCouponActivity(String activityNo, MemberRightsDTOView dto) {
        dto.setActivityNo(activityNo);
        CouponActivityReq couponActivityReq = new CouponActivityReq();
        couponActivityReq.setActivityNo(activityNo);
        couponActivityReq.setChannelCode("MOBILE");
        couponActivityReq.setVersion("10");
        couponActivityReq.setUserNo("10007");
        CouponActivityResp catchCoupons = memberService.toCatchCoupons(couponActivityReq);
        if (StringUtils.isEmpty(catchCoupons.getResultCode())
                || !"1001".equals(catchCoupons.getResultCode())
                || null == catchCoupons.getCouponActivityList()
                || catchCoupons.getCouponActivityList().isEmpty()) {
            dto.setUsingChannels(accountBffConfig.getUsingChannels());
            dto.setApplyRange(accountBffConfig.getApplyRange());
        } else {
            List<CouponActivityInfo> couponActivityList = catchCoupons.getCouponActivityList();
            //适用范围
            dto.setApplyRange(StringUtils.isEmpty(couponActivityList.get(0).getRemark()) ? accountBffConfig.getApplyRange() : couponActivityList.get(0).getRemark());
            //使用渠道
            String channelNm = couponActivityList.get(0).getChannelNm();
            if (StringUtils.isNotEmpty(channelNm)) {
                String[] channelNms = channelNm.split(",");
                for (String c : channelNms) {
                    UsingChannelsEnum usingChannelsEnum = UsingChannelsEnum.checkEnum(c);
                    if (usingChannelsEnum != null) {
                        channelNm = channelNm.replaceAll(usingChannelsEnum.getUsingChannel(), usingChannelsEnum.getDesc());
                    }
                }
            } else {
                channelNm = accountBffConfig.getUsingChannels();
            }
            dto.setUsingChannels(channelNm);
        }
    }


    /**
     * @param memberRightList
     * @param memberRightMap
     * @param bizDto
     * @return void
     * <AUTHOR>
     * @Description 获取权益信息
     * @Date 20:10 2023/7/15
     **/
    private void getMemberRights(Map<String, List<MemberLevelDTOView>> memberRightList,
                                 Map<String, MemberLevelDTOView> memberRightMap,
                                 BizDto bizDto) {
        //换用feign接口获取会员权益
        List<MemberLevelDTOView> queryMemberRights = memberService.toQueryMemberRights(bizDto);
        //将全部会员权益列表，分开保存
        List<MemberLevelDTOView> memberLevelRightList = Lists.newArrayList(); //等级权益
        List<MemberLevelDTOView> memberStarRightList = Lists.newArrayList(); //星级权益
        List<MemberLevelDTOView> memberDoctorRightList = Lists.newArrayList(); //敬医权益
        List<MemberLevelDTOView> memberCompanyRightList = Lists.newArrayList(); //企业专属权益

        for (MemberLevelDTOView item : queryMemberRights) {
            if (item != null) {
                if (MemberRightsTypeEnum.LEVEL.getRuleType().equals(item.getType())) {
                    memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                    memberLevelRightList.add(item);
                } else if (MemberRightsTypeEnum.STAR.getRuleType().equals(item.getType())) {
                    memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                    memberStarRightList.add(item);
                } else if (MemberRightsTypeEnum.OTHER.getRuleType().equals(item.getType())) {
                    memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                    memberDoctorRightList.add(item);
                } else if (MemberRightsTypeEnum.COMPANY.getRuleType().equals(item.getType())) {
                    memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                    memberCompanyRightList.add(item);
                }
            }
        }
        memberRightList.put(MemberRightsTypeEnum.LEVEL.getRuleType(), memberLevelRightList);
        memberRightList.put(MemberRightsTypeEnum.STAR.getRuleType(), memberStarRightList);
        memberRightList.put(MemberRightsTypeEnum.OTHER.getRuleType(), memberDoctorRightList);
        memberRightList.put(MemberRightsTypeEnum.COMPANY.getRuleType(), memberCompanyRightList);
    }

    /**
     * @param requestData
     * @return com.juneyaoair.oneorder.crm.dto.common.LevelTransferDTO
     * <AUTHOR>
     * @Description 查询升级保级信息
     * @Date 17:43 2023/7/15
     **/
    private LevelTransferDTO toCatchUpAndProInfo(RequestData requestData) {
        LevelTransferDTO response = new LevelTransferDTO();
        try {
            //调用crm，查询积分、航段定级标准
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                    , MemberDetailRequestItemsEnum.STATEINFO.eName};
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
            ptMemberDetailRequest.setRequestItems(items);
            detaiReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(detaiReq, false);
            String levelCode = ptCRMResponse.getData().getStateInfo().getMemberLevelCode();
            //星级失效日期
            long starExpireDate = ptCRMResponse.getData().getStateInfo().getStarExpireDate();
            Map<String, MemberLevelUpgradeRuleArray> ruleMap = new HashMap<>();
            MemberUpGradeRequest memberUpGradeRequest = new MemberUpGradeRequest();
            memberUpGradeRequest.setClientCode(channelInfo.getChannelCode());
            memberUpGradeRequest.setSignature(EncoderHandler.encodeByMD5(channelInfo.getChannelCode() + channelInfo.getChannelPwd()));
            MemberLevelInfoResp upGradeRules = memberService.toCatchUpRule(memberUpGradeRequest);
            List<MemberLevelUpgradeRuleArray> memberLevelList = upGradeRules.getMemberLevelUpgradeRuleArray();
            if (CollectionUtils.isNotEmpty(memberLevelList)) {
                // 银卡、金卡、白金卡具有明确的保级规则及晋级规则，其他VIP类型无法晋级或保级
                memberLevelList.forEach(item -> {
                    ruleMap.put(item.getMEMBER_LEVEL_CODE(), item);
                });
            }
            //查询积分账户
            PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
            items = new String[]{MileageAccountRequestItemsEnum.LEVELCHANGE.eName};
            mileageAccountQueryRequest.setMemberCardNo(requestData.getFfpNo());
            mileageAccountQueryRequest.setRequestItems(items);
            ptApiCRMRequest.setData(mileageAccountQueryRequest);
            PtCRMResponse<MileageAccountQueryResponse> mileageAccountQueryResponse = memberService.mileageAccountQuery(ptApiCRMRequest, false);
            MemberLevelChangeSoaModel levelChange = mileageAccountQueryResponse.getData().getLevelChange();

            //会员升降级信息
            //会员保级信息赋值
            MemberProGradeResp memberProGradeResp = new MemberProGradeResp();
            memberProGradeResp.setCurrentLevel(levelCode);
            memberProGradeResp.setCurrentLevelCode(levelCode);
            memberProGradeResp.setMemberStar(ptCRMResponse.getData().getStateInfo().getMemberStar());
            //---------获取星级信息----------
            if ("Y".equals(accountBffConfig.getMemberStarQueryOpen())) {
                PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequestMemberStar = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest();
                memberStarQueryRequest.setId(Integer.valueOf(requestData.getFfpId().toString()));
                ptApiCRMRequestMemberStar.setData(memberStarQueryRequest);
                PtCRMResponse<MemberStarQueryResp> ptCRMResponseMember = memberService.queryMemberStar(ptApiCRMRequestMemberStar, false);
                MemberStarQueryResp memberStarQueryResp = ptCRMResponseMember.getData();
                memberProGradeResp.setMemberStar(memberStarQueryResp.getMemberStarCode());
            }
            //级别失效日期
            long levelExpireDate = ptCRMResponse.getData().getStateInfo().getLevelExpireDate();
            memberProGradeResp.setEndDate(DateUtil.timeStampToDateStr(levelExpireDate));
            memberProGradeResp.setIsDoctorCard(ptCRMResponse.getData().getStateInfo().isIsDoctorCard());
            if (ptCRMResponse.getData().getStateInfo().isIsDoctorCard()) {
                if (ptCRMResponse.getData().getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Silver.getLevelCode())) {
                    memberProGradeResp.setCurrentLevelName("敬医" + MemberLevelEnum.Silver.getLevelName());
                }
                if (ptCRMResponse.getData().getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Golden.getLevelCode())) {
                    memberProGradeResp.setCurrentLevelName("敬医" + MemberLevelEnum.Golden.getLevelName());
                    memberProGradeResp.setDoctorCardDesc("终身有效 享金卡基础权益并同时享有同行权益复制特权");
                }
                memberProGradeResp.setEndDate("终身有效");
                memberProGradeResp.setTimeLeft(-1);
                response.setMemberProGradeResp(memberProGradeResp);
                return response;
            }

            memberProGradeResp.setCurrentLevelName(MemberLevelEnum.findLevelNameByLevelCode(levelChange.getDegradeInfo().getLevelCode()));
            memberProGradeResp.setLevelContinueSegment(levelChange.getDegradeInfo().getSegments());
            memberProGradeResp.setGradingMile(levelChange.getRecentCycleMiles());
            memberProGradeResp.setGradingSegment(levelChange.getRecentCycleSegments());
            //星级失效日期
            if (starExpireDate != 0) {
                memberProGradeResp.setStarExpireDate(DateUtil.timeStampToDateStr(starExpireDate));
            }
            MemberLevelEnum memberLevelEnum = null;
            if (StringUtils.isNotEmpty(levelChange.getDegradeInfo().getLevelCode())) {
                memberLevelEnum = MemberLevelEnum.findByLevelCode(levelChange.getDegradeInfo().getLevelCode());
            }

            if (ptCRMResponse.getData().getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Fu_Card.getLevelCode())) {
                memberProGradeResp.setCurrentLevelName(MemberLevelEnum.Fu_Card.getLevelName());
                memberLevelEnum = MemberLevelEnum.Fu_Card;
                memberProGradeResp.setEndDate("");
            }
            //保级响应对象中增加有效剩余时间字段，保存剩余多少天到期
            if (memberLevelEnum == MemberLevelEnum.Fu_Card) {
                memberProGradeResp.setTimeLeft(-1);
            } else {
                if (StringUtils.isEmpty(memberProGradeResp.getEndDate())) {
                    memberProGradeResp.setTimeLeft(-1);
                } else {
                    int dateDiff = DateUtil.dateDiff(DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN), memberProGradeResp.getEndDate(), DateUtil.YYYY_MM_DD_PATTERN);
                    if (dateDiff < 0) {
                        memberProGradeResp.setTimeLeft(-1);
                    } else {
                        memberProGradeResp.setTimeLeft(dateDiff);

                    }
                }
            }

            if (null != memberLevelEnum) {
                int degradeMiles = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                        0 : ruleMap.get(memberLevelEnum.getLevelCode()).getDEGRADE_MILES();
                int degradeSegments = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                        0 : ruleMap.get(memberLevelEnum.getLevelCode()).getDEGRADE_SEGMENTS();
                memberProGradeResp.setProMilePercent(proportionInt(levelChange.getDegradeInfo().getMiles(), degradeMiles));
                memberProGradeResp.setProSegmentPercent(proportionInt(levelChange.getDegradeInfo().getSegments(), degradeSegments));
                memberProGradeResp.setProMile(degradeMiles);
                memberProGradeResp.setProSegment(degradeSegments);
                memberProGradeResp.setCurrentLevelCode(memberLevelEnum.getLevelCode());
            }
            if (!PLATINUMCARD.contains(levelCode)) {
                //会员升级信息赋值
                MemberUpGradeResp memberUpGradeResp = new MemberUpGradeResp();
                memberUpGradeResp.setNewLevelName(levelChange.getUpgradeInfo().getLevelName());
                memberUpGradeResp.setLevelUPMile(levelChange.getUpgradeInfo().getMiles());
                memberUpGradeResp.setLevelUPSegment(levelChange.getUpgradeInfo().getSegments());
                memberUpGradeResp.setEndDate(levelChange.getUpgradeInfo().getEndDate());
                memberLevelEnum = MemberLevelEnum.findByLevelCode(levelChange.getUpgradeInfo().getLevelCode());
                if (null != memberLevelEnum) {
                    int upgradeMiles = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                            0 : ruleMap.get(memberLevelEnum.getLevelCode()).getUPGRADE_MILES();
                    int upgradeSegments = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                            0 : ruleMap.get(memberLevelEnum.getLevelCode()).getUPGRADE_SEGMENTS();
                    memberUpGradeResp.setUpMilePercent(proportionInt(levelChange.getUpgradeInfo().getMiles(), upgradeMiles));
                    memberUpGradeResp.setUpSegmentPercent(proportionInt(levelChange.getUpgradeInfo().getSegments(), upgradeSegments));
                    memberUpGradeResp.setNextLevelMile(upgradeMiles);
                    memberUpGradeResp.setNextLevelSegment(upgradeSegments);
                    memberUpGradeResp.setNewLevelCode(memberLevelEnum.getLevelCode());
                }
                response.setMemberUpGradeResp(memberUpGradeResp);
            } else {
                if (null != memberLevelEnum) {
                    int degradeMiles = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                            0 : ruleMap.get(memberLevelEnum.getLevelCode()).getDEGRADE_MILES();
                    int degradeSegments = ruleMap.get(memberLevelEnum.getLevelCode()) == null ?
                            0 : ruleMap.get(memberLevelEnum.getLevelCode()).getDEGRADE_SEGMENTS();
                    memberProGradeResp.setProMilePercent(proportionInt(degradeMiles - memberProGradeResp.getGradingMile(), degradeMiles));
                    memberProGradeResp.setProSegmentPercent(proportionInt(degradeSegments - memberProGradeResp.getGradingSegment(), degradeSegments));
                    memberProGradeResp.setProMile(degradeMiles);
                    memberProGradeResp.setProSegment(degradeSegments);
                }
            }
            response.setMemberProGradeResp(memberProGradeResp);
        } catch (Exception e) {
            log.error("查询升级保级出现异常:", e);
        }
        return response;
    }

    /**
     * @param divisor  升级保级差额数据
     * @param dividend 升级保级所需总数
     * @return
     */
    private String proportionInt(Integer divisor, Integer dividend) {
        if (dividend == null || divisor == null) {
            return "100";
        }
        if (dividend == 0) {
            return "100";
        }
        if (divisor <= 0) {
            return "0";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);
        if (result.indexOf('.') != -1) {
            result = String.valueOf(Math.round(Double.parseDouble(result)));
        }
        return result;
    }

    /**
     * @param oriLevelCode
     * @return int
     * <AUTHOR>
     * @Description 确定升级到下一等级所需航段
     * @Date 10:33 2023/9/3
     **/

    private int toConfirmUpSegments(String oriLevelCode) {
        if (StringUtils.isEmpty(oriLevelCode)) {
            return 0;
        }
        if (oriLevelCode.equals(MemberLevelEnum.Fu_Card.getLevelCode())) {
            return 15;
        } else if (oriLevelCode.equals(MemberLevelEnum.Silver.getLevelCode()) || oriLevelCode.equals(MemberLevelEnum.S_Silver.getLevelCode())) {
            return 30;
        } else if (oriLevelCode.equals(MemberLevelEnum.Golden.getLevelCode()) || oriLevelCode.equals(MemberLevelEnum.S_Golden.getLevelCode())) {
            return 60;
        }
        return 0;
    }

    private int toConfirmProSegments(MemberLevelEnum memberLevelEnum) {
        if (null == memberLevelEnum || memberLevelEnum == MemberLevelEnum.Fu_Card) {
            return 0;
        }
        if (memberLevelEnum == MemberLevelEnum.Silver || memberLevelEnum == MemberLevelEnum.S_Silver) {
            return 14;
        } else if (memberLevelEnum == MemberLevelEnum.Golden || memberLevelEnum == MemberLevelEnum.S_Golden) {
            return 27;
        } else {
            return 54;
        }
    }

    @Override
    public boolean toEditMemberInfo(RequestData<MemberInfoEditRequest> requestData) {
        MemberInfoEditRequest editRequest = requestData.getData();
        //1. 已实名 中英文姓名皆不可修改
        if (editRequest.isRealName() && !StringUtils.isAllEmpty(editRequest.getMemberBasicInfo().getCFirstName(), editRequest.getMemberBasicInfo().getCLastName(), editRequest.getMemberBasicInfo().getEFirstName(), editRequest.getMemberBasicInfo().getELastName())) {
            throw ServiceException.fail("已实名，中英文姓名不可修改");
        } else if (!editRequest.isRealName()) {
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            toModifyBasicInfo(requestData, editRequest, channelInfo);
        }
        //处理会员生日与性别
        toHandleBirthday(requestData);
        //处理地址信息
        /*toHandleAddress(requestData, ptMemberDetail);*/
        return true;
    }

    /**
     * @param requestData
     * @param editRequest
     * @param channelInfo
     * @return void
     * <AUTHOR>
     * @Description 更新会员基本信息
     * @Date 10:47 2023/9/11
     **/
    private void toUpdateMemberBasicInfo(RequestData<MemberInfoEditRequest> requestData, MemberInfoEditRequest editRequest, ChannelInfo channelInfo) {
        //有过姓名记录 此时是真正的修改 需要经过审核
        PtApiCRMRequest<MemberKeyInfoReq> ptApiRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), null);
        MemberKeyInfoReq memberKeyInfoReq = new MemberKeyInfoReq();
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getCFirstName())) {
            memberKeyInfoReq.setCFirstName(editRequest.getMemberBasicInfo().getCFirstName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getCLastName())) {
            memberKeyInfoReq.setCLastName(editRequest.getMemberBasicInfo().getCLastName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getEFirstName())) {
            memberKeyInfoReq.setEFirstName(editRequest.getMemberBasicInfo().getEFirstName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getELastName())) {
            memberKeyInfoReq.setELastName(editRequest.getMemberBasicInfo().getELastName());
        }
        ptApiRequest.setData(memberKeyInfoReq);
        PtCRMResponse ptCRMResponse = memberService.applyMemberKeyInfo(ptApiRequest);
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
    }

    /**
     * @param requestData
     * @param editRequest
     * @param channelInfo
     * @return void
     * <AUTHOR>
     * @Description 修改会员基本信息
     * @Date 10:46 2023/9/11
     **/
    private void toModifyBasicInfo(RequestData<MemberInfoEditRequest> requestData, MemberInfoEditRequest editRequest, ChannelInfo channelInfo) {
        PtApiCRMRequest<PtModifyCustomerInfoRequest> ptModifyRequest = CRMReqUtil.buildCommReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
        PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = new PtModifyCustomerInfoRequest();
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getCFirstName())) {
            ptModifyCustomerInfoRequest.setCFirstName(editRequest.getMemberBasicInfo().getCFirstName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getCLastName())) {
            ptModifyCustomerInfoRequest.setCLastName(editRequest.getMemberBasicInfo().getCLastName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getEFirstName())) {
            ptModifyCustomerInfoRequest.setEFirstName(editRequest.getMemberBasicInfo().getEFirstName());
        }
        if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getELastName())) {
            ptModifyCustomerInfoRequest.setELastName(editRequest.getMemberBasicInfo().getELastName());
        }
        ptModifyRequest.setData(ptModifyCustomerInfoRequest);
        memberService.toSaveCusBasicInfo(ptModifyRequest);
    }

    /**
     * @param requestData
     * @param ptMemberDetail
     * @return void
     * <AUTHOR>
     * @Description 处理地址信息
     * @Date 10:15 2023/9/11
     **/
    private void toHandleAddress(RequestData<MemberInfoEditRequest> requestData, PtMemberDetail ptMemberDetail) {
        MemberInfoEditRequest editRequest = requestData.getData();
        if (null == editRequest) {
            return;
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        MemberAddressInfo memberAddressInfo = editRequest.getMemberAddressInfo();
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = new PtCRMResponse<>();
        if (null != memberAddressInfo) {
            if (StringUtils.isAnyEmpty(memberAddressInfo.getCountry(), memberAddressInfo.getProvince(), memberAddressInfo.getTown(), memberAddressInfo.getDetailAddress())) {
                throw ServiceException.fail("会员地址输入错误");
            }
            if (null == ptMemberDetail) {
                PtApiCRMRequest<PtMemberDetailRequest> ptDetailApiRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
                PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
                String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                        , MemberDetailRequestItemsEnum.CONTACTINFO.eName};
                ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
                ptMemberDetailRequest.setRequestItems(items);
                ptDetailApiRequest.setData(ptMemberDetailRequest);
                detailPtCRMResponse = memberService.memberDetail(ptDetailApiRequest, false);
                if (detailPtCRMResponse.getCode() != 0) {
                    throw ServiceException.fail(detailPtCRMResponse.getMsg());
                } else {
                    ptMemberDetail = detailPtCRMResponse.getData();
                }
            }
            if (null != ptMemberDetail) {
                String chinaName = CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo());
                MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(ptMemberDetail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                //如果记录id为null，则为新增地址，否则修改
                if (StringUtils.isEmpty(requestData.getData().getMemberAddressInfo().getRecord())) {
                    PtApiCRMRequest<MemberAddressAdd> ptApiRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
                    MemberAddressAdd memberAddressAdd = new MemberAddressAdd();
                    MemberAddressReq memberAddressReq = new MemberAddressReq();
                    if (StringUtils.isNotEmpty(memberAddressInfo.getCountry())) {
                        memberAddressReq.setCountryCode(memberAddressInfo.getCountry());
                    }

                    if (StringUtils.isNotEmpty(memberAddressInfo.getProvince())) {
                        memberAddressReq.setProvinceCode(memberAddressInfo.getProvince());
                    }

                    if (StringUtils.isNotEmpty(memberAddressInfo.getTown())) {
                        memberAddressReq.setCityCode(memberAddressInfo.getTown());
                    }

                    if (StringUtils.isNotEmpty(memberAddressInfo.getDetailAddress())) {
                        memberAddressReq.setAddress(memberAddressInfo.getDetailAddress());
                    }

                    if (StringUtils.isNotEmpty(memberAddressInfo.getZipCode())) {
                        memberAddressReq.setPostCode(memberAddressInfo.getZipCode());
                    }
                    //2019-05-27 添加时默认收件人为当前会员
                    if (detailPtCRMResponse.isIsSuccess() && detailPtCRMResponse.getCode() == 0) {
                        memberAddressReq.setReceiver(chinaName);
                        memberAddressReq.setReceiverMobile(memberContactSoaModel != null ? memberContactSoaModel.getContactNumber() : "");
                    }
                    memberAddressReq.setAddressType(PostAddressTypeEnum.FAMILY.getCode());
                    memberAddressAdd.setAddressInfo(memberAddressReq);
                    ptApiRequest.setData(memberAddressAdd);
                    memberService.addCustomerAddress(ptApiRequest);
                } else {
                    PtApiCRMRequest<PtMemberAddressReq> ptApiRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
                    PtMemberAddressReq ptMemberAddressReq = new PtMemberAddressReq();
                    if (detailPtCRMResponse.isIsSuccess() && detailPtCRMResponse.getCode() == 0) {
                        ptMemberAddressReq.setReceiver(chinaName);
                        ptMemberAddressReq.setReceiverMobile(memberContactSoaModel != null ? memberContactSoaModel.getContactNumber() : "");
                    }
                    ptMemberAddressReq.setAddressType(PostAddressTypeEnum.FAMILY.getCode());
                    if (StringUtils.isNotEmpty(memberAddressInfo.getCountry())) {
                        ptMemberAddressReq.setCountryCode(memberAddressInfo.getCountry());
                    }
                    if (StringUtils.isNotEmpty(memberAddressInfo.getProvince())) {
                        ptMemberAddressReq.setProvinceCode(memberAddressInfo.getProvince());
                    }
                    if (StringUtils.isNotEmpty(memberAddressInfo.getTown())) {
                        ptMemberAddressReq.setCityCode(memberAddressInfo.getTown());
                    }
                    if (StringUtils.isNotEmpty(memberAddressInfo.getDetailAddress())) {
                        ptMemberAddressReq.setAddress(memberAddressInfo.getDetailAddress());
                    }
                    if (StringUtils.isNotEmpty(memberAddressInfo.getZipCode())) {
                        ptMemberAddressReq.setPostCode(memberAddressInfo.getZipCode());
                    }
                    try {
                        ptMemberAddressReq.setRecordID(Integer.parseInt(AESTool.decrypt(memberAddressInfo.getRecord(), CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16))));
                    } catch (Exception e) {
                        throw ServiceException.fail("记录ID设置出错");
                    }
                    ptApiRequest.setData(ptMemberAddressReq);
                    memberService.modifyCustomerAddress(ptApiRequest);
                }
            }
        }

    }

    /**
     * @param requestData
     * @return void
     * <AUTHOR>
     * @Description 保存会员生日及性别
     * @Date 10:15 2023/9/11
     **/
    private void toHandleBirthday(RequestData<MemberInfoEditRequest> requestData) {
        MemberInfoEditRequest editRequest = requestData.getData();
        if (null == editRequest) {
            return;
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        if (null != editRequest.getMemberBasicInfo()) {
            PtApiCRMRequest<ModifyCustomerInfoRequest> ptApiCRMRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            ModifyCustomerInfoRequest modifyCustomerInfoRequest = new ModifyCustomerInfoRequest();
            if (StringUtils.isNotEmpty(editRequest.getMemberBasicInfo().getBirthday())) {
                modifyCustomerInfoRequest.setBirthday(Objects.requireNonNull(DateUtil.toDate(editRequest.getMemberBasicInfo().getBirthday(), "yyyy-MM-dd")).getTime());
            }
            if (0 != editRequest.getMemberBasicInfo().getSex()) {
                modifyCustomerInfoRequest.setSex(toConvertSex(editRequest.getMemberBasicInfo().getSex()));
            }
            ptApiCRMRequest.setData(modifyCustomerInfoRequest);
            memberService.modifyCustomerInfo(ptApiCRMRequest);
        }
    }

    /**
     * @param originSex
     * @return java.lang.String
     * <AUTHOR>
     * @Description 性别转换
     * @Date 16:12 2023/11/6
     **/
    private String toConvertSex(int originSex) {
        if (1 == originSex) {
            return "M";
        }
        if (2 == originSex) {
            return "F";
        }
        return "U";
    }

    //判断会员是否需要先修改姓名
    private boolean checkModifyName(MemberBasicInfoSoaModel memberBasicInfoSoaModel) {
        //只有完全没填写时可以自动修改
        return StringUtils.isAllEmpty(memberBasicInfoSoaModel.getCLastName(), memberBasicInfoSoaModel.getCFirstName(), memberBasicInfoSoaModel.getELastName(), memberBasicInfoSoaModel.getEFirstName());
    }

    /**
     * 反射判断一个对象里的值是否都为空
     * false表示不为空
     * true表示都为空
     */
    public static boolean isAllNull(Object object) throws IllegalAccessException {
        Class cla = object.getClass();
        //获取属性集合
        Field[] declaredFields = cla.getDeclaredFields();
        boolean flag = true;
        for (Field f : declaredFields) {
            f.setAccessible(true);
            Object o = f.get(object);
            if (o != null) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    public MemberRealInfo toCatchRealNameInfo(RequestData<MemberDetailReq> requestData) {
        try {
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            ptMemberDetailRequest.setRequestItems(items);
            detaiReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq, false);
            if (detailPtCRMResponse.getCode() != 0) {
                throw ServiceException.fail(detailPtCRMResponse.getMsg());
            }

            List<MemberRealNameSummarySoaModel> verifyInfoList = detailPtCRMResponse.getData().getRealVerifyInfos();
            //用户不存在认证记录 认为没进行
            MemberRealInfo memberRealInfo = new MemberRealInfo();

            if (CollectionUtils.isEmpty(verifyInfoList)) {
                //渲染无实名数据结果
                memberRealInfo = initMemberRealInfo("", VerifyStatusEnum.UNKNOW.code, "", "");
            } else {
                //获取最新的认证记录
                MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = verifyInfoList.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
                if (null == memberRealNameSummarySoaModel) {
                    throw ServiceException.fail("未查询到相关实名记录！");
                }
                VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(memberRealNameSummarySoaModel.getStatus());
                VerifyChannelEnum verifyChannelEnum = VerifyChannelEnum.checkEnum(memberRealNameSummarySoaModel.getVerifyChannel());
                if (verifyChannelEnum == null && null != verifyStatusEnum) {
                    memberRealInfo = initMemberRealInfo(verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), "外部认证");
                } else if (null != verifyStatusEnum) {
                    memberRealInfo = initMemberRealInfo(verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), verifyChannelEnum.desc);
                    //认证通过异步存入缓存
                    if (VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                        taskExecutor.execute(new RealInfoThread(requestData.getFfpNo(), verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), verifyChannelEnum.desc));
                    }
                }
                //实名认证审核拒绝加上拒绝原因
                if (verifyStatusEnum != null && VerifyStatusEnum.REJECT.code.equals(verifyStatusEnum.code)) {
                    memberRealInfo.setRejectReason(memberRealNameSummarySoaModel.getComments());
                }
                memberRealInfo.setVerifyDesc(verifyStatusEnum == null ? "" : verifyStatusEnum.desc);
                memberRealInfo.setVerifyStatus(verifyStatusEnum == null ? "-2" : verifyStatusEnum.code);
                //实名认证审核拒绝加上拒绝原因
                if (verifyStatusEnum != null && verifyStatusEnum.code.equals(VerifyStatusEnum.REJECT.code)) {
                    memberRealInfo.setRejectReason(memberRealNameSummarySoaModel.getComments());
                }
            }

            PtMemberDetail detail = detailPtCRMResponse.getData();
            memberRealInfo.setCLastName(detail.getBasicInfo().getCLastName());
            memberRealInfo.setCFirstName(detail.getBasicInfo().getCFirstName());
            memberRealInfo.setELastName(detail.getBasicInfo().getELastName());
            memberRealInfo.setEFirstName(detail.getBasicInfo().getEFirstName());
            MemberContactSoaModel memberContactSoaModel = CRMReqUtil.toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            if (memberContactSoaModel != null) {
                memberRealInfo.setPhone(memberContactSoaModel.getContactNumber());
            }
            //获取身份证信息
            MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.filterCert(detail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
            //护照
            MemberCertificateSoaModelV2 passport = CRMReqUtil.filterCert(detail.getCertificateInfo(), CertificateTypeEnum.PASSPORT.getCode());
            if (memberCertificateSoaModelV2 != null) {
                CertificateModel customerCertificateInfo = new CertificateModel();
                BeanUtils.copyProperties(memberCertificateSoaModelV2, customerCertificateInfo);
                customerCertificateInfo.setCertificateTypeEName(CertificateTypeEnum.ID_CARD.geteName());
                customerCertificateInfo.setCertificateTypeCName(CertificateTypeEnum.ID_CARD.getDesc());
                memberRealInfo.setCertificateInfo(customerCertificateInfo);
            }
            //没有证件信息需要先补全证件
            if (StringUtils.isEmpty(requestData.getData().getVerifyType())) {
                if (CollectionUtils.isEmpty(detail.getCertificateInfo())) {
                    memberRealInfo.setImproveFlag(true);
                }
            } else {
                if (VerifyChannelEnum.ZhiFuBao.code.equals(requestData.getData().getVerifyType())) {
                    //支付宝的目前只要有身份证
                    if (memberCertificateSoaModelV2 == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else if (VerifyChannelEnum.Face.code.equals(requestData.getData().getVerifyType())) {
                    //人脸识别的目前只要有身份证
                    if (memberCertificateSoaModelV2 == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else if (VerifyChannelEnum.Photo.code.equals(requestData.getData().getVerifyType())) {
                    //证件照目前支持的证件类型是身份证和护照
                    if (memberCertificateSoaModelV2 == null && passport == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else {
                    throw ServiceException.fail("暂不支持的认证方式！");
                }
            }
            memberRealInfo.setPhone(encrypt(memberRealInfo.getPhone()));
            if (null != memberRealInfo.getCertificateInfo()) {
                memberRealInfo.getCertificateInfo().setCertificateNumber(encrypt(memberRealInfo.getCertificateInfo().getCertificateNumber()));
            }
            return memberRealInfo;
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            throw ServiceException.fail("获取实名状态出错");
        }
    }

    @Override
    public MemberRealInfo onlyCheckRealNameStatus(RequestData<MemberDetailReq> requestData) {
        try {

            MemberRealInfo memberRealInfo;

            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());

            // 查询实名信息
            List<MemberRealNameSummarySoaModel> verifyInfoList = getRealNameVerifyInfos(requestData, channelInfo);

            memberRealInfo = processRealNameVerifyInfos(verifyInfoList, requestData.getFfpNo());

            // 查询总积分
            updateMemberTotalScore(requestData, channelInfo, memberRealInfo);

            return memberRealInfo;
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            throw ServiceException.fail("获取实名状态出错");
        }
    }

    private List<MemberRealNameSummarySoaModel> getRealNameVerifyInfos(RequestData<MemberDetailReq> requestData, ChannelInfo channelInfo) {
        PtApiCRMRequest<PtMemberDetailRequest> memberReq = CRMReqUtil.buildCommReq(
                requestData.getOriginIp(),
                channelInfo.getChannelCode(),
                channelInfo.getChannelPwd(),
                requestData.getFfpId(),
                ""
        );

        PtMemberDetailRequest memberReqData = new PtMemberDetailRequest();
        memberReqData.setRequestItems(new String[]{
                MemberDetailRequestItemsEnum.BASICINFO.eName,
                MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName
        });
        memberReqData.setCardNO(requestData.getFfpNo());
        memberReq.setData(memberReqData);

        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(memberReq, false);
        return detailPtCRMResponse.getData().getRealVerifyInfos();
    }

    private MemberRealInfo processRealNameVerifyInfos(List<MemberRealNameSummarySoaModel> verifyInfoList, String ffpNo) {
        MemberRealInfo memberRealInfo = new MemberRealInfo();

        if (CollectionUtils.isEmpty(verifyInfoList)) {
            memberRealInfo = initMemberRealInfo("", VerifyStatusEnum.UNKNOW.code, "", "");
        } else {
            MemberRealNameSummarySoaModel realNameModel = verifyInfoList.stream()
                    .max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate))
                    .orElseThrow(() -> ServiceException.fail("未查询到相关实名记录！"));

            VerifyStatusEnum statusEnum = VerifyStatusEnum.formatVerifyStatus(realNameModel.getStatus());
            VerifyChannelEnum channelEnum = VerifyChannelEnum.checkEnum(realNameModel.getVerifyChannel());

            String verifyChannelDesc = (channelEnum == null) ? "外部认证" : channelEnum.desc;

            if (statusEnum != null) {
                memberRealInfo = initMemberRealInfo(
                        statusEnum.desc,
                        statusEnum.code,
                        realNameModel.getVerifyChannel(),
                        verifyChannelDesc
                );

                if (VerifyStatusEnum.PASS.code.equals(statusEnum.code)) {
                    taskExecutor.execute(new RealInfoThread(
                            ffpNo,
                            statusEnum.desc,
                            statusEnum.code,
                            realNameModel.getVerifyChannel(),
                            verifyChannelDesc
                    ));
                }

                if (VerifyStatusEnum.REJECT.code.equals(statusEnum.code)) {
                    memberRealInfo.setRejectReason(realNameModel.getComments());
                }

                memberRealInfo.setVerifyDesc(statusEnum.desc);
                memberRealInfo.setVerifyStatus(statusEnum.code);
            } else {
                memberRealInfo.setVerifyDesc("");
                memberRealInfo.setVerifyStatus("-2");
            }

        }
        return memberRealInfo;
    }


    private void updateMemberTotalScore(RequestData<MemberDetailReq> requestData, ChannelInfo channelInfo, MemberRealInfo memberRealInfo) {
        PtApiCRMRequest<MileageAccountQueryRequest> mileageReq = CRMReqUtil.buildCommReq(
                requestData.getOriginIp(),
                channelInfo.getChannelCode(),
                channelInfo.getChannelPwd(),
                requestData.getFfpId(),
                ""
        );

        MileageAccountQueryRequest mileageReqData = new MileageAccountQueryRequest();
        mileageReqData.setMemberCardNo(requestData.getFfpNo());
        mileageReqData.setRequestItems(new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName});
        mileageReq.setData(mileageReqData);

        MemberRemainScoreResp scoreResp = memberService.queryMemberRemainScore(mileageReq, false);
        int point = Integer.parseInt(scoreResp.getPoint());
        int freezePoint = Integer.parseInt(scoreResp.getFreezePoint());

        memberRealInfo.setTotalScore(scoreResp.getPoint());
        memberRealInfo.setAvailableScore(Math.max(point - freezePoint, 0) + "");
    }


    @Override
    public AvailCouponsResponse toQueryNormalCoupons(RequestData<QueryCouponReq> requestData) {
        QueryCouponReq couponReq = requestData.getData();
        AvailCouponsResponse availCouponsResponse = memberService.toCatchNormalCoupons(requestData);
        if (CollectionUtils.isEmpty(availCouponsResponse.getAvailCouponList())) {
            availCouponsResponse.setAvailCouponList(new ArrayList<>());
            return availCouponsResponse;
        }
        for (AvailCoupon availCoupon : availCouponsResponse.getAvailCouponList()) {
            //优惠券基本属性渲染
            String passengerType = availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getPassengerType();
            String flightType = availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getFlightType();
            Coupon coupon = CouponUtil.initCouponStyle(availCoupon.getCouponSource(), availCoupon.getCouponType(),
                    passengerType, availCoupon.getCouponPrice(), availCoupon.getCouponRebate(), availCoupon.getCouponState(), flightType);

            if (StringUtils.isNotBlank(availCoupon.getUsedStEndDt())) {
                String[] useDates = availCoupon.getUsedStEndDt().split("~");
                if (useDates.length == 2) {
                    availCoupon.setStartDate(useDates[0]);
                    availCoupon.setEndData(useDates[1]);
                }
            }
            availCoupon.setCouponName(coupon.getCouponName());
        }
        //“已使用”优惠券排序逻辑 优惠券排列顺序以优惠券使用时间由近及远排列
        if ("N".equals(couponReq.getCouponState())) {
            availCouponsResponse.getAvailCouponList().sort((e1, e2) -> {
                if (StringUtils.isNotBlank(e1.getUseDate()) && StringUtils.isNotBlank(e2.getUseDate())) {
                    return e2.getUseDate().compareTo(e1.getUseDate());
                } else {
                    return StringUtils.isNotBlank(e2.getUseDate()) ? 1 : -1;
                }
            });
        }

        /* "已过期" 优惠券排序逻辑 优惠券排列顺序优先级依次为：
        0. 若优惠券同级信息相同，则顺延至下一级）
        1. 优惠券失效时间由近及远
        2. 优惠券生效时间由近及远
        3. 优惠券金额由多到少
        4. 优惠券领取时间由远及近*/
        if ("E".equals(couponReq.getCouponState())) {
            List<AvailCoupon> availCoupons = availCouponsResponse.getAvailCouponList().stream()
                    .sorted(Comparator.comparing(AvailCoupon::getEndData, Comparator.reverseOrder())
                            .thenComparing(AvailCoupon::getStartDate, Comparator.reverseOrder())
                            .thenComparing(AvailCoupon::getCouponPrice, Comparator.reverseOrder())
                            .thenComparing(AvailCoupon::getReveiceTime)).collect(Collectors.toList());
            availCouponsResponse.setAvailCouponList(availCoupons);
        }
        return availCouponsResponse;
    }

    @Override
    public List<AvailCoupon> toCatchMyRightCoupons(RequestData<MyCoupon> requestData) {
        PtCouponProductGetResponseDto ptCouponProductGetResponseDto = memberService.toCatchRightCoupons(requestData);
        List<CityInfoDTO> allCityList = basicService.toCatchAllCityList(HoAirIpUtil.getLocalIp(), requestData.getChannelNo(), "");
        List<AirPortInfoDTO> allAirPortList = basicService.toCatchAllAirPortList(HoAirIpUtil.getLocalIp(), requestData.getChannelNo(), "", "");
        List<AvailCoupon> availCouponList = RightCouponConvert.formatAvailCouponList(requestData.getData().getCouponState(), ptCouponProductGetResponseDto.getVouchers(), allCityList, allAirPortList, accountBffConfig);
        if (CollectionUtils.isNotEmpty(availCouponList)) {
            //按照有效期排序 升序排序
            if ("R".equals(requestData.getData().getCouponState())) {
                availCouponList.sort(Comparator.comparing(AvailCoupon::getEndData));
                // 将5天内过期的券设为即将过期
                Calendar expiringDateLimit = Calendar.getInstance();
                expiringDateLimit.add(Calendar.DATE, 5);
                availCouponList.stream().filter(coupon -> {
                    Date couponEndDate = DateUtil.toDate(coupon.getEndData(), DateUtil.YYYY_MM_DD_PATTERN);
                    if (null != couponEndDate) {
                        return couponEndDate.before(expiringDateLimit.getTime());
                    } else {
                        return false;
                    }
                }).forEach(coupon -> coupon.setExpiring(true));
            } else {//降序排序
                availCouponList.sort(Comparator.comparing(AvailCoupon::getEndData).reversed());
            }
        }
        return availCouponList;
    }

    @Override
    public MemberRemainScoreResp toCatchMemberScore(RequestData<BizDto> requestData) {
        MemberRemainScoreResp memberRemainScoreResp = new MemberRemainScoreResp();
        //查询积分余额
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRemainScoreRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
        MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
        String[] items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
        mileageAccountQueryRequest.setMemberCardNo(requestData.getFfpNo());
        mileageAccountQueryRequest.setRequestItems(items);
        ptApiCRMRemainScoreRequest.setData(mileageAccountQueryRequest);
        PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse = memberService.mileageAccountQuery(ptApiCRMRemainScoreRequest, false);
        //可用积分余额
        Integer availableMiles = ptCRMResponse.getData().getTotalBill().getAvailableMiles() == null ? 0 : ptCRMResponse.getData().getTotalBill().getAvailableMiles();
        Integer freezeMiles = ptCRMResponse.getData().getTotalBill().getFreezeMiles() == null ? 0 : ptCRMResponse.getData().getTotalBill().getFreezeMiles();
        memberRemainScoreResp.setFreezePoint(String.valueOf(freezeMiles));
        memberRemainScoreResp.setUsablePoint(String.valueOf(availableMiles));
        memberRemainScoreResp.setPoint(String.valueOf(availableMiles + freezeMiles));

        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMMemberDetailRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
        items = new String[]{MemberDetailRequestItemsEnum.STATEINFO.eName};
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMMemberDetailRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMMemberResponse = memberService.memberDetail(ptApiCRMMemberDetailRequest, false);
        if (ptCRMMemberResponse.getCode() == 0) {
            //小额免密开通状态
            memberRemainScoreResp.setSmallExemptPwdStatus("Y".equals(ptCRMMemberResponse.getData().getStateInfo().getIsSmallExemptPwd()));
            memberRemainScoreResp.setFreeScoreLimit(accountBffConfig.getScoreFreeLimit());
        }
        return memberRemainScoreResp;
    }

    @Override
    public List<MileageRecordQueryResponse> toCatchScoreDetail(RequestData<MileageRecordQueryRequest> requestData) {
        PtMemberMilesResponse catchScoreChangeDetail = memberService.toCatchScoreChangeDetail(requestData.getData().getBeginDate(), requestData.getData().getEndDate(), requestData.getFfpId(), requestData.getChannelNo());
        if (null != catchScoreChangeDetail && CollectionUtils.isNotEmpty(catchScoreChangeDetail.getTransactionDetailDtoList())) {
            List<MileageRecordQueryResponse> responseList = new ArrayList<>();
            catchScoreChangeDetail.getTransactionDetailDtoList().forEach(milesDetail -> {
                MileageRecordQueryResponse mile = toHandleMile(milesDetail);
                if (null != mile) {
                    responseList.add(mile);
                }
            });
            return responseList;
        }
        return null;
    }

    @Override
    public List<SegmentRecordQueryResponse> toQuerySegmentDetail(RequestData<SegmentRecordQueryRequest> requestData) {
        PtCRMResponse<MileageDetailSegmentQueryResp> catchSegmentDetail = memberService.toCatchSegmentDetail(requestData.getData().getStartDate(), requestData.getData().getEndDate(), requestData.getFfpId(), requestData.getChannelNo());
        if (null == catchSegmentDetail || null == catchSegmentDetail.getData() || CollectionUtils.isEmpty(catchSegmentDetail.getData().getSegments())) {
            throw ServiceException.fail("累计航段明细查询为空");
        }
        ArrayList<SegmentRecordQueryResponse> segmentRecordQueryResponse = new ArrayList<>();
        List<AirPortInfoDTO> allAirPortList = basicService.toCatchAllAirPortList(HoAirIpUtil.getLocalIp(), requestData.getChannelNo(), "", "");
        catchSegmentDetail.getData().getSegments().forEach(e -> {
            SegmentRecordQueryResponse segmentRecord = new SegmentRecordQueryResponse();
            if (0 != e.getNumber()) {
                segmentRecord.setSegmentVariable("+" + e.getNumber());
            }
            if (null != e.getSegmentInfo()) {
                if (StringUtils.isNotEmpty(e.getSegmentInfo().getFlightNumber()) && StringUtils.isNotEmpty(e.getSegmentInfo().getOperateAirlineCode())) {
                    segmentRecord.setFlightNo(e.getSegmentInfo().getOperateAirlineCode() + e.getSegmentInfo().getFlightNumber());
                } else {
                    segmentRecord.setFlightNo("--");
                }
                if (StringUtils.isNotEmpty(e.getSegmentInfo().getFlightDate())) {
                    segmentRecord.setFlightDate(DateUtil.getDateString(DateUtil.toDate(e.getSegmentInfo().getFlightDate(), DateUtil.YYYY_MM_DD_PATTERN)));
                } else {
                    segmentRecord.setFlightDate("--");
                }
                String orgCityName = "";
                String desCityName = "";
                if (CollectionUtils.isNotEmpty(allAirPortList) && StringUtils.isNotEmpty(e.getSegmentInfo().getOrigination())) {
                    Optional<AirPortInfoDTO> orgCity = allAirPortList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getAirportCode()) && ele.getAirportCode().equals(e.getSegmentInfo().getOrigination())).findFirst();
                    if (orgCity.isPresent()) {
                        orgCityName = orgCity.get().getCityName();
                    }
                    Optional<AirPortInfoDTO> desCity = allAirPortList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getAirportCode()) && ele.getAirportCode().equals(e.getSegmentInfo().getDestination())).findFirst();
                    if (desCity.isPresent()) {
                        desCityName = desCity.get().getCityName();
                    }
                    if (!StringUtils.isAllEmpty(orgCityName, desCityName)) {
                        segmentRecord.setSegment(orgCityName + "-" + desCityName);
                    } else {
                        segmentRecord.setSegment("--");
                    }
                }
            }
            segmentRecordQueryResponse.add(segmentRecord);
        });
        return segmentRecordQueryResponse;
    }

    /**
     * @param milesDetail
     * @return com.juneyaoair.oneorder.crm.dto.response.MileageRecordQueryResponse
     * <AUTHOR>
     * @Description 积分返回体格式化
     * @Date 20:56 2023/9/10
     **/
    private MileageRecordQueryResponse toHandleMile(PtMemberMilesResponse.MemberMilesDetail milesDetail) {
        if (null == milesDetail) {
            return null;
        }
        return MileageRecordQueryResponse.builder()
                .miles(milesDetail.getMiles())
                .source(milesDetail.getTransactionType())
                .recordDate(StringUtils.isNotEmpty(milesDetail.getEntryDate()) ? DateUtil.getDateString(DateUtil.toDate(milesDetail.getEntryDate(), DateUtil.YYYY_MM_DD_PATTERN)) : "")
                .expireDate(StringUtils.isNotEmpty(milesDetail.getExpireDate()) ? DateUtil.getDateString(DateUtil.toDate(milesDetail.getExpireDate(), DateUtil.YYYY_MM_DD_PATTERN)) : "")
                .remark(milesDetail.getDescription()).build();
    }

    @Override
    public boolean toCompensateScore(RequestData<MemberPointPatchApplyRequest> requestData) {
        MemberPointPatchApplyRequest memberPointPatchApplyRequest = requestData.getData();
        Date flightDate;
        try {
            flightDate = new SimpleDateFormat("yyyy-MM-dd").parse(memberPointPatchApplyRequest.getFlightDate());
        } catch (ParseException e) {
            throw ServiceException.fail("日期解析失败");
        }
        Date now = new Date();
        if (DateUtil.compareDatesByDay(now, flightDate) < 5) {
            throw ServiceException.fail("请于航班起飞4日后再进行补登操作");
        }
        if (DateUtil.compareDatesByMonth(now, flightDate) > 6) {
            throw ServiceException.fail("补登积分时限为乘机之日起6个月");
        }
        if (DateUtil.compareDatesByMonth(now, flightDate) == 6) {
            Calendar calendar = Calendar.getInstance();
            Calendar flightCalendar = Calendar.getInstance();
            flightCalendar.setTime(flightDate);
            if (calendar.get(Calendar.DAY_OF_MONTH) > flightCalendar.get(Calendar.DAY_OF_MONTH)) {
                throw ServiceException.fail("补登积分时限为乘机之日起6个月");
            }
        }
        //0. 姓名拼接

        List<AirPortInfoDTO> allAirPortList = basicService.toCatchAllAirPortList(HoAirIpUtil.getLocalIp(), requestData.getChannelNo(), "", "");
        AirPortInfoDTO oriAirPortInfoDTO = allAirPortList.stream().filter(el -> StringUtils.isNotEmpty(el.getAirportCode()) && el.getAirportCode().equals(memberPointPatchApplyRequest.getOriginationCode())).findFirst().orElse(null);
        AirPortInfoDTO desAirPortInfoDTO = allAirPortList.stream().filter(el -> StringUtils.isNotEmpty(el.getAirportCode()) && el.getAirportCode().equals(memberPointPatchApplyRequest.getDestinationCode())).findFirst().orElse(null);
        String name;
        if (null != oriAirPortInfoDTO && null != desAirPortInfoDTO
                && StringUtils.isNotEmpty(oriAirPortInfoDTO.getIsInternational()) && StringUtils.isNotEmpty(desAirPortInfoDTO.getIsInternational())
                && (oriAirPortInfoDTO.getIsInternational().equals("I") || desAirPortInfoDTO.getIsInternational().equals("I"))) {
            name = memberPointPatchApplyRequest.getLastName() + "/" + memberPointPatchApplyRequest.getFirstName();
        } else {
            name = memberPointPatchApplyRequest.getLastName() + memberPointPatchApplyRequest.getFirstName();
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        CrmMemberBaseApiRequest<PtScoreCompensateReq> scoreCompensateRequest = CRMReqUtil.buildCommCrmMemberReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        FlightRecord flightRecord = FlightRecord.builder()
                .FlightDate(memberPointPatchApplyRequest.getFlightDate())
                .Origination(memberPointPatchApplyRequest.getOriginationCode())
                .Destination(memberPointPatchApplyRequest.getDestinationCode())
                .AirlineCode(memberPointPatchApplyRequest.getAirlineCode())
                .BookingClass(memberPointPatchApplyRequest.getBookingClassCode())
                .FlightNum(memberPointPatchApplyRequest.getFlightNum())
                .TicketNumber(memberPointPatchApplyRequest.getTicketNO())
                .PassengerName(name).build();
        PtScoreCompensateReq compensate = PtScoreCompensateReq.builder()
                .MemberCardNo(requestData.getFfpNo())
                .FlightRecord(flightRecord).build();
        scoreCompensateRequest.setData(compensate);
        memberService.toCompensateScore(scoreCompensateRequest);
        return true;
    }

    @Override
    public List<MileageRetroRecord> toCatchRetroRecord(RequestData<MileageRetroRequest> requestData) {
        String startDate;
        String endDate;
        if (StringUtils.isBlank(requestData.getData().getStartDate())) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -1);
            startDate = new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(calendar.getTime());
        } else {
            startDate = "";
        }
        if (StringUtils.isBlank(requestData.getData().getEndDate())) {
            endDate = new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(new Date());
        } else {
            endDate = "";
        }
        MileageRetroRecordQueryResDto catchRetroRecords = memberService.toCatchRetroRecords(requestData.getFfpId(), requestData.getChannelNo());
        List<AirPortInfoDTO> allAirPortList = basicService.toCatchAllAirPortList(HoAirIpUtil.getLocalIp(), requestData.getChannelNo(), "", "");
        if (null != catchRetroRecords && CollectionUtils.isNotEmpty(catchRetroRecords.getRetroRecords())) {
            List<MileageRetroRecordDto> mileageRetroRecordDtos = catchRetroRecords.getRetroRecords().stream().filter(e -> StringUtils.isNotEmpty(e.getOperateDate()) && e.getOperateDate().compareTo(startDate) >= 0 && e.getOperateDate().compareTo(endDate) >= 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mileageRetroRecordDtos)) {
                ArrayList<MileageRetroRecord> mileageRetroRecords = new ArrayList<>();
                mileageRetroRecordDtos.forEach(e -> {
                    MileageRetroRecord mileageRetroRecord = new MileageRetroRecord();
                    mileageRetroRecord.setApplyDate(e.getOperateDate());
                    mileageRetroRecord.setFlightNo(e.getOperateAirlineCode() + e.getOperateFlightNumber());
                    mileageRetroRecord.setFlightDate(e.getFlightDate());
                    String orgCityName = "";
                    String desCityName = "";
                    if (CollectionUtils.isNotEmpty(allAirPortList)) {
                        Optional<AirPortInfoDTO> orgCity = allAirPortList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getAirportCode()) && ele.getAirportCode().equals(e.getOrigination())).findFirst();
                        if (orgCity.isPresent()) {
                            orgCityName = orgCity.get().getCityName();
                        }
                        Optional<AirPortInfoDTO> desCity = allAirPortList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getAirportCode()) && ele.getAirportCode().equals(e.getDestination())).findFirst();
                        if (desCity.isPresent()) {
                            desCityName = desCity.get().getCityName();
                        }
                    }
                    mileageRetroRecord.setSegment(orgCityName + "-" + desCityName);
                    mileageRetroRecord.setRetroStatus(toConvertApplyStatus(e.getVerifyState()));
                    mileageRetroRecords.add(mileageRetroRecord);
                });
                return mileageRetroRecords;
            }
        }
        return null;
    }

    /**
     * @param verifyState
     * @return java.lang.String
     * <AUTHOR>
     * @Description 转换补登状态
     * @Date 9:55 2023/9/12
     **/
    private String toConvertApplyStatus(String verifyState) {
        if (StringUtils.isEmpty(verifyState)) {
            return verifyState;
        }
        switch (verifyState) {
            case "AP":
                return "待累计";
            case "PD":
                return "确认中";
            case "ER":
                return "累计异常";
            case "AC":
                return "已累计";
            case "PR":
                return "拒绝";
            case "MD":
                return "强制转入";
            default:
                return "-";
        }

    }

    @Override
    public ExpiringMilesQueryResonse toCatchExpiringScore(RequestData<ExpiringMilesQueryRequest> expiringMilesQueryRequest) {
        ExpiringMilesQueryResonse resonse = new ExpiringMilesQueryResonse();
        ExpiringMilesQueryRequest expiringMilesQueryRequestData = expiringMilesQueryRequest.getData();
        resonse.setIntegralSwitch("Y");
        int totalExpiringMiles = 0;
        ChannelInfo channelInfo = commonService.findChannelInfo(expiringMilesQueryRequest.getChannelNo());
        PtCrmMileageRequest<MileageEffectiveReqDto> ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(expiringMilesQueryRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MileageEffectiveReqDto mileageExpireActivityQueryReqDto = new MileageEffectiveReqDto();
        mileageExpireActivityQueryReqDto.setId(Integer.parseInt(expiringMilesQueryRequest.getFfpId()));
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 1);
        mileageExpireActivityQueryReqDto.setStartDate(new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(new Date()));
        mileageExpireActivityQueryReqDto.setEndDate(new SimpleDateFormat(DateUtil.YYYY_MM_DD_PATTERN).format(calendar.getTime()));
        if (null == expiringMilesQueryRequestData.getPageNo() || null == expiringMilesQueryRequestData.getPageSize() || 0 == expiringMilesQueryRequestData.getPageNo() || 0 == expiringMilesQueryRequestData.getPageSize()) {
            expiringMilesQueryRequestData.setPageNo(1);
            expiringMilesQueryRequestData.setPageSize(500);
        }
        mileageExpireActivityQueryReqDto.setPageNum(expiringMilesQueryRequestData.getPageNo());
        mileageExpireActivityQueryReqDto.setPageSize(expiringMilesQueryRequestData.getPageSize());
        ptCrmMileageRequest.setData(mileageExpireActivityQueryReqDto);
        MileageEffectiveResDto expiringMilesResponseV2 = memberService.toCatchExpiringScoreV2(ptCrmMileageRequest);
        if (null != expiringMilesResponseV2 && CollectionUtils.isNotEmpty(expiringMilesResponseV2.getEffectiveList())) {
            List<ExpiringRecord> expiringRecords = new ArrayList<>();
            totalExpiringMiles = expiringMilesResponseV2.getEffectiveList().stream().mapToInt(MileageEffective::getMiles).sum();
            Map<String, List<MileageEffective>> expiringMilesDetail = expiringMilesResponseV2.getEffectiveList().stream()
                    .collect(Collectors.groupingBy(MileageEffective::getExpireDate));
            for (Map.Entry<String, List<MileageEffective>> next : expiringMilesDetail.entrySet()) {
                String expireDateStr = next.getKey();
                List<MileageEffective> expiringMilesDetails = next.getValue();
                int singleDateMiles = expiringMilesDetails.stream().mapToInt(MileageEffective::getMiles).sum();
                Date expireDate = DateUtil.toDate(expireDateStr, DateUtil.YYYY_MM_DD_PATTERN);
                ExpiringRecord expiringRecord = new ExpiringRecord();
                expiringRecord.setMiles(String.valueOf(singleDateMiles));
                expiringRecord.setExpireDate(DateUtil.convertDate2Str(expireDate, DateUtil.YYYY_MM_DD_PATTERN));
                expiringRecords.add(expiringRecord);
            }
            expiringRecords.sort(Comparator.comparing(ExpiringRecord::getExpireDate));
            resonse.setExpiringRecords(expiringRecords);
        }
        resonse.setTotalExpiringMiles(totalExpiringMiles);
        return resonse;
    }

    @Override
    public QueryCommonPersonResp toCatchPassengerList(BizDto bizDto, String language, String ffpId, String ffpCardNo, CommonPersonQuery commonPersonQuery) {
        QueryCommonPersonResp response = new QueryCommonPersonResp();
        //请求渠道转换
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        // 查询常用乘机人
        QueryGeneralContactRequest queryGeneralContactRequest = this.genQueryGeneralContactRequest(ffpId, channelInfo, commonPersonQuery);
        QueryGeneralContactResponse catchGeneralContactsCRM = memberService.toCatchGeneralContacts(queryGeneralContactRequest);
        QueryGeneralContactResponse queryGeneralContactResponse = new QueryGeneralContactResponse();
        List<GeneralContactInfo> generalContactInfos = new ArrayList<>();
        if (null != catchGeneralContactsCRM) {
            generalContactInfos.addAll(catchGeneralContactsCRM.getGeneralContactList());
        }
       queryGeneralContactResponse.setGeneralContactList(generalContactInfos);

        List<QueryCommonPersonInfo> personInfoList = this.responseConvertToQueryCommonPersonInfo(queryGeneralContactResponse, bizDto);
        BeanUtils.copyProperties(queryGeneralContactResponse, response);
        // 标记是否本人
        this.markOwnPass(personInfoList, channelInfo, ffpId, ffpCardNo);
        this.handleCertBC(personInfoList);
        //处理证件的过期提示信息
        toCompletePassengerCertInfo(language, personInfoList);
        response.setCommonPersonInfoList(personInfoList);
        //关键信息脱敏和国际化
        toEncryptPassengerList(language, response.getCommonPersonInfoList());
        return response;
    }

    /**
     * @param language
     * @param commonPersonInfoList
     * @return void
     * <AUTHOR>
     * @Description 加密身份证和手机号
     * @Date 21:00 2023/9/19
     **/

    private void toEncryptPassengerList(String language, List<QueryCommonPersonInfo> commonPersonInfoList) {
        if (CollectionUtils.isEmpty(commonPersonInfoList)) {
            return;
        }
        Map<String, Map<String, String>> countryMap = i18nDictService.fetchDictData(I18nDictionaryTypeEnum.COUNTRY);
        commonPersonInfoList.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getHandphoneNo())) {
                e.setPrivacyHandPhoneNo(SensitiveInfoHider.hidePhone(e.getHandphoneNo()));
                e.setHandphoneNo(encrypt(e.getHandphoneNo()));
            }
            if (CollectionUtils.isNotEmpty(e.getContactCertList())) {
                e.getContactCertList().forEach(el -> {
                    if (StringUtils.isNotEmpty(el.getCertNo())) {
                        el.setPrivacyCertNo(SensitiveInfoHider.hidePartCertNo(el.getCertNo()));
                        el.setCertNo(encrypt(el.getCertNo()));
                    }
                    // 发证国名称
                    String contactBelongCountry = el.getBelongCountry();
                    if (StringUtils.isNotBlank(contactBelongCountry)) {
                        Map<String, String> countryNameMap = countryMap.get(contactBelongCountry);
                        String countryName = countryNameMap.get(language);
                        if (StringUtils.isNotBlank(countryName)) {
                            el.setBelongCountryName(countryName);
                        }
                    }
                });
            }
            // 发证国名称
            String belongCountry = e.getBelongCountry();
            if (StringUtils.isNotBlank(belongCountry)) {
                Map<String, String> countryNameMap = countryMap.get(belongCountry);
                String countryName = countryNameMap.get(language);
                if (StringUtils.isNotBlank(countryName)) {
                    e.setBelongCountryName(countryName);
                }
            }
            // 国籍名称
            String nationality = e.getNationality();
            if (StringUtils.isNotBlank(nationality)) {
                Map<String, String> countryNameMap = countryMap.get(nationality);
                String countryName = countryNameMap.get(language);
                if (StringUtils.isNotBlank(countryName)) {
                    e.setNationalityName(countryName);
                }
            }
        });
    }

    /**
     * @param eLastName
     * @return java.lang.String
     * <AUTHOR>
     * @Description 对英文姓进行脱敏
     * @Date 15:45 2023/8/25
     **/
    private String desensitizeELastName(String eLastName) {
        if (StringUtils.isEmpty(eLastName)) {
            return eLastName;
        }
        StringBuilder desensitizedELastName = new StringBuilder();
        for (int i = 0; i < eLastName.length(); i++) {
            desensitizedELastName.append("*");
        }
        return desensitizedELastName.toString();
    }


    /**
     * 对中文姓进行脱敏
     *
     * @param cLastName
     * @return
     */
    private String desensitizeCLastName(String cLastName) {
        if (StringUtils.isBlank(cLastName) || cLastName.length() == 1) {
            return cLastName;
        }
        String remainCFirstName = "";
        for (int i = 0; i < cLastName.length() - 1; i++) {
            remainCFirstName = remainCFirstName.concat("*");
        }
        return cLastName.charAt(0) + remainCFirstName;
    }

    /**
     * 对中文名进行脱敏
     *
     * @param cFirstName
     * @return
     */
    private String desensitizeCFirstName(String cFirstName) {
        if (StringUtils.isBlank(cFirstName)) {
            return cFirstName;
        }
        int cLastNameLength = cFirstName.length();
        String remainCLastName = "";
        if (cLastNameLength <= 2) {
            for (int i = 0; i < cLastNameLength; i++) {
                remainCLastName = remainCLastName.concat("*");
            }
            return remainCLastName;
        } else {
            for (int i = 0; i < cLastNameLength - 2; i++) {
                remainCLastName = remainCLastName.concat("*");
            }
            return remainCLastName + cFirstName.substring(cLastNameLength - 1);

        }
    }

    /**
     * 对姓名进行脱敏
     *
     * @param checkInName
     * @return
     */
    private String desensitizationName(String checkInName) {
        if (StringUtils.isBlank(checkInName)) {
            return checkInName;
        }
        int length = checkInName.length();
        if (length <= 3) {
            return "*" + checkInName.substring(3 == length ? length - 2 : length - 1);
        }
        if (length <= 6) {
            return "**" + checkInName.substring(length - 2);
        }
        return checkInName.charAt(0) + "***" + checkInName.substring(length - 2);
    }


    /**
     * 对身份证进行脱敏
     *
     * @param idCard
     * @return
     */
    private String desensitizationIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        int length = idCard.length();
        if (length <= 8) {
            return idCard;
        }
        return idCard.substring(0, 6) + "**********" + idCard.substring(length - 2);
    }

    /**
     * 对除身份证以外的证件进行脱敏
     *
     * @param otherCard
     * @return
     */
    private String desensitizationOtherCard(String otherCard) {
        if (StringUtils.isBlank(otherCard)) {
            return otherCard;
        }
        int length = otherCard.length();
        if (length <= 2) {
            return otherCard;
        }
        StringBuilder cardNo = new StringBuilder();
        for (int i = 0; i < length - 2; i++) {
            cardNo.append("*");
        }
        return otherCard.charAt(0) + cardNo.toString() + otherCard.substring(length - 1);
    }

    @Override
    public List<QueryCommonPersonInfo> toAddCommonPerson(BizDto bizDto, RequestDataDto<ModifyPersonV2> modifyPersonRequest) {
        if (null != modifyPersonRequest.getData() && null != modifyPersonRequest.getData().getCommonPersonInfo() && CollectionUtils.isEmpty(modifyPersonRequest.getData().getCommonPersonInfo().getContactCertList())) {
            throw MultiLangServiceException.fail("请至少添加一个证件");
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        GeneralContactRequest generalContactRequest;
        //如果是国际网站渠道,手机号与证件号需要解密
        if (ChannelCodeEnum.G_B2C.name().equals(bizDto.getHeadChannelCode())
            || ChannelCodeEnum.G_MOBILE.name().equals(bizDto.getHeadChannelCode())) {
            generalContactRequest = createGeneralContactRequest(modifyPersonRequest.getFfpId(), modifyPersonRequest.getData(), channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
        } else {
            generalContactRequest = createGeneralContactRequest(modifyPersonRequest.getFfpId(), modifyPersonRequest.getData(), channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), false);
        }
        PtV2GeneralContactResponse ptResponse = orderService.toAddGeneralContact(generalContactRequest);
        List<QueryCommonPersonInfo> queryCommonPersonInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ptResponse.getGeneralContactList())) {
            for (GeneralContactInfo generalContactInfo : ptResponse.getGeneralContactList()) {
                QueryCommonPersonInfo queryCommonPersonInfo = new QueryCommonPersonInfo();
                queryCommonPersonInfo.setCommonContactId(generalContactInfo.getGeneralContactId());
                queryCommonPersonInfo.setPassengerName(generalContactInfo.getPassengerName());
                queryCommonPersonInfo.setPassEnNameS(generalContactInfo.getPassEnNameS());
                queryCommonPersonInfo.setPassEnNameF(generalContactInfo.getPassEnNameF());
                queryCommonPersonInfoList.add(queryCommonPersonInfo);
            }
        }
        return queryCommonPersonInfoList;
    }

    @Override
    public boolean toModifyCommonPerson(BizDto bizDto, RequestDataDto<ModifyPersonV2> modifyPersonRequest) {
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        GeneralContactRequest generalContactRequest;
        ModifyPersonV2 modifyPersonV2 = modifyPersonRequest.getData();
        if (modifyPersonV2.getIsRemove() != null && modifyPersonV2.getIsRemove()) {
            //创建删除请求
            generalContactRequest = createRemoveGeneralContactRequest(modifyPersonRequest.getFfpId(), modifyPersonV2, channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
        } else {
            //如果是国际网站渠道，信息部分修改
            if (ChannelCodeEnum.G_B2C.name().equals(bizDto.getHeadChannelCode())
                    || ChannelCodeEnum.G_MOBILE.name().equals(bizDto.getHeadChannelCode())) {
                List<GeneralContactInfo> generalContactInfoList = memberService.toCatchGeneralContactList(channelInfo, modifyPersonRequest.getFfpId(), modifyPersonRequest.getFfpNo());
                if (CollectionUtils.isEmpty(generalContactInfoList)) {
                    throw MultiLangServiceException.fail("无乘机人信息，请先添加乘机人");
                }
                GeneralContactInfo oldGeneralContactInfo = generalContactInfoList.stream().filter(generalContactInfo -> generalContactInfo.getGeneralContactId().equals(modifyPersonV2.getCommonPersonInfo().getCommonContactId())).findFirst().orElse(null);
                if (oldGeneralContactInfo == null) {
                    throw MultiLangServiceException.fail("未匹配到乘机人信息");
                }
                generalContactRequest = createGeneralContactRequest(modifyPersonRequest.getFfpId(), modifyPersonV2, oldGeneralContactInfo, channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
            } else {
                //原有渠道乘机人变更逻辑暂时不做调整
                generalContactRequest = createGeneralContactRequest(modifyPersonRequest.getFfpId(), modifyPersonV2, channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), true);
            }
        }
        generalContactRequest.setIsRemove(modifyPersonV2.getIsRemove());
        PtResponse ptResponse = orderService.toModifyGeneralContact(generalContactRequest);
        //此错误编码时，前端需根据提示选择操作方式
        if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail("常用乘机人信息重复");
        }

        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail(ptResponse.getErrorInfo());
        }
        return true;
    }

    private GeneralContactRequest createRemoveGeneralContactRequest(String ffpId, ModifyPersonV2 modifyPersonV2, String orderChannelCode, String userNo) {
        QueryCommonPersonInfo commonPersonInfo = modifyPersonV2.getCommonPersonInfo();
        if (null == commonPersonInfo) {
            throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "乘客信息不能为空");
        }
        GeneralContactInfo newGeneralContactInfo = new GeneralContactInfo();
        newGeneralContactInfo.setChannelCustomerType("CRM");
        newGeneralContactInfo.setChannelCustomerNo(ffpId);
        newGeneralContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
        List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
        generalContactInfoList.add(newGeneralContactInfo);
        GeneralContactRequest generalContactRequest = new GeneralContactRequest("V1.0", orderChannelCode, userNo);
        generalContactRequest.setIsRemove(true);
        generalContactRequest.setGeneralContactList(generalContactInfoList);
        return generalContactRequest;
    }

    @Override
    public QueryCommonContactsResp toCatchCommonContacts(RequestData<QueryCommonContactsReq> modifyPersonRequest) {
        ChannelInfo channelInfo = commonService.findChannelInfo(modifyPersonRequest.getChannelNo());
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMMemberDetailRequest = CRMReqUtil.buildCommReq(modifyPersonRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), modifyPersonRequest.getFfpId(), "");
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(modifyPersonRequest.getFfpNo());
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMMemberDetailRequest.setData(ptMemberDetailRequest);
        //获取会员详情
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMMemberDetailRequest, false);
        PtMemberDetail memberDetail = ptCRMResponse.getData();
        //保存本人手机号码
        String mobile = null;
        if (ptCRMResponse.getCode() == 0) {
            List<MemberContactSoaModel> contactInfoList = memberDetail.getContactInfo();
            if (CollectionUtils.isNotEmpty(contactInfoList)) {
                //获取手机号码
                MemberContactSoaModel memberContactMobile = CRMReqUtil.toMemberContactSoaModel(contactInfoList, ContactTypeEnum.MOBILE.getCode());
                if (memberContactMobile != null) {
                    mobile = memberContactMobile.getContactNumber();
                }
            }
        }

        //创建查询常用联系人请求对象，调用查询常用联系人接口
        List<CommonContactsInfo> commonContactsInfoList = memberService.toCatchCommonContacts(modifyPersonRequest.getFfpNo(), modifyPersonRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        if (!commonContactsInfoList.isEmpty()) {
            isOwnPass(commonContactsInfoList, mobile);
        }

        if (CollectionUtils.isNotEmpty(commonContactsInfoList)) {
            commonContactsInfoList.sort(Comparator.comparing(CommonContactsInfo::getPriority));
        }
        QueryCommonContactsResp queryCommonContactsResp = new QueryCommonContactsResp();
        queryCommonContactsResp.setApplyRecords(commonContactsInfoList);
        queryCommonContactsResp.setTotal(CollectionUtils.isNotEmpty(commonContactsInfoList) ? commonContactsInfoList.size() : 0);
        if (CollectionUtils.isNotEmpty(queryCommonContactsResp.getApplyRecords())) {
            queryCommonContactsResp.getApplyRecords().forEach(el -> {
                if (StringUtils.isNotEmpty(el.getMobile())) {
                    el.setMobile(encrypt(el.getMobile()));
                }
                if (StringUtils.isNotEmpty(el.getEmail())) {
                    el.setEmail(encrypt(el.getEmail()));
                }
            });
        }
        return queryCommonContactsResp;
    }

    @Override
    public Integer toAddCommonContacts(RequestData<ModifyCommonContactsReq> modifyPersonRequest) {
        //创建常用联系人添加请求对象，调用常用联系人添加接口
        ChannelInfo channelInfo = commonService.findChannelInfo(modifyPersonRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        //判断常用联系人不能大于20个
        ptCrmMileageRequest.setData(modifyPersonRequest.getFfpNo());
        List<CommonContactsInfo> commonContactsInfoList = memberService.toCatchCommonContacts(modifyPersonRequest.getFfpNo(), modifyPersonRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        if (commonContactsInfoList.size() >= 20) {
            throw ServiceException.fail("常用联系人数量已达上限，请删除后再尝试新增常用联系人");
        }
        //添加常用联系人
        MemberCommonContactsInfo memberCommonContactsInfo = new MemberCommonContactsInfo
                (null, modifyPersonRequest.getData().getCommonContactsInfo().getEmail(), modifyPersonRequest.getData().getCommonContactsInfo().getName(),
                        toCombine(modifyPersonRequest.getData().getCommonContactsInfo().getTelephoneCode(), modifyPersonRequest.getData().getCommonContactsInfo().getMobile()), modifyPersonRequest.getFfpNo());
        ptCrmMileageRequest.setData(memberCommonContactsInfo);
        return memberService.toAddCommonContacts(ptCrmMileageRequest).getData();
    }

    @Override
    public boolean toModifyCommonContacts(RequestData<ModifyCommonContactsReq> modifyPersonRequest) {
        ModifyCommonContactsReq modifyCommonContactsReq = modifyPersonRequest.getData();
        if (modifyCommonContactsReq.getCommonContactsInfo().getRecordId() == null) {
            throw ServiceException.fail("联系人信息ID不能为空！");
        }
        //创建常用联系人修改请求对象，调用常用联系人修改接口
        ChannelInfo channelInfo = commonService.findChannelInfo(modifyPersonRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        MemberCommonContactsInfo memberCommonContactsInfo = new MemberCommonContactsInfo(modifyCommonContactsReq.getCommonContactsInfo().getRecordId(),
                decrypt(modifyCommonContactsReq.getCommonContactsInfo().getEmail()), modifyCommonContactsReq.getCommonContactsInfo().getName(),
                toCombine(modifyCommonContactsReq.getCommonContactsInfo().getTelephoneCode(), decrypt(modifyCommonContactsReq.getCommonContactsInfo().getMobile())), null);
        ptCrmMileageRequest.setData(memberCommonContactsInfo);

        return memberService.toModifyCommonContacts(ptCrmMileageRequest).getData();
    }

    @Override
    public boolean toDeleteCommonContacts(RequestData<ModifyCommonContactsReq> modifyPersonRequest) {
        ModifyCommonContactsReq modifyCommonContactsReq = modifyPersonRequest.getData();
        if (modifyCommonContactsReq.getCommonContactsInfo().getRecordId() == null) {
            throw ServiceException.fail("联系人信息ID不能为空！");
        }
        /*if (modifyCommonContactsReq.getCommonContactsInfo().getIsOwn() == null) {
            throw ServiceException.fail("联系人信息本人标识不能为空！");
        }*/
        /*if (Boolean.TRUE.equals(modifyCommonContactsReq.getCommonContactsInfo().getIsOwn())) {
            throw ServiceException.fail("默认联系人不可删除！");
        }*/
        ChannelInfo channelInfo = commonService.findChannelInfo(modifyPersonRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        ptCrmMileageRequest.setData(modifyCommonContactsReq.getCommonContactsInfo().getRecordId());
        return memberService.toDeleteCommonContacts(ptCrmMileageRequest).getData();
    }

    @Override
    public MemberQueryBeneficiaryResponse toCatchBeneficiaryList(RequestData<MemberQueryBeneficiaryRequest> beneficiaryRequest) {
        MemberQueryBeneficiaryResponse memberQueryBeneficiaryResponse = new MemberQueryBeneficiaryResponse();
        List<String> status = Lists.newArrayList();
        if (CollectionUtils.isEmpty(beneficiaryRequest.getData().getState())) {
            //如果传空则默认查询全部已生效和未生效的记录 注意：此时 已删除和草稿状态的记录是无法查出的
            status.add(ACTIVE);
        } else {
            status.addAll(beneficiaryRequest.getData().getState());
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(beneficiaryRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(beneficiaryRequest.getFfpId()), status, null));
        List<MemberBeneficiaryDTO> beneficiaryDTOList = memberService.listBeneficiaryInfoRecord(ptCrmMileageRequest, beneficiaryRequest.getOriginIp());

        if (CollectionUtils.isEmpty(beneficiaryDTOList)) {
            return memberQueryBeneficiaryResponse;
        }
        //创建查询受益人请求对象，调用查询受益人接口
        List<MemberBeneficiaryInfo> beneficiaryList = new ArrayList<>();
        List<TCountryDTO> tCountryDTOList = basicService.queryCountries(HoAirIpUtil.getLocalIp(), beneficiaryRequest.getChannelNo(), new TCountryReqDTO());
        beneficiaryDTOList.forEach(beneficiaryDTO -> {
            MemberBeneficiaryInfo memberBeneficiaryInfo = new MemberBeneficiaryInfo();
            BeanUtils.copyProperties(beneficiaryDTO, memberBeneficiaryInfo);
            memberBeneficiaryInfo.setCertificate(convertListInfo(beneficiaryDTO.getCertificate(), MemberBeneficiaryCertificateInfo.class));
            //设置还有多少天生效 天 0是已生效 null是不显示
            if (StringUtils.isNotEmpty(beneficiaryDTO.getStatus()) && StringUtils.isNotEmpty(beneficiaryDTO.getEffectDate())) {
                if (ACTIVE.equals(beneficiaryDTO.getStatus())) {
                    int dateDiff = DateUtil.dateDiff(DateUtil.toDate(beneficiaryDTO.getEffectDate(), DateUtil.YYYY_MM_DD_PATTERN),
                            DateUtil.toDate(DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN));
                    memberBeneficiaryInfo.setDay(Math.max(dateDiff, 0));
                }
            }

            //国籍
            TCountryDTO countryDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(memberBeneficiaryInfo.getNationality())).findFirst().orElse(null);
            if (countryDTO != null) {
                memberBeneficiaryInfo.setNationalityName(StringUtils.isEmpty(countryDTO.getCountryName()) ? countryDTO.getCountryCode() : countryDTO.getCountryName());
            }
            memberBeneficiaryInfo.getCertificate().forEach(c -> {
                // 前端及统一订单传入的军官证类型为MP，接口返回MIL，统一以MP为编码
                // 前端及统一订单传入的台湾居民居住证类型为TWP，接口返回TIC，统一以TWP为编码
                if ("MIL".equals(c.getCtype())) {
                    c.setCtype("MP");
                } else if ("TIC".equals(c.getCtype())) {
                    c.setCtype("TWP");
                }
                TCountryDTO issuingCountryDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(c.getSigningCountry())).findFirst().orElse(null);
                if (issuingCountryDTO != null) {
                    c.setSigningCountryName(StringUtils.isEmpty(issuingCountryDTO.getCountryName()) ? issuingCountryDTO.getCountryCode() : issuingCountryDTO.getCountryName());
                }
                //即将过期
                Date now = new Date();
                Date certExpireDate = DateUtil.toDate(c.getExpireDate(), DateUtil.YYYY_MM_DD_PATTERN);
                if (null != certExpireDate) {
                    if (now.after(certExpireDate)) {
                        c.setExpireDesc("已经过期");
                    } else {
                        Calendar expiringDateLimit = Calendar.getInstance();
                        expiringDateLimit.setTime(certExpireDate);
                        expiringDateLimit.add(Calendar.MONTH, -6);
                        // 查询常用乘机人页面，提示有效期在一年内即将到期的护照
                        c.setExpireDesc(now.after(expiringDateLimit.getTime()) ? "即将过期" : "");
                    }
                } else {
                    c.setExpireDesc("");
                }
                //判断证件是否已过期
                if (StringUtils.isEmpty(c.getExpireDate())) {
                    c.setExpired(false);
                } else {
                    c.setExpired(DateUtil.toDate(c.getExpireDate()).before(new Date()));
                }
            });
            beneficiaryList.add(memberBeneficiaryInfo);
        });
        //对关键数据脱敏
        /*toDesensitizeKeyInfo(beneficiaryList);*/
        beneficiaryList.sort(Comparator.comparing(MemberBeneficiaryInfo::getRecordId, Comparator.reverseOrder()));
        if (CollectionUtils.isNotEmpty(beneficiaryList)) {
            beneficiaryList.forEach(el -> {
                if (CollectionUtils.isNotEmpty(el.getCertificate())) {
                    el.getCertificate().forEach(ele -> {
                        if (StringUtils.isNotEmpty(ele.getCnumber())) {
                            ele.setCnumber(encrypt(ele.getCnumber()));
                        }
                    });
                }
            });
        }
        memberQueryBeneficiaryResponse.setRecords(beneficiaryList);
        return memberQueryBeneficiaryResponse;

    }

    /**
     * @param beneficiaryList
     * @return void
     * <AUTHOR>
     * @Description 对关键数据脱敏
     * @Date 14:31 2023/8/22
     **/

    private void toDesensitizeKeyInfo(List<MemberBeneficiaryInfo> beneficiaryList) {
        if (CollectionUtils.isEmpty(beneficiaryList)) {
            return;
        }
        beneficiaryList.forEach(
                beneficiaryInfo -> {
                    //中文姓名脱敏
                    beneficiaryInfo.setCFirstName(desensitizeCFirstName(beneficiaryInfo.getCFirstName()));
                    beneficiaryInfo.setCLastName(desensitizeCLastName(beneficiaryInfo.getCLastName()));
                    //英文姓名脱敏
                    if (StringUtils.isNotEmpty(beneficiaryInfo.getELastName())) {
                        StringBuilder eLastName = new StringBuilder();
                        for (int i = 0; i < beneficiaryInfo.getELastName().length(); i++) {
                            eLastName.append("*");
                        }
                        beneficiaryInfo.setELastName(eLastName.toString());
                    }
                    //证件脱敏
                    if (CollectionUtils.isNotEmpty(beneficiaryInfo.getCertificate())) {
                        beneficiaryInfo.getCertificate().forEach(contactInformation -> {
                            //身份证脱敏
                            if (StringUtils.isNotEmpty(contactInformation.getCtype()) && StringUtils.isNotEmpty(contactInformation.getCnumber()) && CertificateTypeEnum.ID_CARD.getShowCode().equals(contactInformation.getCtype())) {
                                contactInformation.setCnumber(desensitizationIdCard(contactInformation.getCnumber()));
                            } else if (StringUtils.isNotEmpty(contactInformation.getCtype()) && StringUtils.isNotEmpty(contactInformation.getCnumber())) {
                                //其他证件脱敏
                                contactInformation.setCnumber(desensitizationOtherCard(contactInformation.getCnumber()));
                            }
                        });
                    }
                }
        );
    }

    /**
     * @param beneficiaryList
     * @return void
     * <AUTHOR>
     * @Description 对基本信息中的
     * @Date 15:36 2023/8/25
     **/

    private void toDesensitizeBasicInfo(List<MemberBeneficiaryInfo> beneficiaryList) {
        if (CollectionUtils.isEmpty(beneficiaryList)) {
            return;
        }
        beneficiaryList.forEach(
                beneficiaryInfo -> {
                    //中文姓名脱敏
                    beneficiaryInfo.setCFirstName(desensitizeCFirstName(beneficiaryInfo.getCFirstName()));
                    beneficiaryInfo.setCLastName(desensitizeCLastName(beneficiaryInfo.getCLastName()));
                    //英文姓名脱敏
                    if (StringUtils.isNotEmpty(beneficiaryInfo.getELastName())) {
                        StringBuilder eLastName = new StringBuilder();
                        for (int i = 0; i < beneficiaryInfo.getELastName().length(); i++) {
                            eLastName.append("*");
                        }
                        beneficiaryInfo.setELastName(eLastName.toString());
                    }
                    //证件脱敏
                    if (CollectionUtils.isNotEmpty(beneficiaryInfo.getCertificate())) {
                        beneficiaryInfo.getCertificate().forEach(contactInformation -> {
                            //身份证脱敏
                            if (StringUtils.isNotEmpty(contactInformation.getCtype()) && StringUtils.isNotEmpty(contactInformation.getCnumber()) && CertificateTypeEnum.ID_CARD.getShowCode().equals(contactInformation.getCtype())) {
                                contactInformation.setCnumber(desensitizationIdCard(contactInformation.getCnumber()));
                            } else if (StringUtils.isNotEmpty(contactInformation.getCtype()) && StringUtils.isNotEmpty(contactInformation.getCnumber())) {
                                //其他证件脱敏
                                contactInformation.setCnumber(desensitizationOtherCard(contactInformation.getCnumber()));
                            }
                        });
                    }
                }
        );
    }

    @Override
    public CumulativeCalculatorResponse toCalculateCumulative(RequestData<CumulativeCalculatorRequest> calculatorRequest) {
        CumulativeCalculatorRequest reqParam = calculatorRequest.getData();
        //从redis中读取计算规则
        String mileageListsStr = (String) redisUtils.get(MILEAGE_ADD_CALCULATOR_MILEAGE_LIST);
        if (StringUtils.isEmpty(mileageListsStr)) {
            //否则从配置文件中读取
            mileageListsStr = FileUtils.readJson("/mileageCalculator/mileageAddCalculator_mileageList.json");
            redisUtils.set(MILEAGE_ADD_CALCULATOR_MILEAGE_LIST, mileageListsStr, -1);
        }
        String classListsStr = (String) redisUtils.get(MILEAGE_ADD_CALCULATOR_CLASS_LIST);
        if (StringUtils.isEmpty(classListsStr)) {
            //否则从配置文件中读取
            classListsStr = FileUtils.readJson("/mileageCalculator/mileageAddCalculator_classList.json");
            redisUtils.set(MILEAGE_ADD_CALCULATOR_CLASS_LIST, classListsStr, -1);
        }
        String ratioListsStr = (String) redisUtils.get(MILEAGE_ADD_CALCULATOR_RATIO_LIST);
        if (StringUtils.isEmpty(ratioListsStr)) {
            //否则从配置文件中读取
            ratioListsStr = FileUtils.readJson("/mileageCalculator/mileageAddCalculator_ratioList.json");
            redisUtils.set(MILEAGE_ADD_CALCULATOR_RATIO_LIST, ratioListsStr, -1);
        }
        String minMileageListsStr = (String) redisUtils.get(MILEAGE_ADD_CALCULATOR_MIN_MILEAGE_LIST);
        if (StringUtils.isEmpty(minMileageListsStr)) {
            //否则从配置文件中读取
            minMileageListsStr = FileUtils.readJson("/mileageCalculator/mileageAddCalculator_minMileageList.json");
            redisUtils.set(MILEAGE_ADD_CALCULATOR_MIN_MILEAGE_LIST, minMileageListsStr, -1);
        }
        if (StringUtils.isAnyBlank(mileageListsStr, classListsStr, ratioListsStr, minMileageListsStr)) {
            throw new ServiceException(AcBffErrorCode.NO_DATA, "未获取到计算规则");
        }
        Gson gson = new GsonBuilder().create();
        // 0.出发地  1.目的地   2.TPM值  3.GCM值 4.是否国际航班（0否，1是）
        List<List<String>> mileageLists = gson.fromJson(mileageListsStr, new TypeToken<List<List<String>>>() {
        }.getType());
        Stream<List<String>> mileageStream = mileageLists.stream().filter(list ->
                (list.get(0).equals(reqParam.getFromPlace()) && list.get(1).equals(reqParam.getToPlace())) ||
                        (list.get(1).equals(reqParam.getFromPlace()) && list.get(0).equals(reqParam.getToPlace())));
        List<List<String>> matchMileageInfos = mileageStream.collect(Collectors.toList());
        //是否是国际航班
        boolean isInternational;
        StringBuilder tempCompany = new StringBuilder(reqParam.getAirline());
        if (CollectionUtils.isEmpty(matchMileageInfos)) {
            throw new ServiceException(AcBffErrorCode.NO_DATA, "找不到该航段的信息，请选择其他航线");
        }
        List<String> mileageInfo = matchMileageInfos.get(0);
        isInternational = "1".equals(mileageInfo.get(4));
        if (("AC".contentEquals(tempCompany) || "ZH".contentEquals(tempCompany)) && isInternational) {
            tempCompany.append("1");
        }
        List<MileageAddCalculatorClassList> classLists = gson.fromJson(classListsStr, new TypeToken<List<MileageAddCalculatorClassList>>() {
        }.getType());
        Stream<MileageAddCalculatorClassList> classListStream = classLists.stream().filter(list -> list.getAirline().contentEquals(tempCompany));
        List<MileageAddCalculatorClassList> matchClassInfos = classListStream.collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchClassInfos)) {
            throw new ServiceException(AcBffErrorCode.NO_DATA, "找不到该航空公司的信息，请选择其他公司");
        }
        MileageAddCalculatorClassList classList = matchClassInfos.get(0);
        List<CabinMiles> cabinMiles = new ArrayList<>();
        for (CabinInfo cabinInfo : classList.getCabinList()) {
            String cabinClass = cabinInfo.getClassType();
            // 0.公司 1.舱位 2.舱位系数
            List<List<String>> ratioLists = gson.fromJson(ratioListsStr, new TypeToken<List<List<String>>>() {
            }.getType());
            Stream<List<String>> ratioListStream = ratioLists.stream()
                    .filter(ratio -> ratio.get(0).equals("ZH".equals(reqParam.getAirline()) ? tempCompany.toString() : reqParam.getAirline())
                            && ratio.get(1).equals(cabinClass));
            List<List<String>> ratioInfo = ratioListStream.collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ratioInfo)) {
                throw new ServiceException(AcBffErrorCode.NO_DATA, "没有找到该航空公司" + cabinClass + "舱的积分结算系数，请选择其他航线");
            }
            //系数
            BigDecimal ratio = new BigDecimal(ratioInfo.get(0).get(2));
            BigDecimal mileage = new BigDecimal(mileageInfo.get(2));
            BigDecimal mile = mileage.doubleValue() > 0 ? mileage : new BigDecimal(0);
            cabinMiles.add(new CabinMiles(mile.multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue(), cabinInfo.getClassCode(), cabinInfo.getClassName()));
        }
        return new CumulativeCalculatorResponse(new BigDecimal(mileageInfo.get(2)).multiply(new BigDecimal("1.609")).intValue(), cabinMiles);
    }

    @Override
    public ExchangeCalculatorResponse toCalculateExchange(RequestData<ExchangeCalculatorRequest> exchangeCalculatorRequest) {
        ExchangeCalculatorRequest exchangeCalculator = exchangeCalculatorRequest.getData();
        List<MileageExchangeCalculateRule> mileageExchangeCalculateRules;
        //根据城市查询航线
        String redisResult = (String) redisUtils.hget(MILEAGE_EXCHANGE_CALCULATE_RULES_HASH, exchangeCalculator.getFromPlace() + "_" + exchangeCalculator.getToPlace());
        //在判断返程是否存在
        if (StringUtils.isEmpty(redisResult)) {
            //没有则从文件中查询,并保存到redis中
            mileageExchangeCalculateRules = getFileAndPutRedis(exchangeCalculatorRequest.getData());
        } else {
            Type type = new TypeToken<List<MileageExchangeCalculateRule>>() {
            }.getType();
            mileageExchangeCalculateRules = HoAirGsonUtil.fromJson(redisResult, type);
        }
        if (CollectionUtils.isEmpty(mileageExchangeCalculateRules)) {
            throw new ServiceException(AcBffErrorCode.NO_DATA, "查询无数据");
        }
        MileageExchangeCalculateRule mileageExchangeCalculateRule = mileageExchangeCalculateRules.get(0);
        return new ExchangeCalculatorResponse(mileageExchangeCalculateRule.getFirstClass(),
                mileageExchangeCalculateRule.getBusinessClass(), mileageExchangeCalculateRule.getEconomyClass());
    }

    @Override
    public CalculatorAirportSelections toCatchFlightLineAirPorts(RequestData exchangeCalculatorRequest) {
        String airPorts = (String) redisUtils.get(MILEAGE_AIRPORT_SELECTIONS);
        if (StringUtils.isEmpty(airPorts)) {
            airPorts = FileUtils.readJson("/mileageCalculator/mileageAirportSelections.json");
            redisUtils.set(MILEAGE_AIRPORT_SELECTIONS, airPorts, -1);
        }
        if (StringUtils.isNotBlank(airPorts)) {
            List<AirportSelection> airportSelections = new GsonBuilder().create()
                    .fromJson(airPorts, new TypeToken<List<AirportSelection>>() {
                    }.getType());
            return new CalculatorAirportSelections(airportSelections);
        } else {
            throw ServiceException.fail("未获取到航线信息AirportSelection");
        }
    }

    /**
     * 先读取文件然后保存到redis中
     *
     * @param exchangeCalculatorRequest
     * @return
     */
    private List<MileageExchangeCalculateRule> getFileAndPutRedis(ExchangeCalculatorRequest exchangeCalculatorRequest) {
        List<MileageExchangeCalculateRule> mileageExchangeCalculateRules;//否则从配置文件中读取
        String rules = FileUtils.readJson("/mileageCalculator/mileageExchangeCalculateRule.json");

        List<MileageExchangeCalculateRule> ruleList = new GsonBuilder().create()
                .fromJson(rules, new TypeToken<List<MileageExchangeCalculateRule>>() {
                }.getType());
        Map<String, List<MileageExchangeCalculateRule>> dataMap = ruleList.stream()
                .collect(Collectors.groupingBy(o -> o.getChangeDeptAirport() + "_" + o.getChangeArrAirport()));
        Map<String, String> jsonMap = new HashMap<>();
        dataMap.forEach((k, v) -> {
            MileageExchangeCalculateRule rule = v.get(0);
            jsonMap.put(k, JSON.toJSONString(v));
            //添加返程,内存换速度
            jsonMap.put(rule.getChangeArrAirport() + "_" + rule.getChangeDeptAirport(), JSON.toJSONString(v));
        });
        redisUtils.hset(MILEAGE_EXCHANGE_CALCULATE_RULES_HASH, JSON.toJSONString(jsonMap), -1L);
        //获取集合
        mileageExchangeCalculateRules = dataMap.get(
                exchangeCalculatorRequest.getToPlace() + "_" + exchangeCalculatorRequest.getFromPlace());
        if (CollectionUtils.isEmpty(mileageExchangeCalculateRules)) {
            mileageExchangeCalculateRules = dataMap.get(
                    exchangeCalculatorRequest.getFromPlace() + "_" + exchangeCalculatorRequest.getToPlace());
        }
        return mileageExchangeCalculateRules;
    }

    @Override
    public boolean toAddBeneficiary(RequestData<ModifyBeneficiaryRequest> beneficiaryRequest) {
        ModifyBeneficiaryRequest beneficiaryReq = beneficiaryRequest.getData();
        /*if (CollectionUtils.isNotEmpty(beneficiaryReq.getMemberBeneficiaryInfo().getCertificate())) {
            beneficiaryReq.getMemberBeneficiaryInfo().getCertificate().forEach(e -> {
                if (StringUtils.isNotEmpty(e.getCnumber())) {
                    e.setCnumber(decrypt(e.getCnumber()));
                }
            });
        }*/
        List<MemberBeneficiaryCertificateInfo> tempCertLists = beneficiaryReq.getMemberBeneficiaryInfo().getCertificate();
        // 当证件类型都为"BC"时 ,expireDate设为2099-12-31 、默认填充cnumber为生日number，有多个BC类型时填充生日number + 0 + index,证件号实际不使用，回显时以生日处理为准
        List<MemberBeneficiaryCertificateInfo> bcCertList = tempCertLists.stream().filter(c -> CertificateTypeEnum.BC.getShowCode().equals(c.getCtype())).collect(Collectors.toList());
        tempCertLists.removeAll(bcCertList);
        if (bcCertList.size() > 0) {
            for (int i = 0; i < bcCertList.size(); i++) {
                MemberBeneficiaryCertificateInfo cert = bcCertList.get(i);
                cert.setExpireDate("2099-12-31");
                String cnumberSuffix = (i > 0) ? "0" + i : ""; // Add suffix "0" for multiple BC certificates
                cert.setCnumber(beneficiaryReq.getMemberBeneficiaryInfo().getBirthday().replace("-", "") + cnumberSuffix);
            }
            tempCertLists.addAll(bcCertList);
        }
        // 前端及统一订单传入的军官证类型为MP，CRM为MIL，处理为MIL
        // 前端及统一订单传入的台湾居民居住证类型为TWP，CRM为TIC，处理为TIC
        tempCertLists.forEach(c -> {
            if ("MP".equals(c.getCtype())) {
                c.setCtype(CertificateTypeEnum.OFFICER_CARD.getShowCode());
            } else if ("TWP".equals(c.getCtype())) {
                c.setCtype(CertificateTypeEnum.TW_ID_CARD.getShowCode());
            }
        });
        beneficiaryReq.getMemberBeneficiaryInfo().setCertificate(tempCertLists);

        //创建受益人添加请求对象，调用受益人添加接口
        ChannelInfo channelInfo = commonService.findChannelInfo(beneficiaryRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(beneficiaryRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        //判断受益人不能大于10个
        ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(beneficiaryRequest.getFfpId())));
        Integer count = getBeneficiaryByMemberId(ptCrmMileageRequest);
        if (count >= 10) {
            throw ServiceException.fail("受益人数量已达上限，请删除后再尝试新增受益人");
        }
        //添加受益人
        MemberBeneficiaryDTO memberBeneficiaryDTO = new MemberBeneficiaryDTO();
        BeanUtils.copyProperties(beneficiaryReq.getMemberBeneficiaryInfo(), memberBeneficiaryDTO);
        memberBeneficiaryDTO.setCertificate(convertListDTO(beneficiaryReq.getMemberBeneficiaryInfo().getCertificate(), MemberBeneficiaryCertificateDTO.class));
        memberBeneficiaryDTO.setMemberId(Integer.parseInt(beneficiaryRequest.getFfpId()));
        ptCrmMileageRequest.setData(memberBeneficiaryDTO);
        if (checkMemberBenefitInfoCname(memberBeneficiaryDTO)) {
            throw ServiceException.fail("中文姓名不可为空");
        }
        ;
        //直接激活受益人
        ArrayList<Integer> toActiveList = new ArrayList<>();
        toActiveList.add(memberService.toAddBeneficiary(ptCrmMileageRequest));
        ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(beneficiaryRequest.getFfpId()), toActiveList));
        memberService.toActiveBeneficiary(ptCrmMileageRequest);
        return true;
    }

    @Override
    public boolean toModifyBeneficiary(RequestData<ModifyBeneficiaryRequest> beneficiaryRequest) {
        if (CollectionUtils.isNotEmpty(beneficiaryRequest.getData().getMemberBeneficiaryInfo().getCertificate())) {
            beneficiaryRequest.getData().getMemberBeneficiaryInfo().getCertificate().forEach(e -> {
                e.setCnumber(decrypt(e.getCnumber()));
            });
        }
        //创建受益人修改请求对象，调用受益人修改接口
        ChannelInfo channelInfo = commonService.findChannelInfo(beneficiaryRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(beneficiaryRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        //修改受益人
        MemberBeneficiaryDTO memberBeneficiaryDTO = new MemberBeneficiaryDTO();
        BeanUtils.copyProperties(beneficiaryRequest.getData().getMemberBeneficiaryInfo(), memberBeneficiaryDTO);
        memberBeneficiaryDTO.setCertificate(convertListDTO(beneficiaryRequest.getData().getMemberBeneficiaryInfo().getCertificate(), MemberBeneficiaryCertificateDTO.class));
        memberBeneficiaryDTO.setMemberId(Integer.parseInt(beneficiaryRequest.getFfpId()));
        if (checkMemberBenefitInfoCname(memberBeneficiaryDTO)) {
            throw ServiceException.fail("中文姓名不可为空");
        }
        ptCrmMileageRequest.setData(memberBeneficiaryDTO);
        memberService.toModifyBeneficiary(ptCrmMileageRequest);
        return true;
    }

    @Override
    public boolean toDeleteBeneficiary(RequestData<ModifyBeneficiaryDelRequest> beneficiaryRequest) {
        if (CollectionUtils.isEmpty(beneficiaryRequest.getData().getDeleteBeneficial())) {
            throw ServiceException.fail("待删除的受益人ID不能为空");
        }
        //创建受益人删除请求对象，调用受益人删除接口
        ChannelInfo channelInfo = commonService.findChannelInfo(beneficiaryRequest.getChannelNo());
        PtCrmMileageRequest ptCrmMileageRequest = CRMReqUtil.buildCommCrmReq(beneficiaryRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(beneficiaryRequest.getFfpId()), beneficiaryRequest.getData().getDeleteBeneficial().get(0)));
        memberService.toDeleteBeneficiary(ptCrmMileageRequest);
        return true;
    }

    @Override
    public boolean toDeleteCertificate(RequestData<MemberCertificateDelRequest> beneficiaryRequest) {
        MemberCertificateDelRequest memberCertificateDelRequest = beneficiaryRequest.getData();
        //请求解绑
        ChannelInfo channelInfo = commonService.findChannelInfo(beneficiaryRequest.getChannelNo());
        PtMemberCertificateDelRequest ptMemberCertificateDelRequest = new PtMemberCertificateDelRequest();
        PtApiCRMRequest<PtMemberCertificateDelRequest> ptApiRequest = CRMReqUtil.buildCommReq(beneficiaryRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), beneficiaryRequest.getFfpId(), "");
        ptMemberCertificateDelRequest.setRecordId(decrypt(memberCertificateDelRequest.getRecord()));
        ptApiRequest.setData(ptMemberCertificateDelRequest);
        memberService.deleteCertificate(ptApiRequest);
        return true;
    }

    @Override
    public NumberAndTypeResponse toPreserveRealNameInfo(RequestData<MemberInfoPreserveRequest> requestData) {
        try {
            MemberInfoPreserveRequest memberInfoPreserveRequest = requestData.getData();
            if (StringUtils.isNotEmpty(memberInfoPreserveRequest.getCertificateNumber())) {
                memberInfoPreserveRequest.setCertificateNumber(decrypt(memberInfoPreserveRequest.getCertificateNumber()));
            }
            PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = buildPtModifyCustomerInfoRequest(memberInfoPreserveRequest);
            //身份证类型的根据证件判断下基本信息
            boolean isIdCard = CertificateTypeEnum.ID_CARD.geteName().equals(memberInfoPreserveRequest.getCertificateType());
            if (isIdCard) {
                Pattern pattern = Pattern.compile(PatternCommon.ID_NUMBER);
                Matcher matcher = pattern.matcher(memberInfoPreserveRequest.getCertificateNumber());
                if (!matcher.matches()) {
                    throw ServiceException.fail("请输入正确的证件号！");
                }
                String birthDate = CertUtil.certNoToDate(memberInfoPreserveRequest.getCertificateNumber());
                if (StringUtils.isEmpty(birthDate)) {
                    throw ServiceException.fail("请输入正确的证件号！");
                }
                Date birDate = DateUtil.toDate(birthDate, DateUtil.YYYY_MM_DD_PATTERN);
                if (null != birDate) {
                    ptModifyCustomerInfoRequest.setBirthday(birDate.getTime());
                }
                String sex = CertUtil.checkSex(memberInfoPreserveRequest.getCertificateNumber());
                ptModifyCustomerInfoRequest.setSex(sex);
                ptModifyCustomerInfoRequest.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
                //身份证的情况下，中文姓名不能为空
                if (StringUtils.isAnyEmpty(memberInfoPreserveRequest.getCLastName(), memberInfoPreserveRequest.getCFirstName())) {
                    throw ServiceException.fail("请输入中文姓名！");
                }
            } else {
                //其他证件下，英文姓名不能为空
                if (StringUtils.isAnyEmpty(memberInfoPreserveRequest.getELastName(), memberInfoPreserveRequest.getEFirstName())) {
                    throw ServiceException.fail("请输入英文姓名！");
                }
                ptModifyCustomerInfoRequest.setSex(SexEnum.UNKNOWN.eName);
                //护照
                if (CertificateTypeEnum.PASSPORT.geteName().equals(memberInfoPreserveRequest.getCertificateType())) {
                    ptModifyCustomerInfoRequest.setSex(StringUtils.isEmpty(memberInfoPreserveRequest.getSex()) ? SexEnum.UNKNOWN.eName : memberInfoPreserveRequest.getSex());
                    if (StringUtils.isNotEmpty(memberInfoPreserveRequest.getBirthDate())) {
                        Date birthDate = DateUtil.toDate(memberInfoPreserveRequest.getBirthDate(), DateUtil.YYYY_MM_DD_PATTERN);
                        if (birthDate == null) {
                            throw ServiceException.fail("出生日期格式：yyyy-MM-dd！");
                        }
                        ptModifyCustomerInfoRequest.setBirthday(birthDate.getTime());
                    }
                }
            }
            //查询下现有信息，比对是否可以修改姓名信息
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> detailReq = CRMReqUtil.buildCommReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
            ptMemberDetailRequest.setRequestItems(items);
            detailReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(detailReq, false);
            PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
            boolean addCertFlag = false;//是否需要添加证件
            boolean modifyCustomInfo;//是否修改客户基础信息
            if (CollectionUtils.isEmpty(ptMemberDetail.getCertificateInfo())) {
                addCertFlag = true;
            } else {
                boolean isExist = ptMemberDetail.getCertificateInfo().stream().anyMatch(memberCertificateSoaModelV2 -> memberInfoPreserveRequest.getCertificateNumber().equals(memberCertificateSoaModelV2.getCertificateNumber()));
                if (!isExist) {
                    addCertFlag = true;
                }
            }
            if (addCertFlag) {
                //证件类型检查
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkName(memberInfoPreserveRequest.getCertificateType());
                if (certificateTypeEnum == null) {
                    throw ServiceException.fail("证件类型不合法");
                }
                //会员申请  先添加证件 后修改信息
                CrmMemberBaseApiRequest<MemberCertificateReqDto> addCertificateRequest = CRMReqUtil.buildCommCrmMemberReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MemberCertificateReqDto addCertDto = new MemberCertificateReqDto();
                addCertDto.setCertificateNumber(memberInfoPreserveRequest.getCertificateNumber());
                addCertDto.setCertificateType(certificateTypeEnum.getCode());
                addCertDto.setMemberId(Integer.parseInt(requestData.getFfpId()));
                addCertificateRequest.setData(addCertDto);
                CrmMemberBaseApiResponse<MemberCertificateResDto> addCertResponse = memberService.addMemberCertificate(addCertificateRequest);
                if (127007 == addCertResponse.getCode()) {
                    throw ServiceException.fail("您填写的证件信息，已被其它会员占用，请输入新证件信息");
                } else if (addCertResponse.getCode() != 0) {
                    throw ServiceException.fail(addCertResponse.getDesc());
                }
                modifyCustomInfo = true;
            } else {
                boolean allowModify = false;
                List<MemberRealNameSummarySoaModel> realVerifyInfos = ptMemberDetail.getRealVerifyInfos();
                //验证是否实名认证 未实名的情况允许修改
                if (!CrmUtil.toCheckRealNameStatus(realVerifyInfos)) {
                    allowModify = true;
                }
                modifyCustomInfo = allowModify;
            }
            if (!modifyCustomInfo) {
                throw ServiceException.fail("请联系客服修改");
            } else {
                PtApiCRMRequest<PtModifyCustomerInfoRequest> ptApiCRMRequest = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
                ptApiCRMRequest.setData(ptModifyCustomerInfoRequest);
                memberService.toSaveCusBasicInfo(ptApiCRMRequest);
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            throw ServiceException.fail(e.getMessage());
        }
        return null;
    }


    /**
     * @param certificateType
     * @param certificateNumber
     * @param ffpId
     * @param ip
     * @param channelCode
     * @param channelPwd
     * @param items
     * @return com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest<com.juneyaoair.oneorder.crm.dto.request.PtMemberDetailRequest>
     * <AUTHOR>
     * @Description 构建用证件信息查询会员信息请求
     * @Date 13:19 2023/9/13
     **/
    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberQueryCardNoReq(String certificateType, String
            certificateNumber, String ffpId, String ip, String channelCode, String channelPwd, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCertificateType(certificateType);
        ptMemberDetailRequest.setCertificateNumber(certificateNumber);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = CRMReqUtil.buildHeader(ip, ffpId, "");
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    /**
     * @param memberInfoPreserveRequest
     * @return com.juneyaoair.oneorder.crm.dto.request.PtModifyCustomerInfoRequest
     * <AUTHOR>
     * @Description 修改客户信息
     * @Date 16:53 2023/9/12
     **/
    private PtModifyCustomerInfoRequest buildPtModifyCustomerInfoRequest(MemberInfoPreserveRequest memberInfoPreserveRequest) {
        //需判断用户会员信息中英文姓名是否完全匹配
        if (StringUtils.isAnyEmpty(memberInfoPreserveRequest.getEFirstName(), memberInfoPreserveRequest.getELastName(), memberInfoPreserveRequest.getCLastName(), memberInfoPreserveRequest.getCFirstName())) {
            throw ServiceException.fail("中英文姓名不匹配，请致电021-95520");
        }
        boolean checkPinYin = false;
        List<String> pingYins = ChineseSpellUTils.getPingYin(memberInfoPreserveRequest.getCLastName() + memberInfoPreserveRequest.getCFirstName());
        if (CollectionUtils.isNotEmpty(pingYins)) {
            for (String pinyin : pingYins) {
                pinyin = pinyin.replaceAll(" ", "");
                if (pinyin.equalsIgnoreCase(memberInfoPreserveRequest.getELastName() + memberInfoPreserveRequest.getEFirstName())) {
                    checkPinYin = true;
                    break;
                }
            }
        }

        if (!checkPinYin) {
            throw ServiceException.fail("中英文姓名不匹配，请致电021-95520");
        }

        PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = new PtModifyCustomerInfoRequest();
        BeanUtils.copyProperties(memberInfoPreserveRequest, ptModifyCustomerInfoRequest);
        //处理英文姓名大小写问题
        if (StringUtils.isNotEmpty(memberInfoPreserveRequest.getELastName())) {
            ptModifyCustomerInfoRequest.setELastName(memberInfoPreserveRequest.getELastName().toUpperCase());
        }
        if (StringUtils.isNotEmpty(memberInfoPreserveRequest.getEFirstName())) {
            ptModifyCustomerInfoRequest.setEFirstName(memberInfoPreserveRequest.getEFirstName().toUpperCase());
        }
        return ptModifyCustomerInfoRequest;
    }

    @Override
    public String toModifyCertificate(RequestData<ModifyCertificateNumberReq> modifyCertificateNumberReq) {
        String record = "";
        ModifyCertificateNumberReq modifyCertificateNumber = modifyCertificateNumberReq.getData();
        if (StringUtils.isNotEmpty(modifyCertificateNumber.getCertificateInfo().getCertificateNumber())) {
            modifyCertificateNumber.getCertificateInfo().setCertificateNumber(decrypt(modifyCertificateNumber.getCertificateInfo().getCertificateNumber()));
        }
        CertificateInfo info = modifyCertificateNumber.getCertificateInfo();
        //判断证件信息的正则
        if ("1".equals(info.getCertificateType())) {
            boolean matches = info.getCertificateNumber().matches(PatternCommon.ID_NUMBER);
            if (!matches) {
                throw ServiceException.fail("身份证格式不正确");
            }
        } else if ("2".equals(info.getCertificateType())) {
            boolean matches = info.getCertificateNumber().matches(PatternCommon.PASSPORT_NO);
            if (!matches) {
                throw ServiceException.fail("护照格式不正确");
            }
        } else if ("5".equals(info.getCertificateType()) || "7".equals(info.getCertificateType())) {
            boolean matches = info.getCertificateNumber().matches(PatternCommon.PERMIT_HKMOTW);
            if (!matches) {
                throw ServiceException.fail("港澳台通行证格式不正确");
            }
        } else if ("10".equals(info.getCertificateType()) || "11".equals(info.getCertificateType())) {
            boolean matches = info.getCertificateNumber().matches(PatternCommon.HMT_ID_CARD);
            if (!matches) {
                throw ServiceException.fail("证件格式不正确");
            }
        }
        // 20210120 为临时解决优惠券只能本人使用的校验漏洞，暂不支持维护其他类型的证件
        if (CertificateTypeEnum.OTHER.getCode() == NumberUtils.toInt(info.getCertificateType())) {
            throw ServiceException.fail("暂不支持新增或修改其他类型的证件");
        }
        if (!"1".equals(info.getCertificateType()) && !"6".equals(info.getCertificateType()) && !"10".equals(info.getCertificateType())
                && !"11".equals(info.getCertificateType()) && info.getCertificateNumber().length() > 17) {
            throw ServiceException.fail("您输入的证件号码长度不符合规定，请检查。");
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(modifyCertificateNumberReq.getChannelNo());
        if (StringUtils.isEmpty(info.getRecord())) {
            CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertRequest = CRMReqUtil.buildCommCrmMemberReq(modifyCertificateNumberReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            QueryMemberCertificateReqDto queryCertDto = new QueryMemberCertificateReqDto();
            queryCertDto.setMemberId(Integer.parseInt(modifyCertificateNumberReq.getFfpId()));
            queryCertRequest.setData(queryCertDto);
            CrmMemberBaseApiResponse<QueryMemberCertificateResDto> queryCertResponse = memberService.toCatchMemberCertList(queryCertRequest, false);
            List<MemberCertificateResModel> certList = queryCertResponse.getData().getCertificateList();
            if (CollectionUtils.isNotEmpty(certList) && certList.size() >= 4) {
                throw ServiceException.fail("您已达最高4个绑定证件数量限制");
            }
            //表示为新增，添加到新增集合infos
            CrmMemberBaseApiRequest<MemberCertificateReqDto> addCertificateRequest = CRMReqUtil.buildCommCrmMemberReq(modifyCertificateNumberReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            MemberCertificateReqDto addCertDto = new MemberCertificateReqDto();
            addCertDto.setCertificateNumber(info.getCertificateNumber());
            addCertDto.setCertificateType(Integer.parseInt(info.getCertificateType()));
            addCertDto.setRemark((StringUtils.isEmpty(info.getRemark())) ? "" : info.getRemark());
            addCertDto.setMemberId(Integer.parseInt(modifyCertificateNumberReq.getFfpId()));
            addCertificateRequest.setData(addCertDto);
            CrmMemberBaseApiResponse<MemberCertificateResDto> addCertResponse = memberService.addMemberCertificate(addCertificateRequest);
            if (addCertResponse.getCode() == 127007) {
                throw ServiceException.fail("保存失败，该证件被其他用户添加，请联系客服");
            }
            if (addCertResponse.getCode() != 0) {
                throw ServiceException.fail(addCertResponse.getDesc());
            }
            String recordId = String.valueOf(addCertResponse.getData().getRecordId());
            try {
                return AESTool.encrypt(recordId, CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16));
            } catch (Exception e) {
                throw ServiceException.fail("加密出错");
            }

        } else {
            //表示为修改
            CrmMemberBaseApiRequest<ModifyMemberCertificateReqDto> modifyCertRequest = CRMReqUtil.buildCommCrmMemberReq(modifyCertificateNumberReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            ModifyMemberCertificateReqDto modifyDto = new ModifyMemberCertificateReqDto();
            modifyDto.setCertificateNumber(info.getCertificateNumber());
            modifyDto.setCertificateType(Integer.parseInt(info.getCertificateType()));
            modifyDto.setRemark((StringUtils.isEmpty(info.getRemark())) ? "" : info.getRemark());
            try {
                modifyDto.setRecordId(Integer.valueOf(AESTool.decrypt(info.getRecord(), CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16))));
            } catch (Exception e) {
                throw ServiceException.fail("解密出错");
            }
            modifyDto.setMemberId(Integer.parseInt(modifyCertificateNumberReq.getFfpId()));
            modifyCertRequest.setData(modifyDto);
            memberService.modifyMemberCertificate(modifyCertRequest);
        }


        return record;
    }

    @Override
    public boolean toAddContact(RequestData<ContactReq> contactReq) {
        ContactReq request = contactReq.getData();
        /*if (StringUtils.isNotEmpty(request.getTelephone())) {
            request.setTelephone(decrypt(request.getTelephone()));
        }
        if (StringUtils.isNotEmpty(request.getEmail())) {
            request.setEmail(decrypt(request.getEmail()));
        }*/
        //1. 验证码判断
        String emailCode = (String) redisUtils.get(request.getEmail() + request.getType());

        String telCode = (String) redisUtils.get("SMS:" + request.getTelephone() + request.getType());

        if (!((StringUtils.isNotEmpty(emailCode) && emailCode.equals(request.getVerifyCode())) || (StringUtils.isNotEmpty(telCode) && telCode.equals(request.getVerifyCode())))) {
            throw ServiceException.fail("验证码不正确或已失效");
        }
        //2.请求CRM
        try {
            ChannelInfo channelInfo = commonService.findChannelInfo(contactReq.getChannelNo());
            PtApiCRMRequest<PtContactInformationReq> ptApiRequest = CRMReqUtil.buildCommReq("", channelInfo.getChannelCode(), channelInfo.getChannelPwd(), contactReq.getFfpId(), "");
            PtContactInformationReq informationReq = new PtContactInformationReq();
            ContactInfo contactInfo = new ContactInfo();
            contactInfo.setContactType(request.getContactType());
            contactInfo.setContactNumber(StringUtils.isNotEmpty(request.getTelephone()) ? request.getTelephone() : request.getEmail());
            informationReq.setContactInfo(contactInfo);
            ptApiRequest.setData(informationReq);
            memberService.addContact(ptApiRequest);
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            throw ServiceException.fail("绑定联系方式出错");
        }
        return true;
    }

    @Override
    public boolean toUpdateMobileNum(RequestData<TelephoneInfoReq> telephoneInfoReq) {
        try {
            //判断是否登录
            TelephoneInfoReq request = telephoneInfoReq.getData();
            //判断验证码是否正确
            String veriCode = "SMS:" + decrypt(request.getTelephone()) + request.getType();
            String code = request.getVeriCode();
            String redisCode = (String) redisUtils.get(veriCode);
            if (StringUtils.isBlank(code) || StringUtils.isBlank(redisCode) || !code.equals(redisCode)) {
                throw ServiceException.fail("验证码错误或已经失效！");
            }
            //原手机号和新手机号不可一致
            ChannelInfo channelInfo = commonService.findChannelInfo(telephoneInfoReq.getChannelNo());
            String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(telephoneInfoReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), telephoneInfoReq.getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(telephoneInfoReq.getFfpNo());
            ptMemberDetailRequest.setRequestItems(items);
            detaiReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(detaiReq, false);
            if (ptCRMResponse.getCode() != 0) {
                throw ServiceException.fail(ptCRMResponse.getMsg());
            }
            String mobile = null;
            List<MemberContactSoaModel> contactInfoList = ptCRMResponse.getData().getContactInfo();
            MemberContactSoaModel memberContactMobile = new MemberContactSoaModel();
            if (CollectionUtils.isNotEmpty(contactInfoList)) {
                //获取手机号码
                memberContactMobile = CRMReqUtil.toMemberContactSoaModel(contactInfoList, ContactTypeEnum.MOBILE.getCode());
                if (memberContactMobile != null) {
                    mobile = memberContactMobile.getContactNumber();
                }
            }
            if (null == mobile) {
                throw ServiceException.fail("未获取到原手机号");
            } else {
                if (mobile.equals(decrypt(request.getTelephone()))) {
                    throw ServiceException.fail("新手机号不可与当前手机号相同");
                }
            }
            //构建请求类
            CrmMemberBaseApiRequest<ModifyMemberContactReqDto> crmMemberBaseApiRequest = CRMReqUtil.buildCommCrmMemberReq(telephoneInfoReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            ModifyMemberContactReqDto contactInfo = ModifyMemberContactReqDto.builder().MemberId(Integer.valueOf(telephoneInfoReq.getFfpId())).ContactType(1).ContactValue(decrypt(request.getTelephone())).RecordId((int) memberContactMobile.getRecordId()).build();
            crmMemberBaseApiRequest.setData(contactInfo);
            memberService.toModifyMemberContactInfo(crmMemberBaseApiRequest);
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            throw ServiceException.fail("修改手机号出错！");
        }
        return true;
    }

    @Override
    public boolean toUnbindEmail(RequestData<UnbingingEmailReq> unbingingEmailReq) {
        try {
            //判断验证码是否正确
            unbingingEmailReq.getData().setEmail(decrypt(unbingingEmailReq.getData().getEmail()));
            if (!checkPattern(unbingingEmailReq.getData().getEmail(), PatternCommon.EMAIL)) {
                throw ServiceException.fail("请输入正确的邮箱号");
            }
            String veriCode = unbingingEmailReq.getData().getEmail() + unbingingEmailReq.getData().getType();
            String code = unbingingEmailReq.getData().getVeriCode();
            String redisCode = (String) redisUtils.get(veriCode);
            if (StringUtils.isBlank(code) || StringUtils.isBlank(redisCode) || !code.equals(redisCode)) {
                throw ServiceException.fail("验证码错误或已经失效！");
            }
            UnbingingEmailReq request = unbingingEmailReq.getData();
            //请求解绑
            ChannelInfo channelInfo = commonService.findChannelInfo(unbingingEmailReq.getChannelNo());
            PtApiCRMRequest<UnbingingEmailReq> ptApiRequest = CRMReqUtil.buildCommReq(unbingingEmailReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), unbingingEmailReq.getFfpId(), "");
            request.setRecordId(decrypt(request.getRecord()));
            ptApiRequest.setData(request);
            memberService.deleteContactInformation(ptApiRequest);
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            throw ServiceException.fail("解绑邮箱出错！");
        }
        return true;
    }

    private boolean checkPattern(String originStr, String patternCommon) {
        if (StringUtils.isAnyEmpty(originStr, patternCommon)) {
            return false;
        } else {
            Pattern pattern = Pattern.compile(patternCommon);
            Matcher matcher = pattern.matcher(originStr);
            return matcher.matches();
        }
    }

    @Override
    public void toFirstSetLoginPwd(RequestData<FirstSetLoginPwd> setLoginPwdRequest) {
        FirstSetLoginPwd firstSetLoginPwd = setLoginPwdRequest.getData();
        firstSetLoginPwd.setPassWord(decrypt(firstSetLoginPwd.getPassWord()));
        firstSetLoginPwd.setConfirmedPwd(decrypt(firstSetLoginPwd.getConfirmedPwd()));
        Pattern pattern = Pattern.compile(PatternCommon.LOGIN_P_W_D);
        Matcher matcherPwd = pattern.matcher(firstSetLoginPwd.getPassWord());
        Matcher matcherConfirmedPwd = pattern.matcher(firstSetLoginPwd.getConfirmedPwd());
        if (!matcherPwd.matches() || !matcherConfirmedPwd.matches()) {
            throw ServiceException.fail("密码必须为6-16位数字与字母组合");
        }
        if (!firstSetLoginPwd.getPassWord().equals(firstSetLoginPwd.getConfirmedPwd())) {
            throw ServiceException.fail("两次密码不一致！");
        }
        //首次修改登录密码
        ChannelInfo channelInfo = commonService.findChannelInfo(setLoginPwdRequest.getChannelNo());
        PtApiCRMRequest<ModifyLoginPwd> ptApiRequest = CRMReqUtil.buildCommReq(setLoginPwdRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), setLoginPwdRequest.getFfpId(), "");
        ModifyLoginPwd modifyLoginPwd = new ModifyLoginPwd();
        modifyLoginPwd.setNewPassword(firstSetLoginPwd.getPassWord());
        ptApiRequest.setData(modifyLoginPwd);
        memberService.modifyLoginPassword(ptApiRequest);
    }

    @Override
    public void toResetLoginPwd(RequestData<SetLoginPwd> setLoginPwdRequest) {
        setLoginPwdRequest.getData().setNewPwd(decrypt(setLoginPwdRequest.getData().getNewPwd()));
        setLoginPwdRequest.getData().setConfirmedPwd(decrypt(setLoginPwdRequest.getData().getConfirmedPwd()));
        Pattern pattern = Pattern.compile(PatternCommon.LOGIN_P_W_D);
        Matcher matcherPwd = pattern.matcher(setLoginPwdRequest.getData().getNewPwd());
        Matcher matcherConfirmedPwd = pattern.matcher(setLoginPwdRequest.getData().getConfirmedPwd());
        if (!matcherPwd.matches() || !matcherConfirmedPwd.matches()) {
            throw ServiceException.fail("密码必须为6-16位数字与字母组合");
        }
        if (StringUtils.isNotEmpty(setLoginPwdRequest.getData().getMobileNum())) {
            setLoginPwdRequest.getData().setMobileNum(decrypt(setLoginPwdRequest.getData().getMobileNum()));
        }
        if (!setLoginPwdRequest.getData().getNewPwd().equals(setLoginPwdRequest.getData().getConfirmedPwd())) {
            throw ServiceException.fail("两次密码不一致！");
        }
        SetLoginPwd setLoginPwd = setLoginPwdRequest.getData();
        ChannelInfo channelInfo = commonService.findChannelInfo(setLoginPwdRequest.getChannelNo());
        PtApiCRMRequest<PtResetLoginPasswordRequest> ptApiRequest = CRMReqUtil.buildCommReq(setLoginPwdRequest.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), setLoginPwdRequest.getFfpId(), "");
        PtResetLoginPasswordRequest ptResetLoginPasswordRequest = new PtResetLoginPasswordRequest();
        ptResetLoginPasswordRequest.setResetType(1);
        ptResetLoginPasswordRequest.setAccount(PhoneUtil.formatMobile(setLoginPwd.getCountryCode(), setLoginPwd.getMobileNum()));
        ptResetLoginPasswordRequest.setNewPassword(setLoginPwd.getNewPwd());
        ptResetLoginPasswordRequest.setIsAutoGenerate(false);
        ptResetLoginPasswordRequest.setCaptcha(setLoginPwd.getVeriCode());
        ptApiRequest.setData(ptResetLoginPasswordRequest);
        memberService.resetLoginPassword(ptApiRequest);
    }

    /**
     * @param mobileNum
     * @param ffpCardNo
     * @return java.lang.String
     * <AUTHOR>
     * @Description 原手机号被脱敏 需要先获取原手机号
     * @Date 16:56 2023/9/10
     **/
    private String toFindMobile(String mobileNum, String ffpCardNo, String ip) {
        if (StringUtils.isAnyEmpty(mobileNum, ffpCardNo) || mobileNum.length() < 5) {
            return mobileNum;
        }
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(ip, B2C_CHANNEL, B2C_CHANNEL_PWD);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        String[] items = {com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest, false);
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            throw ServiceException.fail("会员基本信息查询出错");
        }
        //手机号校验
        MemberContactSoaModel targetTelephone = CRMReqUtil.toMemberContactSoaModel(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
        if (null == targetTelephone
                || StringUtils.isAnyEmpty(mobileNum, targetTelephone.getContactNumber())
                || mobileNum.length() < 11 || targetTelephone.getContactNumber().length() < 11) {
            throw ServiceException.fail("手机号校验出错");
        }
        if (!mobileNum.substring(0, 3).equals(targetTelephone.getContactNumber().substring(0, 3))
                || !mobileNum.substring(mobileNum.length() - 2).equals(targetTelephone.getContactNumber().substring(targetTelephone.getContactNumber().length() - 2))) {
            throw ServiceException.fail("手机号校验出错");
        }
        return targetTelephone.getContactNumber();
    }

    @Override
    public boolean toSetConsumptionPwd(RequestData<ResetSalePassword> consumptionPwdReq) {
        String authKey = "ALIPAYAUTH:" + consumptionPwdReq.getFfpNo();
        if (StringUtils.isEmpty((String) redisUtils.get(authKey))) {
            throw ServiceException.fail("您没有通过支付宝认证或认证时间已超过有效期，请重新认证");
        }
        consumptionPwdReq.getData().setConsumePwd(decrypt(consumptionPwdReq.getData().getConsumePwd()));
        consumptionPwdReq.getData().setConfirmedPwd(decrypt(consumptionPwdReq.getData().getConfirmedPwd()));
        Pattern pattern = Pattern.compile(PatternCommon.SALE_P_W_D);
        Matcher matcherPwd = pattern.matcher(consumptionPwdReq.getData().getConsumePwd());
        Matcher matcherConfirmedPwd = pattern.matcher(consumptionPwdReq.getData().getConfirmedPwd());
        if (!matcherPwd.matches() || !matcherConfirmedPwd.matches()) {
            throw ServiceException.fail("消费密码必须为六位数字");
        }
        ResetSalePassword consumption = consumptionPwdReq.getData();
        if (!consumption.getConsumePwd().equals(consumption.getConfirmedPwd())) {
            throw ServiceException.fail("两次消费密码不一致，请重新输入");
        }

        //检查认证状态
        ChannelInfo channelInfo = commonService.findChannelInfo(consumptionPwdReq.getChannelNo());
        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), consumptionPwdReq.getFfpId(), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(consumptionPwdReq.getFfpNo());
        String[] items = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(detaiReq, false);
        if (ptCRMResponse.getCode() != 0) {
            throw ServiceException.fail("未查询到用户信息");
        }
        if (!CrmUtil.toCheckRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos())) {
            throw ServiceException.fail("用户未认证成功");
        }

        //设置消费密码
        PtApiCRMRequest<PtSetConsumePasswordRequest> ptApiRequest = CRMReqUtil.buildCommReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), consumptionPwdReq.getFfpId(), "");
        PtSetConsumePasswordRequest ptSetConsumePasswordRequest = new PtSetConsumePasswordRequest();
        ptSetConsumePasswordRequest.setPassword(consumption.getConfirmedPwd());
        ptApiRequest.setData(ptSetConsumePasswordRequest);
        memberService.setConsumePassword(ptApiRequest);
        return true;
    }

    @Override
    @SuppressWarnings("all")
    public void toCheckSecurityOfConsumptionPwd(RequestData<SecurityCheckRequest> consumptionPwdReq, HttpServletRequest request) {
        //0. 安全类型检查
        SecurityCheckTypeEnum securityCheckType = SecurityCheckTypeEnum.toFilterSecurityCheckType(consumptionPwdReq.getData().getSecurityType());
        if (null == securityCheckType) {
            throw ServiceException.fail("安全检查类型不在指定范围内");
        }
        //1. 基础信息校验
        PtCRMResponse memberInfo = toTakeBaseCheck(consumptionPwdReq.getFfpNo(), consumptionPwdReq.getChannelNo(), consumptionPwdReq.getOriginIp());
        ChannelDTO lastRealNameChannel = toCatchLastRealNameChannel(memberInfo);
        if (SecurityCheckTypeEnum.FSET == securityCheckType) {
            //2'. 设置安全认证缓存信息
            if (null != lastRealNameChannel && StringUtils.isNotEmpty(lastRealNameChannel.getChannelCode())) {
                if (VerifyChannelEnum.Photo.code.equals(lastRealNameChannel.getChannelCode())) {
                    // 最近一次认证时间是否在7天内
                    boolean verifyIn7Days = toCheckLastRealNameIn7Days(lastRealNameChannel);
                    if (verifyIn7Days) {
                        toSettingCache(consumptionPwdReq.getFfpNo());
                        return;
                    }
                } else if (VerifyChannelEnum.ZhiFuBao.code.equals(lastRealNameChannel.getChannelCode())) {
                    // 最近一次认证时间在24小时内
                    boolean verifyIn24Hours = toCheckLastRealNameIn24Hours(lastRealNameChannel);
                    if (verifyIn24Hours) {
                        toSettingCache(consumptionPwdReq.getFfpNo());
                        return;
                    }
                }
            }
        } else if (SecurityCheckTypeEnum.RESET == securityCheckType) {
            //2. 最近一次认证(3年内)是否可信渠道
            if (null != lastRealNameChannel && lastRealNameChannel.isCredentialChannel() && toCheckLastRealNameIn3Years(lastRealNameChannel)) {
                String securityCheckKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.RESET_SECURITY_CHECK_KEY + consumptionPwdReq.getFfpNo());
                redisUtils.set(securityCheckKey, SecurityCheckStatusEnum.PASSED.getCode(), 300L);
                return;
            }
        }
        throw new ServiceException(CommonErrorCode.NEED_CONFIRM, CommonErrorCode.NEED_CONFIRM.getMessage());
    }

    @Override
    @SuppressWarnings("all")
    public void toChangeConsumptionPwd(RequestData<PassWordSettingRequest> consumptionPwd, HttpServletRequest request) {
        //0. 是否进行过安全检查 或者 是否进行过支付宝认证
        String securityCheckKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SECURITY_CHECK_KEY + consumptionPwd.getFfpNo());
        String aliPayAuthKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.ALIPAY_AUTH_KEY + consumptionPwd.getFfpNo());
        if (StringUtils.isEmpty((String) redisUtils.get(securityCheckKey)) && StringUtils.isEmpty((String) redisUtils.get(aliPayAuthKey))) {
            throw ServiceException.fail("未做安全校验，请退出此模块后，重新操作");
        }
        //1. 消费密码格式校验
        consumptionPwd.getData().setConsumePwd(decrypt(consumptionPwd.getData().getConsumePwd()));
        consumptionPwd.getData().setConfirmedPwd(decrypt(consumptionPwd.getData().getConfirmedPwd()));
        Pattern pattern = Pattern.compile(PatternCommon.SALE_P_W_D);
        Matcher matcherPwd = pattern.matcher(consumptionPwd.getData().getConsumePwd());
        Matcher matcherConfirmedPwd = pattern.matcher(consumptionPwd.getData().getConfirmedPwd());
        if (!matcherPwd.matches() || !matcherConfirmedPwd.matches()) {
            throw ServiceException.fail("消费密码必须为六位数字");
        }
        if (!consumptionPwd.getData().getConsumePwd().equals(consumptionPwd.getData().getConfirmedPwd())) {
            throw ServiceException.fail("两次消费密码不一致，请重新输入");
        }

        //2. 基础校验
        PtCRMResponse ptCRMResponse = toTakeBaseCheck(consumptionPwd.getFfpNo(), consumptionPwd.getChannelNo(), consumptionPwd.getOriginIp());
        //是否设置过消费密码
        boolean consumePwdSetted = toCheckIfSetConsumePwd(ptCRMResponse);
        //3. 验证码校验
        smsCodeSendService.checkVerifyCodeByCardNo(consumptionPwd.getFfpNo(), new VerifyCodeCheckRequest(consumePwdSetted ? SourceType.RESET_SALE_PWD.value : SourceType.SET_CONSUMPTION_PWD.value, consumptionPwd.getData().getVerifyCode()), ServiceContext.getHead().channelNo, request);

        //4. 设置消费密码
        ChannelInfo channelInfo = commonService.findChannelInfo(consumptionPwd.getChannelNo());
        PtApiCRMRequest<PtSetConsumePasswordRequest> ptApiRequest = CRMReqUtil.buildCommReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), consumptionPwd.getFfpId(), "");
        PtSetConsumePasswordRequest ptSetConsumePasswordRequest = new PtSetConsumePasswordRequest();
        ptSetConsumePasswordRequest.setPassword(consumptionPwd.getData().getConfirmedPwd());
        ptApiRequest.setData(ptSetConsumePasswordRequest);
        memberService.setConsumePassword(ptApiRequest);
    }

    /**
     * @param ptCRMResponse
     * @return boolean
     * <AUTHOR>
     * @Description 检查是否设置过消费密码
     * @Date 20:32 2024/8/7
     **/
    @SuppressWarnings("all")
    private boolean toCheckIfSetConsumePwd(PtCRMResponse ptCRMResponse) {
        if (null == ptCRMResponse || null == ptCRMResponse.getData() || null == ptCRMResponse.getData()) {
            return false;
        }
        PtMemberDetail ptCRMResponseData = (PtMemberDetail) ptCRMResponse.getData();
        if (null == ptCRMResponseData.getBasicInfo()) {
            return false;
        }
        return ptCRMResponseData.getBasicInfo().getIsSetConsumePwd();
    }


    /**
     * @param ffpCardNo
     * @param channelNo
     * @param ip
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 基础校验
     * @Date 14:22 2024/8/7
     **/
    @SuppressWarnings("rawtypes")
    private PtCRMResponse toTakeBaseCheck(String ffpCardNo, String channelNo, String ip) {
        if (StringUtils.isAnyEmpty(ffpCardNo, channelNo, ip)) {
            throw ServiceException.fail("基础校验出错");
        }
        //0. 检查是否已通过同盾校验
        String tongdunAuthKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.TONGDUN_AUTH_KEY + ffpCardNo);
        if (StringUtils.isEmpty((String) redisUtils.get(tongdunAuthKey))) {
            throw ServiceException.fail("未做安全校验，请退出此模块后，重新操作");
        }
        PtCRMResponse memberInfo = toCatchMemberInfo(ffpCardNo, channelNo, ip);
        //1. 校验账户是否已绑定手机号
        boolean mobileBindAlready = toCheckIfBindingMobile(memberInfo);
        if (!mobileBindAlready) {
            throw ServiceException.fail("账户未绑定手机号，请先绑定手机号");
        }
        //2. 校验账户是否已实名
        boolean realName = toCheckIfRealName(memberInfo);
        if (!realName) {
            throw ServiceException.fail("账户未实名，请先进行实名");
        }
        return memberInfo;
    }

    /**
     * @param ffpCardNo
     * @return void
     * <AUTHOR>
     * @Description 设置缓存 用于安全认证
     * @Date 17:19 2024/8/7
     **/
    private void toSettingCache(String ffpCardNo) {
        if (StringUtils.isEmpty(ffpCardNo)) {
            throw new ServiceException("会员卡号为空，设置缓存出错");
        }
        String securityCheckKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SECURITY_CHECK_KEY + ffpCardNo);
        redisUtils.set(securityCheckKey, SecurityCheckStatusEnum.PASSED.getCode(), 300L);
    }

    /**
     * @param lastRealNameChannel
     * @return boolean
     * <AUTHOR>
     * @Description 检查最近一次认证是否在24小时内
     * @Date 13:11 2024/8/7
     **/

    private boolean toCheckLastRealNameIn24Hours(ChannelDTO lastRealNameChannel) {
        if (null == lastRealNameChannel || lastRealNameChannel.getVerifyDate() <= 0L) {
            return false;
        }
        String verifyDate = DateUtil.timeStampToDateStr(lastRealNameChannel.getVerifyDate(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN);
        return DateUtil.hoursDiff(verifyDate, DateUtil.catchCurrentDateTimeStr(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN) <= 24;
    }

    /**
     * @param lastRealNameChannel
     * @return boolean
     * <AUTHOR>
     * @Description 检查最近一次认证是否在3年内
     * @Date 13:11 2024/8/7
     **/
    private boolean toCheckLastRealNameIn3Years(ChannelDTO lastRealNameChannel) {
        if (null == lastRealNameChannel || lastRealNameChannel.getVerifyDate() <= 0L) {
            return false;
        }
        String verifyDate = DateUtil.timeStampToDateStr(lastRealNameChannel.getVerifyDate(), DateUtil.YYYY_MM_DD_PATTERN);
        return DateUtil.addDateYears(DateUtil.stringToDate(verifyDate, DateUtil.YYYY_MM_DD_PATTERN), 3).after(new Date());
    }

    /**
     * @param lastRealNameChannel
     * @return boolean
     * <AUTHOR>
     * @Description 检查最近一次认证是否在7天内
     * @Date 10:48 2024/8/7
     **/
    private boolean toCheckLastRealNameIn7Days(ChannelDTO lastRealNameChannel) {
        if (null == lastRealNameChannel || lastRealNameChannel.getVerifyDate() <= 0L) {
            return false;
        }
        String verifyDate = DateUtil.timeStampToDateStr(lastRealNameChannel.getVerifyDate(), DateUtil.YYYY_MM_DD_PATTERN);
        return DateUtil.dateDiff(verifyDate, DateUtil.getCurrentDateStr(), DateUtil.YYYY_MM_DD_PATTERN) <= 7;
    }

    @SuppressWarnings("all")
    private ChannelDTO toCatchLastRealNameChannel(PtCRMResponse memberInfo) {
        return toCatchRealNameChannelName(memberInfo);
    }

    /**
     * @param ffpCardNo
     * @param channelNo
     * @param ip
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 获取账户信息
     * @Date 10:12 2024/8/7
     **/
    @SuppressWarnings("all")
    private PtCRMResponse toCatchMemberInfo(String ffpCardNo, String channelNo, String ip) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(ip, channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        return memberService.memberDetail(ptApiRequest, false);
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 校验用户是否已实名 true-已实名
     * @Date 9:53 2024/8/7
     **/
    @SuppressWarnings("all")
    private boolean toCheckIfRealName(PtCRMResponse ptCRMResponse) {
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            return false;
        }
        PtMemberDetail ptCRMResponseData = (PtMemberDetail) ptCRMResponse.getData();
        if (CollectionUtils.isEmpty(ptCRMResponseData.getRealVerifyInfos())) {
            return false;
        }
        List<MemberRealNameSummarySoaModel> realVerifyInfos = ptCRMResponseData.getRealVerifyInfos();
        if (CollectionUtils.isEmpty(realVerifyInfos)) {
            return false;
        }
        return realVerifyInfos.stream().anyMatch(e -> StringUtils.isNotEmpty(e.getStatus()) && e.getStatus().equals(VerifyStatusEnum.PASS.code));
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 校验是否已绑定手机号 true-已绑定
     * @Date 9:27 2024/8/7
     **/
    @SuppressWarnings("all")
    private boolean toCheckIfBindingMobile(PtCRMResponse ptCRMResponse) {
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            return false;
        }
        PtMemberDetail ptCRMResponseData = (PtMemberDetail) ptCRMResponse.getData();
        List<MemberContactSoaModel> contactInfo = ptCRMResponseData.getContactInfo();
        if (CollectionUtils.isEmpty(contactInfo)) {
            return false;
        }
        return null != contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.MOBILE.getCode() == e.getContactType()).findFirst().orElse(null);
    }

    @Override
    @SuppressWarnings("all")
    public boolean toResetConsumptionPwd(RequestData<ModifyConsumerPwd> consumptionPwdReq) {
        //1. 是否已通过安全检查
        String resetSecurityCheckKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.RESET_SECURITY_CHECK_KEY + consumptionPwdReq.getFfpNo());
        String aliPayAuthKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.ALIPAY_AUTH_KEY + consumptionPwdReq.getFfpNo());
        if (StringUtils.isEmpty((String) redisUtils.get(resetSecurityCheckKey)) && StringUtils.isEmpty((String) redisUtils.get(aliPayAuthKey))) {
            throw ServiceException.fail("未做安全校验，请退出此模块后，重新操作");
        }

        //2. 基础校验
        toTakeBaseCheck(consumptionPwdReq.getFfpNo(), consumptionPwdReq.getChannelNo(), consumptionPwdReq.getOriginIp());

        //3. 参数校验
        ModifyConsumerPwd modifyConsumerPwd = consumptionPwdReq.getData();
        modifyConsumerPwd.setNewPassword(decrypt(modifyConsumerPwd.getNewPassword()));
        modifyConsumerPwd.setOldPassword(decrypt(modifyConsumerPwd.getOldPassword()));
        modifyConsumerPwd.setConfirmedPassword(decrypt(modifyConsumerPwd.getConfirmedPassword()));
        Pattern pattern = Pattern.compile(PatternCommon.SALE_P_W_D);
        Matcher matcherPwdOld = pattern.matcher(modifyConsumerPwd.getOldPassword());
        Matcher matcherPwd = pattern.matcher(modifyConsumerPwd.getNewPassword());
        Matcher matcherConfirmedPwd = pattern.matcher(modifyConsumerPwd.getConfirmedPassword());
        if (!matcherPwdOld.matches() || !matcherPwd.matches() || !matcherConfirmedPwd.matches()) {
            throw ServiceException.fail("消费密码必须为六位数字");
        }
        if (modifyConsumerPwd.getNewPassword().equals(modifyConsumerPwd.getOldPassword())) {
            throw ServiceException.fail("新密码不可与旧密码一致");
        }
        if (StringUtils.isEmpty(modifyConsumerPwd.getConfirmedPassword())) {
            throw ServiceException.fail("确认密码不可为空");
        }
        if (!modifyConsumerPwd.getNewPassword().equals(modifyConsumerPwd.getConfirmedPassword())) {
            throw ServiceException.fail("两次消费密码不一致，请重新输入");
        }
        //4. 设置消费密码
        ChannelInfo channelInfo = commonService.findChannelInfo(consumptionPwdReq.getChannelNo());
        PtApiCRMRequest<PtResetConsumerPasswordRequest> ptApiRequest = CRMReqUtil.buildCommReq(consumptionPwdReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), consumptionPwdReq.getFfpId(), "");
        PtResetConsumerPasswordRequest ptResetConsumerPasswordRequest = new PtResetConsumerPasswordRequest();
        ptResetConsumerPasswordRequest.setIsVerify(false);
        ptResetConsumerPasswordRequest.setOldPassword(modifyConsumerPwd.getOldPassword());
        ptResetConsumerPasswordRequest.setNewPassword(modifyConsumerPwd.getNewPassword());
        ptApiRequest.setData(ptResetConsumerPasswordRequest);
        memberService.modifyConsumePassword(ptApiRequest);
        return true;
    }

    @Override
    public InfoIntegrityResponse toQueryAccountIntegrity(RequestData consumptionPwdReq) {
        ChannelInfo channelInfo = commonService.findChannelInfo(consumptionPwdReq.getChannelNo());
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(consumptionPwdReq.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(consumptionPwdReq.getFfpNo());
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse ptCRMResponse = memberService.memberDetail(ptApiRequest, false);

        InfoIntegrityResponse infoIntegrityResposne = new InfoIntegrityResponse();

        PtMemberDetail memberDetail = (PtMemberDetail) ptCRMResponse.getData();
        MemberBasicInfoSoaModel basicInfo = memberDetail.getBasicInfo();
        List<MemberContactSoaModel> contactInfo = memberDetail.getContactInfo();
        List<MemberRealNameSummarySoaModel> realVerifyInfos = memberDetail.getRealVerifyInfos();
        MemberContactSoaModel mobileContact = null;
        MemberContactSoaModel emailContact = null;
        if (CollectionUtils.isNotEmpty(contactInfo)) {
            mobileContact = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.MOBILE.getCode() == e.getContactType()).findFirst().orElse(null);
            emailContact = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.EMAIL.getCode() == e.getContactType()).findFirst().orElse(null);
        }

        if (CollectionUtils.isNotEmpty(realVerifyInfos)) {
            Optional<MemberRealNameSummarySoaModel> realNameInfo = realVerifyInfos.stream().filter(e -> StringUtils.isNotEmpty(e.getStatus()) && e.getStatus().equals(VerifyStatusEnum.PASS.code)).findFirst();
            infoIntegrityResposne.setIsRealName(realNameInfo.isPresent());
        } else {
            infoIntegrityResposne.setIsRealName(false);
        }
        infoIntegrityResposne.setBindingMobile(null != mobileContact);
        infoIntegrityResposne.setBindingEmail(null != emailContact);
        infoIntegrityResposne.setLoginPwd(basicInfo.getIsSetLoginPwd());
        infoIntegrityResposne.setConsumePwd(basicInfo.getIsSetConsumePwd());
        infoIntegrityResposne.setSecurityLevel(toJudgeSecuLevel(ptCRMResponse));
        infoIntegrityResposne.setIntegrityLevel(toJudgeInfoIntegrity(ptCRMResponse));
        return infoIntegrityResposne;
    }

    @Override
    public void toTakeAlipayAuth(RequestData<AlipayAuthRequest> requestData) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        PtCRMResponse aliPayAuthResult = memberService.toTakeAliPayAuth(requestData.getFfpId(), requestData.getData().getAuthCode(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), HoAirIpUtil.getLocalIp());
        if (!aliPayAuthResult.isIsSuccess()) {
            throw ServiceException.fail(aliPayAuthResult.getMsg());
        } else {
            String authKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.ALIPAY_AUTH_KEY + requestData.getFfpNo());
            redisUtils.set(authKey, AliPayAuthStatusEnum.AUTHED.getCode(), 300L);
        }
    }

    @Override
    public void toTakeTongdunAuth(RequestData<TongdunAuthRequest> requestData) {
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        String clientIp = ServiceContext.getHead().clientIp;
        String channelCode = ServiceContext.getHead().channelNo;
        String platform = ServiceContext.getHead().platformInfo;
        String userAgent = ServiceContext.getHead().userAgent;
        String blackBox = ServiceContext.getHead().blackBox;
        if (StringUtils.isEmpty(blackBox)) {
            throw ServiceException.fail("设备指纹不可为空！");
        }
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};

        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = CRMReqUtil.buildCommReq(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), requestData.getFfpId(), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(requestData.getFfpNo());
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(detaiReq, false);
        Map<String, String> params = TongDunUtil.createCommonParam(channelCode, platform, "", TongDunEventType.MODIFY.getEventType(), "", tongDunConfig);
        if (null != ptCRMResponse.getData() && CollectionUtils.isNotEmpty(ptCRMResponse.getData().getContactInfo())) {
            MemberContactSoaModel memberContactSoaModelMobile = CRMReqUtil.toMemberContactSoaModel(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            if (null != memberContactSoaModelMobile && StringUtils.isNotEmpty(memberContactSoaModelMobile.getContactNumber())) {
                params.put("account_mobile", memberContactSoaModelMobile.getContactNumber());
            }
            MemberContactSoaModel memberContactSoaModelEmail = CRMReqUtil.toMemberContactSoaModel(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.EMAIL.getCode());
            if (null != memberContactSoaModelEmail && StringUtils.isNotEmpty(memberContactSoaModelEmail.getContactNumber())) {
                params.put("account_email", memberContactSoaModelEmail.getContactNumber());
            }
        }
        params.put("black_box", blackBox);
        params.put("user_agent", userAgent);
        params.put("ip_address", clientIp);
        params.put("account_login", getFfpCardNo(requestData.getFfpNo()));
        params.put("modify_type", ConsumpPwdSetEnum.FSET.getCode());

        BaseRequestDTO<Antifraud> antifraudBaseRequestDTO = new BaseRequestDTO<>();
        antifraudBaseRequestDTO.setIp(clientIp);
        antifraudBaseRequestDTO.setChannelCode(channelCode);
        antifraudBaseRequestDTO.setFfpCardNo(requestData.getFfpNo());
        antifraudBaseRequestDTO.setFfpId(requestData.getFfpId());
        antifraudBaseRequestDTO.setUserNo("10001");
        antifraudBaseRequestDTO.setVersion("V1.0");
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(clientIp);
        antifraud.setFfpCardNo(requestData.getFfpNo());
        antifraud.setFfpId(requestData.getFfpId());
        antifraud.setParamMap(params);
        antifraudBaseRequestDTO.setRequest(antifraud);
        BaseResponseDTO<AntiResult> antiFraudResult = flightBasicConsumerClient.antifraud(antifraudBaseRequestDTO);
        if (null != antiFraudResult.getObjData() && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiFraudResult.getObjData().getFinal_decision())) {
            throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_WARNING);
        }
        //同盾校验成功后设置缓存 用于后续设置消费密码接口校验
        String tongdunAuthKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.TONGDUN_AUTH_KEY + requestData.getFfpNo());
        redisUtils.set(tongdunAuthKey, TongdunAuthStatusEnum.PASSED.getCode(), 300L);
    }

    @Override
    public PageResult<CouponProductResult> queryCouponProduct(RequestData requestData, CouponProductParam couponProductParam) {
        PtCouponProductGetRequestDto couponProductGetRequest = new PtCouponProductGetRequestDto();
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        couponProductGetRequest.setChannelCode(channelInfo.getOrderChannelCode());
        couponProductGetRequest.setFfpId(requestData.getFfpId());
        couponProductGetRequest.setFfpCardNo(requestData.getFfpNo());
        couponProductGetRequest.setVersion("10");
        couponProductGetRequest.setPageNo(couponProductParam.getPageNum());
        couponProductGetRequest.setPageSize(couponProductParam.getPageSize());
        // 默认权益券
        String ruleModel = couponProductParam.getRuleModel();
        couponProductGetRequest.setRuleModel(null == ruleModel ? "Coupon" : ruleModel);
        couponProductGetRequest.setVoucherTypes(Lists.newArrayList(couponProductParam.getVoucherTypes()));
        // 默认查询未使用
        List<String> couponState = couponProductParam.getCouponState();
        if (null == couponState) {
            couponState = Lists.newArrayList();
        }
        if (couponState.isEmpty()) {
            couponState.add("Not");
        }
        couponProductGetRequest.setCouponState(couponState);
        String availableStatus = couponProductParam.getAvailableStatus();
        couponProductGetRequest.setAvailableStatus(null == availableStatus ? "Not" : availableStatus);
        SingleBookCondition singleBookCondition = CouponProductMapper.MAPPER.getSingleBookCondition(couponProductParam.getCouponProductCondition());
        if (null != singleBookCondition) {
            couponProductGetRequest.setSingleBookConditions(Lists.newArrayList(singleBookCondition));
        }
        PtCouponProductGetResponseDto catchRightCoupons = memberService.toCatchRightCoupons(couponProductGetRequest);
        PageResult<CouponProductResult> pageResult = new PageResult<>();
        pageResult.setPageNum(couponProductParam.getPageNum());
        pageResult.setPageSize(couponProductParam.getPageSize());
        pageResult.setTotal(catchRightCoupons.getTotalRecords());
        List<CouponProductResult> couponProductResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(catchRightCoupons.getVouchers())) {
            for (MainVoucherInfo mainVoucherInfo : catchRightCoupons.getVouchers()) {
                for (VoucherInfo voucherInfo : mainVoucherInfo.getVoucherInfos()) {
                    CouponProductResult couponProductResult = CouponProductMapper.MAPPER.getCouponProductResult(voucherInfo);
                    if (null != couponProductResult) {
                        couponProductResultList.add(couponProductResult);
                    }
                }
            }
        }
        pageResult.setRows(couponProductResultList);
        return pageResult;
    }

    private static String getFfpCardNo(String ffpCardNo) {
        return subStrFromStart(ffpCardNo, AirCompanyEnum.HO.getAirCompanyCode());
    }

    /**
     * 截取某个字符串开头的字符串
     *
     * @param originStr 原始字符串
     * @param subStr    需要截取的字符串
     * @return
     */
    public static String subStrFromStart(String originStr, String subStr) {
        if (originStr.startsWith(subStr)) {
            originStr = originStr.substring(subStr.length());
        }
        return originStr;
    }

    /**
     * 身份证时校验中文姓名不为空
     *
     * @param memberBeneficiaryDTO
     * @return
     */
    private boolean checkMemberBenefitInfoCname(MemberBeneficiaryDTO memberBeneficiaryDTO) {
        if (memberBeneficiaryDTO.getCertificate().stream().anyMatch(certificate -> CertificateTypeEnum.ID_CARD.getCode() == certificate.getCtype()
                || CertificateTypeEnum.HMT_ID_CARD.getCode() == certificate.getCtype())) {
            return StringUtils.isAnyBlank(memberBeneficiaryDTO.getCFirstName(), memberBeneficiaryDTO.getCLastName());
        }
        return false;
    }

    /**
     * @param input 输入集合
     * @param clzz  输出集合类型
     * @return 返回集合
     */
    public List<MemberBeneficiaryCertificateDTO> convertListDTO(List<MemberBeneficiaryCertificateInfo> input, Class<MemberBeneficiaryCertificateDTO> clzz) {
        List<MemberBeneficiaryCertificateDTO> output = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(input)) {
            for (MemberBeneficiaryCertificateInfo source : input) {
                MemberBeneficiaryCertificateDTO target = BeanUtils.instantiate(clzz);
                BeanUtils.copyProperties(source, target);
                CertificateTypeEnum typeEnum = CertificateTypeEnum.checkShowCode(source.getCtype());
                target.setCtype(typeEnum == null ? CertificateTypeEnum.UNKNOW.getCode() : typeEnum.getCode());
                output.add(target);
            }
        }
        return output;
    }

    /**
     * 获取草稿状态和激活状态的受益人信息数量
     *
     * @param ptCrmMileageRequest
     * @return
     */
    private Integer getBeneficiaryByMemberId(PtCrmMileageRequest ptCrmMileageRequest) {
        PtCrmMileageResponse<Map<String, Integer>> ptCrmMileageResponse = memberService.toCountBeneficiaryNumByMemberId(ptCrmMileageRequest);
        Integer count = null;
        Map<String, Integer> map = ptCrmMileageResponse.getData();
        if (null != map) {
            Integer m = map.get("M") == null ? 0 : map.get("M");
            Integer a = map.get("A") == null ? 0 : map.get("A");
            count = m + a;
        } else {
            count = 0;
        }
        return count;
    }


    /**
     * @param input 输入集合
     * @param clzz  输出集合类型
     * @return 返回集合
     */
    public List<MemberBeneficiaryCertificateInfo> convertListInfo(List<MemberBeneficiaryCertificateDTO> input, Class<MemberBeneficiaryCertificateInfo> clzz) {
        List<MemberBeneficiaryCertificateInfo> output = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(input)) {
            for (MemberBeneficiaryCertificateDTO source : input) {
                MemberBeneficiaryCertificateInfo target = BeanUtils.instantiate(clzz);
                BeanUtils.copyProperties(source, target);
                CertificateTypeEnum typeEnum = CertificateTypeEnum.checkType(source.getCtype());
                target.setCtype(typeEnum == null ? CertificateTypeEnum.UNKNOW.getShowCode() : typeEnum.getShowCode());
                output.add(target);
            }
        }
        return output;
    }

    /**
     * 赋值本人标识，并将本人排到最前面
     *
     * @param commonContactsInfoList
     * @param mobile
     */
    private void isOwnPass(List<CommonContactsInfo> commonContactsInfoList, String mobile) {
        int priority = 1;
        //将会员本人的证件排列在列表前面
        if (CollectionUtils.isNotEmpty(commonContactsInfoList)) {
            for (CommonContactsInfo commonContactsInfo : commonContactsInfoList) {
                if (StringUtils.isNotEmpty(commonContactsInfo.getMobile())) {
                    boolean isOwn = false;
                    commonContactsInfo.setPriority(priority);
                    //判断手机号码是否是本人
                    if (mobile != null) {
                        if (commonContactsInfo.getMobile().equals(mobile)) {
                            isOwn = true;
                            commonContactsInfo.setPriority(0);
                            commonContactsInfo.setIsOwn(isOwn);
                            continue;
                        } else {
                            commonContactsInfo.setIsOwn(isOwn);
                        }
                    } else {
                        commonContactsInfo.setIsOwn(isOwn);
                    }
                    priority++;
                }
            }
            commonContactsInfoList.sort(Comparator.comparing(CommonContactsInfo::getPriority));
        }
    }

    /**
     * 标记受益人
     *
     * @param queryCommonPersonInfoList
     * @param beneficiaryDTOList
     */
    private void toMarkBeneficiary(List<QueryCommonPersonInfo> queryCommonPersonInfoList, List<MemberBeneficiaryDTO> beneficiaryDTOList) {
        List<QueryCommonPersonInfo> finalPersonInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryCommonPersonInfoList) || CollectionUtils.isNotEmpty(beneficiaryDTOList)) {
            List<QueryCommonPersonInfo> ownPersons = queryCommonPersonInfoList.stream().filter(e -> null != e.getIsOwn() && StringUtils.isNotEmpty(e.getPassengerType()) && e.getIsOwn() && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ownPersons)) {
                //将本人的是否可用积分复制给证件是否可用
                for (QueryCommonPersonInfo ownPerson : ownPersons
                ) {
                    if (CollectionUtils.isNotEmpty(ownPerson.getContactCertList())) {
                        for (GeneralContactCertInfo generalContact : ownPerson.getContactCertList()
                        ) {
                            generalContact.setValid(generalContact.isUseScore());
                        }
                    }

                }
                finalPersonInfoList.addAll(ownPersons);
            }

            List<QueryCommonPersonInfo> notOwnPersons = queryCommonPersonInfoList.stream().filter(e -> null == e.getIsOwn() || !e.getIsOwn() && StringUtils.isNotEmpty(e.getPassengerType()) && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notOwnPersons)) {
                for (QueryCommonPersonInfo queryCommonPersonInfo : notOwnPersons) {
                    if (CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                        boolean isBeneficial = false;
                        for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                            boolean isValid = false;
                            if (StringUtils.isNotEmpty(generalContactCertInfo.getCertNo())) {
                                for (MemberBeneficiaryDTO memberBeneficiaryDTO : beneficiaryDTOList) {
                                    if (CollectionUtils.isNotEmpty(memberBeneficiaryDTO.getCertificate())) {
                                        for (MemberBeneficiaryCertificateDTO beneficiaryCertificate : memberBeneficiaryDTO.getCertificate()
                                        ) {
                                            CertificateTypeEnum memberCertEnum = CertificateTypeEnum.checkType(beneficiaryCertificate.getCtype());
                                            if (memberCertEnum != null && (memberCertEnum.getShowCode().equals(generalContactCertInfo.getCertType())
                                                    && generalContactCertInfo.getCertNo().equals(beneficiaryCertificate.getCnumber()))
                                                    // 处理CRM那边记录的其他类型的证件
                                                    || (memberCertEnum == CertificateTypeEnum.OTHER
                                                    && generalContactCertInfo.getCertNo().equals(beneficiaryCertificate.getCnumber()))) {
                                                // 不存在英文姓名 或 英文姓名匹配
                                                boolean enName = (StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameS()) && StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameF())) || toMatchEnName(memberBeneficiaryDTO, queryCommonPersonInfo);
                                                // 身份证匹配姓名
                                                if ((CertificateTypeEnum.ID_CARD.equals(memberCertEnum) || CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum))
                                                        && (memberBeneficiaryDTO.getCLastName() + memberBeneficiaryDTO.getCFirstName()).equals(queryCommonPersonInfo.getPassengerName()) && enName) {
                                                    isBeneficial = true;
                                                    isValid = true;
                                                } else if (!CertificateTypeEnum.ID_CARD.equals(memberCertEnum) && !CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum)
                                                        && toMatchEnName(memberBeneficiaryDTO, queryCommonPersonInfo)) {
                                                    // 其他证件匹配英文姓名
                                                    isBeneficial = true;
                                                    isValid = true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            generalContactCertInfo.setValid(isValid);
                        }
                        queryCommonPersonInfo.setIsBeneficiary(isBeneficial);
                    }
                }
            }
            List<QueryCommonPersonInfo> beneficialPersons = notOwnPersons.stream().filter(e -> null != e.getIsOwn() && null != e.getIsBeneficiary() && (e.getIsOwn() || e.getIsBeneficiary())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(beneficialPersons)) {
                finalPersonInfoList.addAll(beneficialPersons);
            }

        }
        queryCommonPersonInfoList.clear();
        if (CollectionUtils.isNotEmpty(finalPersonInfoList)) {
            queryCommonPersonInfoList.addAll(finalPersonInfoList);
        } else {
            List<QueryCommonPersonInfo> ownPersons = queryCommonPersonInfoList.stream().filter(e -> null != e.getIsOwn() && e.getIsOwn() && StringUtils.isNotEmpty(e.getPassengerType()) && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            queryCommonPersonInfoList.addAll(ownPersons);
        }
    }

    private boolean toMatchEnName(MemberBeneficiaryDTO memberBeneficiaryDTO, QueryCommonPersonInfo commonPersonInfo) {
        if (StringUtils.isNotBlank(commonPersonInfo.getPassEnNameS()) && StringUtils.isNotBlank(commonPersonInfo.getPassEnNameF())) {
            return (commonPersonInfo.getPassEnNameS().equals(memberBeneficiaryDTO.getELastName()) && commonPersonInfo.getPassEnNameF().equals(memberBeneficiaryDTO.getEFirstName()));
        } else {
            return false;
        }
    }


    /**
     * 处理证件类型为BC:出生医学证明证件号回显为出生日期
     *
     * @param personInfoList
     */
    private void handleCertBC(List<QueryCommonPersonInfo> personInfoList) {
        if (CollectionUtils.isNotEmpty(personInfoList)) {
            personInfoList.forEach(queryCommonPersonInfo -> {
                // 遍历contactCertList
                if (CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                    queryCommonPersonInfo.getContactCertList().forEach(contactCertInfo -> {
                        if (CertificateTypeEnum.BC.getShowCode().equals(contactCertInfo.getCertType())) {
                            contactCertInfo.setCertNo(queryCommonPersonInfo.getBirthdate().replace("-", ""));
                        }
                    });
                }
            });
        }
    }


    /**
     * @param language
     * @param queryCommonPersonInfoList
     * @return void
     * <AUTHOR>
     * @Description 补充乘机人证件信息是否即将过期信息
     * @Date 13:28 2024/8/13
     **/
    private void toCompletePassengerCertInfo(String language, List<QueryCommonPersonInfo> queryCommonPersonInfoList) {
        try {
            Map<String, Map<String, String>> certExpireTagMap = i18nDictService.fetchDictData(I18nDictionaryTypeEnum.CERT_EXPIRE_TAG);
            for (QueryCommonPersonInfo queryCommonPersonInfo : queryCommonPersonInfoList) {
                for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                    Date certExpireDate = DateUtil.toDate(generalContactCertInfo.getCertValidity(), DateUtil.YYYY_MM_DD_PATTERN);
                    if (null != certExpireDate) {
                        Calendar expiringDateLimit = Calendar.getInstance();
                        expiringDateLimit.setTime(certExpireDate);
                        expiringDateLimit.add(Calendar.MONTH, -6);
                        Date now = new Date();
                        if (now.after(certExpireDate)) {
                            // 已过期
                            Map<String, String> expireMap = certExpireTagMap.get("EXPIRE");
                            generalContactCertInfo.setExpiringMessage(expireMap.get(language));
                        } else if (CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            // 查询常用乘机人页面，提示有效期在一年内即将到期的护照
                            expiringDateLimit.add(Calendar.MONTH, -6);
                            // 即将过期
                            Map<String, String> expireSoonMap = certExpireTagMap.get("ABOUT_EXPIRE");
                            generalContactCertInfo.setExpiringMessage(now.after(expiringDateLimit.getTime()) ? expireSoonMap.get(language) : "");
                        } else {
                            // 即将过期
                            Map<String, String> expireSoonMap = certExpireTagMap.get("ABOUT_EXPIRE");
                            generalContactCertInfo.setExpiringMessage(now.after(expiringDateLimit.getTime()) ? expireSoonMap.get(language) : "");
                        }
                    }
                    // NI类型证件无过期提示
                    if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                        generalContactCertInfo.setExpiringMessage("");
                    }
                }
            }
        } catch (Exception e) {
            log.error("卡号", e);
        }
    }

    /**
     * 畅飞卡2.0
     *
     * @param req
     * @param commonPersonQuery
     * @param personInfoList
     * @return
     */
    private List<QueryCommonPersonInfo> handleFreeFlightCard(RequestData<CommonPersonQuery> req, CommonPersonQuery commonPersonQuery, List<QueryCommonPersonInfo> personInfoList) {
        // 畅飞卡2.0存在成人付费购买普通舱位的情况，另加字段处理
        if (req.getData().getCabinTypes().contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())
                || req.getData().getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())) {
            personInfoList = dealUnlimitedFly(personInfoList, req, req.getData().getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType()));
        } else if (req.getData().getCabinTypes().contains(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType()) || req.getData().isUnlimitedCardV2()) {
            UnlimitedCard2Config unlimitedCard2Config = accountBffConfig.getUnlimitedCard2Config();
            boolean springFestival = false;
            Date limitedDateBegin = DateUtil.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeBegin(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN);
            Date limitedDateEnd = DateUtil.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeEnd(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN);
            Date flightDate = DateUtil.toDate(req.getData().getDepartureDate(), DateUtil.YYYY_MM_DD_PATTERN);
            if (null != limitedDateBegin && null != limitedDateEnd && null != flightDate
                    && flightDate.getTime() <= limitedDateEnd.getTime() && flightDate.getTime() >= limitedDateBegin.getTime()) {
                springFestival = true;
            }
            personInfoList = dealUnlimitedFlyV2(personInfoList, req, springFestival);
        } else if (!Collections.disjoint(commonPersonQuery.getCabinTypes(), accountBffConfig.getThemeCouponList())) {
            personInfoList = dealUnlimitedTheme(personInfoList, req, commonPersonQuery.getCabinTypes(),
                    commonPersonQuery.getDepartureDate(), commonPersonQuery.getReturnDate());
        }
        return personInfoList;
    }

    private List<QueryCommonPersonInfo> dealUnlimitedTheme(List<QueryCommonPersonInfo> queryCommonPersonInfoList, RequestData<CommonPersonQuery> req, List<String> cabinTypes, String departureDate, String returnDate) {
        Set<QueryCommonPersonInfo> result = new HashSet<>();
        try {
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.ThemeCouponBindRecord(req.getChannelNo(), req.getChannelNo(), req.getOriginIp(), cabinTypes);

            if (CollectionUtils.isNotEmpty(unlimitedFlyV2BindRecords)) {
                // 筛选有效绑定记录
                unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                        "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyV2BindRecord bindRecord : unlimitedFlyV2BindRecords) {
                    queryCommonPersonInfoList.forEach(commonPersonInfo -> bindingMatch(commonPersonInfo, bindRecord));
                }
                //乘机人证件过滤
                queryCommonPersonInfoList.forEach(queryCommonPersonInfo -> {
                    List<GeneralContactCertInfo> newQueryCommonPerson = queryCommonPersonInfo.getContactCertList().stream().filter(commonPersonQuery -> StringUtils.isNotEmpty(commonPersonQuery.getCertNo())).collect(Collectors.toList());
                    queryCommonPersonInfo.setContactCertList(newQueryCommonPerson);
                });

                //按照兑换航班日期和儿童生日日期作比较
                queryCommonPersonInfoList.forEach(queryCommonPersonInfo -> {
                    if (StringUtils.isNotBlank(departureDate)) {
                        queryCommonPersonInfo.setPassengerType(adjustPassType(queryCommonPersonInfo.getBirthdate(), departureDate));
                    }
                    if (StringUtils.isNotBlank(returnDate)) {
                        queryCommonPersonInfo.setPassengerType(adjustPassType(queryCommonPersonInfo.getBirthdate(), returnDate));
                    }
                });

                //主题卡判断
                queryCommonPersonInfoList = queryCommonPersonInfoList.stream()
                        .filter(passenger -> cabinTypes.get(0).equals(passenger.getContactType())).collect(Collectors.toList());
                // 成人乘机人
                Optional<QueryCommonPersonInfo> ownPassenger = queryCommonPersonInfoList.stream().filter(passenger ->
                        PassengerTypeEnum.ADT.getPassType().equals(passenger.getPassengerType()) && passenger.getIsOwn()
                ).findFirst();
                // 儿童乘机人
                List<QueryCommonPersonInfo> chdPassenger = queryCommonPersonInfoList.stream()
                        .filter(passenger -> PassengerTypeEnum.CHD.getPassType().equals(passenger.getPassengerType())
                                /*绑定记录未匹配上的不展示*/).collect(Collectors.toList());
                ownPassenger.ifPresent(passenger -> result.add(passenger));

                result.addAll(chdPassenger);
            }
        } catch (Exception e) {
            log.error("查询畅飞卡飞绑定记录出现异常", e);
        }
        return Lists.newArrayList(result);
    }

    /**
     * 调整乘客类型
     *
     * @param birthDate       出生日期
     * @param finalFlightDate 最终的航班出发日期
     * @return
     */
    public static String adjustPassType(String birthDate, String finalFlightDate) {
        int age = DateUtil.getAgeByBirthIncludeBirthDay(birthDate, finalFlightDate, DateUtil.YYYY_MM_DD_PATTERN);
        return toPassType(age);
    }

    private List<QueryCommonPersonInfo> dealUnlimitedFlyV2(List<QueryCommonPersonInfo> queryCommonPersonInfoList, RequestData<CommonPersonQuery> req, boolean springFestival) {
        Set<QueryCommonPersonInfo> result = new HashSet<>();
        try {
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.listFlyCard2BindRecord(req.getChannelNo(), req.getChannelNo(), req.getOriginIp());
            if (CollectionUtils.isNotEmpty(unlimitedFlyV2BindRecords)) {
                // 筛选有效绑定记录
                unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                        "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyV2BindRecord bindRecord : unlimitedFlyV2BindRecords) {
                    queryCommonPersonInfoList.forEach(commonPersonInfo -> bindingMatch(commonPersonInfo, bindRecord));
                }
                // 成人乘机人
                Optional<QueryCommonPersonInfo> ownPassenger = queryCommonPersonInfoList.stream().filter(passenger ->
                        PassengerTypeEnum.ADT.getPassType().equals(passenger.getPassengerType()) && passenger.getIsOwn()
                ).findFirst();
                // 儿童乘机人
                List<QueryCommonPersonInfo> chdPassenger = queryCommonPersonInfoList.stream()
                        .filter(passenger -> PassengerTypeEnum.CHD.getPassType().equals(passenger.getPassengerType())
                                && StringUtils.isNotBlank(passenger.getUnlimitedCardType())/*绑定记录未匹配上的不展示*/).collect(Collectors.toList());
                // 设置提示信息
                if (springFestival) {
                    // 成人未购买春运卡，儿童购买了春运卡，且购买春运期间航班，提示信息并允许正价购买
                    ownPassenger.ifPresent(passenger -> {
                        if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(passenger.getUnlimitedCardType())) {
                            passenger.getContactCertList().forEach(cert -> cert.setCouponNo(null));// 不使用畅飞卡
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_PAY.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡春运版，预订该航班将会按照正价收费，确认预订吗？");
                        } else if (StringUtils.isBlank(passenger.getUnlimitedCardType())) {
                            // 成人未购卡，儿童购买春运版
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_NOT_PURCHASED.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡，预订该航班将会按照正价收费，确认预订吗？");
                        }
                    });
                    // 成人购买了春运卡，儿童购买了非春运卡，且购买春运期间航班，儿童不可购买并提示
                    if (CollectionUtils.isNotEmpty(chdPassenger)) {
                        chdPassenger.forEach(passenger -> {
                            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(passenger.getUnlimitedCardType())) {
                                passenger.setMessageType(CommonPersonMessageType.NOT_ALLOWED.getCode());
                                passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡春运版，不可以预订该航班");
                            }
                        });
                    }
                } else {
                    // 成人未购卡，儿童购买卡，提示信息并允许正价购买
                    ownPassenger.ifPresent(passenger -> {
                        if (StringUtils.isBlank(passenger.getUnlimitedCardType())) {
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_NOT_PURCHASED.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡，预订该航班将会按照正价收费，确认预订吗？");
                        }
                    });
                }
                ownPassenger.ifPresent(passenger -> result.add(passenger));
                result.addAll(chdPassenger);
            }
        } catch (Exception e) {
            log.error("查询畅飞卡飞绑定记录出现异常", e);
        }
        return Lists.newArrayList(result);
    }

    /**
     * 判断是否绑定并设置绑定信息
     *
     * @param commonPersonInfo
     * @param bindRecord
     * @return
     */
    private boolean bindingMatch(QueryCommonPersonInfo commonPersonInfo, UnlimitedFlyV2BindRecord bindRecord) {
        if (CollectionUtils.isEmpty(commonPersonInfo.getContactCertList())) {
            return false;
        }
        boolean match = false;
        if (PassengerTypeEnum.ADT.getPassType().equals(commonPersonInfo.getPassengerType())
                && !StringUtils.isAnyBlank(bindRecord.getAultIdNumber(), bindRecord.getAdultName(), bindRecord.getAdultElastName(), bindRecord.getAdultEfirstName())) {
            for (GeneralContactCertInfo certInfo : commonPersonInfo.getContactCertList()) {
                // 身份证号相同且中文名相同允许使用畅飞卡
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                    if (bindRecord.getAultIdNumber().equals(certInfo.getCertNo())
                            && bindRecord.getAdultName().equals(commonPersonInfo.getPassengerName())) {
                        match = true;
                        break;
                    }
                } else {//其他证件匹配英文姓名一致证件
                    if (bindRecord.getAdultElastName().equals(commonPersonInfo.getPassEnNameS()) && bindRecord.getAdultEfirstName().equals(commonPersonInfo.getPassEnNameF())) {
                        if (commonPersonInfo.getContactCertList().stream().anyMatch(cert -> bindRecord.getAultIdNumber().equals(cert.getCertNo()))) {
                            match = true;
                            break;
                        }
                    }
                }
            }
        } else if (PassengerTypeEnum.CHD.getPassType().equals(commonPersonInfo.getPassengerType())
                && !StringUtils.isAnyBlank(bindRecord.getChildIdNumber(), bindRecord.getChildCnName(), bindRecord.getChildEfirstName(), bindRecord.getChildElastName())) {
            List<GeneralContactCertInfo> contactCertInfos = commonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo ->
                    CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())
                            || CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())).collect(Collectors.toList());
            commonPersonInfo.setContactCertList(contactCertInfos);
            for (GeneralContactCertInfo certInfo : commonPersonInfo.getContactCertList()) {
                // 身份证号相同且中文名相同允许使用畅飞卡
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                    if (bindRecord.getChildIdNumber().equals(certInfo.getCertNo())
                            && bindRecord.getChildCnName().equals(commonPersonInfo.getPassengerName())) {
                        match = true;
                        break;
                    }
                } else {//其他证件目前匹配英文姓名一致
                    if (bindRecord.getChildElastName().equals(commonPersonInfo.getPassEnNameS()) && bindRecord.getChildEfirstName().equals(commonPersonInfo.getPassEnNameF())) {
                        if (commonPersonInfo.getContactCertList().stream().anyMatch(cert -> bindRecord.getChildIdNumber().equalsIgnoreCase(cert.getCertNo()))) {
                            match = true;
                            break;
                        }
                    }
                }
            }
        }
        if (match) {
            // 设置畅飞卡卡号
            commonPersonInfo.getContactCertList().forEach(userCert -> {
                userCert.setCouponNo(bindRecord.getVoucherNo());
                userCert.setCouponType(bindRecord.getResourceType());
            });

        }
        return match;
    }


    private List<QueryCommonPersonInfo> dealUnlimitedFly(List<QueryCommonPersonInfo> queryCommonPersonInfoList, RequestData<CommonPersonQuery> req, boolean adtUnlimitedFly) {
        List<QueryCommonPersonInfo> result = Lists.newArrayList();
        List<QueryCommonPersonInfo> ownerList = queryCommonPersonInfoList.stream().filter(QueryCommonPersonInfo::getIsOwn).collect(Collectors.toList());
        ownerList.forEach(commonPersonInfo -> commonPersonInfo.getContactCertList().stream()
                .filter(cert -> StringUtils.isNotBlank(cert.getCertNo())).forEach(cert -> cert.setCabinType(CommonPersonCabinType.CUF_ADT.getCode())));
        List<QueryCommonPersonInfo> chdUnlimitedFlyList = Lists.newArrayList();
        try {
            List<UnlimitedFlyBindRecord> childFlyBindRecords = unlimitedFlyService.listChdBindRecord(req.getChannelNo(), req.getChannelNo(), req.getOriginIp());
            if (CollectionUtils.isNotEmpty(childFlyBindRecords)) {
                childFlyBindRecords = childFlyBindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyBindRecord childFlyBindRecord : childFlyBindRecords) {
                    for (QueryCommonPersonInfo queryCommonPersonInfo : queryCommonPersonInfoList) {
                        if (PassengerTypeEnum.CHD.getPassType().equals(queryCommonPersonInfo.getPassengerType())
                                && CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                            // 儿童畅飞卡支持使用身份证或护照
                            List<GeneralContactCertInfo> contactCertInfos = queryCommonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo ->
                                    CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())
                                            || CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())).collect(Collectors.toList());
                            for (GeneralContactCertInfo certInfo : queryCommonPersonInfo.getContactCertList()) {
                                boolean match = false;
                                // 身份证号相同且中文名相同允许使用儿童随心飞
                                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                                    if (childFlyBindRecord.getChildIdNumber().equals(certInfo.getCertNo())
                                            && childFlyBindRecord.getChildCnName().equals(queryCommonPersonInfo.getPassengerName())) {
                                        match = true;
                                    }
                                } else {
                                    //其他证件目前匹配英文姓名一致
                                    if (childFlyBindRecord.getChildEnName().equals(queryCommonPersonInfo.getPassEnNameS() + "/" + queryCommonPersonInfo.getPassEnNameF())) {
                                        match = true;
                                    }
                                }
                                if (match) {
                                    contactCertInfos.forEach(useCert -> {
                                        useCert.setCabinType(CommonPersonCabinType.CUF_CHD.getCode());
                                        useCert.setCouponNo(childFlyBindRecord.getVoucherNo());
                                    });
                                    queryCommonPersonInfo.setContactCertList(contactCertInfos);
                                    chdUnlimitedFlyList.add(queryCommonPersonInfo);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            List<UnlimitedFlyBindRecord> adultFlyBindRecords = unlimitedFlyService.listChdBindRecord(req.getChannelNo(), req.getChannelNo(), req.getOriginIp());
            if (CollectionUtils.isNotEmpty(adultFlyBindRecords)) {
                Optional<UnlimitedFlyBindRecord> adultFlyBindRecord = adultFlyBindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())).findFirst();
                adultFlyBindRecord.ifPresent(bindRecord ->
                        ownerList.forEach(owner -> {
                            for (GeneralContactCertInfo certInfo : owner.getContactCertList()) {
                                if (adtUnlimitedFly) {
                                    certInfo.setCabinType(CommonPersonCabinType.UF_ADT.getCode());
                                    certInfo.setCouponNo(bindRecord.getVoucherNo());
                                } else {
                                    certInfo.setCabinType(CommonPersonCabinType.CUF_ADT.getCode());
                                }
                            }
                        })
                );
            }
        } catch (Exception e) {
            log.error("查询随心飞绑定记录出现异常", e);
        }
        result.addAll(ownerList);
        result.addAll(chdUnlimitedFlyList);
        return result;
    }

    private void markOwnPass(List<QueryCommonPersonInfo> personInfoList, ChannelInfo channelInfo, String ffpId, String ffpCardNo) {
        if (StringUtils.isAnyBlank(ffpId, ffpCardNo)) {
            return;
        }
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        QueryAccountInfoReqDto queryAccountInfoReqDto= new QueryAccountInfoReqDto();
        queryAccountInfoReqDto.setRequestItems(items);
        queryAccountInfoReqDto.setDataValue(ffpCardNo);
        queryAccountInfoReqDto.setDataType("0");
        CrmRequestDto<QueryAccountInfoReqDto> crmRequestDto  = CRMReqUtil.buildCrmRequestDto(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(),queryAccountInfoReqDto);
        crmRequestDto.setChannel(channelInfo.getChannelCode());
        crmRequestDto.setChannelPwd(channelInfo.getChannelPwd());
        crmRequestDto.setData(queryAccountInfoReqDto);
        QueryAccountInfoResDto queryAccountInfo = iAccountService.queryAccountInfo(crmRequestDto);
        if (null == queryAccountInfo) {
            return;
        }
        //填充国籍名称
        if (CollectionUtils.isNotEmpty(personInfoList)) {
            for (QueryCommonPersonInfo commonPersonInfo : personInfoList
            ) {
                if (StringUtils.isEmpty(commonPersonInfo.getNationality())) {
                    continue;
                }
                TCountryReqDTO tCountryReqDTO = new TCountryReqDTO();
                tCountryReqDTO.setCountryCode(commonPersonInfo.getNationality());
                List<TCountryDTO> tCountryDTOS = basicService.queryCountries(HoAirIpUtil.getLocalIp(), channelInfo.getHeadChannelCode(), tCountryReqDTO);
                if (CollectionUtils.isNotEmpty(tCountryDTOS)) {
                    commonPersonInfo.setNationalityName(tCountryDTOS.get(0).getCountryName());
                }
            }
        }
        List<MemberCertInfoModel> certificateInfoList = queryAccountInfo.getCertInfos();
        MemberBasicInfoModel memberInfo = queryAccountInfo.getBasicInfo();
        if (ChannelCodeEnum.G_MOBILE.getChannelCode().equals(channelInfo.getHeadChannelCode())){
            int priority = 1;
            for (QueryCommonPersonInfo commonPersonInfo : personInfoList
            ) {
                priority++;
                commonPersonInfo.setPriority(priority);
            }
            // 如果不包含会员本人则手动添加会员本人信息
            QueryCommonPersonInfo personInfo = addOwnerCommonPerson(channelInfo, queryAccountInfo);
            if (personInfo != null) {
                personInfoList.add(personInfo);
            }
        }else {
            if ((CollectionUtils.isNotEmpty(certificateInfoList)) && (CollectionUtils.isNotEmpty(personInfoList))) {
                int priority = 1;
                for (QueryCommonPersonInfo queryCommonPersonInfo : personInfoList) {
                    if (CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                        boolean isOwn = false;
                        queryCommonPersonInfo.setPriority(priority);
                        for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                            if (StringUtils.isNotEmpty(generalContactCertInfo.getCertNo())) {
                                for (MemberCertInfoModel memberCertificateSoaModelV2 : certificateInfoList) {
                                    if  (memberCertificateSoaModelV2.getCertType().equals(generalContactCertInfo.getCertType())
                                            && generalContactCertInfo.getCertNo().equals(memberCertificateSoaModelV2.getCertNumber())
                                            // 处理CRM那边记录的其他类型的证件
                                            || ( CertificateTypeEnum.OTHER.geteName().equals(memberCertificateSoaModelV2.getCertType() )
                                            && generalContactCertInfo.getCertNo().equals(memberCertificateSoaModelV2.getCertNumber()))) {
                                        // 不存在英文姓名 或 英文姓名匹配
                                        boolean enName = (StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameS()) && StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameF())) || CRMReqUtil.matchEnName(memberInfo, queryCommonPersonInfo);
                                        // 身份证匹配姓名
                                        if ((CertificateTypeEnum.ID_CARD.geteName().equals(memberCertificateSoaModelV2.getCertType()) || CertificateTypeEnum.HMT_ID_CARD.geteName().equals(memberCertificateSoaModelV2.getCertType()))
                                                && (memberInfo.getCLastName() + memberInfo.getCFirstName()).equals(queryCommonPersonInfo.getPassengerName()) && enName) {
                                            isOwn = true;
                                        } else if (!CertificateTypeEnum.ID_CARD.geteName().equals(memberCertificateSoaModelV2.getCertType()) && !CertificateTypeEnum.HMT_ID_CARD.geteName().equals(memberCertificateSoaModelV2.getCertType())
                                                && CRMReqUtil.matchEnName(memberInfo, queryCommonPersonInfo)) {
                                            // 其他证件匹配英文姓名
                                            isOwn = true;
                                            generalContactCertInfo.setUseScore(true);// 本人可以使用积分
                                        }
                                    }
                                }
                            }
                        }
                        priority++;
                        queryCommonPersonInfo.setIsOwn(isOwn);
                        if (isOwn) {
                            queryCommonPersonInfo.setPriority(0);
                        }
                    }
                }
            }

        }
        //将会员本人的证件排列在列表前面
        if (CollectionUtils.isNotEmpty(personInfoList)) {
            personInfoList.sort(Comparator.comparing(QueryCommonPersonInfo::getPriority));
        }
    }

    private QueryCommonPersonInfo addOwnerCommonPerson(ChannelInfo channelInfo, QueryAccountInfoResDto ptMemberDetail) {
        QueryCommonPersonInfo personInfo = new QueryCommonPersonInfo();
        MemberBasicInfoModel basicInfoSoaModel = ptMemberDetail.getBasicInfo();
        String ffpId = ptMemberDetail.getMemberId() + "";
        personInfo.setSex(SexEnum.MALE.desc.equals(basicInfoSoaModel.getSex()) ? "M" : "F");
        personInfo.setBirthdate(basicInfoSoaModel.getBirthday());
        personInfo.setFfCardNo("HO" + ptMemberDetail.getMemberCardNo());
        personInfo.setChannelCustomerNo(ffpId);
        personInfo.setChannelCustomerType("CRM");
        if (StringUtils.isNotBlank(basicInfoSoaModel.getCLastName())&&StringUtils.isNotBlank(basicInfoSoaModel.getCFirstName())){
            personInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
        }
        personInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
        personInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
        personInfo.setCommonContactId(-1);
        CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertRequest
                = CRMReqUtil.buildCommCrmMemberReq(HoAirIpUtil.getLocalIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        QueryMemberCertificateReqDto queryCertDto = new QueryMemberCertificateReqDto();
        queryCertDto.setMemberId(Integer.valueOf(ffpId));
        queryCertRequest.setData(queryCertDto);

        CrmMemberBaseApiResponse<QueryMemberCertificateResDto> queryCertResponse = memberService.toCatchMemberCertList(queryCertRequest, false);

        List<MemberCertificateResModel> newCertList = new ArrayList<>();
        List<MemberCertificateSoaModelV2> certificateInfoList = new ArrayList<>();
        List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
        List<MemberCertificateResModel> certificateList = queryCertResponse.getData().getCertificateList();
        //同一种证件类型,多个证件号,取最大的证件有效期
        if (CollectionUtils.isNotEmpty(certificateList)) {
            Map<String, List<MemberCertificateResModel>> memberCertificateSoaModelV2Map = certificateList.stream().collect(Collectors.groupingBy(MemberCertificateResModel::getCertificateType));
            for (Map.Entry<String, List<MemberCertificateResModel>> entry : memberCertificateSoaModelV2Map.entrySet()) {
                List<MemberCertificateResModel> memberCertificateSoaModels = entry.getValue();
                if (CollectionUtils.isNotEmpty(memberCertificateSoaModels)) {
                    // 分离 validDate 为 null 的对象
                    Map<Boolean, List<MemberCertificateResModel>> partitioned = memberCertificateSoaModels.stream()
                            .collect(Collectors.partitioningBy(m -> m.getValidDate() != null));

                    List<MemberCertificateResModel> validDateNullList = partitioned.get(false); // validDate 为 null 的对象
                    List<MemberCertificateResModel> validDateNotNullList = partitioned.get(true); // validDate 不为 null 的对象

                    MemberCertificateResModel memberCertificateResModel = validDateNotNullList.stream().max(Comparator.comparing(MemberCertificateResModel::getValidDate)).orElse(null);
                    if (memberCertificateResModel != null) {
                        newCertList.add(memberCertificateResModel);
                    }
                    newCertList.addAll(validDateNullList);
                }
            }
            certificateInfoList = newCertList.stream().map(MemberCertificateResModel::convertToMemberCertificateSoaModelV2).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(certificateInfoList)) {
            for (MemberCertificateSoaModelV2 memberCertificateSoaModelV2 : certificateInfoList) {
                GeneralContactCertInfo contactCertInfo = new GeneralContactCertInfo();
                contactCertInfo.setCertValidity(memberCertificateSoaModelV2.getValidDate());
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkType(memberCertificateSoaModelV2.getCertificateType());
                contactCertInfo.setCertType(certificateTypeEnum.getShowCode());
                contactCertInfo.setCertNo(memberCertificateSoaModelV2.getCertificateNumber());
                contactCertInfo.setUseScore(true);
                TCountryReqDTO tCountryReqDTO = new TCountryReqDTO();
                tCountryReqDTO.setCountryCode(memberCertificateSoaModelV2.getSigningAuthority());
                List<TCountryDTO> tCountryDTOS = basicService.queryCountries(HoAirIpUtil.getLocalIp(), channelInfo.getHeadChannelCode(), tCountryReqDTO);
                if (CollectionUtils.isNotEmpty(tCountryDTOS)) {
                    contactCertInfo.setBelongCountryName(tCountryDTOS.get(0).getCountryName());
                    contactCertInfo.setBelongCountry(tCountryDTOS.get(0).getCountryCode());
                }
                contactCertList.add(contactCertInfo);

            }
            //联系方式处理
            personInfo.setCountryTelCode("86");
            MemberContactModel mobile = CRMReqUtil.getContactInfo(ptMemberDetail.getContactInfos(), ContactTypeEnum.MOBILE.geteName());
            if (mobile != null) {
                CrmPhoneInfo crmPhoneInfo = CRMReqUtil.spiltPhoneNum(mobile.getContactValue());
                personInfo.setCountryTelCode(crmPhoneInfo.getAreaId());
                personInfo.setHandphoneNo(crmPhoneInfo.getPhone());
            }
        }
        personInfo.setContactCertList(contactCertList);
        //国籍以及签发国处理，目前无会员国籍，暂时使用签发国代替
        GeneralContactCertInfo generalContactCertInfo = filterGeneralContactCertInfo(contactCertList);
        if (generalContactCertInfo != null) {
            //如果是身份证件，国籍默认都是CN
            if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                personInfo.setBelongCountry("CN");
                if (StringUtils.isBlank(personInfo.getBirthdate())){
                  String  birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                  personInfo.setBirthdate(birthDate);
                }
            } else {
                personInfo.setBelongCountry(generalContactCertInfo.getBelongCountry());
            }
        }
        if (!StringUtils.isBlank(basicInfoSoaModel.getNationality())){
            personInfo.setNationality(basicInfoSoaModel.getNationality());
        }else {
            personInfo.setNationality("CN");
        }
        TCountryReqDTO tCountryReqDTO = new TCountryReqDTO();
        tCountryReqDTO.setCountryCode(personInfo.getNationality());
        List<TCountryDTO> tCountryDTOS = basicService.queryCountries(HoAirIpUtil.getLocalIp(), channelInfo.getHeadChannelCode(), tCountryReqDTO);
        if (CollectionUtils.isNotEmpty(tCountryDTOS)) {
            personInfo.setNationalityName(tCountryDTOS.get(0).getCountryName());
        }
        if (StringUtils.isNotBlank(personInfo.getBirthdate())){
            personInfo.setPassengerType(adjustPassType(personInfo.getBirthdate(), DateUtil.getCurrentDateStr()));
        }else {
            personInfo.setPassengerType(PassengerTypeEnum.ADT.getPassType());
        }
        personInfo.setIsOwn(true);
        personInfo.setPriority(0);
        return personInfo;
        }



    private GeneralContactCertInfo filterGeneralContactCertInfo(List<GeneralContactCertInfo> addContactCertList) {
        GeneralContactCertInfo generalContactCertInfo = null;
        generalContactCertInfo = addContactCertList.stream().filter(cert -> CertificateTypeEnum.ID_CARD.getShowCode().equals(cert.getCertType())).findFirst().orElse(null);
        if (generalContactCertInfo != null) {
            return generalContactCertInfo;
        }
        generalContactCertInfo = addContactCertList.stream().filter(cert -> CertificateTypeEnum.PASSPORT.getShowCode().equals(cert.getCertType())).findFirst().orElse(null);
        if (generalContactCertInfo != null) {
            return generalContactCertInfo;
        }
        List<GeneralContactCertInfo> list = addContactCertList.stream().filter(cert -> !CertificateTypeEnum.ID_CARD.getShowCode().equals(cert.getCertType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

        /**
         * @param ffpId
         * @param modifyPerson          变动乘机人信息
         * @param oldGeneralContactInfo 原始乘机人信息
         * @param channelCode
         * @param userNo
         * @return GeneralContactRequest
         * @description
         * <AUTHOR>
         * @date 2024/12/14 14:32
         **/
        private GeneralContactRequest createGeneralContactRequest (String ffpId, ModifyPersonV2
        modifyPerson, GeneralContactInfo oldGeneralContactInfo, String channelCode, String userNo){
            QueryCommonPersonInfo commonPersonInfo = modifyPerson.getCommonPersonInfo();
            if (null == commonPersonInfo) {
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "乘客信息不能为空");
            }
            if (commonPersonInfo.getCommonContactId() == null) {
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "记录不存在");
            }
            GeneralContactInfo newGeneralContactInfo = new GeneralContactInfo();
            BeanUtils.copyProperties(oldGeneralContactInfo, newGeneralContactInfo);
            BeanUtils.copyProperties(commonPersonInfo, newGeneralContactInfo);
            String birthDate = commonPersonInfo.getBirthdate();
            String sex = commonPersonInfo.getSex();
            //校验中文姓名
            if (StringUtils.isNotBlank(commonPersonInfo.getPassengerName())) {
                String passengerName = commonPersonInfo.getPassengerName();
                if (passengerName.contains("  ")) {
                    throw MultiLangServiceException.fail("乘客中文姓名不能有连续空格.");
                }
                if (passengerName.length() > 11 || passengerName.length() == 0) {
                    throw MultiLangServiceException.fail("乘客中文姓名长度为1-11.");
                }
                passengerName = AirStringUtil.removeNullTable(passengerName);
                passengerName = passengerName.replace("/", "");
                if (AirStringUtil.checkExistNumber(passengerName) || AirStringUtil.checkExistSpecialChar(passengerName)) {
                    throw MultiLangServiceException.fail("乘客中文姓名包含了数字或非法字符.");
                }
                commonPersonInfo.setPassengerName(passengerName.toUpperCase());
            }
            //国际区号
            if (StringUtils.isNotBlank(commonPersonInfo.getCountryTelCode())) {
                //校验手机区号位数
                Pattern telCodePattern = Pattern.compile(PatternCommon.TELEPHONE_CODE_MATCH);
                Matcher telCodematcher = telCodePattern.matcher(commonPersonInfo.getCountryTelCode());
                if (!telCodematcher.matches()) {
                    throw MultiLangServiceException.fail("请输入正确的国际区号！");
                }
            }
            //手机号解密操作
            if (StringUtils.isNotBlank(commonPersonInfo.getHandphoneNo())) {
                String handPhoneNo = AESTool.decrypt(commonPersonInfo.getHandphoneNo(), appConfig.getHoAesKey());
                if (handPhoneNo.contains("*")) {
                    throw MultiLangServiceException.fail("请勿包含非法字符");
                }
                newGeneralContactInfo.setHandphoneNo(handPhoneNo);
                boolean rightTelNum;
                if (StringUtils.isNotBlank(commonPersonInfo.getCountryTelCode())) {
                    if ("86".equals(commonPersonInfo.getCountryTelCode())) {
                        Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
                        Matcher matcher = pattern.matcher(handPhoneNo);
                        rightTelNum = matcher.matches();
                    } else {
                        //国际手机号只校验位数
                        Pattern pattern = Pattern.compile(PatternCommon.MOBILE_GLOBAL_MATCH);
                        Matcher matcher = pattern.matcher(handPhoneNo);
                        rightTelNum = matcher.matches();
                    }
                    if (!rightTelNum) {
                        throw MultiLangServiceException.fail("请输入正确的手机号!");
                    }
                }
            }
            //证件号解密操作
            List<GeneralContactCertInfo> certList = commonPersonInfo.getContactCertList();
            if (CollectionUtils.isNotEmpty(certList)) {
                //排除本地删除的证件信息 没有记录ID且被标记为删除的证件
                certList.removeIf(GeneralContactCertInfo::filterRemovedCert);
                for (GeneralContactCertInfo generalContactCertInfo : certList) {
                    if (StringUtils.isBlank(generalContactCertInfo.getCertType())) {
                        throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "证件类型不可为空");
                    }
                    //远程删除的证件信息,证件删除的无需进行格式校验
                    if (generalContactCertInfo.getIsRemove() != null && generalContactCertInfo.getIsRemove()) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(generalContactCertInfo.getCertNo())) {
                        String certNo = AESTool.decrypt(generalContactCertInfo.getCertNo(), appConfig.getHoAesKey());
                        if (certNo.contains("*")) {
                            throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "请勿包含非法字符");
                        }
                        generalContactCertInfo.setCertNo(certNo);
                        //身份证
                        if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certNo)) {
                            if (!Pattern.matches(PatternCommon.ID_NUMBER, certNo)) {
                                throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.ID_CARD.getDesc()));
                            }
                            //根据身份证信息获取性别和出生日期
                            birthDate = CertUtil.certNoToDate(certNo);
                            sex = CertUtil.checkSex(certNo);
                        }
                        //港澳居民居住证
                        if (CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            if (!Pattern.matches(PatternCommon.HMT_ID_CARD, generalContactCertInfo.getCertNo())) {
                                throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                            }
                            //根据身份证信息获取性别和出生日期
                            birthDate = CertUtil.certNoToDate(certNo);
                            sex = CertUtil.checkSex(certNo);
                        }
                        // 若类型为BC:出生医学证明，不校验，
                        if (CertificateTypeEnum.BC.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            if (AccountBffConfig.PASSENGER_TYPE_ADT.equals(commonPersonInfo.getPassengerType())) {
                                throw MultiLangServiceException.fail("成人不允许添加出生医学证明！");
                            }
                            generalContactCertInfo.setBelongCountry("CN");
                            generalContactCertInfo.setCertValidity("2099-12-31");
                            generalContactCertInfo.setCertNo(commonPersonInfo.getPassengerName() + birthDate.replaceAll("-", ""));//姓名加出生日期
                        }
                    } else {
                        GeneralContactCertInfo oldGeneralContactCertInfo = oldGeneralContactInfo.getContactCertList().stream().filter(cert -> cert.getGeneralContactCertId().equals(generalContactCertInfo.getGeneralContactCertId())).findFirst().orElse(null);
                        if (oldGeneralContactCertInfo != null) {
                            generalContactCertInfo.setCertNo(oldGeneralContactCertInfo.getCertNo());
                        }
                    }
                    //非身份证类型证件需要判断其他信息内容
                    if (!generalContactCertInfo.getCertType().equals(CertificateTypeEnum.ID_CARD.getShowCode())) {
                        if (StringUtils.isBlank(generalContactCertInfo.getBelongCountry())) {
                            throw MultiLangServiceException.fail("证件签发国不能为空！");
                        }
                        if (StringUtils.isBlank(generalContactCertInfo.getCertValidity())) {
                            throw MultiLangServiceException.fail("证件有效期不能为空！");
                        } else {
                            if (!Pattern.matches(PatternCommon.DATE_NORMAL, generalContactCertInfo.getCertValidity())) {
                                throw MultiLangServiceException.fail("证件有效期格式为yyyy-MM-dd！");
                            }
                        }
                    }
                }
            }
            //参数构造
            String passType = commonPersonInfo.getPassengerType();
            //出生日期以及年龄检查
            if (StringUtils.isNotBlank(birthDate)) {
                if (!Pattern.matches(PatternCommon.DATE_NORMAL, birthDate)) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "日期格式为yyyy-MM-dd！");
                }
                String date = DateUtil.getCurrentDateStr();
                int age = DateUtil.getAgeByBirthIncludeBirthDay(birthDate, date, DateUtil.YYYY_MM_DD_PATTERN);
                if (age < 0) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "出生日期不合法！");
                }
                passType = toPassType(age);
            }
            List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
            newGeneralContactInfo.setContactCertList(certList);
            newGeneralContactInfo.setChannelCustomerType("CRM");
            newGeneralContactInfo.setChannelCustomerNo(ffpId);
            newGeneralContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
            newGeneralContactInfo.setBirthdate(birthDate);
            newGeneralContactInfo.setSex(sex);
            newGeneralContactInfo.setPassengerType(passType);
            //中文姓名为空的情况下，默认使用英文姓名赋值
            //中文姓名包含“/”时，且英文姓名不为空时已英文姓名为主
            //其他情况暂时原样赋值
            if (StringUtils.isBlank(newGeneralContactInfo.getPassengerName()) && !(StringUtils.isAnyBlank(newGeneralContactInfo.getPassEnNameS(), newGeneralContactInfo.getPassEnNameF()))) {
                newGeneralContactInfo.setPassengerName(newGeneralContactInfo.getPassEnNameS() + "/" + newGeneralContactInfo.getPassEnNameF());
            } else if (newGeneralContactInfo.getPassengerName().contains("/")) {//如果是英文的名字，放到对应字段里
                String[] enName = newGeneralContactInfo.getPassengerName().split("/");
                if (StringUtils.isAnyBlank(newGeneralContactInfo.getPassEnNameS(), newGeneralContactInfo.getPassEnNameF())) {
                    newGeneralContactInfo.setPassEnNameS(enName[0]);
                    newGeneralContactInfo.setPassEnNameF(enName[1]);
                } else {
                    newGeneralContactInfo.setPassengerName(newGeneralContactInfo.getPassEnNameS() + "/" + newGeneralContactInfo.getPassEnNameF());
                }
            }
            generalContactInfoList.add(newGeneralContactInfo);
            GeneralContactRequest generalContactRequest = new GeneralContactRequest("V1.0", channelCode, userNo);
            generalContactRequest.setAddFlag(modifyPerson.getAddFlag());
            generalContactRequest.setGeneralContactList(generalContactInfoList);
            return generalContactRequest;
        }

        /**
         * 第三方请求参数构造 新增常用乘机人
         *
         * @param modifyPerson
         * @param channelCode
         * @param userNo
         * @return
         */
        private GeneralContactRequest createGeneralContactRequest (String ffpId, ModifyPersonV2 modifyPerson, String
        channelCode, String userNo){
            QueryCommonPersonInfo commonPersonInfo = modifyPerson.getCommonPersonInfo();
            if (null == commonPersonInfo) {
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "乘客信息不能为空");
            }
            toCheckPassengerName(commonPersonInfo);
            if (StringUtils.isNotBlank(commonPersonInfo.getHandphoneNo())) {
                commonPersonInfo.setHandphoneNo(AESTool.decrypt(commonPersonInfo.getHandphoneNo(), appConfig.getHoAesKey()));
            }
            if (CollectionUtils.isNotEmpty(commonPersonInfo.getContactCertList())) {
                commonPersonInfo.getContactCertList().forEach(el -> {
                    if (StringUtils.isNotEmpty(el.getCertNo())) {
                        el.setCertNo(AESTool.decrypt(el.getCertNo(), appConfig.getHoAesKey()));
                    }
                });
            }
            if (StringUtils.isNotBlank(commonPersonInfo.getPassengerName())) {
                commonPersonInfo.setPassengerName(commonPersonInfo.getPassengerName().toUpperCase());
            }
            List<GeneralContactCertInfo> contactCertList = commonPersonInfo.getContactCertList();
            List<GeneralContactCertInfo> tmpContactList = new ArrayList<>(contactCertList);
            String birthDate = commonPersonInfo.getBirthdate();
            String sex = commonPersonInfo.getSex();
            int age = 0;
            boolean isRemove = modifyPerson.getIsRemove() != null && modifyPerson.getIsRemove();
            // 国际航线
            if (!isRemove) {
                if (CollectionUtils.isEmpty(contactCertList)) {
                    throw MultiLangServiceException.fail("证件信息不能为空！");
                }
                //排除本地删除的证件信息
                contactCertList.removeIf(GeneralContactCertInfo::filterRemovedCert);
                if (CollectionUtils.isEmpty(contactCertList)) {
                    throw MultiLangServiceException.fail("请至少添加一个证件！");
                }
                if (StringUtils.isEmpty(commonPersonInfo.getPassengerName()) && (StringUtils.isEmpty(commonPersonInfo.getPassEnNameF()) || StringUtils.isEmpty(commonPersonInfo.getPassEnNameS()))) {
                    throw MultiLangServiceException.fail("中文姓名与英文姓名不能同时为空！");
                }
                if (StringUtils.isEmpty(commonPersonInfo.getCountryTelCode())) {
                    throw MultiLangServiceException.fail("国际区号不能为空！");

                }
                if (StringUtils.isEmpty(commonPersonInfo.getHandphoneNo())) {
                    throw MultiLangServiceException.fail("手机号不能为空！");
                }
                //校验手机区号位数
                Pattern telCodePattern = Pattern.compile(PatternCommon.TELEPHONE_CODE_MATCH);
                Matcher telCodematcher = telCodePattern.matcher(commonPersonInfo.getCountryTelCode());
                if (!telCodematcher.matches()) {
                    throw MultiLangServiceException.fail("请输入正确的国际区号！");
                }
                boolean rightTelNum;
                if ("86".equals(commonPersonInfo.getCountryTelCode())) {
                    Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
                    Matcher matcher = pattern.matcher(commonPersonInfo.getHandphoneNo());
                    rightTelNum = matcher.matches();
                } else {
                    //国际手机号只校验位数
                    Pattern pattern = Pattern.compile(PatternCommon.MOBILE_GLOBAL_MATCH);
                    Matcher matcher = pattern.matcher(commonPersonInfo.getHandphoneNo());
                    rightTelNum = matcher.matches();
                }
                if (!rightTelNum) {
                    throw MultiLangServiceException.fail("请输入正确的手机号!");
                }

                boolean hasId = false;
                for (GeneralContactCertInfo generalContactCertInfo : contactCertList) {
                    if (!(generalContactCertInfo.getIsRemove() != null && generalContactCertInfo.getIsRemove())) {
                        //身份证
                        if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            if (!Pattern.matches(PatternCommon.ID_NUMBER, generalContactCertInfo.getCertNo())) {
                                throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.ID_CARD.getDesc()));
                            }
                            //根据身份证信息获取性别和出生日期
                            birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                            sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                            hasId = true;
                        } else {
                            if (CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                if (!Pattern.matches(PatternCommon.HMT_ID_CARD, generalContactCertInfo.getCertNo())) {
                                    throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                                }
                                //根据身份证信息获取性别和出生日期
                                birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                                sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                                hasId = true;
                            }
                            // 若类型为BC:出生医学证明，不校验，
                            if (CertificateTypeEnum.BC.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                if (AccountBffConfig.PASSENGER_TYPE_ADT.equals(commonPersonInfo.getPassengerType())) {
                                    throw MultiLangServiceException.fail("成人不允许添加出生医学证明！");
                                }
                                generalContactCertInfo.setBelongCountry("CN");
                                generalContactCertInfo.setCertValidity("2099-12-31");
                                generalContactCertInfo.setCertNo(commonPersonInfo.getPassengerName() + birthDate.replaceAll("-", ""));//姓名加出生日期
                                continue;
                            }
                            if (StringUtils.isEmpty(generalContactCertInfo.getCertType()) || (StringUtils.isNotEmpty(generalContactCertInfo.getCertType()) && !generalContactCertInfo.getCertType().equals(CertificateTypeEnum.ID_CARD.getShowCode()))) {
                                if (StringUtils.isEmpty(generalContactCertInfo.getBelongCountry())) {
                                    throw MultiLangServiceException.fail("证件签发国不能为空！");
                                }
                                if (StringUtils.isEmpty(generalContactCertInfo.getCertValidity())) {
                                    throw MultiLangServiceException.fail("证件有效期不能为空！");
                                } else {
                                    if (!Pattern.matches(PatternCommon.DATE_NORMAL, generalContactCertInfo.getCertValidity())) {
                                        throw MultiLangServiceException.fail("证件有效期格式为yyyy-MM-dd！");
                                    }
                                }
                            }
                        }
                    }
                }
                //不存在身份证的情况下，检验出生日期
                if (!hasId) {
                    if (StringUtils.isEmpty(birthDate)) {
                        throw MultiLangServiceException.fail("出生日期不能为空！");
                    } else {
                        if (!Pattern.matches(PatternCommon.DATE_NORMAL, birthDate)) {
                            throw MultiLangServiceException.fail("出生日期格式为yyyy-MM-dd！");
                        }
                    }
                }
                String date = DateUtil.getCurrentDateStr();
                // 存在航班日期使用航班日期计算年龄 否则使用当前时间计算
                if (StringUtils.isNotBlank(modifyPerson.getFlightDate())) {
                    date = modifyPerson.getFlightDate();
                }
                age = toCheckBirthday(commonPersonInfo, date);
            }

            String passType = toPassType(age);
            //参数构造
            List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
            GeneralContactInfo generalContactInfo = new GeneralContactInfo();
            BeanUtils.copyProperties(commonPersonInfo, generalContactInfo);
            generalContactInfo.setContactCertList(tmpContactList);
            generalContactInfo.setChannelCustomerType("CRM");
            generalContactInfo.setChannelCustomerNo(ffpId);
            generalContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
            generalContactInfo.setSex(sex);
            generalContactInfo.setBirthdate(birthDate);
            generalContactInfo.setPassengerType(passType);
            //中文姓名为空的情况下，默认使用英文姓名赋值
            //中文姓名包含“/”时，且英文姓名不为空时已英文姓名为主
            //其他情况暂时原样赋值
            if (StringUtils.isEmpty(generalContactInfo.getPassengerName())
                    && !(StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF()))) {
                generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
            } else if (generalContactInfo.getPassengerName().contains("/")) {//如果是英文的名字，放到对应字段里
                String[] enName = generalContactInfo.getPassengerName().split("/");
                if (StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF())) {
                    generalContactInfo.setPassEnNameS(enName[0]);
                    generalContactInfo.setPassEnNameF(enName[1]);
                } else {
                    generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
                }
            }
            generalContactInfoList.add(generalContactInfo);
            GeneralContactRequest generalContactRequest = new GeneralContactRequest("V1.0", channelCode, userNo);
            generalContactRequest.setAddFlag(modifyPerson.getAddFlag());
            generalContactRequest.setGeneralContactList(generalContactInfoList);
            return generalContactRequest;
        }

        /**
         * 第三方请求参数构造 新增，修改常用乘机人
         *
         * @param modifyPerson
         * @param channelCode
         * @param userNo
         * @return
         */
        private GeneralContactRequest createGeneralContactRequest (String ffpId, ModifyPersonV2 modifyPerson, String
        channelCode, String userNo,boolean encrypted){
            QueryCommonPersonInfo commonPersonInfo = modifyPerson.getCommonPersonInfo();
            if (null == commonPersonInfo) {
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "乘客信息不能为空");
            }
            toCheckPassengerName(commonPersonInfo);
            if (encrypted) {
                commonPersonInfo.setHandphoneNo(decrypt(commonPersonInfo.getHandphoneNo()));
                if (CollectionUtils.isNotEmpty(commonPersonInfo.getContactCertList())) {
                    commonPersonInfo.getContactCertList().forEach(el -> {
                        if (StringUtils.isNotEmpty(el.getCertNo())) {
                            el.setCertNo(decrypt(el.getCertNo()));
                        }
                    });
                }
            }
            if (StringUtils.isNotBlank(commonPersonInfo.getPassengerName())) {
                commonPersonInfo.setPassengerName(commonPersonInfo.getPassengerName().toUpperCase());
            }
            List<GeneralContactCertInfo> contactCertList = commonPersonInfo.getContactCertList();
            List<GeneralContactCertInfo> tmpContactList = new ArrayList<>(contactCertList);
            String birthDate = commonPersonInfo.getBirthdate();
            String sex = commonPersonInfo.getSex();
            int age = 0;
            boolean isRemove = modifyPerson.getIsRemove() != null && modifyPerson.getIsRemove();
            // 国际航线
            if (!isRemove) {
                if (CollectionUtils.isEmpty(contactCertList)) {
                    throw MultiLangServiceException.fail("证件信息不能为空！");
                }
                //排除本地删除的证件信息
                contactCertList.removeIf(GeneralContactCertInfo::filterRemovedCert);
                if (CollectionUtils.isEmpty(contactCertList)) {
                    throw MultiLangServiceException.fail("请至少添加一个证件！");
                }
                if (StringUtils.isEmpty(commonPersonInfo.getPassengerName()) && (StringUtils.isEmpty(commonPersonInfo.getPassEnNameF()) || StringUtils.isEmpty(commonPersonInfo.getPassEnNameS()))) {
                    throw MultiLangServiceException.fail("中文姓名与英文姓名不能同时为空！");
                }
                if (StringUtils.isEmpty(commonPersonInfo.getCountryTelCode())) {
                    throw MultiLangServiceException.fail("国际区号不能为空！");

                }
                if (StringUtils.isEmpty(commonPersonInfo.getHandphoneNo())) {
                    throw MultiLangServiceException.fail("手机号不能为空！");
                }
                //校验手机区号位数
                Pattern telCodePattern = Pattern.compile(PatternCommon.TELEPHONE_CODE_MATCH);
                Matcher telCodematcher = telCodePattern.matcher(commonPersonInfo.getCountryTelCode());
                if (!telCodematcher.matches()) {
                    throw MultiLangServiceException.fail("请输入正确的国际区号！");
                }
                boolean rightTelNum;
                if ("86".equals(commonPersonInfo.getCountryTelCode())) {
                    Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
                    Matcher matcher = pattern.matcher(commonPersonInfo.getHandphoneNo());
                    rightTelNum = matcher.matches();
                } else {
                    //国际手机号只校验位数
                    Pattern pattern = Pattern.compile(PatternCommon.MOBILE_GLOBAL_MATCH);
                    Matcher matcher = pattern.matcher(commonPersonInfo.getHandphoneNo());
                    rightTelNum = matcher.matches();
                }
                if (!rightTelNum) {
                    throw MultiLangServiceException.fail("请输入正确的手机号!");
                }

                boolean hasId = false;
                for (GeneralContactCertInfo generalContactCertInfo : contactCertList) {
                    if (!(generalContactCertInfo.getIsRemove() != null && generalContactCertInfo.getIsRemove())) {
                        //身份证
                        if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            if (!Pattern.matches(PatternCommon.ID_NUMBER, generalContactCertInfo.getCertNo())) {
                                throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.ID_CARD.getDesc()));
                            }
                            //根据身份证信息获取性别和出生日期
                            birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                            sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                            hasId = true;
                        } else {
                            if (CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                if (!Pattern.matches(PatternCommon.HMT_ID_CARD, generalContactCertInfo.getCertNo())) {
                                    throw MultiLangServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                                }
                                //根据身份证信息获取性别和出生日期
                                birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                                sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                                hasId = true;
                            }
                            // 若类型为BC:出生医学证明，不校验，
                            if (CertificateTypeEnum.BC.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                if (AccountBffConfig.PASSENGER_TYPE_ADT.equals(commonPersonInfo.getPassengerType())) {
                                    throw MultiLangServiceException.fail("成人不允许添加出生医学证明！");
                                }
                                generalContactCertInfo.setBelongCountry("CN");
                                generalContactCertInfo.setCertValidity("2099-12-31");
                                generalContactCertInfo.setCertNo(commonPersonInfo.getPassengerName() + birthDate.replaceAll("-", ""));//姓名加出生日期
                                continue;
                            }
                            if (StringUtils.isEmpty(generalContactCertInfo.getCertType()) || (StringUtils.isNotEmpty(generalContactCertInfo.getCertType()) && !generalContactCertInfo.getCertType().equals(CertificateTypeEnum.ID_CARD.getShowCode()))) {
                                if (StringUtils.isEmpty(generalContactCertInfo.getBelongCountry())) {
                                    throw MultiLangServiceException.fail("证件签发国不能为空！");
                                }
                                if (StringUtils.isEmpty(generalContactCertInfo.getCertValidity())) {
                                    throw MultiLangServiceException.fail("证件有效期不能为空！");
                                } else {
                                    if (!Pattern.matches(PatternCommon.DATE_NORMAL, generalContactCertInfo.getCertValidity())) {
                                        throw MultiLangServiceException.fail("证件有效期格式为yyyy-MM-dd！");
                                    }
                                }
                            }
                        }
                    }
                }
                //不存在身份证的情况下，检验出生日期
                if (!hasId) {
                    if (StringUtils.isEmpty(birthDate)) {
                        throw MultiLangServiceException.fail("出生日期不能为空！");
                    } else {
                        if (!Pattern.matches(PatternCommon.DATE_NORMAL, birthDate)) {
                            throw MultiLangServiceException.fail("出生日期格式为yyyy-MM-dd！");
                        }
                    }
                }
                String date = DateUtil.getCurrentDateStr();
                // 存在航班日期使用航班日期计算年龄 否则使用当前时间计算
                if (StringUtils.isNotBlank(modifyPerson.getFlightDate())) {
                    date = modifyPerson.getFlightDate();
                }
                age = toCheckBirthday(commonPersonInfo, date);
            }

            String passType = toPassType(age);
            //参数构造
            List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
            GeneralContactInfo generalContactInfo = new GeneralContactInfo();
            BeanUtils.copyProperties(commonPersonInfo, generalContactInfo);
            generalContactInfo.setContactCertList(tmpContactList);
            generalContactInfo.setChannelCustomerType("CRM");
            generalContactInfo.setChannelCustomerNo(ffpId);
            generalContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
            generalContactInfo.setSex(sex);
            generalContactInfo.setBirthdate(birthDate);
            generalContactInfo.setPassengerType(passType);
            //中文姓名为空的情况下，默认使用英文姓名赋值
            //中文姓名包含“/”时，且英文姓名不为空时已英文姓名为主
            //其他情况暂时原样赋值
            if (StringUtils.isEmpty(generalContactInfo.getPassengerName())
                    && !(StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF()))) {
                generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
            } else if (generalContactInfo.getPassengerName().contains("/")) {//如果是英文的名字，放到对应字段里
                String[] enName = generalContactInfo.getPassengerName().split("/");
                if (StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF())) {
                    generalContactInfo.setPassEnNameS(enName[0]);
                    generalContactInfo.setPassEnNameF(enName[1]);
                } else {
                    generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
                }
            }
            generalContactInfoList.add(generalContactInfo);
            GeneralContactRequest generalContactRequest = new GeneralContactRequest("V1.0", channelCode, userNo);
            generalContactRequest.setAddFlag(modifyPerson.getAddFlag());
            generalContactRequest.setGeneralContactList(generalContactInfoList);
            return generalContactRequest;
        }

        /**
         * @param commonPersonInfo
         * @param date
         * @return void
         * <AUTHOR>
         * @Description 校验用户出生日期合法性
         * @Date 9:10 2024/7/2
         **/
        private int toCheckBirthday (QueryCommonPersonInfo commonPersonInfo, String date){
            if (null == commonPersonInfo || CollectionUtils.isEmpty(commonPersonInfo.getContactCertList()) || StringUtils.isEmpty(date)) {
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "输入参数有问题");
            }
            String birthDate = commonPersonInfo.getBirthdate();
            if (StringUtils.isEmpty(birthDate)) {
                throw MultiLangServiceException.fail("用户出生日期不可为空");
            }

            Optional<GeneralContactCertInfo> certId = commonPersonInfo.getContactCertList().stream().filter(el -> CertificateTypeEnum.ID_CARD.getShowCode().equals(el.getCertType())).findFirst();
            if (certId.isPresent()) {
                String idCertNo = certId.get().getCertNo();
                if (StringUtils.isEmpty(idCertNo)) {
                    throw ServiceException.fail("【#TYPE#】号码不可为空！".replace("#TYPE#", CertificateTypeEnum.ID_CARD.getDesc()));
                }
                birthDate = CertUtil.certNoToDate(idCertNo);
                if (!commonPersonInfo.getBirthdate().equals(birthDate)) {
                    throw ServiceException.fail("您填写的出生日期与身份证中的出生日期不一致");
                }
            }
            Optional<GeneralContactCertInfo> certHMT = commonPersonInfo.getContactCertList().stream().filter(el -> CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(el.getCertType())).findFirst();
            if (certHMT.isPresent()) {
                String hmtCertNo = certHMT.get().getCertNo();
                if (StringUtils.isEmpty(hmtCertNo)) {
                    throw ServiceException.fail("【#TYPE#】号码不可为空！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                }
                if (!(hmtCertNo.length() == 15 || hmtCertNo.length() == 18)) {
                    throw ServiceException.fail("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                }
                birthDate = CertUtil.certNoToDate(hmtCertNo);
                if (!commonPersonInfo.getBirthdate().equals(birthDate)) {
                    throw ServiceException.fail("您填写的出生日期与港澳居民居住证中的出生日期不一致");
                }
            }
            int age = DateUtil.getAgeByBirthIncludeBirthDay(birthDate, date, DateUtil.YYYY_MM_DD_PATTERN);
            if (age < 0) {
                throw ServiceException.fail("出生日期不合法！");
            }
            return age;
        }

        /**
         * @param commonPersonInfo
         * @return void
         * <AUTHOR>
         * @Description 校验用户姓名合法性
         * @Date 14:17 2024/6/20
         **/

        private void toCheckPassengerName (QueryCommonPersonInfo commonPersonInfo){
            if (StringUtils.isAllEmpty(commonPersonInfo.getPassengerName(), commonPersonInfo.getPassEnNameS(), commonPersonInfo.getPassEnNameF())) {
                throw MultiLangServiceException.fail("乘客姓名不可为空");
            }
            if (StringUtils.isEmpty(commonPersonInfo.getPassengerName()) && StringUtils.isAnyEmpty(commonPersonInfo.getPassEnNameS(), commonPersonInfo.getPassEnNameF())) {
                throw MultiLangServiceException.fail("乘客英文姓及英文名须同时存在");
            }
            //校验中文姓名
            if (StringUtils.isNotEmpty(commonPersonInfo.getPassengerName())) {
                String passengerName = commonPersonInfo.getPassengerName();
                if (passengerName.contains("  ")) {
                    throw MultiLangServiceException.fail("乘客中文姓名不能有连续空格.");
                }
                if (passengerName.length() > 11 || passengerName.length() == 0) {
                    throw MultiLangServiceException.fail("乘客中文姓名长度为1-11.");
                }
                passengerName = AirStringUtil.removeNullTable(passengerName);
                passengerName = passengerName.replace("/", "");
                if (AirStringUtil.checkExistNumber(passengerName) || AirStringUtil.checkExistSpecialChar(passengerName)) {
                    throw MultiLangServiceException.fail("乘客中文姓名包含了数字或非法字符.");
                }
            }
        }

        private List<QueryCommonPersonInfo> responseConvertToQueryCommonPersonInfo (QueryGeneralContactResponse
        queryGeneralContactResponse, BizDto bizDto){
            List<QueryCommonPersonInfo> personInfoList = new ArrayList<>();
            List<TCountryDTO> tCountryDTOList = basicService.queryCountries(HoAirIpUtil.getLocalIp(), bizDto.getHeadChannelCode(), new TCountryReqDTO());
            if (CollectionUtils.isNotEmpty(queryGeneralContactResponse.getGeneralContactList())) {
                for (GeneralContactInfo generalContactInfo : queryGeneralContactResponse.getGeneralContactList()) {
                    QueryCommonPersonInfo queryCommonPersonInfo = new QueryCommonPersonInfo();
                    BeanUtils.copyProperties(generalContactInfo, queryCommonPersonInfo);
                    //判断证件是否过期
                    if (CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                        toCheckCertExpired(queryCommonPersonInfo.getContactCertList());
                    }
                    if (CollectionUtils.isNotEmpty(tCountryDTOList) && CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                        queryCommonPersonInfo.getContactCertList().forEach(el -> {
                            tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(el.getBelongCountry())).findFirst().ifPresent(countryDTO -> el.setBelongCountryName(countryDTO.getCountryName()));
                        });
                    }
                    // 生僻字拼音转大写
                    if (StringUtils.isNotBlank(generalContactInfo.getPassengerName())) {
                        queryCommonPersonInfo.setPassengerName(generalContactInfo.getPassengerName().toUpperCase());
                    }
                    queryCommonPersonInfo.setCommonContactId(generalContactInfo.getGeneralContactId());
                    queryCommonPersonInfo.setIsOwn(false);
                    personInfoList.add(queryCommonPersonInfo);
                }
            }
            return personInfoList;
        }

        /**
         * @param contactCertList
         * @return void
         * <AUTHOR>
         * @Description 判断证件是否过期
         * @Date 14:52 2023/10/12
         **/

        private void toCheckCertExpired (List < GeneralContactCertInfo > contactCertList) {
            if (CollectionUtils.isEmpty(contactCertList)) {
                return;
            }
            contactCertList.forEach(el -> {
                if (StringUtils.isEmpty(el.getCertType())) {
                    el.setExpired(true);
                } else if (CertificateTypeEnum.ID_CARD.getShowCode().equals(el.getCertType())) {
                    el.setExpired(false);
                } else if (StringUtils.isEmpty(el.getCertValidity())) {
                    el.setExpired(true);
                } else {
                    LocalDate currDate = LocalDate.now();
                    LocalDate validDate = LocalDate.parse(el.getCertValidity(), DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_PATTERN));
                    el.setExpired(validDate.isBefore(currDate));
                }
            });
        }

        private QueryGeneralContactRequest genQueryGeneralContactRequest (String ffpId, ChannelInfo
        channelInfo, CommonPersonQuery commonPersonQuery){
            QueryGeneralContactRequest queryGeneralContactRequest = new QueryGeneralContactRequest("V1.0", channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), "CRM", "LastBooking");
            queryGeneralContactRequest.setChannelCustomerNo(ffpId);
            queryGeneralContactRequest.setIsGMJC(commonPersonQuery.getIsGmjc());

            // 多程参数未实装,前端未传值
            if (CollectionUtils.isNotEmpty(commonPersonQuery.getSegments())) {
                // 多程机票下单， 优先使用国际城市查询，其次使用港澳台城市查询
                for (QueryCommonPersonSegment segment : commonPersonQuery.getSegments()) {
                    ApiCityInfoDto dept = cacheService.getLocalCity(segment.getDepCityCode());
                    ApiCityInfoDto arr = cacheService.getLocalCity(segment.getArrCityCode());
                    boolean HKMCTWRegion = false;
                    boolean international = false;
                    if (TripTypeEnum.TRIP_TYPE_I.getCode().equals(dept.getIsInternational())) {
                        if (accountBffConfig.getHKMCTWRegions().contains(segment.getDepCityCode())) {
                            HKMCTWRegion = true;
                        } else {
                            international = true;
                        }
                    }
                    if (TripTypeEnum.TRIP_TYPE_I.getCode().equals(arr.getIsInternational())) {
                        if (accountBffConfig.getHKMCTWRegions().contains(segment.getArrCityCode())) {
                            HKMCTWRegion = true;
                        } else {
                            international = true;
                        }
                    }
                    segment.setHKMCTWRegion(HKMCTWRegion);
                    segment.setInterNational(international);
                }
                Optional<QueryCommonPersonSegment> segment = commonPersonQuery.getSegments().stream().filter(QueryCommonPersonSegment::isInterNational).findFirst();
                if (!segment.isPresent()) {
                    segment = commonPersonQuery.getSegments().stream().filter(QueryCommonPersonSegment::isHKMCTWRegion).findFirst();
                }
                if (!segment.isPresent()) {
                    segment = commonPersonQuery.getSegments().stream().findFirst();
                }
                if (segment.isPresent()) {
                    commonPersonQuery.setDepCityCode(segment.get().getDepCityCode());
                    commonPersonQuery.setArrCityCode(segment.get().getArrCityCode());
                }
                // 出发时间取最大的出发时间
                commonPersonQuery.getSegments().sort(Comparator.comparing(QueryCommonPersonSegment::getDepartureDate).reversed());
                commonPersonQuery.setDepartureDate(commonPersonQuery.getSegments().get(0).getDepartureDate());
            }
            queryGeneralContactRequest.setPageNo(1);
            queryGeneralContactRequest.setPageSize(50);
            if (CollectionUtils.isNotEmpty(commonPersonQuery.getCabinTypes())) {
                if (commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())
                        || commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())) {
                    queryGeneralContactRequest.setContactType(Arrays.asList("A", "B"));
                } else if (commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType())) {
                    queryGeneralContactRequest.setContactType(Arrays.asList("C", "D"));
                } else if (!Collections.disjoint(commonPersonQuery.getCabinTypes(), accountBffConfig.getThemeCouponList())) {
                    queryGeneralContactRequest.setContactType(commonPersonQuery.getCabinTypes());
                }
            }
            return queryGeneralContactRequest;
        }

        /**
         * 设置是否显示学生认证入口
         *
         * @param accountResp
         */
        private void checkStudentAuth (AccountInfoResp accountResp){
            if (accountResp.getVerifyStatus().equals(VerifyStatusEnum.PASS.code)) {
                accountResp.setIsStudentAuth(false);
                if (StringUtils.isNotEmpty(accountResp.getBirthDay())) {
                    int age = DateUtil.getAgeByBirthIncludeBirthDay(
                            accountResp.getBirthDay(), DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN), DateUtil.YYYY_MM_DD_PATTERN);
                    if (age >= 18 && age <= 24) {
                        accountResp.setIsStudentAuth(true);
                    }
                }
            } else {
                accountResp.setIsStudentAuth(true);
            }
        }

        /**
         * @param studentAuthDetailQueryResponse
         * @param studentAuthDetailResponse
         */
        private void setStudentAuthDetail (StudentAuthDetailQueryResponse
        studentAuthDetailQueryResponse, StudentAuthDetailResponse studentAuthDetailResponse){
            StudentVerifyStatusEnum studentVerifyStatusEnum = StudentVerifyStatusEnum.formatVerifyStatus(studentAuthDetailResponse.getStatus());
            studentAuthDetailQueryResponse.setAuthStatus(studentVerifyStatusEnum == null ? "UNKNOW" : studentVerifyStatusEnum.eName);
            studentAuthDetailQueryResponse.setAuthDesc(studentVerifyStatusEnum == null ? "未认证" : studentVerifyStatusEnum.desc);
            studentAuthDetailQueryResponse.setBeginDate(studentAuthDetailResponse.getAdmissionYear() == null ? "" : studentAuthDetailResponse.getAdmissionYear().toString() + "年");
            studentAuthDetailQueryResponse.setEndDate(studentAuthDetailResponse.getForecastGraduateYear() == null ? "" : studentAuthDetailResponse.getForecastGraduateYear().toString() + "年");
            studentAuthDetailQueryResponse.setRejectReason(studentAuthDetailResponse.getRejectReason());
            //判断是否失效
            if (studentVerifyStatusEnum != null && studentVerifyStatusEnum == StudentVerifyStatusEnum.EXPIRED) {
                Integer dateInt = Integer.parseInt(DateUtil.dateToString(new Date(), "YYYY"));
                if (studentAuthDetailResponse.getForecastGraduateYear().intValue() < dateInt) {
                    studentAuthDetailQueryResponse.setExpiredReason("逾期失效，请重新认证");
                }
            }
            studentAuthDetailQueryResponse.setSchoolName(studentAuthDetailResponse.getCollage());
            StudentDegreeEnum studentDegreeEnum = StudentDegreeEnum.formatStudentDegreeEname(studentAuthDetailResponse.getDegree());
            studentAuthDetailQueryResponse.setSchoolRoll(studentDegreeEnum == null ? StudentDegreeEnum.UNKNOW.desc : studentDegreeEnum.desc);
            studentAuthDetailQueryResponse.setStudentCardImg(studentAuthDetailResponse.getStudentCertificateSnapUrl());
            studentAuthDetailQueryResponse.setStudentInfoImg(studentAuthDetailResponse.getPersonalProfileSnapUrl());
            studentAuthDetailQueryResponse.setStudentRecordImg(studentAuthDetailResponse.getRegisterRecordSnapUrl());
            studentAuthDetailQueryResponse.setMemberId(studentAuthDetailResponse.getUserId());
        }

        private BaseResp<MemberStarInfo> queryMemberStar (RequestData requestData){
            BaseResp<MemberStarInfo> resp = new BaseResp<>();
            try {
                //----------------获取会员等级信息----------------
                ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
                PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
                String[] items = new String[]{MileageAccountRequestItemsEnum.LEVELCHANGE.eName};
                mileageAccountQueryRequest.setMemberCardNo(requestData.getFfpNo());
                mileageAccountQueryRequest.setRequestItems(items);
                ptApiCRMRequest.setData(mileageAccountQueryRequest);
                MemberStarInfo memberStarInfo = new MemberStarInfo();
                PtCRMResponse<MileageAccountQueryResponse> mileageAccountQueryResponse = memberService.mileageAccountQuery(ptApiCRMRequest, false);
                MemberLevelChangeSoaModel levelChange = mileageAccountQueryResponse.getData().getLevelChange();
                memberStarInfo.setCurrentLevel(levelChange.getDegradeInfo().getLevelCode());
                memberStarInfo.setCurrentLevelName(MemberLevelEnum.findLevelNameByLevelCode(levelChange.getDegradeInfo().getLevelCode()));
                memberStarInfo.setCurrentScoreGrading(levelChange.getRecentCycleMiles());
                memberStarInfo.setEndDate(
                        levelChange.getDegradeInfo().getEndDate().length() > 0 ? levelChange.getDegradeInfo().getEndDate().replace("-", ".") : null
                );
                //---------获取星级信息----------
                PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequestMemberStar = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest();
                memberStarQueryRequest.setId(Integer.valueOf(requestData.getFfpId()));
                ptApiCRMRequestMemberStar.setData(memberStarQueryRequest);
                PtCRMResponse<MemberStarQueryResp> ptCRMResponse = memberService.queryMemberStar(ptApiCRMRequestMemberStar, false);
                MemberStarQueryResp memberStarQueryResp = ptCRMResponse.getData();
                memberStarInfo.setCurrentStarLevel(memberStarQueryResp.getMemberStarCode());
                //----------计算会员星级信息-------------
                // 1.通过当前等级获取 当前等级星级信息,2.匹配当前星级信息获取最大航段值,计算升级所需航段,3.排序获取最大星级
                PtApiCRMRequest<MemberStarQueryRuleReq> ptApiCRMRequestMemberStarRule = CRMReqUtil.buildCommReqNoToken(requestData.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
                MemberStarQueryRuleReq memberStarQueryRuleReq = new MemberStarQueryRuleReq();
                //每个级别的特批和它的原级别是一个规则
                memberStarQueryRuleReq.setMemberLevelCode(transSpecialLevel(memberStarInfo.getCurrentLevel()));
                memberStarQueryRuleReq.setPageIndex(0);
                memberStarQueryRuleReq.setPageSize(999);
                ptApiCRMRequestMemberStarRule.setData(memberStarQueryRuleReq);
                PtCRMResponse<MemberStarQueryRulePage> pageResp = memberService.queryMemberStarRule(ptApiCRMRequestMemberStarRule, false);
                List<MemberStarQueryRuleResp> memberStarQueryRuleRespList = pageResp.getData().getRuleDetails();
                if (CollectionUtils.isEmpty(memberStarQueryRuleRespList)) {
                    memberStarInfo.setMaxStarLevel(0);
                    memberStarInfo.setNextStarLevelSegment(0);
                } else {
                    Optional<MemberStarQueryRuleResp> first = memberStarQueryRuleRespList.stream()
                            .filter(o -> o.getMemberStarCode().equals(memberStarInfo.getCurrentStarLevel() + ""))
                            .findFirst();
                    if (!first.isPresent()) {
                        throw ServiceException.fail("查询不到当前等级对应星级");
                    }
                    //获取当前星级规则
                    MemberStarQueryRuleResp currentMemberStarQueryRule = first.get();
                    int maxSegment = Integer.parseInt(currentMemberStarQueryRule.getSegmentMax());
                /*memberStarInfo.setNextStarLevelSegment(maxSegment + 1 - memberStarQueryResp.getSegments());
                if (memberStarInfo.getNextStarLevelSegment() >= 900) {
                    memberStarInfo.setNextStarLevelSegment(0);
                }
                PtCRMResponse<MileageDetailSegmentQueryResp> catchSegmentDetail = memberService.toCatchSegmentDetail(null, null, requestData.getFfpId());
                if (null != catchSegmentDetail && null != catchSegmentDetail.getData() && CollectionUtils.isNotEmpty(catchSegmentDetail.getData().getSegments())) {
                    memberStarInfo.setCurrentSegmentGrading(catchSegmentDetail.getData().getSegments().stream().mapToInt(MileageDetailSegmentQueryResp.SegmentsDTO::getNumber).sum());
                }*/
                    memberStarInfo.setNextStarLevelSegment(46 >= Integer.parseInt(currentMemberStarQueryRule.getSegmentMin()) ? maxSegment + 1 - memberStarQueryResp.getSegments() : 0);
                    if (Integer.parseInt(currentMemberStarQueryRule.getMemberLevelCode()) >= 6 && Integer.parseInt(currentMemberStarQueryRule.getMemberStarCode()) >= 3) {
                        memberStarInfo.setNextStarLevelSegment(0);
                    }
                    memberStarInfo.setCurrentSegmentGrading(memberStarQueryResp.getSegments());
                    Optional<MemberStarQueryRuleResp> max = memberStarQueryRuleRespList.stream()
                            .max(Comparator.comparingInt(o -> Integer.parseInt(o.getMemberStarCode())));
                    //获取最大星级
                    if (max.isPresent()) {
                        MemberStarQueryRuleResp maxMemberStarQueryRule = max.get();
                        memberStarInfo.setMaxStarLevel(Integer.parseInt(maxMemberStarQueryRule.getMemberStarCode()));
                    }
                    //------获取是否开启------
                    memberStarInfo.setIsOpen("Y".equals(accountBffConfig.getMemberStarQueryOpen()));
                    //------获取是否就医卡，级别开始结束日期
                    //调用crm，查询会员当前级别
                    String[] pmtItems = {MemberDetailRequestItemsEnum.STATEINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> pt = CRMReqUtil.buildMemberDetailReq(
                            requestData.getFfpNo(), requestData.getFfpId(), channelInfo.getChannelCode(), channelInfo.getChannelPwd(), items);
                    PtMemberDetailRequest pmt = new PtMemberDetailRequest();
                    pmt.setCardNO(requestData.getFfpNo());
                    pmt.setRequestItems(pmtItems);
                    pt.setData(pmt);
                    PtCRMResponse<PtMemberDetail> pr = memberService.memberDetail(pt, false);
                    MemberStateInfoSoaModel memberStateInfo = pr.getData().getStateInfo();
                    //------获取是否就医卡，级别开始结束日期
                    if (null != memberStateInfo) {
                        //是否就医卡
                        memberStarInfo.setIsDoctorCard(memberStateInfo.isIsDoctorCard());
                        if (memberStateInfo.isIsDoctorCard()) {
                            memberStarInfo.setCurrentLevelName("敬医" + MemberLevelEnum.findLevelNameByLevelCode(levelChange.getDegradeInfo().getLevelCode()));
                            memberStarInfo.setEndDate("终身有效");
                        } else {
                            if (Integer.parseInt(memberStarInfo.getCurrentLevel()) >= Integer.parseInt(MemberLevelEnum.Fu_Card.getLevelCode())) {
                                long levelExpireDate = memberStateInfo.getLevelExpireDate();
                                long levelStartDate = memberStateInfo.getLevelStartDate();
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
                                memberStarInfo.setStarDate(simpleDateFormat.format(new Date(levelStartDate)));
                                memberStarInfo.setEndDate(simpleDateFormat.format(new Date(levelExpireDate)));
                            }
                        }
                    }
                    resp.setObjData(memberStarInfo);
                    resp.setResultCode("10001");
                    resp.setResultInfo("成功");
                }
            } catch (Exception e) {
                log.error("查询升级保级出现异常", e);
                throw ServiceException.fail("查询升级保级出现异常");
            }
            return resp;
        }

        /**
         * @param ptCRMResponse
         * @return java.lang.String
         * <AUTHOR>
         * @Description 判断账户安全等级
         * @Date 18:59 2023/7/13
         **/

        private String toJudgeSecuLevel (PtCRMResponse < PtMemberDetail > ptCRMResponse) {
            if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
                return SecuLevelEnum.LOW.code;
            }
            PtMemberDetail ptCRMResponseData = ptCRMResponse.getData();
            MemberBasicInfoSoaModel basicInfo = ptCRMResponseData.getBasicInfo();
            List<MemberRealNameSummarySoaModel> realVerifyInfos = ptCRMResponseData.getRealVerifyInfos();
            List<MemberContactSoaModel> contactInfo = ptCRMResponseData.getContactInfo();
            boolean isRealName = false;
            boolean isRealMobile = false;
            boolean isRealEmail = false;
            if (CollectionUtils.isNotEmpty(realVerifyInfos)) {
                MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = realVerifyInfos.stream().filter(e -> StringUtils.isNotEmpty(e.getStatus()) && e.getStatus().equals(VerifyStatusEnum.PASS.code)).findFirst().orElse(null);
                if (null != memberRealNameSummarySoaModel) {
                    isRealName = true;
                }
            }
            //手机号及邮箱是否已验证
            if (CollectionUtils.isNotEmpty(contactInfo)) {
                MemberContactSoaModel mobileConttactReal = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.MOBILE.getCode() == e.getContactType() && 2 == e.getIsVerify()).findFirst().orElse(null);
                if (null != mobileConttactReal) {
                    isRealMobile = true;
                }
                MemberContactSoaModel emailConttactReal = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.EMAIL.getCode() == e.getContactType() && 2 == e.getIsVerify()).findFirst().orElse(null);
                if (null != emailConttactReal) {
                    isRealEmail = true;
                }

            }
            if (Boolean.TRUE.equals(isRealName && isRealMobile && isRealEmail && basicInfo.getIsSetConsumePwd() && basicInfo.getIsSetLoginPwd())) {
                return SecuLevelEnum.HIGH.code;
            }
            if (toCheckMediumSecuLevel(isRealMobile, isRealEmail, basicInfo.getIsSetLoginPwd())) {
                return SecuLevelEnum.MEDIUM.code;
            }
            return SecuLevelEnum.LOW.code;
        }

        private boolean toCheckMediumSecuLevel ( boolean isRealMobile, boolean isRealEmail, boolean hasSetLoginPwd){
            boolean mediumSeculevel = false;
            if (!isRealEmail && isRealMobile && hasSetLoginPwd) {
                mediumSeculevel = true;
            }
            if (isRealEmail && !isRealMobile && hasSetLoginPwd) {
                mediumSeculevel = true;
            }
            if (isRealEmail && isRealMobile && !hasSetLoginPwd) {
                mediumSeculevel = true;
            }
            if (isRealEmail && isRealMobile && hasSetLoginPwd) {
                mediumSeculevel = true;
            }
            return mediumSeculevel;
        }

        /**
         * @param ptCRMResponse
         * @return java.lang.String
         * <AUTHOR>
         * @Description 判别信息完整度
         * @Date 18:59 2023/7/13
         **/
        private String toJudgeInfoIntegrity (PtCRMResponse < PtMemberDetail > ptCRMResponse) {
            if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
                return InfoIntegrityEnum.LOW.code;
            }
            PtMemberDetail memberDetail = ptCRMResponse.getData();
            MemberBasicInfoSoaModel basicInfo = memberDetail.getBasicInfo();
            List<MemberCertificateSoaModelV2> certificateInfo = memberDetail.getCertificateInfo();
            List<MemberAddressSoaModel> addressInfos = memberDetail.getAddressInfos();
            List<MemberContactSoaModel> contactInfo = memberDetail.getContactInfo();
            if (null != basicInfo && null != certificateInfo && null != addressInfos && null != contactInfo) {
                MemberContactSoaModel mobileContact = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.MOBILE.getCode() == e.getContactType()).findFirst().orElse(null);
                MemberContactSoaModel emailContact = contactInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getContactNumber()) && ContactTypeEnum.EMAIL.getCode() == e.getContactType()).findFirst().orElse(null);
                if (!StringUtils.isAnyEmpty(basicInfo.getCFirstName(), basicInfo.getCLastName(), basicInfo.getEFirstName(), basicInfo.getELastName())
                        && 0 != basicInfo.getBirthDay() && !certificateInfo.isEmpty() && (1 == basicInfo.getSex() || 2 == basicInfo.getSex())
                        && null != mobileContact && null != emailContact && !addressInfos.isEmpty()) {
                    return InfoIntegrityEnum.HIGH.code;
                }
            }
            if (null != basicInfo && null != certificateInfo) {
                if (!StringUtils.isAnyEmpty(basicInfo.getCFirstName(), basicInfo.getCLastName(), basicInfo.getEFirstName(), basicInfo.getELastName())
                        && 0 != basicInfo.getBirthDay() && !certificateInfo.isEmpty() && (1 == basicInfo.getSex() || 2 == basicInfo.getSex())) {
                    return InfoIntegrityEnum.MEDIUM.code;
                }
            }
            return InfoIntegrityEnum.LOW.code;
        }

        /**
         * @param ptCRMResponse
         * @return java.lang.String
         * <AUTHOR>
         * @Description 获取认证的渠道名
         * @Date 15:18 2023/8/24
         **/
        @SuppressWarnings("rawtypes")
        private ChannelDTO toCatchRealNameChannelName (PtCRMResponse ptCRMResponse){
            if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
                return null;
            }
            PtMemberDetail ptCRMResponseData = (PtMemberDetail) ptCRMResponse.getData();
            if (CollectionUtils.isEmpty(ptCRMResponseData.getRealVerifyInfos())) {
                return null;
            }
            //获取最新的认证记录
            MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = ptCRMResponseData.getRealVerifyInfos().stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
            if (null == memberRealNameSummarySoaModel) {
                return null;
            }
            ChannelDTO channelDTO = new ChannelDTO();

            VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(memberRealNameSummarySoaModel.getStatus());
            VerifyChannelEnum verifyChannelEnum = VerifyChannelEnum.checkEnum(memberRealNameSummarySoaModel.getVerifyChannel());
            if (verifyChannelEnum == null && null != verifyStatusEnum) {
                channelDTO.setChannelCode("");
                channelDTO.setChannelName("外部认证");
                channelDTO.setVerifyDate(memberRealNameSummarySoaModel.getVerifyDate());
                return channelDTO;
            } else if (null != verifyStatusEnum) {
                channelDTO.setChannelCode(verifyChannelEnum.code);
                channelDTO.setChannelName(verifyChannelEnum.desc);
                channelDTO.setCredentialChannel(VerifyChannelEnum.toCheckEnumCredential(verifyChannelEnum));
                channelDTO.setVerifyDate(memberRealNameSummarySoaModel.getVerifyDate());
                return channelDTO;
            }
            return null;
        }


        //处理会员航段数信息
        private void processMemberSegmentInfo (MemberCenterInfoResponse
        detail, PtCrmMileageResponse < MemberSegmentResponse > ptCrmMileageResponse){
            if (null != ptCrmMileageResponse && ptCrmMileageResponse.getCode() == 0) {
                int totalNumbers = 0;
                if (null != ptCrmMileageResponse.getData()) {
                    if (CollectionUtils.isNotEmpty(ptCrmMileageResponse.getData().getSegments())) {
                        for (MemberSegmentResponse.Segments segments : ptCrmMileageResponse.getData().getSegments()) {
                            totalNumbers += segments.getNumber();
                        }
                    }
                }
                detail.setMySegment(totalNumbers);
            } else {
                throw ServiceException.fail("【会员中心】会员航段数信息处理异常");
            }
        }

        //处理会员积分信息
        private void processMemberRemainScoreInfo (MemberCenterInfoResponse detail, MemberRemainScoreResp
        memberRemainScoreResp){
            if (memberRemainScoreResp != null && "000".equals(memberRemainScoreResp.getStatusCode())) {
                detail.setMyScore(Long.valueOf(memberRemainScoreResp.getPoint()));
            } else {
                throw ServiceException.fail("【会员中心】会员积分信息处理异常");
            }
        }

        /**
         * 特批卡转换原级别
         *
         * @param level
         * @return 原级别id
         * 1    福卡
         * 2    银卡
         * 3    特批银卡
         * 4    金卡
         * 5    特批金卡
         * 6    白金卡
         * 7    特批VIP白金卡
         * 8    特批白金卡
         * 9    VISA VIP白金卡
         */
        private String transSpecialLevel (String level){
            //每个级别的特批和它的原级别是一个规则
            if (level.equals("3")) {
                return "2";
            }
            if (level.equals("5")) {
                return "4";
            }
            if (level.equals("7") || level.equals("8") || level.equals("9")) {
                return "6";
            }
            return level;
        }

        private List<Product> toCatchThemeCardDetail (String productType){
            List<Product> products = new ArrayList<>();
            PtRequest ptRequest = new PtRequest("V1.0", "B2C", "10001");
            ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
            ptRequest.setThemeCouponTypeList(new String[]{productType});
            ptRequest.setRequest(productQueryRequestDto);
            PtResponse<List<ProductInfo>> ptResponse = queryProducts(ptRequest);
            if (ptResponse != null) {
                List<ScheduledDetails> scheduledDetails = ptResponse.getScheduledDetails();
                if (CollectionUtils.isNotEmpty(scheduledDetails)) {
                    Product product = new Product();
                    if (CollectionUtils.isNotEmpty(scheduledDetails)) {
                        product.setStartAvailDate(scheduledDetails.get(0).getEffectiveStartTime() == null ? "" : scheduledDetails.get(0).getEffectiveStartTime().split(" ")[0]);
                        product.setEndAvailDate(scheduledDetails.get(0).getEffectiveEndTime() == null ? "" : scheduledDetails.get(0).getEffectiveEndTime().split(" ")[0]);
                        if (CollectionUtils.isNotEmpty(scheduledDetails.get(0).getIneffectiveSchedules())) {
                            product.setStartUnAvailDate(scheduledDetails.get(0).getIneffectiveSchedules().get(0).getIneffectiveStartTime() == null
                                    ? "" : scheduledDetails.get(0).getIneffectiveSchedules().get(0).getIneffectiveStartTime().split(" ")[0]);
                            product.setEndUnAvailDate(scheduledDetails.get(0).getIneffectiveSchedules().get(0).getIneffectiveEndTime() == null
                                    ? "" : scheduledDetails.get(0).getIneffectiveSchedules().get(0).getIneffectiveEndTime().split(" ")[0]);
                        }
                    }
                    products.add(product);
                }
            }
            return products;
        }

        public PtResponse queryProducts (PtRequest < ProductQueryRequestDto > ptRequest) {
            String url = accountBffConfig.getUrlCouponApi() + AccountBffConfig.QUERY_THEME_COUPON;
            HttpResult httpResult = HttpUtil.doPostClient(ptRequest, url);
            if (!httpResult.isResult()) {
                throw MultiLangServiceException.fail("调用远程服务繁忙");
            }
            String result = httpResult.getResponse();
            if (StringUtils.isBlank(result)) {
                throw ServiceException.fail("主题卡查询异常");
            }
            Type type = new TypeToken<PtResponse>() {
            }.getType();
            return HoAirGsonUtil.fromJson(result, type);
        }

        /**
         * 我的权益券请求参数
         *
         * @return
         */
        private PtCouponProductGetRequestDto createCouponProductGetRequestDto
        (RequestData < MyCoupon > requestData, ChannelInfo channelInfo,boolean showChangeCoupon){
            MyCoupon data = requestData.getData();
            PtCouponProductGetRequestDto ptCouponProductGetRequestDto = new PtCouponProductGetRequestDto("V1.0", channelInfo.getOrderChannelCode(),
                    requestData.getFfpId(), requestData.getFfpNo());
            List<String> stateList = new ArrayList<>();
            /**
             * 用于区分app上权益券列表里的 可使用和已使用   可使用:Not 已使用:Used 已过期:Expired
             */
            //未使用的
            if ("R".equals(data.getCouponState())) {
                String[] stateArr = {OrderCouponStateEnum.Not.getStateCode(), OrderCouponStateEnum.Appointment.getStateCode(), OrderCouponStateEnum.Giving.getStateCode()};
                stateList = Arrays.asList(stateArr);
                ptCouponProductGetRequestDto.setAvailableStatus("Not");
            }
            if ("E".equals(data.getCouponState())) {//失效状态
                String[] stateArr = {OrderCouponStateEnum.Overdue.getStateCode(), OrderCouponStateEnum.Refund.getStateCode()};
                stateList = Arrays.asList(stateArr);
                ptCouponProductGetRequestDto.setAvailableStatus("Expired");
            } else if ("U".equals(data.getCouponState())) {//已使用
                String[] stateArr = {OrderCouponStateEnum.Used.getStateCode(), OrderCouponStateEnum.WittenOff.getStateCode(), OrderCouponStateEnum.GiveAway.getStateCode()};
                stateList = Arrays.asList(stateArr);
                ptCouponProductGetRequestDto.setAvailableStatus("Used");
            }
            List<String> voucherTypes = Lists.newArrayList();
            if (StringUtils.isNotBlank(data.getCouponSource())) {
                voucherTypes.add(data.getCouponSource());
            } else {
                voucherTypes.addAll(Arrays.asList(VoucherTypesEnum.UPGRADE.getCode(),
                        VoucherTypesEnum.BAGGAGE.getCode(),
                        VoucherTypesEnum.LOUNGE.getCode(),
                        VoucherTypesEnum.ONBOARDWIFI.getCode(),
                        VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode(),
                        VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode(),
                        VoucherTypesEnum.UNLIMITED_FLY_V2.getCode(),
                        VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode(),
                        VoucherTypesEnum.UnlimitUpgradeYear.getCode(),
                        VoucherTypesEnum.RESCHEDULE.getCode(),
                        VoucherTypesEnum.UPGRADECOUPON.getCode(),
                        VoucherTypesEnum.LOUNGECOUPON.getCode(),
                        VoucherTypesEnum.BAGGAGECOUPON.getCode(),
                        VoucherTypesEnum.THEMECOUPON.getCode()
                ));
                //主题卡类型
                voucherTypes.addAll(accountBffConfig.getThemeCouponList());
                voucherTypes.addAll(ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList());
                if (showChangeCoupon) {
                    voucherTypes.add(VoucherTypesEnum.RESCHEDULECOUPON.getCode());
                }
            }
            List<String> couponStateList = stateList.stream().map(couponState -> {
                        EnumVoucherStates voucherStates = (EnumVoucherStates) HoEnumUtil.getEnumByString(EnumVoucherStates.class, couponState);
                        if (null != voucherStates) {
                            return voucherStates.getCode();
                        }
                        return couponState;
                    }
            ).collect(Collectors.toList());
            ptCouponProductGetRequestDto.setVoucherTypes(voucherTypes);
            ptCouponProductGetRequestDto.setCouponState(couponStateList);
            ptCouponProductGetRequestDto.setPageNo(1);
            ptCouponProductGetRequestDto.setPageSize(100);
            return ptCouponProductGetRequestDto;
        }


        //我的优惠券查询条件
        private CouponQueryRequest CreateQueryCouponRequest (RequestData < QueryCouponReq > requestData, ChannelInfo
        channelInfo){
            QueryCouponReq couponReq = requestData.getData();
            List<PtSegmentInfo> segmentInfoList = new ArrayList<>();
            String couponState = couponReq.getCouponState();
            if (CouponStateEnum.R.getState().equals(couponReq.getCouponState())) {//已领取
                couponState = CouponStateEnum.R.getState() + "," + CouponStateEnum.C.getState();
            } else if (CouponStateEnum.N.getState().equals(couponReq.getCouponState())) {//已使用
                couponState = CouponStateEnum.N.getState() + "," + CouponStateEnum.G.getState();
            }
            CouponQueryRequest couponQueryReq = new CouponQueryRequest(
                    "V1.0",
                    channelInfo.getOrderChannelCode(),
                    channelInfo.getUserNo(),
                    requestData.getFfpId(),
                    requestData.getFfpNo(),
                    couponState,
                    segmentInfoList
            );
            return couponQueryReq;
        }

        /**
         * 渲染认证信息
         *
         * @param desc
         * @param verifyStatus
         * @param verifyChannel
         * @param verifyChannelName
         * @return
         */
        private MemberRealInfo initMemberRealInfo (String desc, String verifyStatus, String verifyChannel, String
        verifyChannelName){
            MemberRealInfo memberRealInfo = new MemberRealInfo();
            memberRealInfo.setVerifyDesc(desc);
            memberRealInfo.setVerifyStatus(verifyStatus);
            memberRealInfo.setVerifyChannel(verifyChannel);
            memberRealInfo.setVerifyChannelName(verifyChannelName);
            return memberRealInfo;
        }

        class RealInfoThread implements Runnable {

            private String ffpCardNo;
            private String verifyStatusDesc;
            private String verifyStatusCode;
            private String verifyChannel;
            private String verifyChannelDesc;

            public RealInfoThread(String ffpCardNo, String verifyStatusDesc, String verifyStatusCode, String verifyChannel, String verifyChannelDesc) {
                this.ffpCardNo = ffpCardNo;
                this.verifyStatusDesc = verifyStatusDesc;
                this.verifyStatusCode = verifyStatusCode;
                this.verifyChannel = verifyChannel;
                this.verifyChannelDesc = verifyChannelDesc;
            }

            /**
             * When an object implementing interface <code>Runnable</code> is used
             * to create a thread, starting the thread causes the object's
             * <code>run</code> method to be called in that separately executing
             * thread.
             * <p>
             * The general contract of the method <code>run</code> is that it may
             * take any action whatsoever.
             *
             * @see Thread#run()
             */
            @Override
            public void run() {
                //默认缓存认证实名状态10分钟
                Map<String, String> map = new HashMap<>();
                map.put("ffpCardNo", ffpCardNo);
                map.put("verifyStatusDesc", verifyStatusDesc);
                map.put("verifyStatusCode", verifyStatusCode);
                map.put("verifyChannel", verifyChannel);
                map.put("verifyChannelDesc", verifyChannelDesc);
                redisUtils.set(AccountBffConfig.MEMBER_REAL + ffpCardNo, map, 60 * 10L);
            }

        }

        /**
         * @param ip
         * @param channelCode
         * @param ffpId
         * @return com.juneyaoair.oneorder.accountbff.dto.response.MemberTagResponse
         * <AUTHOR>
         * @Description 拥军标签查询
         * @Date 13:24 2023/6/21
         **/
        public MemberTagResponse queryMemberTag (String ip, String channelCode, String ffpId){
            ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
            CrmMemberBaseApiRequest<MemberTagQueryReqDto> memberTagQueryRequest = CRMReqUtil.buildCommCrmMemberReq(ip, channelInfo.getChannelCode(), channelInfo.getChannelPwd());
            MemberTagQueryReqDto memberTagQueryReqDto = new MemberTagQueryReqDto();
            MemberTagResponse memberTagResponse = new MemberTagResponse();
            memberTagResponse.setIsEffective(false);
            memberTagQueryReqDto.setId(Integer.valueOf(ffpId));
            memberTagQueryReqDto.setIsValid(false);
            memberTagQueryRequest.setData(memberTagQueryReqDto);
            try {
                CrmMemberBaseApiResponse<MemberTagQueryResDto> memberCertificateResResponse = memberService.queryMemberTag(memberTagQueryRequest, false);
                if (memberCertificateResResponse.getCode() == 0) {
                    List<TagQueryListResDto> resultList = memberCertificateResResponse.getData().getResultList();
                    //获取最新的认证记录
                    TagQueryListResDto tagQueryListResDto = resultList.stream().filter(t -> "YDZ".equals(t.getTagCode())).max(Comparator.comparing(TagQueryListResDto::getExpireDate)).orElse(null);
                    if (tagQueryListResDto != null) {
                        Date date = new Date();
                        if ("YDZ".equals(tagQueryListResDto.getTagCode()) && date.before(DateUtil.toDate(tagQueryListResDto.getExpireDate()))) {
                            memberTagResponse.setIsEffective(true);
                        }
                        memberTagResponse.setExpireDate(tagQueryListResDto.getEffectiveDate());
                    } else {
                        throw ServiceException.fail("无认证记录");
                    }

                } else {
                    throw ServiceException.fail(memberCertificateResResponse.getDesc());
                }
            } catch (Exception e) {
                StackTraceElement stackTraceElement = e.getStackTrace()[0];
                log.info("查看拥军优待资格 出错行数 {}, 错误内容 {}", stackTraceElement.getLineNumber(), e.getMessage());
                memberTagResponse.setIsEffective(false);
            }
            return memberTagResponse;
        }

        /**
         * @param telephoneCode 国际区号
         * @param telephoneNum  手机号
         * @return java.lang.String
         * <AUTHOR>
         * @Description 拼接手机区号和手机号
         * @Date 15:11 2024/6/17
         **/
        private String toCombine (String telephoneCode, String telephoneNum){
            if (StringUtils.isAnyEmpty(telephoneCode, telephoneNum)) {
                throw new ServiceException("国际区号和手机号不可为空");
            }
            return telephoneCode + "-" + telephoneNum;
        }

    }
