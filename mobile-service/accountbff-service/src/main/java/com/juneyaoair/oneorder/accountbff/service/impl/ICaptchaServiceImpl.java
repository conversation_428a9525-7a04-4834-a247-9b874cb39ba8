package com.juneyaoair.oneorder.accountbff.service.impl;

import com.google.common.collect.Maps;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.AESTool;
import com.juneyaoair.oneorder.accountbff.dto.request.CommonCaptchaRequest;
import com.juneyaoair.oneorder.accountbff.dto.request.SendEmailRequest;
import com.juneyaoair.oneorder.accountbff.service.CaptchaService;
import com.juneyaoair.oneorder.accountbff.service.SendMailService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.enums.CrmCommonEnum;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.request.LimitCount;
import com.juneyaoair.oneorder.common.response.SendSmsCodeResponse;
import com.juneyaoair.oneorder.common.util.FraudApiInvoker;
import com.juneyaoair.oneorder.config.SmsConfig;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MemberContactSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.SourceType;
import com.juneyaoair.oneorder.crm.dto.login.PtSendCaptchaRequest;
import com.juneyaoair.oneorder.crm.dto.request.MemberVerifyAccountResp;
import com.juneyaoair.oneorder.crm.dto.request.PtMemberDetailRequest;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.*;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName CaptchaServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/8/30 12:31
 * @Version 1.0
 */
@Slf4j
@Service
public class ICaptchaServiceImpl extends CommonService implements CaptchaService {

    private final String B2C_CHANNEL = "B2CWEB";
    private final String B2C_CHANNEL_PWD = "B2CCRM2013";
    private final static String FROM = "吉祥航空";

    private final static String LOGINCODE_IP_SOURCE = "logincodeip";

    private final static String LOGINCODE_MOBILE_SOURCE = "logincodemobile";

    private final static String SMS_COMMON_SOURCE = "sms_common";

    @Autowired
    private IMemberService memberService;
    @Autowired
    private SendMailService sendMailService;
    @Autowired
    private FraudApiInvoker fraudApiInvoker;
    @Resource
    private SmsConfig smsConfig;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private CommonService commonService;

    @Override
    public void commonCaptchaSend(RequestDataDto<CommonCaptchaRequest> commonCaptchaReq, BizDto bizDto) {
        try {
            String blackBox = ServiceContext.getHead().blackBox;
            if (StringUtils.isEmpty(blackBox)) {
                throw ServiceException.fail("设备指纹不可为空");
            }
            CommonCaptchaRequest commonCaptchaRequest = commonCaptchaReq.getData();
            //如果手机号被脱敏，先去获取原手机号
            commonCaptchaRequest.setMobileNum(decrypt(commonCaptchaRequest.getMobileNum()));
            commonCaptchaReq.setBlackBox(blackBox);
            //校验手机号格式
            boolean rightTelNum;
            if ("86".equals(commonCaptchaRequest.getCountryCode())) {
                Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
                Matcher matcher = pattern.matcher(commonCaptchaRequest.getMobileNum());
                rightTelNum = matcher.matches();
            } else {
                //国际手机号只校验位数
                Pattern pattern = Pattern.compile(PatternCommon.MOBILE_INTERNATIONAL);
                Matcher matcher = pattern.matcher(commonCaptchaRequest.getMobileNum());
                rightTelNum = matcher.matches();
            }
            if (!rightTelNum) {
                throw MultiLangServiceException.fail(CommonErrorCode.PHONE_INCORRECT);
            }
            log.info("IP:{},手机号:{}", commonCaptchaRequest.getIp(), commonCaptchaRequest.getMobileNum());
            SourceType sourceType = SourceType.toCatchSourceType(commonCaptchaRequest.getType());
            if (null == sourceType) {
                throw new ServiceException("短信类型非法");
            }
            String cardNo = "";
            String ffpId = "";
            String scene = "";
            if (SourceType.ADD_CONTACT_MOBILE.getValue().equals(commonCaptchaRequest.getType())) {
                //新增手机号时 对于IP和手机号的校验采用新的通用校验方式 后面对于IP及手机号的校验推荐使用新的校验方式
                // IP校验
                CheckDayLicense ipCheckDayLicense = new CheckDayLicense(commonCaptchaRequest.getIp(), sourceType.name() + "_IP", "短信验证码发送失败，达到单日IP发送上限");
                // 手机号校验
                CheckDayLicense phoneCheckDayLicense = new CheckDayLicense(commonCaptchaRequest.getMobileNum(), sourceType.name() + "_PHONE", "短信验证码发送失败，达到单日手机号发送上限");
                commonService.checkDayLicense(false, ipCheckDayLicense, phoneCheckDayLicense);
            } else {
                //记录手机IP对应关系
                Map<String, Object> info = new HashMap<>();
                info.put("mobile", commonCaptchaRequest.getMobileNum());
                info.put("ip", commonCaptchaRequest.getIp());
                this.saveDayInfo(commonCaptchaRequest.getType(), commonCaptchaRequest.getIp(), commonCaptchaRequest.getMobileNum(), info, DateUtil.addOrLessDay(new Date(), 1));
                if ((SourceType.MOBILE_SOURCE.getValue().equals(commonCaptchaRequest.getType()) ||
                        SourceType.CONSUME_SOURCE.getValue().equals(commonCaptchaRequest.getType()) ||
                        SourceType.UPDATEPWD_SOURCE.getValue().equals(commonCaptchaRequest.getType())) && !this.chkDayTotalVisit(commonCaptchaRequest.getIp(), "")) {
                    throw new ServiceException("短信发送失败3");
                }
                if (SourceType.CANCEL_SEAT_PNR.value.equals(commonCaptchaRequest.getType())) {
                    //取消pnr短信发送次数检验
                    LimitCount limitCount = smsConfig.getSendSmsLimit();
                    boolean chkFlag = this.chkSendSmsCountOperation("", commonCaptchaRequest.getMobileNum(), commonCaptchaRequest.getType(), limitCount, null);
                    if (!chkFlag) {
                        throw new ServiceException("短信发送过于频繁");
                    }
                    this.sendSmsCountOperation("", commonCaptchaRequest.getMobileNum(), commonCaptchaRequest.getType(), limitCount);
                }
                //验证IP是否可以发送短信
                if (!this.chkDayVisit(commonCaptchaRequest.getIp(), commonCaptchaRequest.getType(), "")) {
                    throw new ServiceException("今日验证码发送数量已达上限，验证码发送失败");
                }
                //忘记密码  校验手机账户
                if (commonCaptchaRequest.getType().equals(SourceType.FORGETPWD_SOURCE.value)) {
                    String phone = PhoneUtil.formatMobile(commonCaptchaRequest.getCountryCode(), commonCaptchaRequest.getMobileNum());
                    String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(phone, HoAirIpUtil.getLocalIp(), B2C_CHANNEL, B2C_CHANNEL_PWD, items);
                    PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest, false);
                    if (0 != ptMemberDetailPtCRMResponse.getCode()) {
                        throw new ServiceException("验证码发送失败，该手机尚未注册！");
                    }
                    PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                    MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
                    cardNo = basicInfo.getCardNO();
                    ffpId = String.valueOf(basicInfo.getMemberId());
                }
                // 发送登录验证码校验同盾
                if ((SourceType.CANCEL_CHECKIN_SOURCE.value.equals(commonCaptchaRequest.getType()) ||
                        SourceType.EMD_CANCEL_SEAT.value.equals(commonCaptchaRequest.getType()) ||
                        SourceType.ACTIVITY_RESERVATION.getValue().equals(commonCaptchaRequest.getType()))) {
                    String phone = PhoneUtil.formatMobile(commonCaptchaRequest.getCountryCode(), commonCaptchaRequest.getMobileNum());
                    String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(phone, HoAirIpUtil.getLocalIp(), B2C_CHANNEL, B2C_CHANNEL_PWD, items);
                    PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest, false);
                    if (0 != ptMemberDetailPtCRMResponse.getCode()) {
                        throw new ServiceException("验证码发送失败，该手机尚未注册！");
                    }
                    PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                    MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
                    if (null == basicInfo) {
                        throw new ServiceException("会员信息查询出错");
                    }
                    cardNo = basicInfo.getCardNO();
                    ffpId = String.valueOf(basicInfo.getMemberId());
                    scene = "2";
                    //fraudApiInvoker.sendSMSRiskControl(B2C_CHANNEL, null, null, commonCaptchaRequest.getCountryCode(),commonCaptchaRequest.getMobileNum(), HoAirIpUtil.getLocalIp(), "2", null, commonCaptchaRequest.getBlackBox(), basicInfo.getCardNO(), String.valueOf(basicInfo.getMemberId()));
                }
            }
            if ("Y".equals(smsConfig.getSmsUseTongDun())) {
                fraudApiInvoker.sendSMSRiskControl(ServiceContext.getHead().channelNo, null, null, commonCaptchaRequest.getCountryCode(), commonCaptchaRequest.getMobileNum(), bizDto.getIp(), scene, commonCaptchaReq.getBlackBox(), cardNo, ffpId);
            }
            String sendCode = this.getChkCode();
            redisUtil.set("SMS:" + commonCaptchaRequest.getMobileNum() + commonCaptchaRequest.getType(), sendCode, 300);
            Map<String, String> extras = Maps.newHashMap();
            extras.put("sendCode", sendCode);
            extras.put("function", sourceType.getFunction());
            captchaService.commonSmsSend(smsConfig.getSmsCommonSendCode(), commonCaptchaRequest.getCountryCode(), commonCaptchaRequest.getMobileNum(), extras);
            if (SourceType.ADD_CONTACT_MOBILE.getValue().equals(commonCaptchaRequest.getType())) {
                // IP校验
                CheckDayLicense ipCheckDayLicense = new CheckDayLicense(commonCaptchaRequest.getIp(), sourceType.name() + "_IP", null);
                // 手机号校验
                CheckDayLicense phoneCheckDayLicense = new CheckDayLicense(commonCaptchaRequest.getMobileNum(), sourceType.name() + "_PHONE", null);
                commonService.addDayLicense(ipCheckDayLicense, phoneCheckDayLicense);
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * @param originStr
     * @param patternCommon
     * @return boolean
     * <AUTHOR>
     * @Description
     * @Date 15:05 2023/10/20
     **/

    private boolean checkPattern(String originStr, String patternCommon) {
        if (StringUtils.isAnyEmpty(originStr, patternCommon)) {
            return false;
        } else {
            Pattern pattern = Pattern.compile(patternCommon);
            Matcher matcher = pattern.matcher(originStr);
            return matcher.matches();
        }
    }

    /**
     * @param encryptedStr
     * @return java.lang.String
     * <AUTHOR>
     * @Description 解密某数据
     * @Date 21:23 2023/9/19
     **/

    private String decrypt(String encryptedStr) {
        if (StringUtils.isEmpty(encryptedStr)) {
            return encryptedStr;
        }
        try {
            return AESTool.decrypt(encryptedStr, CrmConfig.DEFAULT_TOKEN.substring(0, 16), CrmConfig.DEFAULT_TOKEN.substring(0, 16));
        } catch (Exception e) {
            log.error("解密出错，", e);
            throw new ServiceException("解密出错");
        }
    }

    @Override
    public SendSmsCodeResponse<MemberVerifyAccountResp> checkVerifyCode(RequestData<CommonCaptchaRequest> commonCaptchaReq) {
        CommonCaptchaRequest commonCaptchaRequest = commonCaptchaReq.getData();
        commonCaptchaRequest.setMobileNum(decrypt(commonCaptchaRequest.getMobileNum()));
        //校验手机号格式
        boolean rightTelNum;
        if ("86".equals(commonCaptchaRequest.getCountryCode())) {
            Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
            Matcher matcher = pattern.matcher(commonCaptchaRequest.getMobileNum());
            rightTelNum = matcher.matches();
        } else {
            //国际手机号只校验位数
            Pattern pattern = Pattern.compile(PatternCommon.MOBILE_INTERNATIONAL);
            Matcher matcher = pattern.matcher(commonCaptchaRequest.getMobileNum());
            rightTelNum = matcher.matches();
        }
        if (!rightTelNum) {
            throw MultiLangServiceException.fail(CommonErrorCode.PHONE_INCORRECT);
        }
        SendSmsCodeResponse<MemberVerifyAccountResp> resp = new SendSmsCodeResponse<>();
        if (!SourceType.checkType(commonCaptchaRequest.getType())) {
            throw MultiLangServiceException.fail("非法的请求类型！");
        }
        if (!SourceType.ADD_CONTACT_MOBILE.getValue().equals(commonCaptchaRequest.getType())) {
            //验证IP
            if (!this.chkDayOptErr(commonCaptchaRequest.getIp(), commonCaptchaRequest.getType(), "")) {
                throw ServiceException.fail("IP操作过于频繁！");
            }
            //验证手机
            if (!this.chkDayOptErr(commonCaptchaRequest.getMobileNum(), commonCaptchaRequest.getType(), "")) {
                throw ServiceException.fail("账号操作过于频繁！");
            }
        }
        String regCacheKey = "SMS:" + commonCaptchaRequest.getMobileNum() + commonCaptchaRequest.getType();
        String veryCode = commonCaptchaRequest.getVerifyCode();
        String veryCodeCacheStr = (String) redisUtil.get(regCacheKey);
        if (StringUtils.isBlank(veryCode) || StringUtils.isBlank(veryCodeCacheStr) || !veryCode.equals(veryCodeCacheStr)) {
            throw ServiceException.fail("验证码错误或已经失效！");
        }
        //验证成功，清除账号限制
        if (!SourceType.ADD_CONTACT_MOBILE.getValue().equals(commonCaptchaRequest.getType())) {
            this.clearDayVisit("", commonCaptchaRequest.getMobileNum(), commonCaptchaRequest.getType(), "");
        }
        resp.setResultCode("10001");
        resp.setErrorInfo("正常");
        resp.setResultInfo("正常");
        MemberVerifyAccountResp accountResp = new MemberVerifyAccountResp();
        Date date = new Date();
        String time = DateUtil.dateToString(date, DateUtil.YYYY_MM_DD_PATTERN);
        accountResp.setTime(time);
        accountResp.setUserName(commonCaptchaRequest.getMobileNum());
        //使用card+time生成一个凭证，使用md5加密
        String pass = commonCaptchaRequest.getMobileNum() + time;
        accountResp.setProof(EncoderHandler.encodeByMD5(pass));
        resp.setObjData(accountResp);
        return resp;
    }

    @Override
    public void h5CaptchaSend(RequestDataDto<CommonCaptchaRequest> requestData, BizDto bizDto) {
        try {
            CommonCaptchaRequest request = requestData.getData();
            if (!SourceType.checkType(request.getType())) {
                throw new ServiceException("短信类型未知");
            }
            if (StringUtils.isNotEmpty(request.getCountryCode())
                    && !request.getCountryCode().matches(PatternCommon.TELEPHONE_CODE_MATCH)) {
                throw new ServiceException("国际区号只能为数字");
            }
            request.setMobileNum(decrypt(request.getMobileNum()));
            String phone = PhoneUtil.formatMobile(request.getCountryCode(), request.getMobileNum());
            //一期暂时不经过极验
/*            HashMap<String, String> param = new HashMap<>();
            DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
            param.put("digestmod", digestmodEnum.getName());
            param.put("user_id", bizDto.getIp()); //网站用户id
            param.put("client_type", request.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
            param.put("ip_address", bizDto.getIp()); //传输用户请求验证时所携带的IP
            geetestService.validate(request.getScene(),request,param);*/
            // 发送登录验证码校验同盾
            String scene = "";
            String cardNo = "";
            String ffpId = "";
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = null;
            if (SourceType.LOGINCODE_MOBILE_SOURCE.value.equals(request.getType())) {
                String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(phone, HoAirIpUtil.getLocalIp(), B2C_CHANNEL, B2C_CHANNEL_PWD, items);
                ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest, false);
                if (0 != ptMemberDetailPtCRMResponse.getCode()) {
                    throw new ServiceException("验证码发送失败，该手机尚未注册！");
                }
                PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
                if (null == basicInfo) {
                    throw new ServiceException("会员信息查询出错");
                }
                scene = "2";
                cardNo = basicInfo.getCardNO();
                ffpId = String.valueOf(basicInfo.getMemberId());
            }
            //注册 或者 忘记密码 检验手机号是否注册
            if (SourceType.SMS_SOURCE.value.equals(request.getType()) || SourceType.FORGETPWD_SOURCE.value.equals(request.getType())) {
                if (null == ptMemberDetailPtCRMResponse) {
                    String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(phone, request.getIp(), B2C_CHANNEL, B2C_CHANNEL_PWD, items);
                    ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest, false);
                }
                if (SourceType.SMS_SOURCE.value.equals(request.getType())) {
                    if (CrmCommonEnum.SUC0.getCode() == ptMemberDetailPtCRMResponse.getCode()) {
                        throw new ServiceException("该手机号已被注册");
                    } else {
                        checkReg(requestData.getOriginIp(), requestData.getData().getMobileNum());
                    }
                } else if (SourceType.FORGETPWD_SOURCE.value.equals(request.getType())) {
                    if (CrmCommonEnum.SUC0.getCode() != ptMemberDetailPtCRMResponse.getCode()) {
                        throw new ServiceException("该账号不存在，请重新输入");
                    }
                }
                scene = "1";
            } else if (SourceType.LOGINCODE_MOBILE_SOURCE.value.equals(request.getType())) {
                //快捷登录
                checkLogin(request.getIp(), phone);
            } else {
                //其余短信默认限制次数
                checkCommonSendCount(request.getIp(), request);
            }
            if ("Y".equals(smsConfig.getSmsUseTongDun())) {
                fraudApiInvoker.sendSMSRiskControl(ServiceContext.getHead().channelNo, null, null, request.getCountryCode(), request.getMobileNum(), bizDto.getIp(), scene, requestData.getBlackBox(), cardNo, ffpId);
            }
            //会员验证码发送
            PtSendCaptchaRequest ptSendCaptchaRequest = PtSendCaptchaRequest.builder()
                    //目前固定为1手机
                    .SendChannel(1)
                    .ChannelValue(PhoneUtil.formatMobile(request.getCountryCode(), request.getMobileNum()))
                    .IsInternational(PhoneUtil.checkInternational(request.getCountryCode()))
                    .build();
            Header header = Header.builder()
                    .ClientIP(requestData.getOriginIp())
                    .Timestamp(System.currentTimeMillis())
                    .build();
            PtApiCRMRequest ptApiCRMRequest = PtApiCRMRequest.builder()
                    .requestId(HoAirUuidUtil.randomUUID8())
                    .Data(ptSendCaptchaRequest)
                    .Channel(B2C_CHANNEL)
                    .ChannelPwd(B2C_CHANNEL_PWD)
                    .Header(header)
                    .build();
            captchaService.send(ptApiCRMRequest);
        } catch (Exception e) {
            throw new ServiceException("验证码发送失败！");
        }
    }

    /**
     * @param ip
     * @param mobile
     * @return void
     * <AUTHOR>
     * @Description 检验注册条件
     * @Date 8:45 2023/9/7
     **/
    private void checkReg(String ip, String mobile) {
        //记录手机IP对应关系
        Map<String, Object> info = new HashMap<>();
        info.put("mobile", mobile);
        info.put("ip", ip);
        this.saveDayInfo("register", ip, mobile, info, DateUtil.addOrLessDay(new Date(), 1));
        //验证是否可以发送短信
        if (!this.chkDayOptErr(ip, SourceType.SMS_SOURCE.value, "")) {
            throw new ServiceException("访问频繁！");
        }
        if (!this.chkDayOptErr(mobile, SourceType.SMS_SOURCE.value, "")) {
            throw new ServiceException("注册验证码发送频繁！");
        }
    }


    /**
     * @param ip
     * @param captcha
     * @return boolean
     * <AUTHOR>
     * @Description 通用短信发送次数验证
     * @Date 8:55 2023/9/7
     **/
    private void checkCommonSendCount(String ip, CommonCaptchaRequest captcha) {
        //记录手机IP对应关系
        Map<String, Object> info = new HashMap<>();
        info.put("mobile", captcha.getMobileNum());
        info.put("ip", ip);
        this.saveDayInfo(captcha.getType(), ip, captcha.getMobileNum(), info, DateUtil.addOrLessDay(new Date(), 1));
        //验证是否可以发送短信
        if (!this.chkDayOptErr(ip, SMS_COMMON_SOURCE, "")) {
            throw new ServiceException("访问频繁！");
        }
        if (!this.chkDayOptErr(captcha.getMobileNum(), SMS_COMMON_SOURCE, "")) {
            throw new ServiceException("验证码发送频繁！");
        }
    }


    /**
     * @param ip
     * @param mobile
     * @return void
     * <AUTHOR>
     * @Description 检验登录限制条件
     * @Date 8:45 2023/9/7
     **/
    private void checkLogin(String ip, String mobile) {
        //记录手机IP对应关系
        Map<String, Object> info = new HashMap<>();
        info.put("mobile", mobile);
        info.put("ip", ip);
        this.saveDayInfo("quickLogin", ip, mobile, info, DateUtil.addOrLessDay(new Date(), 1));
        //每日出错控制
        if (!this.chkDayOptErr(ip, "loginErr", "")) {
            throw new ServiceException("错误次数过多！已被暂时限制使用此功能！");
        }
        //账户控制
        if (!this.chkDayOptErr(mobile, "loginErr", "")) {
            throw new ServiceException("账号出错频繁！已被暂时限制使用此功能！");
        }
        //每日访问控制
        if (!this.chkDayOptErr(ip, LOGINCODE_IP_SOURCE, "")) {
            throw new ServiceException("短信发送过于频繁！");
        }
        //账号控制
        if (!this.chkDayOptErr(mobile, LOGINCODE_MOBILE_SOURCE, "")) {
            throw new ServiceException("该账号已达今日发送次数上限！");
        }
    }


    @Override
    public boolean sendEmailCode(RequestData<SendEmailRequest> request) {
        SendEmailRequest req = request.getData();
        req.setEmail(decrypt(req.getEmail()));
        //校验邮箱格式
        if (!checkPattern(req.getEmail(), PatternCommon.EMAIL)) {
            throw new ServiceException("请输入正确的邮箱号");
        }
        if (!SourceType.checkType(req.getType())) {
            throw new ServiceException("非法的渠道类型！");
        }
        String reqType = "";
        if (req.getType().equals(SourceType.EMAIL_CODE_SOURCE.value)) {
            reqType = "绑定邮箱验证码";
        } else if (req.getType().equals(SourceType.MEMBERLOGOUT_EMAIL_SOURCE.value)) {
            reqType = SourceType.MEMBERLOGOUT_EMAIL_SOURCE.desc;
        }
        //验证该邮箱是否可以发送验证码
        if (!this.chkDayEmailVisit(req.getIp(), req.getType(), req.getEmail())) {
            throw new ServiceException(reqType + "超出每日次数限制！");
        }
        try {
            //发送验证码
            String sendCode;
            String oldCode = (String) redisUtil.get(req.getEmail() + req.getType());
            if (null == oldCode || ("").equals(oldCode)) {
                try {
                    sendCode = getChkCode(6);
                } catch (Exception e) {
                    throw new ServiceException("验证码生成失败！");
                }
                redisUtil.set(req.getEmail() + req.getType(), sendCode, 300);
            } else {
                sendCode = oldCode;
            }
            String content = "您" + reqType + "为" + sendCode + "，3分钟内输入有效。为了保证安全，请勿向他人泄露。";
            boolean sendEmail = sendMailService.toSendEmail(FROM, reqType, null, req.getEmail(), content, null, null);
            if (sendEmail) {
                return true;
            } else {
                throw new ServiceException("验证码发送异常");
            }
        } catch (Exception e) {
            log.info("邮箱发送失败", e);
            throw new ServiceException("验证码发送异常");
        }
    }


    /**
     * @param commonCaptchaReq
     * @return java.lang.String
     * <AUTHOR>
     * @Description 原手机号被脱敏 需要先获取原手机号
     * @Date 14:11 2023/8/25
     **/
    private String toFindMobile(RequestData<CommonCaptchaRequest> commonCaptchaReq) {
        CommonCaptchaRequest commonCaptchaRequest = commonCaptchaReq.getData();
        if (null == commonCaptchaRequest || null == commonCaptchaRequest.getMobileNum()) {
            return null;
        }
        if (commonCaptchaRequest.getMobileNum().length() < 5) {
            return commonCaptchaRequest.getMobileNum();
        }
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(commonCaptchaRequest.getIp(), B2C_CHANNEL, B2C_CHANNEL_PWD);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(commonCaptchaReq.getFfpNo());
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest, false);
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            throw new ServiceException("会员基本信息查询出错");
        }
        //手机号校验
        String originTelephone = commonCaptchaRequest.getMobileNum();
        MemberContactSoaModel targetTelephone = CRMReqUtil.toMemberContactSoaModel(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
        if (null == targetTelephone
                || StringUtils.isAnyEmpty(originTelephone, targetTelephone.getContactNumber())
                || originTelephone.length() < 11 || targetTelephone.getContactNumber().length() < 11) {
            throw new ServiceException("手机号校验出错");
        }
        if (!originTelephone.substring(0, 3).equals(targetTelephone.getContactNumber().substring(0, 3))
                || !originTelephone.substring(originTelephone.length() - 2).equals(targetTelephone.getContactNumber().substring(targetTelephone.getContactNumber().length() - 2))) {
            throw new ServiceException("手机号校验出错");
        }
        return targetTelephone.getContactNumber();
    }

    /**
     * @param sendEmailReq
     * @return java.lang.String
     * <AUTHOR>
     * @Description 原邮件被加密 需要先获取原邮件地址
     * @Date 14:12 2023/8/25
     **/
    private String toFindEmail(RequestData<SendEmailRequest> sendEmailReq) {
        SendEmailRequest sendEmailRequest = sendEmailReq.getData();
        if (null == sendEmailRequest || StringUtils.isEmpty(sendEmailRequest.getEmail()) || !sendEmailRequest.getEmail().contains("@")) {
            return null;
        }
        String[] splitEmail = sendEmailRequest.getEmail().split("@");
        if (splitEmail[0].length() <= 3) {
            return sendEmailRequest.getEmail();
        }
        PtApiCRMRequest ptApiRequest = CRMReqUtil.buildCommReqNoToken(sendEmailRequest.getIp(), B2C_CHANNEL, B2C_CHANNEL_PWD);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(sendEmailReq.getFfpNo());
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest, false);
        if (null == ptCRMResponse || null == ptCRMResponse.getData()) {
            throw new ServiceException("会员基本信息查询出错");
        }
        MemberContactSoaModel memberContactSoaModel = CRMReqUtil.toMemberContactSoaModel(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.EMAIL.getCode());
        if (null == memberContactSoaModel) {
            return null;
        }
        String oriEmail = sendEmailRequest.getEmail();
        String targetEmail = memberContactSoaModel.getContactNumber();
        if (oriEmail.length() != targetEmail.length() || !targetEmail.contains("@")) {
            throw new ServiceException("邮箱校验出错");
        }
        //校验前三位及@后面的部分
        if (!oriEmail.substring(0, 3).equals(targetEmail.substring(0, 3)) || !splitEmail[1].equals(targetEmail.split("@")[1])) {
            throw new ServiceException("邮箱校验出错");
        }
        return targetEmail;
    }

}
