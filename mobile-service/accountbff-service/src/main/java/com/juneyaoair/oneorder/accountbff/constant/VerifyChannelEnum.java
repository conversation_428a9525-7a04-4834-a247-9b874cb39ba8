package com.juneyaoair.oneorder.accountbff.constant;

/**
 * <AUTHOR>
 * @description 认证方式说明
 * @date 2018/8/9  10:06.
 */
public enum VerifyChannelEnum {
    UNKNOWN("Unknown", "未实名"),
    Photo("Photo", "其他证件照认证"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("ZhiFuBao", "支付宝认证"),
    Unionpay("Unionpay", "银行卡认证"),
    Face("Face", "人脸识别认证"),
    TENCENT("Tencent", "手机号认证"),
    AliFace("AliFace", "支付宝人脸识别认证"),
    AlipayApp("AlipayApp", "");
    public final String code;
    public final String desc;

    VerifyChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VerifyChannelEnum checkEnum(String code) {
        for (VerifyChannelEnum c : VerifyChannelEnum.values()) {
            if (c.code.equals(code)) {
                return c;
            }
        }
        return null;
    }

    public static boolean toCheckEnumCredential(VerifyChannelEnum verifyChannelEnum) {
        if (null == verifyChannelEnum) {
            return false;
        }

        switch (verifyChannelEnum) {
            case Face:
            case ZhiFuBao:
            case TENCENT:
            case Photo:
            case Unionpay:
            case AlipayApp:
                return true;
            default:
                return false;
        }
    }

}
