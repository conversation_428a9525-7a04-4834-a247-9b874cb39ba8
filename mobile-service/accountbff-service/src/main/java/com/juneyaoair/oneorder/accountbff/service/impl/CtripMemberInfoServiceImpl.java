package com.juneyaoair.oneorder.accountbff.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.accountbff.service.CtripMemberInfoService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.api.order.constant.OrderUrlConstant;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MemberStateInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.request.*;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.dto.GeetestKey;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;


@Service
public class CtripMemberInfoServiceImpl extends HttpBaseServiceImpl implements CtripMemberInfoService {
    @Resource
    private CrmConfig crmConfig;

    @Resource
    private CommonService commonService;

    @Resource
    private IMemberService memberService;

    @Value("${ctrip.member.level.classified.rights:}")
    private String levelClassifiedRights;

    @Value("${ctrip.member.activityNo:1F3D60113C1D44B59B9766AEF4A7A368}")
    private String activityNo;

    @Value("${ctrip.member.activity.rules:}")
    private String activityRules;

    @Resource
    private OrderConfig orderConfig;

    /**
     * 联合会员报名
     * @return
     */
    public ResponseData ctripUnionMemberApply(RequestData<CtripUnionMemberApplyAppReqDto> baseReq) {

        CtripUnionMemberApplyAppReqDto  ctripUnionMemberApplyAppReqDto = baseReq.getData();
        if (ObjectUtils.isEmpty(ctripUnionMemberApplyAppReqDto)) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        if (StringUtils.isBlank(ctripUnionMemberApplyAppReqDto.getHoMemberCardNo())){
            ctripUnionMemberApplyAppReqDto.setHoMemberCardNo(baseReq.getFfpNo());
        }

        ChannelInfo channelInfo = commonService.findChannelInfo(baseReq.getChannelNo());
        CrmMemberBaseApiRequest<CtripUnionMemberApplyAppReqDto> req = CRMReqUtil.buildCommCrmMemberReq(SecurityContextHolder.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        req.setData(ctripUnionMemberApplyAppReqDto);
        CrmMemberBaseApiResponse crmMemberBaseApiResponse= invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.CTRIP_UNION_MEMBER_APPLY,
                new TypeToken<CrmMemberBaseApiResponse>() {
                }.getType());
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw MultiLangServiceException.fail(crmMemberBaseApiResponse.getDesc());
        } else {
            return ResponseData.success();
        }
    }

    public ResponseData<CtripUnionMemberInfoAppResDto> ctripUnictripUnionMemberQueryMemberApply(RequestData<CtripUnionMemberApplyAppReqDto> baseReq) {
        CtripUnionMemberApplyAppReqDto  ctripUnionMemberApplyAppReqDto = baseReq.getData();
        if (ObjectUtils.isEmpty(ctripUnionMemberApplyAppReqDto)) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        if (StringUtils.isBlank(ctripUnionMemberApplyAppReqDto.getHoMemberCardNo())){
            ctripUnionMemberApplyAppReqDto.setHoMemberCardNo(baseReq.getFfpNo());
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(baseReq.getChannelNo());
        CrmMemberBaseApiRequest<CtripUnionMemberApplyAppReqDto> req = CRMReqUtil.buildCommCrmMemberReq(SecurityContextHolder.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        req.setData(ctripUnionMemberApplyAppReqDto);
        CrmMemberBaseApiResponse<CtripUnionMemberInfoAppResDto>   crmMemberBaseApiResponse= invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.CTRIP_UNION_MEMBER_QUERY,
                new TypeToken<CrmMemberBaseApiResponse<CtripUnionMemberInfoAppResDto>>() {
                }.getType());
        CtripUnionMemberInfoAppResDto  ctripUnionMemberInfoAppResDto = new CtripUnionMemberInfoAppResDto();
        if (0 != crmMemberBaseApiResponse.getCode()) {
            ctripUnionMemberInfoAppResDto.setCtripMember(false);

        } else {
            ctripUnionMemberInfoAppResDto  =crmMemberBaseApiResponse.getData();
            ctripUnionMemberInfoAppResDto.setCtripMember(true);
        }
        PtMemberDetail memberDetail = memberService.memberDetail(baseReq.getChannelNo(), ctripUnionMemberApplyAppReqDto.getHoMemberCardNo(),
                new String[]{MemberDetailRequestItemsEnum.STATEINFO.eName,MemberDetailRequestItemsEnum.BASICINFO.eName});
        MemberStateInfoSoaModel memberStateInfoSoaModel = memberDetail.getStateInfo();
       if (memberStateInfoSoaModel!=null){
           ctripUnionMemberInfoAppResDto.setMemberLevel(memberStateInfoSoaModel.getMemberLevel());
           ctripUnionMemberInfoAppResDto.setMemberLevelCode(memberStateInfoSoaModel.getMemberLevelCode());
       }
        MemberBasicInfoSoaModel basicInfoSoaModel = memberDetail.getBasicInfo();
       if (basicInfoSoaModel!=null){
           String  name = basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName();
           ctripUnionMemberInfoAppResDto.setMemberName(SensitiveInfoHider.hideName(name));
       }
        return ResponseData.success(ctripUnionMemberInfoAppResDto);
    }

    public ResponseData ctripUnionMemberRightsReceive(RequestData<CtripUnionMemberRightsAppReqDto> baseReq) {
        CtripUnionMemberRightsAppReqDto  ctripUnionMemberApplyAppReqDto = baseReq.getData();
        if (ObjectUtils.isEmpty(ctripUnionMemberApplyAppReqDto)) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        if (StringUtils.isBlank(ctripUnionMemberApplyAppReqDto.getHoMemberCardNo())){
            ctripUnionMemberApplyAppReqDto.setHoMemberCardNo(baseReq.getFfpNo());
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(ChannelCodeEnum.MOBILE.getChannelCode());
        CrmMemberBaseApiRequest<CtripUnionMemberRightsAppReqDto> req = CRMReqUtil.buildCommCrmMemberReq(SecurityContextHolder.getOriginIp(), channelInfo.getChannelCode(), channelInfo.getChannelPwd());
        req.setData(ctripUnionMemberApplyAppReqDto);
        CrmMemberBaseApiResponse crmMemberBaseApiResponse =invokeHttpClient(req,
                crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.CTRIP_UNION_MEMBER_RIGHTS_RECEIVE,
                new TypeToken<CrmMemberBaseApiResponse>() {
                }.getType());
        if (0 != crmMemberBaseApiResponse.getCode()) {
            throw MultiLangServiceException.fail(crmMemberBaseApiResponse.getDesc());
        } else {
            return ResponseData.success();
        }
    }

    @Override
    public ResponseData<List<RightCategoryInfo>> getLevelClassifiedRights(RequestData<LevelClassifiedRightsReq> baseReq) {
        if (ObjectUtils.isEmpty(baseReq.getData())) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        Type type = new TypeToken<Map<String, RightCategoryInfoDto>>() {}.getType();
        Map<String, RightCategoryInfoDto> rightCategoryInfoMap =HoAirGsonUtil.fromJson(levelClassifiedRights,type);
        RightCategoryInfoDto  rightCategoryInfo = rightCategoryInfoMap.get(baseReq.getData().getMemberLevelCode());
         if (!CollectionUtils.isEmpty(rightCategoryInfo.getRightCategoryInfoList())) {
             return ResponseData.success(rightCategoryInfo.getRightCategoryInfoList());
         }
        return ResponseData.success();
    }


    public ResponseData<String> getRules(RequestData<String> baseReq) {
        if (!ObjectUtils.isEmpty(baseReq.getData())) {
            String  rules = baseReq.getData();
            if("ACTIVITY".equals(rules)){
                return ResponseData.success(activityRules);
            }
            if("AGREEMENT".equals(rules)){
                String  ActivityRules = getActivityRules(activityNo);
                return ResponseData.success(ActivityRules);
            }
        }
        return ResponseData.success();
    }

    public String getActivityRules(String activityNo) {
        CouponActivityReq couponActivityReq = new CouponActivityReq();
        couponActivityReq.setActivityNo(activityNo);
        couponActivityReq.setChannelCode("CTRIP");
        couponActivityReq.setVersion("10");
        couponActivityReq.setUserNo("10007");
        CouponActivityResp catchCoupons = QueryCouponActivity(couponActivityReq);
        if (!CollectionUtils.isEmpty(catchCoupons.getCouponActivityList())) {
            return  catchCoupons.getCouponActivityList().get(0).getRemark();
        }else {
            return "";
        }
    }

    public CouponActivityResp QueryCouponActivity(CouponActivityReq couponActivityReq) {
        String url = orderConfig.URL_FARE_API_COUPON + OrderUrlConstant.SUB_QUERY_COUPON_ACTIVITY;
        HttpResult httpResult = HttpUtil.doPostClient(couponActivityReq,url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw ServiceException.fail("服务器网络错误，查询用户优惠券发生错误");
        }
        Type type = new TypeToken<CouponActivityResp>() {
        }.getType();
        CouponActivityResp ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (null == ptCRMResponse || !"1001".equals(ptCRMResponse.getResultCode())) {
            CouponActivityResp couponActivityResp = new CouponActivityResp();
            couponActivityResp.setResultCode("1002");
            return couponActivityResp;
        }
        return ptCRMResponse;
    }
}
