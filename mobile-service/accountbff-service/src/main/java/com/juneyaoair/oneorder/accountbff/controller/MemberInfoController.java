package com.juneyaoair.oneorder.accountbff.controller;

import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.accountbff.dto.common.MemberRealInfo;
import com.juneyaoair.oneorder.accountbff.dto.request.*;
import com.juneyaoair.oneorder.accountbff.dto.response.*;
import com.juneyaoair.oneorder.accountbff.service.CaptchaService;
import com.juneyaoair.oneorder.accountbff.service.IMemberInfoService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.service.SmsCodeSendService;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.crm.dto.common.MemberRightsResponse;
import com.juneyaoair.oneorder.crm.dto.request.*;
import com.juneyaoair.oneorder.crm.dto.response.MemberRemainScoreResp;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.order.dto.ModifyPersonV2;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName MemberInfoController
 * @Description
 * <AUTHOR>
 * @Date 2023/6/19 13:23
 * @Version 1.0
 */
@Api(value = "MemberInfoController", tags = "会员服务")
@RequestMapping("/member")
@RestController
public class MemberInfoController extends BaseController {

    @Resource
    private IMemberInfoService memberInfoService;

    @Resource
    private CaptchaService captchaService;
    @Resource
    private IGeetestService geetestService;

    @Resource
    private SmsCodeSendService smsCodeSendService;

    @ApiOperation(value = "仅查询我的实名认证状态", notes = "仅查询我的实名认证状态")
    @PostMapping("/onlyCheckRealNameState")
    public ResponseData<MemberRealInfo> onlyCheckRealNameState(@RequestBody @Validated RequestData<MemberDetailReq> requestData) {
        MemberDetailReq memberDetailReq = requestData.getData();
        if (ObjectUtils.isEmpty(memberDetailReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.onlyCheckRealNameStatus(requestData));
    }

    @ApiOperation(value = "会员基本信息", notes = "会员基本信息")
    @PostMapping("/accountBasicInfo")
    public ResponseData<MenberBasicInfo> accountBasicInfo(@RequestBody @Validated RequestData<BizDto> requestData, HttpServletRequest request) {
        initBizDto(requestData.getData(), request);
        return ResponseData.suc(memberInfoService.toCatchBasicInfo(requestData.getFfpId(),requestData.getFfpNo(),requestData.getData()));
    }

    @ApiOperation(value = "账户资产", notes = "账户资产")
    @PostMapping("/accountAsset")
    public ResponseData<AssetResponse> accountAsset(@RequestBody @Validated RequestData requestData) {
        return ResponseData.suc(memberInfoService.toCatchAssetInfo(requestData));
    }


    @ApiOperation(value = "会员等级详情", notes = "会员等级详情")
    @PostMapping("/accountLevelDetail")
    public ResponseData<AccountLevelDetailResp> accountLevelDetail(@RequestBody @Validated RequestData requestData) {
        return ResponseData.suc(memberInfoService.toCatchLevelDetail(requestData));
    }

    @ApiOperation(value = "常用乘机人列表查询", notes = "常用乘机人列表查询")
    @PostMapping("/toCatchPassengerList")
    public ResponseData<QueryCommonPersonResp> toCatchPassengerList(@RequestBody @Validated RequestData<CommonPersonQuery> requestData,HttpServletRequest httpServletRequest) {
        CommonPersonQuery commonPersonQuery = requestData.getData();
        if (ObjectUtils.isEmpty(commonPersonQuery)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        BizDto bizDto = initBizDto(httpServletRequest);
        return ResponseData.suc(memberInfoService.toCatchPassengerList(bizDto, requestData.getLanguage(), requestData.getFfpId(),requestData.getFfpNo(),requestData.getData()));
    }

    @ApiOperation(value = "查询常用联系人", notes = "查询常用联系人")
    @PostMapping("/toCatchCommonContacts")
    public ResponseData<QueryCommonContactsResp> toCatchCommonContacts(@RequestBody @Validated RequestData<QueryCommonContactsReq> requestData) {
        QueryCommonContactsReq commonContactsReq = requestData.getData();
        if (ObjectUtils.isEmpty(commonContactsReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchCommonContacts(requestData));
    }

    @ApiOperation(value = "查询受益人列表", notes = "查询受益人列表")
    @PostMapping("/toCatchBeneficiaryList")
    public ResponseData<MemberQueryBeneficiaryResponse> toCatchBeneficiaryList(@RequestBody @Validated RequestData<MemberQueryBeneficiaryRequest> requestData) {
        MemberQueryBeneficiaryRequest beneficiaryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(beneficiaryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchBeneficiaryList(requestData));
    }

    @ApiOperation(value = "积分累积计算器", notes = "积分累积计算器")
    @PostMapping("/toCalculateCumulative")
    public ResponseData<CumulativeCalculatorResponse> toCalculateCumulative(@RequestBody @Validated RequestData<CumulativeCalculatorRequest> requestData) {
        CumulativeCalculatorRequest calculatorRequest = requestData.getData();
        if (ObjectUtils.isEmpty(calculatorRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCalculateCumulative(requestData));
    }

    @ApiOperation(value = "积分兑换计算器", notes = "积分兑换计算器")
    @PostMapping("/toCalculateExchange")
    public ResponseData<ExchangeCalculatorResponse> toCalculateExchange(@RequestBody @Validated RequestData<ExchangeCalculatorRequest> requestData) {
        ExchangeCalculatorRequest exchangeCalculatorRequest = requestData.getData();
        if (ObjectUtils.isEmpty(exchangeCalculatorRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCalculateExchange(requestData));
    }

    @ApiOperation(value = "航线机场查询", notes = "航线机场查询")
    @PostMapping("/toCatchFlightLineAirPorts")
    public ResponseData<CalculatorAirportSelections> toCatchFlightLineAirPorts(@RequestBody RequestData requestData) {
        return ResponseData.suc(memberInfoService.toCatchFlightLineAirPorts(requestData));
    }

    @ApiOperation(value = "查询账户完整度及安全等级", notes = "查询账户完整度及安全等级")
    @PostMapping("/toQueryAccountIntegrity")
    public ResponseData<InfoIntegrityResponse> toQueryAccountIntegrity(@RequestBody @Validated RequestData requestData) {
        return ResponseData.suc(memberInfoService.toQueryAccountIntegrity(requestData));
    }

    @ApiOperation(value = "发送验证码(通用)", notes = "发送验证码（通用）")
    @PostMapping(value = "/commonSend", produces = {"application/json;charset=UTF-8"})
    public ResponseData commonCaptchaSend(@RequestBody @Validated RequestDataDto<CommonCaptchaRequest> requestData, HttpServletRequest request) {
        CommonCaptchaRequest commonCaptchaRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(commonCaptchaRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        commonCaptchaRequest.setIp(IpUtils.getIpAddr(request));
        BizDto bizDto = initBizDto(request);
        captchaService.commonCaptchaSend(requestData,bizDto);
        return ResponseData.suc();
    }

    @ApiOperation(value = "电子行程单验证码发送", notes = "电子行程单验证码发送")
    @PostMapping(value = "/ecItineraryVerifyCodeSend", produces = {"application/json;charset=UTF-8"})
    @SuppressWarnings("rawtypes")
    public ResponseData ecItineraryVerifyCodeSend(@RequestBody @Validated RequestData<GeetestDto> requestData, HttpServletRequest request) {
        GeetestDto geetestDto = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(geetestDto)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        smsCodeSendService.sendVerifyCodeByFfp(requestData.getFfpNo(), geetestDto, requestData.getChannelNo(), request);
        return ResponseData.suc();
    }

    @ApiOperation(value = "通过会员卡号发送验证码", notes = "通过会员卡号发送验证码")
    @PostMapping(value = "/sendVerifyCodeByCardNo", produces = {"application/json;charset=UTF-8"})
    @SuppressWarnings("rawtypes")
    public ResponseData sendVerifyCodeByCardNo(@RequestBody @Validated RequestData<VerifyCodeSendRequest> requestData, HttpServletRequest request) {
        VerifyCodeSendRequest verifyCodeSendRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(verifyCodeSendRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        smsCodeSendService.sendVerifyCodeByCardNo(requestData.getFfpNo(), verifyCodeSendRequest, requestData.getChannelNo(), request);
        return ResponseData.suc();
    }

    @ApiOperation(value = "验证码校验", notes = "验证码校验（通过会员卡号）")
    @PostMapping(value = "/codeVerifyByCardNo", produces = {"application/json;charset=UTF-8"})
    @SuppressWarnings("rawtypes")
    public ResponseData codeVerifyByCardNo(@RequestBody @Validated RequestData<VerifyCodeCheckRequest> requestData, HttpServletRequest request) {
        VerifyCodeCheckRequest verifyCodeCheckRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(verifyCodeCheckRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        smsCodeSendService.checkVerifyCodeByCardNo(requestData.getFfpNo(), verifyCodeCheckRequest, requestData.getChannelNo(), request);
        return ResponseData.suc();
    }


    @ApiOperation(value = "电子行程单验证码验证", notes = "电子行程单验证码验证")
    @PostMapping(value = "/ecItineraryCodeVerify", produces = {"application/json;charset=UTF-8"})
    @SuppressWarnings("rawtypes")
    public ResponseData ecItineraryCodeVerify(@RequestBody @Validated RequestData<String> requestData, HttpServletRequest request) {
        String checkSmsCode = requestData.getData();
        if (StringUtils.isEmpty(checkSmsCode)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        BizDto bizDto = initBizDto(request);
        smsCodeSendService.checkSmsCommonCodeByFfp(requestData.getFfpNo(), requestData.getData(), requestData.getChannelNo(),bizDto,true);
        return ResponseData.suc();
    }

    @ApiOperation(value = "验证码校验(通用)", notes = "验证码校验（通用）")
    @PostMapping(value = "/commonSendCheck", produces = {"application/json;charset=UTF-8"})
    public ResponseData commonSendCheck(@RequestBody @Validated RequestData<CommonCaptchaRequest> requestData, HttpServletRequest request) {
        CommonCaptchaRequest commonCaptchaRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(commonCaptchaRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        commonCaptchaRequest.setIp(IpUtils.getIpAddr(request));
        return ResponseData.suc(captchaService.checkVerifyCode(requestData));
    }

    @ApiOperation(value = "h5验证码发送", notes = "底层是调取CRM短信发送接口，目的在于由CRM进行验证码的验证")
    @PostMapping(value = "/h5CaptchSend", produces = {"application/json;charset=UTF-8"})
    public ResponseData h5CaptchSend(@RequestBody @Validated RequestDataDto<CommonCaptchaRequest> requestData, HttpServletRequest request) {
        CommonCaptchaRequest commonCaptchaRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(commonCaptchaRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        commonCaptchaRequest.setIp(IpUtils.getIpAddr(request));
        BizDto bizDto = initBizDto(request);
        captchaService.h5CaptchaSend(requestData, bizDto);
        return ResponseData.suc();
    }

    @ApiOperation(value = "邮件验证码发送", notes = "邮件验证码发送")
    @PostMapping(value = "/sendEmailCode", produces = {"application/json;charset=UTF-8"})
    public ResponseData sendEmailCode(@RequestBody @Validated RequestData<SendEmailRequest> requestData, HttpServletRequest request) {
        SendEmailRequest sendEmailRequest = requestData.getData();
        if (org.springframework.util.ObjectUtils.isEmpty(sendEmailRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        sendEmailRequest.setIp(IpUtils.getIpAddr(request));
        return ResponseData.suc(captchaService.sendEmailCode(requestData));
    }

    @ApiOperation(value = "修改手机号", notes = "修改手机号")
    @PostMapping("/toUpdateMobileNum")
    public ResponseData<Boolean> toUpdateMobileNum(@RequestBody @Validated RequestData<TelephoneInfoReq> requestData) {
        TelephoneInfoReq contactReq = requestData.getData();
        if (ObjectUtils.isEmpty(contactReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toUpdateMobileNum(requestData));
    }

    @ApiOperation(value = "邮箱解除绑定", notes = "邮箱解除绑定")
    @PostMapping("/toUnbindEmail")
    public ResponseData<Boolean> toUnbindEmail(@RequestBody @Validated RequestData<UnbingingEmailReq> requestData) {
        UnbingingEmailReq unbingingEmailReq = requestData.getData();
        if (ObjectUtils.isEmpty(unbingingEmailReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toUnbindEmail(requestData));
    }

    @ApiOperation(value = "绑定联系方式", notes = "绑定联系方式（手机/邮箱等）")
    @PostMapping("/toBindingContact")
    public ResponseData<Boolean> toBindingMobile(@RequestBody @Validated RequestData<ContactReq> requestData) {
        ContactReq contactReq = requestData.getData();
        if (ObjectUtils.isEmpty(contactReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toAddContact(requestData));
    }

    @ApiOperation(value = "查询我的账户中的优惠券", notes = "查询我的账户中的优惠券")
    @PostMapping("/toQueryMyNormalCoupons")
    public ResponseData<AvailCouponsResponse> queryMyNormalCoupons(@RequestBody @Validated RequestData<QueryCouponReq> requestData) {
        QueryCouponReq queryCouponReq = requestData.getData();
        if (ObjectUtils.isEmpty(queryCouponReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toQueryNormalCoupons(requestData));
    }

    @ApiOperation(value = "查询我的账户中的权益券", notes = "查询我的账户中的权益券")
    @PostMapping("/toQueryMyRightCoupons")
    public ResponseData<List<AvailCoupon>> queryMyRightCoupons(@RequestBody @Validated RequestData<MyCoupon> requestData, HttpServletRequest request) {
        MyCoupon myCoupon = requestData.getData();
        if (ObjectUtils.isEmpty(myCoupon)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchMyRightCoupons(requestData));
    }

    @ApiOperation(value = "查询指定类型权益券清单", notes = "查询指定类型权益券清单")
    @PostMapping("/queryCouponProduct")
    public ResponseData<PageResult<CouponProductResult>> queryCouponProduct(@RequestBody @Validated RequestData<CouponProductParam> requestData) {
        PageResult<CouponProductResult> pageResult = memberInfoService.queryCouponProduct(requestData, requestData.getData());
        return ResponseData.suc(pageResult);
    }

    @ApiOperation(value = "设置消费密码", notes = "设置消费密码")
    @PostMapping("/toSetConsumePwd")
    @Deprecated
    public ResponseData<Boolean> toSetConsumePwd(@RequestBody @Validated RequestData<ResetSalePassword> requestData) {
        ResetSalePassword consumptionPwd = requestData.getData();
        if (ObjectUtils.isEmpty(consumptionPwd)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toSetConsumptionPwd(requestData));
    }

    @ApiOperation(value = "检查消费密码设置安全性", notes = "检查消费密码设置安全性")
    @PostMapping("/toCheckSecurityOfConsumptionPwd")
    @SuppressWarnings("all")
    public ResponseData<Boolean> toCheckSecurityOfConsumptionPwd(@RequestBody @Validated RequestData<SecurityCheckRequest> requestData, HttpServletRequest request) {
        SecurityCheckRequest securityCheckRequest = requestData.getData();
        if (ObjectUtils.isEmpty(securityCheckRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toCheckSecurityOfConsumptionPwd(requestData, request);
        return ResponseData.suc();
    }

    @ApiOperation(value = "首次设置消费密码或忘记密码", notes = "首次设置消费密码或忘记密码")
    @PostMapping("/toSetConsumptionPwd")
    @SuppressWarnings("all")
    public ResponseData toSetConsumePwd(@RequestBody @Validated RequestData<PassWordSettingRequest> requestData, HttpServletRequest request) {
        PassWordSettingRequest passWordSettingRequest = requestData.getData();
        if (ObjectUtils.isEmpty(passWordSettingRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toChangeConsumptionPwd(requestData, request);
        return ResponseData.suc();
    }

    @ApiOperation(value = "修改消费密码", notes = "修改消费密码（利用旧密码修改）")
    @PostMapping("/toResetConsumptionPwd")
    public ResponseData<Boolean> toResetConsumptionPwd(@RequestBody @Validated RequestData<ModifyConsumerPwd> requestData) {
        ModifyConsumerPwd modifyConsumerPwd = requestData.getData();
        if (ObjectUtils.isEmpty(modifyConsumerPwd)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toResetConsumptionPwd(requestData));
    }

    @ApiOperation(value = "首次设置登录密码", notes = "首次设置登录密码")
    @PostMapping("/toFirstSetLoginPwd")
    public ResponseData<Boolean> toFirstSetLoginPwd(@RequestBody @Validated RequestData<FirstSetLoginPwd> requestData) {
        FirstSetLoginPwd firstSetLoginPwd = requestData.getData();
        if (ObjectUtils.isEmpty(firstSetLoginPwd)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toFirstSetLoginPwd(requestData);
        return ResponseData.suc();
    }

    @ApiOperation(value = "利用验证码修改登录密码", notes = "利用验证码修改登录密码")
    @PostMapping("/toResetLoginPwd")
    public ResponseData<Boolean> toResetLoginPwd(@RequestBody @Validated RequestData<SetLoginPwd> requestData) {
        SetLoginPwd modifyLoginPwd = requestData.getData();
        if (ObjectUtils.isEmpty(modifyLoginPwd)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toResetLoginPwd(requestData);
        return ResponseData.suc();
    }

    @ApiOperation(value = "添加常用联系人", notes = "添加常用联系人")
    @PostMapping("/toAddCommonContacts")
    public ResponseData<Integer> toAddCommonContacts(@RequestBody @Validated RequestData<ModifyCommonContactsReq> requestData) {
        ModifyCommonContactsReq modifyCommonContactsReq = requestData.getData();
        if (ObjectUtils.isEmpty(modifyCommonContactsReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toAddCommonContacts(requestData));
    }

    @ApiOperation(value = "修改常用联系人", notes = "修改常用联系人")
    @PostMapping("/toModifyCommonContacts")
    public ResponseData<Boolean> toModifyCommonContacts(@RequestBody @Validated RequestData<ModifyCommonContactsReq> requestData) {
        ModifyCommonContactsReq modifyCommonContactsReq = requestData.getData();
        if (ObjectUtils.isEmpty(modifyCommonContactsReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toModifyCommonContacts(requestData));
    }

    @ApiOperation(value = "删除常用联系人", notes = "删除常用联系人")
    @PostMapping("/toDeleteCommonContacts")
    public ResponseData<Boolean> toDeleteCommonContacts(@RequestBody @Validated RequestData<ModifyCommonContactsReq> requestData) {
        ModifyCommonContactsReq modifyCommonContactsReq = requestData.getData();
        if (ObjectUtils.isEmpty(modifyCommonContactsReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toDeleteCommonContacts(requestData));
    }

    @ApiOperation(value = "添加常用乘机人", notes = "添加常用乘机人")
    @PostMapping("/toAddCommonPerson")
    public ResponseData<List<QueryCommonPersonInfo>> toAddCommonPerson(@RequestBody @Validated RequestDataDto<ModifyPersonV2> requestData,HttpServletRequest httpServletRequest) {
        BizDto bizDto = initBizDto(httpServletRequest);
        return ResponseData.suc(memberInfoService.toAddCommonPerson(bizDto,requestData));
    }

    @ApiOperation(value = "修改常用乘机人", notes = "修改常用乘机人以及删除乘机人")
    @PostMapping("/toModifyCommonPerson")
    public ResponseData<Boolean> toModifyCommonPerson(@RequestBody @Validated RequestDataDto<ModifyPersonV2> requestData,HttpServletRequest httpServletRequest) {
        BizDto bizDto = initBizDto(httpServletRequest);
        return ResponseData.suc(memberInfoService.toModifyCommonPerson(bizDto,requestData));
    }

    @ApiOperation(value = "新增受益人", notes = "新增受益人")
    @PostMapping("/toAddBeneficiary")
    public ResponseData<Boolean> toAddBeneficiary(@RequestBody @Validated RequestData<ModifyBeneficiaryRequest> requestData) {
        ModifyBeneficiaryRequest modifyBeneficiaryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(modifyBeneficiaryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toAddBeneficiary(requestData));
    }

    @ApiOperation(value = "修改受益人", notes = "修改受益人")
    @PostMapping("/toModifyBeneficiary")
    public ResponseData<Boolean> toModifyBeneficiary(@RequestBody @Validated RequestData<ModifyBeneficiaryRequest> requestData) {
        ModifyBeneficiaryRequest modifyBeneficiaryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(modifyBeneficiaryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toModifyBeneficiary(requestData));
    }

    @ApiOperation(value = "删除受益人", notes = "删除受益人")
    @PostMapping("/toDeleteBeneficiary")
    public ResponseData<Boolean> toDeleteBeneficiary(@RequestBody @Validated RequestData<ModifyBeneficiaryDelRequest> requestData) {
        ModifyBeneficiaryDelRequest modifyBeneficiaryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(modifyBeneficiaryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toDeleteBeneficiary(requestData));
    }

    @ApiOperation(value = "查询积分余额", notes = "查询积分余额")
    @PostMapping("/toQueryMemberRemainScore")
    public ResponseData<MemberRemainScoreResp> queryMemberRemainScore(@RequestBody @Validated RequestData<BizDto> requestData) {
        return ResponseData.suc(memberInfoService.toCatchMemberScore(requestData));
    }

    @ApiOperation(value = "即将失效积分查询", notes = "即将失效积分查询")
    @PostMapping("/toCatchExpiringScore")
    public ResponseData<ExpiringMilesQueryResonse> toCatchExpiringScore(@RequestBody @Validated RequestData<ExpiringMilesQueryRequest> requestData) {
        ExpiringMilesQueryRequest expiringMilesQueryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(expiringMilesQueryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchExpiringScore(requestData));
    }

    @ApiOperation(value = "查询积分调整明细", notes = "查询积分调整明细")
    @PostMapping("/toQueryScoreDetail")
    public ResponseData<List<MileageRecordQueryResponse>> toQueryScoreDetail(@RequestBody @Validated RequestData<MileageRecordQueryRequest> requestData) {
        MileageRecordQueryRequest mileageRecordQueryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(mileageRecordQueryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchScoreDetail(requestData));
    }

    @ApiOperation(value = "查询航段调整明细", notes = "查询航段调整明细")
    @PostMapping("/toQuerySegmentDetail")
    public ResponseData<List<SegmentRecordQueryResponse>> toQuerySegmentDetail(@RequestBody @Validated RequestData<SegmentRecordQueryRequest> requestData) {
        SegmentRecordQueryRequest segmentRecordQueryRequest = requestData.getData();
        if (ObjectUtils.isEmpty(segmentRecordQueryRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toQuerySegmentDetail(requestData));
    }

    @ApiOperation(value = "会员信息编辑", notes = "会员信息编辑")
    @PostMapping("/toEditMemberInfo")
    public ResponseData<Boolean> toEditMemberInfo(@RequestBody @Validated RequestData<MemberInfoEditRequest> requestData) {
        MemberInfoEditRequest memberInfoEditRequest = requestData.getData();
        if (ObjectUtils.isEmpty(memberInfoEditRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toEditMemberInfo(requestData));
    }

    @ApiOperation(value = "删除证件", notes = "删除证件")
    @PostMapping("/toDeleteCertificate")
    public ResponseData<Boolean> toDeleteCertificate(@RequestBody @Validated RequestData<MemberCertificateDelRequest> requestData) {
        MemberCertificateDelRequest memberCertificateDelRequest = requestData.getData();
        if (ObjectUtils.isEmpty(memberCertificateDelRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toDeleteCertificate(requestData));
    }

    @ApiOperation(value = "保存实名信息", notes = "保存实名信息")
    @PostMapping("/toPreserveRealNameInfo")
    public ResponseData<NumberAndTypeResponse> toPreserveRealNameInfo(@RequestBody @Validated RequestData<MemberInfoPreserveRequest> requestData) {
        MemberInfoPreserveRequest memberInfoPreserveReq = requestData.getData();
        if (ObjectUtils.isEmpty(memberInfoPreserveReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toPreserveRealNameInfo(requestData));
    }


    @ApiOperation(value = "添加/修改证件", notes = "添加/修改证件")
    @PostMapping("/toModifyCertificate")
    public ResponseData<String> toAddCertificate(@RequestBody @Validated RequestData<ModifyCertificateNumberReq> requestData) {
        ModifyCertificateNumberReq modifyCertificateNumberReq = requestData.getData();
        if (ObjectUtils.isEmpty(modifyCertificateNumberReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toModifyCertificate(requestData));
    }

    @ApiOperation(value = "积分补登", notes = "积分补登")
    @PostMapping("/toCompensateScore")
    public ResponseData<Boolean> toCompensateScore(@RequestBody @Validated RequestData<MemberPointPatchApplyRequest> requestData) {
        MemberPointPatchApplyRequest memberPointPatchApplyRequest = requestData.getData();
        if (ObjectUtils.isEmpty(memberPointPatchApplyRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCompensateScore(requestData));
    }

    @ApiOperation(value = "积分补登列表查询", notes = "积分补登列表查询")
    @PostMapping("/toCatchRetroRecord")
    public ResponseData<List<MileageRetroRecord>> toCatchRetroRecord(@RequestBody @Validated RequestData<MileageRetroRequest> requestData,
                                                                     HttpServletRequest request) {
        if (SceneEnum.getEnumByCode(
                requestData.getData().getScene()) != SceneEnum.TICKET_CHECK) {
            ServiceException.fail("验证失败");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        HashMap<String, String> param = new HashMap<>();
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", IpUtils.getIpAddr(request)); //网站用户id
        param.put("client_type", requestData.getData().getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ServiceContext.getHead().clientIp); //传输用户请求验证时所携带的IP
        geetestService.validate(requestData.getData().getScene(), requestData.getData(), param);
        MileageRetroRequest mileageRetroRequest = requestData.getData();
        if (ObjectUtils.isEmpty(mileageRetroRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchRetroRecord(requestData));
    }

    @ApiOperation(value = "查询我的实名认证信息", notes = "查询我的实名认证信息")
    @PostMapping("/toCatchRealNameInfo")
    public ResponseData<MemberRealInfo> toCatchRealNameInfo(@RequestBody @Validated RequestData<MemberDetailReq> requestData) {
        MemberDetailReq memberDetailReq = requestData.getData();
        if (ObjectUtils.isEmpty(memberDetailReq)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        return ResponseData.suc(memberInfoService.toCatchRealNameInfo(requestData));
    }

    @ApiOperation(value = "获取全部等级权益详情", notes = "获取全部等级权益详情")
    @PostMapping("/toCatchCommonRights")
    public ResponseData<SeachMemberRightsResponse> toCatchCommonRights(@RequestBody @Validated RequestDataDto requestData,HttpServletRequest request) {
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(memberInfoService.toCatchCommonRights(requestData,bizDto));
    }

    @ApiOperation(value = "获取会员当前等级的权益详情", notes = "获取会员当前等级的权益详情")
    @PostMapping("/toCatchCurrentRights")
    public ResponseData<MemberRightsResponse> toCatchCurrentRights(@RequestBody @Validated RequestDataDto requestData,HttpServletRequest request) {
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(memberInfoService.toCatchCurrentRights(requestData,bizDto));
    }

    @ApiOperation(value = "支付宝实名认证", notes = "支付宝实名认证")
    @PostMapping("/toTakeAlipayAuth")
    public ResponseData toTakeAlipayAuth(@RequestBody @Validated RequestData<AlipayAuthRequest> requestData) {
        AlipayAuthRequest alipayAuthRequest = requestData.getData();
        if (ObjectUtils.isEmpty(alipayAuthRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toTakeAlipayAuth(requestData);
        return ResponseData.suc();
    }

    @ApiOperation(value = "同盾认证", notes = "同盾认证")
    @PostMapping("/toTakeTongdunAuth")
    public ResponseData toTakeTongdunAuth(@RequestBody @Validated RequestData<TongdunAuthRequest> requestData) {
        TongdunAuthRequest tongdunAuthRequest = requestData.getData();
        if (ObjectUtils.isEmpty(tongdunAuthRequest)) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        memberInfoService.toTakeTongdunAuth(requestData);
        return ResponseData.suc();
    }
}
